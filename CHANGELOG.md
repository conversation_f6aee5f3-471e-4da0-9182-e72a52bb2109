# 更新日誌

## [未發布]

### 新增
- 節點定義管理 API
  - 新增節點定義
  - 更新節點定義
  - 刪除節點定義
  - 獲取節點定義列表
- 工作流程範本 API
  - 基礎的 CRUD 操作
  - 範本驗證機制
- 統計分析 API
  - 基礎統計分析
  - 假設檢定
  - 相關性分析
  - 卡方分析

### 優化
- 改進 RBAC 權限控制系統
- 優化資料庫查詢效能
- 改進錯誤處理機制
- 加強 API 參數驗證

### 修復
- 修正使用者認證相關問題
- 修復資料庫連接池設定
- 修正檔案上傳大小限制

## [0.1.0] - 2024-03-20

### 新增
- 初始專案設置
- 基本的使用者認證系統
- 資料庫遷移和種子資料
- API 文件自動生成
- 基礎的 CRUD API 實作

### 技術基礎
- FastAPI 框架設置
- PostgreSQL 資料庫整合
- Prisma ORM 設置
- JWT 認證機制
- CORS 設置

## [1.1.0] - 2024-02-16

### 新增
- 新增系統代碼（SystemCode）模型
- 新增專案編號（projectNumber）欄位
- 新增預設系統代碼環境變數配置

### 變更
- 移除 Project 和 SystemCode 之間的關聯，改為使用預設系統代碼
- 更新專案創建邏輯，自動生成專案編號
- 優化數據庫結構，簡化系統代碼的使用方式

### 技術細節
- 系統代碼預設為 "IYM"
- 專案編號格式：`${systemCode}_${YYYYMMDD}_${RANDOM}`
- 環境變數：
  - `DEFAULT_SYSTEM_CODE`: 預設系統代碼
  - `DEFAULT_SYSTEM_NAME`: 預設系統名稱

### 待辦事項
- [ ] 考慮將系統代碼相關配置移至配置文件
- [ ] 優化專案編號生成邏輯
- [ ] 補充 API 文檔 
- [ ] 實作暗黑模式功能
  - [ ] 設定 Tailwind CSS 暗黑模式配置
  - [ ] 更新 Element Plus 主題
  - [ ] 優化所有元件的暗黑模式樣式
  - [ ] 實作主題切換的持久化儲存 