<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :help-content="helpContent"
    :icon="AlignStartVertical"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2 dark:text-white">
          目標機台: {{ selectedMachines.join(", ") || "" }}
        </h3>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="chartReady && nodeContext?.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 機台不良率圖表 - 依照機台順序顯示 -->
          <div
            v-for="machine in orderedMachines"
            :key="machine"
            class="mb-8">
            <h4 class="text-sm font-medium text-gray-700 mb-2 dark:text-white">
              {{ machine }}
            </h4>
            <Chart
              width="auto"
              height="400px"
              :options="chartOptions(machine)" />
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { AlignStartVertical } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";
import Chart from "@/components/chart.vue";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "機台不良率統計表",
  },
  description: {
    type: String,
    default: "層別出在不同製程下各機台對不良率的貢獻",
  },
  helpContent: {
    type: String,
    default: `## XGBoost分類模型分析
    層別出在不同製程下各機台對不良率的貢獻`,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

const nodeRef = ref(null);
const chartReady = ref(false);
const selectedMachines = ref([]);

// 初始化 nodeContext，提供默認值避免 undefined 錯誤
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  partNo: "",
  lot: [],
  variables: [],
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "ml-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 建立圖表配置
const chartOptions = (machine) => {
  const data = nodeContext.value?.output;
  if (!data?.machine_data || !machine) {
    return {};
  }

  const machineData = data.machine_data[machine];
  if (!machineData || !Array.isArray(machineData)) {
    logger.warn(
      "MachineDefectRateProcess",
      `No data available for machine: ${machine}`
    );
    return {};
  }

  // 從API回應中提取數據(反轉順序)
  const featureImportance = [...machineData].reverse();
  // 提取特徵名稱和重要性值
  const features = featureImportance.map((item) => item.machine);
  const values = featureImportance.map((item) => item.mean_defect_rate);

  return {
    title: {
      text: `${machine} 不良率統計`,
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        const data = params[0];
        return `${data.name}: ${(data.value * 100).toFixed(2)}%`;
      },
    },
    grid: {
      left: "30%",
      right: "10%",
      bottom: "25%",
    },
    xAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return (value * 100).toFixed(0) + "%";
        },
      },
    },
    yAxis: {
      type: "category",
      data: features,
      axisLabel: {
        rotate: 45,
        formatter: function (value) {
          return value.length > 15 ? value.substring(0, 12) + "..." : value;
        },
      },
    },
    series: [
      {
        name: "重要性",
        type: "bar",
        data: values,
        itemStyle: {
          color: "#3498db",
        },
        label: {
          show: true,
          position: "right",
          formatter: function (params) {
            return (params.value * 100).toFixed(2) + "%";
          },
        },
      },
    ],
  };
};

// 加入 computed 屬性來控制機台順序
const orderedMachines = computed(() => {
  if (!nodeContext.value?.output?.machine_data) return [];
  // 使用原始機台陣列的順序
  return selectedMachines.value.filter((machine) =>
    nodeContext.value.output.machine_data.hasOwnProperty(machine)
  );
});

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `XGBoost節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 取得resourceType判斷API資源類型
  const resourceType = globalVariables.resourceType;
  if (resourceType && resourceType === "PBC")
    console.log("預備調用空版API(急件處理)");

  // 由先前節點獲取參數
  formData.value.partNo =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  formData.value.lot =
    context.sourceNodeOutput?.lot || globalVariables?.lot || [];
  formData.value.continuousVariables =
    context.sourceNodeOutput?.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];

  // 從全域變數中取得機台陣列並取前三個
  const shapMachines = globalVariables?.shap_machines_xgboost || [];
  selectedMachines.value = Array.isArray(shapMachines)
    ? shapMachines.slice(0, 3)
    : [];

  logger.debug(props.title, "formData:", formData.value);
  logger.debug(props.title, "selectedMachines:", selectedMachines.value);

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: formData.value.partNo,
    工單: formData.value.lot?.length > 0,
    連續變數: formData.value.continuousVariables?.length > 0,
  };
  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);
  if (missingParams.length > 0 && resourceType && resourceType !== "PBC") {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行輸入節點並選擇不良原因`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  // 準備處理函數
  const processFunction = async (inputData) => {
    // 為每個機台發送請求
    const machinePromises = selectedMachines.value.map(async (machine) => {
      const response = await request.post(
        "/external/iym/iym_defect_rate_by_machine",
        {
          start_date: globalVariables?.startDate,
          end_date: globalVariables?.endDate,
          part_no: formData.value.partNo,
          defect_code: "S03",
          process_name: globalVariables?.processes?.join(",") || "",
          // machine_column: machine,
          machine_column: "RTR VCP全局面鍍銅", // TODO 暫時寫死，後端也不連動直接取用excel資料的...
        }
      );

      const result = response?.stat || [];
      if (!Array.isArray(result)) {
        throw new Error(`機台 ${machine} 分析未返回有效數據`);
      }

      return {
        machine,
        data: result,
      };
    });

    // 等待所有機台的請求完成
    const machineResults = await Promise.all(machinePromises);

    // 組織機台數據
    const machineData = {};
    machineResults.forEach(({ machine, data }) => {
      machineData[machine] = data;
    });

    // 構建完整的結果對象
    const completeResult = {
      partNo: formData.value.partNo,
      lot: formData.value.lot,
      continuousVariables: formData.value.continuousVariables,
      machines: selectedMachines.value,
      machine_data: machineData,
      timestamp: new Date().toISOString(),
    };

    // 更新nodeContext
    nodeContext.value = {
      ...nodeContext.value,
      output: completeResult,
    };

    // 確保圖表更新
    chartReady.value = false;
    await nextTick();
    chartReady.value = true;

    return completeResult;
  };

  try {
    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);
    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    logger.debug(props.title, "之前的分析結果:", previousData);

    // 恢復先前的參數
    formData.value.partNo = previousData.detail.partNo || "";
    formData.value.lot = previousData.detail.lot || [];
    formData.value.continuousVariables =
      previousData.detail.continuousVariables || [];

    // 恢復之前的機台選擇
    selectedMachines.value = previousData.detail.machines || [];

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };

    // 設置圖表準備好顯示
    chartReady.value = true;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 800px;
  overflow-y: auto;
}
</style>
