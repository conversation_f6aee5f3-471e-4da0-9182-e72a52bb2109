# SFDA Analysis 後端服務

這是一個基於 FastAPI 的統計分析 API 服務，作為 SFDA 系統的 analysis 後端，提供多種統計分析方法的 RESTful API 接口。

## 專案說明

SFDA Analysis 後端服務是 SFDA 系統的統計分析引擎，提供豐富的統計分析方法和視覺化功能。服務分為兩個主要部分：

1. 基礎統計分析（statistics）：提供通用的統計分析方法
2. 專案特定分析（projects）：針對不同子專案（如 IYM）提供特定的分析功能

## 系統架構

```
analysis/
├── app/                # 應用程式目錄
│   ├── routers/       # API 路由
│   │   ├── statistics/    # 基礎統計分析路由
│   │   │   ├── basic_stats.py      # 基本統計分析
│   │   │   ├── correlation.py      # 相關性分析
│   │   │   ├── regression.py       # 回歸分析
│   │   │   ├── survival.py         # 存活分析
│   │   │   └── dimension_reduction.py  # 降維分析
│   │   └── iym/          # IYM 專案特定路由
│   │       ├── basic_stats.py      # IYM 基礎統計分析
│   │       ├── process.py          # 製程相關 API
│   │       └── machine_learning.py # 機器學習分析 API
│   ├── models/        # 數據模型
│   │   ├── request.py    # 請求模型
│   │   └── response.py   # 回應模型
│   ├── services/      # 業務邏輯
│   │   ├── data_service.py         # 資料處理服務
│   │   ├── random_forest_service.py # 隨機森林分析服務
│   │   ├── xgboost_service.py      # XGBoost 分析服務
│   │   └── model_comparison_service.py # 模型比較服務
│   ├── utils/         # 工具函數
│   │   ├── database.py     # 資料庫操作
│   │   ├── date_utils.py   # 日期工具
│   │   ├── ml_utils.py     # 機器學習工具
│   │   └── errors.py       # 錯誤處理
│   └── main.py        # 主程式
├── static/            # 靜態資源
└── tests/             # 測試文件
```

## API 路由結構

### 基礎統計分析（/api/v1/statistics/）

- 基本統計分析
- 相關性分析
- 回歸分析
- 存活分析
- 降維分析

### IYM 專案分析（/api/v1/iym/）

- 良率分析（/yield）
- 卡方檢定分析（/chi_square）
- 製程不良率分析（/get_defect_rate_by_process）

### IYM 機器學習分析（/api/v1/iym/machine_learning/）

- 隨機森林分析（/random_forest）
- XGBoost 分析（/xgboost）
- 可靠性指數評估（/reliability_index）
- 模型比較分析（/rf_xgb_comparison）

## 技術棧

- **核心框架：** FastAPI 0.104.1 + Uvicorn 0.24.0
- **數據處理：** NumPy 1.26.2 + Pandas 2.1.3 + SciPy 1.11.4
- **統計分析：** Scikit-learn 1.3.2 + Factor-analyzer 0.5.1
- **機器學習：** Scikit-learn 1.3.2 + XGBoost 2.0.2
- **視覺化：** Matplotlib 3.8.2 + Seaborn 0.13.0
- **配置管理：** Pydantic-settings 2.7.1
- **文件處理：** Python-multipart 0.0.6

## 環境設置

### 前置需求

1. **R 語言環境**
   - 安裝 R 語言 (建議版本 4.3.x 或以上)
   - 設定 R_HOME 環境變數
     - Windows 範例：`C:\Program Files\R\R-4.3.3`
     - 可在系統環境變數中新增，或在專案啟動前設定
   - 確認 R 語言可正常執行

### Conda 環境設置

- (windows)建置前請先確認全域環境中具 C++工具(C++ build tools、Windows 10 SDK 或 Windows 11 SDK、MSVC)，如果沒有，可至微軟網站下載"Build Tools for Visual Studio"進行安裝(需 IT 協助)

```bash
# 創建 conda 環境
conda create -n py312_sfda_iym python=3.12

# 啟動環境
conda activate py312_sfda_iym

# 安裝所需套件
pip install -r requirements.txt

# 匯出已安裝套件
pip list --format=freeze > requirements.txt
```

### 啟動服務

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
```

## API 使用說明

### 基礎統計分析

- **POST /api/v1/statistics/analyze**
  - 統一的分析端點，根據方法參數提供不同的統計分析
  - 支援的分析方法：
    - `descriptive_analysis`：描述性統計分析
    - `correlation_analysis`：相關性分析
    - `linear_regression`：線性回歸
    - `anova`：變異數分析
    - `chi_square_test`：卡方檢定
    - `t_test`：獨立樣本 t 檢定
    - `paired_t_test`：配對樣本 t 檢定
    - `survival_analysis`：存活分析
    - `hypothesis_test`：假設檢定
    - `factor_analysis`：因子分析

### IYM 良率分析

- **POST /api/v1/iym/yield**
  - 查詢指定日期範圍和品目的不良品統計資料
  - 參數：
    - `start_date`：開始日期（格式：YYYY-MM-DD）
    - `end_date`：結束日期（格式：YYYY-MM-DD）
    - `part_no`：品目代碼
  - 回傳資料：
    - `不良大項代碼`：不良分類代碼
    - `defect_pcs`：不良數量
    - `cumulative_count`：累積不良數量
    - `cumulative_percentage`：累積百分比

### IYM 機器學習分析

- **POST /api/v1/iym/machine_learning/random_forest**

  - 使用隨機森林演算法分析製程對不良率的影響
  - 參數：
    - `start_date`：開始日期（格式：YYYY-MM-DD）
    - `end_date`：結束日期（格式：YYYY-MM-DD）
    - `part_no`：品目代碼
    - `defect_code`：不良項目代碼
    - `process_name`：製程名稱（逗號分隔的多個製程）
  - 回傳資料：
    - `feature_importance_mapping`：特徵重要性映射（字典格式）
    - `feature_importance`：特徵重要性列表（降序排列）
    - `importance_plot`：特徵重要性圖（base64 編碼）
    - `model_performance`：模型性能指標
    - `execution_times`：執行時間統計

- **POST /api/v1/iym/machine_learning/xgboost**

  - 使用 XGBoost 演算法分析製程對不良率的影響
  - 參數與隨機森林分析相同
  - 回傳資料格式與隨機森林分析相同

- **POST /api/v1/iym/machine_learning/reliability_index**

  - 比較隨機森林和 XGBoost 模型的特徵重要性，計算可靠性指數
  - 參數：
    - `rf_feature_importance`：隨機森林模型的特徵重要性列表
    - `xgb_feature_importance`：XGBoost 模型的特徵重要性列表
    - `top_n`：考慮的頂部特徵數量（預設為 5）
  - 回傳資料：
    - `reliability_index`：可靠性指數 K 值
    - `rf_importance_score`：隨機森林模型特徵得分
    - `xgb_importance_score`：XGBoost 模型特徵得分
    - `execution_times`：執行時間統計

- **POST /api/v1/iym/machine_learning/rf_xgb_comparison**
  - 一次性執行隨機森林和 XGBoost 分析，並計算它們的可靠性指數
  - 參數：與隨機森林/XGBoost 分析相同，外加`top_n`參數
  - 回傳資料：
    - `random_forest_result`：隨機森林分析結果
    - `xgboost_result`：XGBoost 分析結果
    - `reliability_index_result`：可靠性指數評估結果
    - `execution_times`：整體執行時間統計

## 系統整合

SFDA Analysis 後端服務是 SFDA 系統的一部分，與其他組件的整合關係如下：

1. 前端（Vue.js）：發送分析請求
2. Node.js 後端：接收請求並轉發到 Analysis 後端
3. Analysis 後端：執行分析並返回結果
4. Node.js 後端：處理結果並返回給前端
5. 前端：展示分析結果

## 待辦事項

1. **功能開發**

   - [x] 添加隨機森林分析功能
   - [x] 添加 XGBoost 分析功能
   - [x] 實現模型可靠性指數評估
   - [ ] 優化現有統計分析方法
   - [ ] 添加更多機器學習模型

2. **系統優化**

   - [x] 統一執行時間格式為 HH:mm:ss.SSS
   - [ ] 統一回傳資料格式
   - [ ] 優化圖片生成和傳輸
   - [ ] 改進錯誤處理機制
   - [ ] 優化大數據集處理
   - [ ] 實現異步分析任務
   - [ ] 添加結果快取機制

3. **資料庫優化**
   - [ ] 優化查詢效能
   - [ ] 添加資料庫連接池

## 貢獻指南

1. Fork 專案
2. 建立特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m '添加一些很棒的功能'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 授權

本專案採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 檔案

## 負責人

John
