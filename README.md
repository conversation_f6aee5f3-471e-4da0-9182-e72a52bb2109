# SFDA IYM（良率分析平台）前端專案

這是一個基於 Vue 3 的現代化工作流程設計和執行平台，專注於提供良率分析、數據處理和業務流程自動化的完整解決方案。

## 專案說明

SFDA IYM（良率分析平台）是一個企業級的工作流程管理系統，採用模組化架構設計，包含節點定義、流程模板、流程實例和執行引擎等核心組件。該系統允許用戶透過拖拽方式設計複雜的業務流程，並提供強大的數據分析和視覺化功能。

## 系統架構

```
┌─────────────────────────────────────────────────────────────────┐
│                        前端流程系統架構                           │
└─────────────────────────────────────────────────────────────────┘
                               │
           ┌──────────────────┼──────────────────┐
           ▼                   ▼                  ▼
┌─────────────────┐   ┌─────────────────┐  ┌─────────────────┐
│    視圖層 (Views) │   │  組件層 (Components) │  │  存儲層 (Stores)  │
└─────────────────┘   └─────────────────┘  └─────────────────┘
           │                   │                  │
           │                   │                  │
           ▼                   ▼                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                     可組合函數層 (Composables)                    │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                         API 層 (API Layer)                       │
└─────────────────────────────────────────────────────────────────┘
```

## 技術棧

- **前端框架：** Vue 3 (Composition API)
- **構建工具：** Vite 5.0
- **UI 框架：** Element Plus
- **狀態管理：** Pinia
- **路由管理：** Vue Router 4
- **HTTP 客戶端：** Axios
- **樣式：** TailwindCSS 3.x + PostCSS
- **工具庫：** VueUse, Lodash
- **圖表庫：** Chart.js, ECharts
- **流程圖：** Vue Flow
- **圖標：** Lucide Vue Next
- **開發工具：** ESLint, Prettier
- **類型檢查：** JSDoc（可選 TypeScript 遷移）

## 開發環境要求

- **Node.js：** >= 16.0.0
- **npm：** >= 7.0.0
- **瀏覽器：** Chrome 90+, Firefox 90+, Safari 14+

## 安裝與運行

```bash
# 克隆專案
git clone <repository-url>
cd sfda_iym_frontend

# 安裝依賴
npm install

# 開發環境運行
npm run dev

# 生產環境打包
npm run build

# 預覽生產環境
npm run preview

# 程式碼檢查
npm run lint

# 程式碼格式化
npm run format
```

## 環境變數配置

創建 `.env.local` 檔案並設定以下變數：

```env
# API 基礎 URL
VITE_API_BASE_URL=http://localhost:3001

# 分析服務 URL
VITE_ANALYSIS_API_BASE_URL=http://localhost:8001

# 應用標題
VITE_APP_TITLE=SFDA IYM 良率分析平台

# 除錯模式
VITE_DEBUG=true
```

## 開發規範

### 程式碼風格
- 使用 ESLint + Prettier 進行程式碼檢查和格式化
- 遵循 Vue 3 官方風格指南
- 使用 Composition API（組合式 API） 而非 Options API（選項式 API）
- 優先使用 `<script setup>` 語法

### 命名規範（基於專案實際使用）
- **組件：** PascalCase (例：`FileUpload.vue`, `UserAvatar.vue`)
- **Composables：** camelCase 以 `use` 開頭 (例：`useNodeExecution.js`, `useFlowInstance.js`)
- **變數/函數：** camelCase (例：`handleDragOver`, `executeNode`)
- **常數：** SCREAMING_SNAKE_CASE (例：`NODE_STATES`, `NODE_STATUS_TYPES`)
- **視圖檔案：** 多數使用 PascalCase，部分使用 kebab-case (例：`Home.vue`, `getting-started.vue`)

### 組件開發
- 每個組件都應有適當的 props 定義和類型檢查
- 使用 `defineEmits` 定義事件
- 適當使用 `defineExpose` 暴露組件方法
- 組件應具備良好的可重用性

### Composables 開發
- 每個 composable 應專注於單一職責
- 返回的狀態和方法應具有描述性的名稱
- 適當使用 `readonly` 保護內部狀態
- 提供清晰的 JSDoc 文檔

## [尚未用]部署



## 效能優化

### 已實現的優化
- **程式碼分割：** 路由級別的懶加載
- **組件懶載入：** 大型組件按需載入
- **圖片優化：** WebP 格式、懶載入
- **緩存策略：** API 響應緩存、瀏覽器緩存
- **虛擬滾動：** 大列表性能優化

### 建議的優化
- 實施 Service Worker 用於離線支援
- 使用 CDN 加速靜態資源載入
- 實現更細粒度的組件緩存
- 優化 Bundle 大小

## [尚未用]測試

### 單元測試
```bash
# 執行單元測試
npm run test:unit

# 測試覆蓋率報告
npm run test:coverage
```

### E2E 測試
```bash
# 執行端到端測試
npm run test:e2e

# 開發模式下執行 E2E 測試
npm run test:e2e:dev
```

## [尚未整理]故障排除

### 常見問題

**1. 節點執行失敗**
- 檢查前置節點是否已完成
- 確認節點配置參數正確
- 查看瀏覽器控制台錯誤訊息

**2. 流程設計器載入緩慢**
- 清除瀏覽器緩存
- 檢查網路連線狀態
- 確認後端 API 服務正常

**3. 權限相關問題**
- 確認用戶角色權限設定
- 檢查 JWT 令牌是否過期
- 聯繫管理員檢查權限配置

## 核心功能模組

### 1. 用戶認證與權限系統 (RBAC)

- **多角色支援：** SUPER_ADMIN, ADMIN, POWERUSER, READER
- **JWT 令牌管理：** 自動刷新、安全存儲
- **權限繼承：** 上級角色自動繼承下級權限
- **會話管理：** 記住我、自動登出
- **安全機制：** 密碼加密、防 CSRF

### 2. 專案管理系統

- **專案生命週期管理：** 創建、編輯、歸檔、刪除
- **專案編號自動生成：** systemCode_YYYYMMDD_HHMMSS_XXXXX 格式
- **專案成員管理：** 角色分配、權限控制
- **專案統計：** 進度追蹤、資源使用情況
- **專案搜索與篩選：** 多條件查詢、狀態篩選

### 3. 檔案管理系統

- **多格式支援：** 圖片、文檔、Office 文件、CSV/JSON
- **檔案上傳：** 拖拽上傳、批量上傳、進度追蹤
- **檔案處理：** 預覽、下載、重命名、分類
- **存儲優化：** 自動清理、檔案去重
- **訪問控制：** 權限驗證、安全下載

### 4. 工作流程設計與執行系統

#### 4.1 流程設計器
- **拖拽式設計：** 視覺化流程設計界面
- **節點類型系統：** 基礎節點、業務節點、決策節點
- **連接線管理：** 智能連接、條件分支
- **佈局系統：** 自動佈局、手動調整
- **屬性編輯：** 節點配置、參數設定

#### 4.2 節點系統架構
```
components/flow-nodes/
├── base/           # 基礎節點
│   ├── BaseNode.vue       # 節點基類
│   ├── HttpRequestNode.vue # HTTP 請求節點
│   └── DataSourceNode.vue  # 數據源節點
└── business/       # 業務節點
    ├── TemplateInput.vue   # 範本輸入節點
    ├── TemplateProcess.vue  # 範本處理節點
    └── ...  # 其他實作節點
```

#### 4.3 流程執行引擎
- **工作流生命週期：** 初始化 → 執行 → 完成
- **節點執行模式：** 順序執行、並行處理、條件分支
- **依賴關係管理：** 前置節點檢查、依賴解析
- **狀態管理：** 節點狀態追蹤、執行歷史記錄
- **錯誤處理：** 異常捕獲、錯誤恢復、重試機制


## 專案結構

```
frontend/
├── src/
│   ├── components/             # 通用組件
│   │   ├── flow-nodes/        # 工作流程節點組件
│   │   │   ├── base/          # 基礎節點（HTTP、數據源等）
│   │   │   ├── business/      # 業務節點（良率分析、客訴處理等）
│   │   │   └── index.js       # 節點註冊
│   │   ├── ui/               # UI 基礎組件
│   │   └── common/           # 通用業務組件
│   ├── views/                # 頁面組件
│   │   ├── flow/             # 流程相關頁面
│   │   │   ├── templates/    # 流程模板管理
│   │   │   ├── instance/     # 流程實例管理
│   │   │   └── node-definitions/ # 節點定義管理
│   │   ├── projects/         # 專案管理頁面
│   │   ├── files/           # 檔案管理頁面
│   │   └── auth/            # 認證相關頁面
│   ├── composables/          # 可組合函數
│   │   ├── flow/            # 流程相關邏輯
│   │   │   ├── useFlowTemplate.js  # 流程模板操作
│   │   │   ├── useFlowNodes.js     # 節點管理
│   │   │   ├── useFlowCanvas.js    # 畫布操作
│   │   │   └── useFlowLayout.js    # 佈局管理
│   │   ├── useWorkflowManager.js   # 工作流執行引擎
│   │   ├── useFlowInstance.js      # 流程實例管理
│   │   └── useNodeExecution.js     # 節點執行邏輯
│   ├── stores/               # 狀態管理
│   │   ├── flowStore.js     # 流程狀態管理
│   │   ├── flowTemplate.js  # 流程模板狀態
│   │   ├── user.js          # 用戶狀態
│   │   └── projects.js      # 專案狀態
│   ├── api/                 # API 介面
│   │   └── modules/         # 模組化 API
│   │       ├── auth.js      # 認證 API
│   │       ├── flow.js      # 流程 API
│   │       ├── project.js   # 專案 API
│   │       └── files.js     # 檔案 API
│   ├── utils/              # 工具函數
│   │   ├── auth.js         # 認證工具
│   │   ├── request.js      # HTTP 請求封裝
│   │   └── eventBus.js     # 事件總線
│   ├── constants/          # 常數定義
│   │   ├── nodeStates.js   # 節點狀態常數
│   │   └── flowStates.js   # 流程狀態常數
│   ├── layouts/            # 佈局組件
│   │   └── MainLayout.vue  # 主佈局
│   ├── router/             # 路由配置
│   ├── assets/             # 靜態資源
│   └── docs/               # 文檔
│       ├── flow-system-documentation.md # 系統架構文檔
│       └── flow-components.md           # 組件使用指南
├── public/                 # 公共資源
└── package.json           # 專案配置
```

## 工作流程系統詳細架構

### 核心組件層級

#### 1. 流程節點 (Flow Nodes)
流程節點是工作流的基本構建單元


#### 2. 工作流執行引擎
**核心 Composables：**
- `useWorkflowManager.js` - 工作流程流轉邏輯、節點依賴關係管理
- `useFlowInstance.js` - 流程實例上下文管理、節點間數據交互
- `useNodeExecution.js` - 單個節點執行生命週期管理

**執行特性：**
- **強制執行原則：** 每個節點必須執行完成，不支持暫停或停止
- **依賴關係管理：** 自動檢查前置節點完成狀態
- **並行處理支援：** 支援多個節點同時執行
- **錯誤恢復機制：** 節點執行失敗時的重試和恢復邏輯

#### 3. 數據流管理
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    節點 A       │────▶│    節點 B       │────▶│    節點 C       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                       共享數據 (Shared Data)                     │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                     全局變量 (Global Variables)                  │
└─────────────────────────────────────────────────────────────────┘
```

### 節點狀態流轉

```
┌─────────────┐
│   初始化    │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│   準備就緒   │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│   執行中     │
└──────┬──────┘
       │
       ▼
┌─────────────────────┐
│      執行結果?       │
└─────────┬───────────┘
          │
  ┌───────┴───────┐
  ▼               ▼
┌─────────┐   ┌─────────┐
│  成功   │   │  失敗   │
└─────────┘   └─────────┘
```

## 功能特性

- 🔐 **企業級認證：** JWT + RBAC 權限控制
- 🎨 **響應式設計：** 支援多種螢幕尺寸
- 📱 **移動端適配：** PWA 支援
- 🌙 **主題切換：** 亮色/暗色模式
- 🔍 **全局搜索：** 智能搜索與篩選
- 📊 **數據視覺化：** 多種圖表類型
- 🚀 **高性能：** 虛擬滾動、懒加載
- 🔄 **即時更新：** WebSocket 即時通訊
- 📋 **拖拽操作：** 直觀的操作體驗
- 🎯 **錯誤追蹤：** 完整的錯誤處理機制

## 最新更新

- 優化用戶認證流程
- 改進「記住我」功能的實現
- 修復登出相關問題
- 添加路由權限控制
- 優化錯誤處理機制
- 實作工作流程設計器的初步功能
- 添加基礎節點系統

## 開發規範

- 使用 ESLint 和 Prettier 進行代碼格式化
- 遵循 Vue 3 組合式 API 最佳實踐
- 組件命名採用 PascalCase
- 文件命名採用 kebab-case
- 使用 TypeScript 進行類型檢查
- 按需引入原則，減少不必要的依賴
- 使用 Element Plus 元件進行視覺設計

## 待辦事項與路線圖

### 短期目標 (1-2 個月)

**節點系統優化**
- [ ] 新增更多業務節點類型（決策節點、循環節點）
- [ ] 實作節點組合與封裝功能
- [ ] 改善節點執行效能和記憶體使用
- [ ] 實作節點版本管理系統

**工作流程增強**
- [ ] 實作條件分支邏輯
- [ ] 新增並行流程支援
- [ ] 實作流程範本匯入/匯出
- [ ] 新增流程執行時間預估

**用戶體驗改善**
- [ ] 實作拖拽操作的視覺反饋
- [ ] 改善行動裝置操作體驗
- [ ] 新增鍵盤快捷鍵支援
- [ ] 實作無障礙功能支援

### 中期目標 (3-6 個月)

**整合與擴展**
- [ ] 整合第三方 API 服務
- [ ] 實作 Webhook 支援
- [ ] 新增排程執行功能
- [ ] 實作流程監控儀表板

**分析功能增強**
- [ ] 新增更多統計分析方法
- [ ] 實作機器學習模型訓練
- [ ] 新增預測分析功能
- [ ] 實作異常檢測算法

**效能與安全**
- [ ] 實作資料庫連接池優化
- [ ] 新增 API 速率限制
- [ ] 實作資料加密傳輸
- [ ] 新增審計日誌功能

### 長期目標 (6-12 個月)

**企業級功能**
- [ ] 實作多租戶支援
- [ ] 新增組織架構管理
- [ ] 實作資源配額管理
- [ ] 新增費用追蹤功能

**AI 與自動化**
- [ ] 實作智能流程推薦
- [ ] 新增自動化測試生成
- [ ] 實作智能錯誤診斷
- [ ] 新增自然語言流程設計

## 貢獻指南

我們歡迎貢獻！請遵循以下步驟：

### 程式碼審查標準
- 程式碼必須通過所有測試
- 遵循專案的程式碼風格規範
- 包含適當的文檔和註釋
- 新功能需要相應的測試用例

### 問題回報
使用 GitHub Issues 回報問題，請包含：
- 問題的詳細描述
- 重現步驟
- 預期行為與實際行為
- 螢幕截圖（如適用）
- 環境資訊（瀏覽器、作業系統等）

## 授權

本專案為台郡科技企業內部所有。

## 聯絡資訊

- **專案維護者：** 數位開發處 數據分析部 開發團隊

## 致謝

感謝以下開源專案和貢獻者：

- **Vue.js 團隊** - 提供優秀的前端框架
- **Element Plus 團隊** - 提供完整的 UI 組件庫
- **Vue Flow 社群** - 提供強大的流程圖解決方案
- **所有貢獻者** - 感謝每一位為專案做出貢獻的開發者

---

📝 **注意：** 本文檔會隨著專案發展持續更新。如有任何疑問或建議，歡迎聯繫開發團隊。
