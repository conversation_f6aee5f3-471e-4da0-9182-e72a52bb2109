# 良率分析平台

這是一個專門用於半導體製造業的良率分析平台，提供直觀的數據分析和可視化工具，幫助工程師和管理者更好地理解和改善製造流程。

## 功能特點

- 📊 **專案管理**
  - 多專案支援
  - 專案狀態追蹤
  - 團隊協作功能

- 🔄 **工作流程**
  - 自定義工作流程
  - 流程模板管理
  - 即時狀態監控

- 📁 **文件管理**
  - 檔案上傳與管理
  - 版本控制
  - 權限管理

- 📈 **數據分析**
  - 良率趨勢分析
  - 製程參數分析
  - 數據可視化

## 專案概述

本系統旨在提供一個直觀的良率分析平台，通過可視化工作流的方式，幫助用戶進行製程、檢驗站等維度的不良率分析。系統支持數據上傳、統計分析、結果展示等功能，並提供靈活的工作流管理機制。

## 核心功能

### 不良率分析
- 整體不良率展示（總數據、趨勢圖）
- 多維度不良率拆解分析（製程、檢驗站等）
- 豐富的統計圖表展示（柱狀圖、折線圖、餅圖等）

### 工作流管理
- 可視化工作流畫布
- 支持多種節點類型：
  - 數據輸入節點
  - 分析節點
  - 附件節點
  - 結果節點
- 節點屬性自定義
- 支持節點間的拖放與連接
- 支持撤銷/重做功能（Ctrl+Z / Ctrl+Shift+Z）
- 支持工作區自適應（自動縮放以適應畫布）
- 支持便利貼功能（可自定義顏色和大小）

### 數據分析
- 數據清洗與預處理
- 多維度統計分析
- 不良原因關聯性分析
- 支持決策樹、相關性矩陣等分析方法

### 文件管理
- 支持拖放上傳
- 文件預覽功能
- 與節點綁定機制

### 結果導出
- 支持圖表/表格導出（圖片、PDF）
- 工作流配置導出（JSON格式）

## 待辦事項 (TODO)

1. 數據處理與分析功能
   - [ ] 實現數據預處理功能
   - [ ] 添加統計分析方法
   - [ ] 實現即時數據處理
   - [ ] 添加數據驗證機制

2. 節點驗證規則
   - [ ] 添加輸入數據格式驗證
   - [ ] 實現節點連接規則驗證
   - [ ] 添加必填欄位檢查
   - [ ] 實現數據流驗證

3. 工作流管理功能
   - [ ] 實現工作流配置的保存
   - [ ] 添加工作流模板功能
   - [ ] 實現工作流版本控制
   - [ ] 添加工作流導入/導出功能

4. 分析方法與圖表
   - [ ] 添加更多統計分析方法
   - [ ] 擴充圖表類型
   - [ ] 實現自定義圖表配置
   - [ ] 添加交互式圖表功能

## 技術架構

### 前端技術棧
- Vue 3
- Vue-Flow（工作流畫布）
- Element UI（UI組件）
- Tailwind CSS（樣式框架）
- ECharts.js（數據可視化）

### 後端技術棧
- Node.js + Express
  - 用戶管理
  - 文件管理
  - 工作流配置管理
- Python + FastAPI
  - 數據分析
  - 統計建模
  - API服務

### 數據庫
- SQLite（開發階段）
- MySQL（生產環境）

## 系統模組

### 前端模組
1. 畫布模組
   - 節點拖放
   - 連線管理
   - 屬性編輯
2. 圖表模組
   - 多種圖表類型
   - 動態數據更新
3. 文件管理模組
   - 上傳/下載
   - 預覽功能
4. 結果導出模組
   - 圖片導出
   - PDF生成

### 後端模組
1. Node.js服務
   - 用戶管理
   - 文件處理
   - 配置管理
2. FastAPI服務
   - 數據預處理
   - 統計分析
   - 模型訓練
   - 報表生成

## 安全性考慮
- API訪問限制
- 數據加密傳輸
- 敏感數據脫敏
- 操作日誌記錄

## 部署架構
- 容器化部署（Docker）
- 系統監控（Prometheus + Grafana）
- 日誌管理
- 性能優化

## 開發流程

1. 系統設計
   - 需求分析
   - 技術選型
   - 數據庫設計
   - API設計

2. 前端開發
   - 畫布實現
   - 組件開發
   - 介面整合

3. 後端開發
   - 基礎服務搭建
   - 分析功能實現
   - API開發

4. 測試與優化
   - 單元測試
   - 整合測試
   - 性能優化

## 擴展計劃
- 多人協作功能
- 智能分析推薦
- 自動化報告生成
- 更多分析模型支持

## 開發環境設置

### 前端
```bash
# 安裝依賴
npm install

# 開發環境運行
npm run dev

# 生產環境建構
npm run build
```

### 後端
```bash
# Node.js
npm install
npm run start

# Python
pip install -r requirements.txt
uvicorn main:app --reload
```

## 貢獻指南
1. Fork 本專案
2. 創建特性分支
3. 提交變更
4. 發起合併請求

## 授權協議
[待定] 

## 已完成功能

1. 工作流畫布
   - [x] 節點拖放與連接
   - [x] 自定義節點類型
   - [x] 節點屬性編輯
   - [x] 撤銷/重做功能
   - [x] 工作區自適應
   - [x] 便利貼功能
   - [x] 自動布局
   - [x] JSON 數據導出
   - [x] 可折疊的浮動配置面板

2. 檔案管理
   - [x] 拖放上傳
   - [x] 檔案預覽（圖片、PDF、文字、Excel、PPT）
   - [x] 檔案下載
   - [x] 檔案類型驗證
   - [x] 上傳進度顯示
   - [x] 檔案大小限制
   - [x] 檔案操作按鈕優化

3. 介面優化
   - [x] 響應式布局
   - [x] 暗色主題支持
   - [x] 動畫效果
   - [x] 操作提示
   - [x] 智能面板收合
   - [x] 檔案節點視覺優化

## 版本資訊

- 當前版本：1.0.1
- 最後更新：2024-03-20
- 更新內容：
  1. 優化檔案節點視覺效果
  2. 改進檔案操作按鈕布局
  3. 支援更多檔案類型預覽
  4. 修復檔案上傳相關問題

## 瀏覽器支援

- Chrome (推薦)
- Firefox
- Safari
- Edge

## 團隊

- 系統管理員：王小明
- 資深工程師：陳美玲
- 品質主管：林志偉
- 數據分析師：張雅婷
- 製程工程師：黃建志

## 聯絡資訊

如有任何問題或建議，請聯繫系統管理員：
- Email: <EMAIL>