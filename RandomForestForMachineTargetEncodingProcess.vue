<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :help-content="helpContent"
    :icon="AlignStartVertical"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2 dark:text-white">
          Random Forest參數分析
        </h3>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="chartReady && nodeContext?.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 隨機森林圖 -->
          <div class="mb-4">
            <Chart
              width="auto"
              height="400px"
              :options="chartOption" />
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { AlignStartVertical } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";
import Chart from "@/components/chart.vue";

const isDark = inject("isDark");
console.log("child isDark", isDark);
// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "Random Forest參數分析",
  },
  description: {
    type: String,
    default: "使用Random Forest分析參數重要性",
  },
  helpContent: {
    type: String,
    default: `## Random Forest分類模型分析

### 邏輯概念：
用隨機森林進行分類，分析多個參數對分類結果的影響，並排序特徵重要性。

### 適用情境：
- 分類型目標變數
- 多變量分類問題

### 可用來做什麼：
- 關鍵參數自動篩選
- 製程異常品預測`,
  },

  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點狀態
const nodeRef = ref(null);
const chartReady = ref(false);

// 初始化 nodeContext
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  partNo: "",
  lot: [],
  variables: [],
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "ml-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 建立圖表配置
const chartOption = computed(() => {
  const data = nodeContext.value?.output;
  if (!data?.feature_importance) {
    console.warn("No feature_importance data available");
    return {};
  }

  // 從API回應中提取數據(反轉順序)
  const featureImportance = [...(data.feature_importance || [])].reverse();
  // 提取特徵名稱和重要性值
  const features = featureImportance.map((item) => item.Feature);
  const values = featureImportance.map((item) => item.Importance);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        const data = params[0];
        return `${data.name}: ${(data.value * 100).toFixed(2)}%`;
      },
    },
    grid: {
      left: "30%",
      right: "10%",
      bottom: "25%",
    },
    xAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return (value * 100).toFixed(0) + "%";
        },
      },
    },
    yAxis: {
      type: "category",
      data: features,
      axisLabel: {
        rotate: 45,
        formatter: function (value) {
          return value.length > 15 ? value.substring(0, 12) + "..." : value;
        },
      },
    },
    series: [
      {
        name: "重要性",
        type: "bar",
        data: values,
        itemStyle: {
          color: "#3498db",
        },
        label: {
          show: true,
          position: "right",
          formatter: function (params) {
            return (params.value * 100).toFixed(2) + "%";
          },
        },
      },
    ],
  };
});

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `Random Forest節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);
  console.log("XXXXXXXXXXXXXXX", globalVariables);

  // 取得resourceType判斷API資源類型
  const resourceType = globalVariables.resourceType;
  if (resourceType && resourceType === "PBC")
    console.log("預備調用空版API(急件處理)");

  // 由先前節點獲取參數
  formData.value.partNo =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  formData.value.lot =
    context.sourceNodeOutput?.lot || globalVariables?.lot || [];
  formData.value.continuousVariables =
    context.sourceNodeOutput?.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];
  logger.debug(props.title, "formData:", formData.value);

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: formData.value.partNo,
    工單: formData.value.lot?.length > 0,
    連續變數: formData.value.continuousVariables?.length > 0,
  };
  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);
  if (missingParams.length > 0 && resourceType && resourceType !== "PBC") {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行輸入節點並選擇不良原因`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  try {
    // 準備處理函數
    const processFunction = async (inputData) => {
      let result = null;
      if (resourceType && resourceType === "PBC") {
        result = await request.post("/external/iym/iym_rf_feature_importance", {
          start_date: globalVariables?.startDate,
          end_date: globalVariables?.endDate,
          part_no: formData.value.partNo,
          defect_code: "S03",
          process_name: globalVariables?.processes?.join(",") || "",
        });
      } else {
        result = await request.post(
          "/external/iym/machine_learning/random_forest_categorical_for_parameter",
          {
            part_no: formData.value.partNo,
            work_order: formData.value.lot.join(","),
            variables: formData.value.continuousVariables,
            target: "ooc",
          }
        );
      }

      if (!Array.isArray(result)) {
        throw new Error("Random Forest分析未返回有效數據");
      }

      await updateGlobalVariable("feature_importance_randomForest", result);

      // 構建完整的結果對象
      const completeResult = {
        partNo: formData.value.partNo,
        lot: formData.value.lot,
        continuousVariables: formData.value.continuousVariables,
        feature_importance: result, // API直接返回feature_importance陣列
        timestamp: new Date().toISOString(),
      };

      // 更新nodeContext
      nodeContext.value = {
        ...nodeContext.value,
        output: completeResult,
      };

      // 確保圖表更新
      chartReady.value = false;
      await nextTick();
      chartReady.value = true;

      return completeResult;
    };

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);
    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    logger.debug(props.title, "之前的分析結果:", previousData);

    // 恢復先前的參數
    formData.value.partNo = previousData.detail.partNo || "";
    formData.value.lot = previousData.detail.lot || [];
    formData.value.continuousVariables =
      previousData.detail.continuousVariables || [];

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };

    // 設置圖表準備好顯示
    chartReady.value = true;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 800px;
  overflow-y: auto;
}
</style>
