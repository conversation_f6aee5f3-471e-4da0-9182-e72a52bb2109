<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :isReportMode="isReportMode"
    @run="handleRun">
    <div class="p-4">
      <!-- 分析結果區域 -->
      <div v-if="nodeData_value.chartReady && nodeData_value.chartData">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <div
            v-for="(processData, processIndex) in groupedChartData"
            :key="processIndex"
            class="mb-6">
            <div
              v-for="(chart, chartIndex) in processData.charts"
              :key="chartIndex"
              class="mb-4">
              <Chart
                width="auto"
                height="280px"
                :options="getChartOption(chart, processData.processName)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, computed, onMounted, watch } from "vue";
import Chart from "@/components/chart.vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "SHAP表 - Random Forest(PBC)",
  },
  description: {
    type: String,
    default: "各製程對不良率的貢獻度",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartReady: false,
  chartData: null,
  process: [],
  shapValues: [],
  partNo: "", // API需要的參數
  startDate: null, // API需要的參數
  endDate: null, // API需要的參數
  processes: "", // API需要的參數
  defectCode: "S03", // API需要的參數，預設值
};

// 節點數據結構
const nodeData_value = ref({
  chartReady: nodeData_default.chartReady,
  chartData: nodeData_default.chartData,
  process: [...nodeData_default.process],
  shapValues: [...nodeData_default.shapValues],
  partNo: nodeData_default.partNo,
  startDate: nodeData_default.startDate,
  endDate: nodeData_default.endDate,
  processes: nodeData_default.processes,
  defectCode: nodeData_default.defectCode,
});

// 圖表數據處理
const groupedChartData = computed(() => {
  if (!nodeData_value.value.chartData) {
    return [];
  }

  // 將數據組織成所需的格式
  return [
    {
      processName: "SHAP Values Distribution",
      charts: [nodeData_value.value.chartData],
    },
  ];
});

// 獲取圖表選項
const getChartOption = (chartData, processName) => {
  // ...existing commented code...

  return {
    title: {
      text: processName,
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 暗色模式使用白色，亮色模式使用深灰色
      },
    },
    legend: {
      data: ["SHAP值"],
      left: "right",
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 同步調整圖例顏色
      },
    },
    tooltip: {
      position: "top",
      formatter: function (params) {
        return (
          "SHAP值: " +
          params.value[2] +
          "<br/>" +
          nodeData_value.value.process[params.value[0]] +
          " / " +
          nodeData_value.value.shapValues[params.value[1]]
        );
      },
    },
    grid: {
      left: 2,
      bottom: 10,
      right: 60, // 增加右側空間以容納熱力條
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: nodeData_value.value.shapValues,
      boundaryGap: false,
      splitLine: {
        show: true,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "category",
      data: nodeData_value.value.process,
      axisLine: {
        show: false,
      },
    },
    visualMap: {
      min: 0,
      max: 15, // 根據您的數據可能需要調整
      calculable: true,
      orient: "vertical",
      right: 0,
      top: "center",
      inRange: {
        color: ["#3b5af9", "#f23c55"], // 從藍色到紅色的漸變
        symbolSize: [10, 30],
      },
      text: ["High", "Low"], // 添加高低值標籤
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 暗色模式使用白色，亮色模式使用深灰色
      },
    },
    series: [
      {
        name: "SHAP值",
        type: "scatter",
        symbolSize: function (val) {
          return val[2] * 2;
        },
        data: chartData,
        itemStyle: {
          color: function (params) {
            // 根據值大小決定顏色
            const value = params.value[2];
            const max = 15; // 和visualMap設置的max一致
            const ratio = Math.min(value / max, 1);
            // 從藍色(#3b5af9)過渡到紅色(#f23c55)
            return {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: "#3b5af9" }, // 藍色
                { offset: ratio, color: "#f23c55" }, // 紅色
              ],
            };
          },
        },
        animationDelay: function (idx) {
          return idx * 5;
        },
      },
    ],
  };
};

const processFunction = async (inputData) => {
  printLog("processFunction inputData:", inputData);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  printLog("globalVariables:", globalVariables);

  // 由先前節點獲取參數，更新nodeData_value
  nodeData_value.value.partNo = globalVariables?.partNo || "";
  nodeData_value.value.startDate = globalVariables?.startDate || "";
  nodeData_value.value.endDate = globalVariables?.endDate || "";
  nodeData_value.value.processes = globalVariables?.processes || [];

  // 檢查必要參數
  // if (!nodeData_value.value.partNo) {
  //   const errorMsg = "請填寫品目代碼";
  //   console.error(props.title, errorMsg);
  //   ElMessage.warning(errorMsg);
  //   return;
  // }

  // if (!nodeData_value.value.target) {
  //   const errorMsg = "請選擇target";
  //   console.error(props.title, errorMsg);
  //   ElMessage.warning(errorMsg);
  //   return;
  // }

  // 檢查必要參數
  // if (!nodeData_value.value.partNo) {
  //   const errorMsg = "請填寫品目代碼";
  //   console.error(props.title, errorMsg);
  //   ElMessage.warning(errorMsg);
  //   return;
  // }

  // if (!nodeData_value.value.target) {
  //   const errorMsg = "請選擇target";
  //   console.error(props.title, errorMsg);
  //   ElMessage.warning(errorMsg);
  //   return;
  // }

  const response = await request.post("/external/iym/iym_rf_shap_summary", {
    start_date: nodeData_value.value.startDate,
    end_date: nodeData_value.value.endDate,
    part_no: nodeData_value.value.partNo,
    defect_code: nodeData_value.value.defectCode,
    process_name: nodeData_value.value.processes.join(","),
  });
  printLog("API response:", response);

  // 處理API返回的數據
  const apiData = response?.rf_shap_summary;
  if (!apiData) {
    ElMessage.error("API返回數據格式錯誤");
    return;
  }

  const { feature_names, shap_values, feature_values } = apiData;

  // 將SHAP值分為5個區間
  const getAllShapValues = () => {
    const allValues = shap_values.flat();
    const min = Math.min(...allValues);
    const max = Math.max(...allValues);
    const step = (max - min) / 5;

    return Array.from({ length: 5 }, (_, i) => {
      const rangeStart = min + i * step;
      const rangeEnd = min + (i + 1) * step;
      return `${rangeStart.toFixed(3)} ~ ${rangeEnd.toFixed(3)}`;
    });
  };

  const shapValues = getAllShapValues();
  const process = feature_names;

  // 構建圖表數據
  const result = [];
  shap_values.forEach((shapRow, rowIndex) => {
    shapRow.forEach((shapValue, colIndex) => {
      // 確定SHAP值所屬的區間
      const allValues = shap_values.flat();
      const min = Math.min(...allValues);
      const max = Math.max(...allValues);
      const step = (max - min) / 5;
      const rangeIndex = Math.min(Math.floor((shapValue - min) / step), 4);

      // 使用feature_values作為點的大小，並放大以便可見
      const pointSize = Math.abs(feature_values[rowIndex][colIndex]) * 100;

      result.push([
        colIndex, // X軸：feature索引
        rangeIndex, // Y軸：SHAP值範圍索引
        pointSize, // 點的大小
      ]);
    });
  });
  // const result = {
  //   partNo: nodeData_value.value.partNo,
  //   startDate: nodeData_value.value.dateRange
  //     ? nodeData_value.value.dateRange[0]
  //     : null,
  //   endDate: nodeData_value.value.dateRange
  //     ? nodeData_value.value.dateRange[1]
  //     : null,
  //   lot: nodeData_value.value.lot,
  //   lotOptions: nodeData_value.value.lotOptions,
  //   target: nodeData_value.value.target,
  //   timestamp: new Date().toISOString(),
  // };

  // // 設置全域變數
  // await updateGlobalVariable("partNo", result.partNo);
  // if (nodeData_value.value.dateRange) {
  //   await updateGlobalVariable("startDate", result.startDate);
  //   await updateGlobalVariable("endDate", result.endDate);
  // }
  // await updateGlobalVariable("lot", result.lot);
  // await updateGlobalVariable("target", result.target); // TODO 目前沒有用到，後面API都還是寫死的，需要串入

  printLog("processFunction result:", result);

  // 更新 nodeData_value，使圖表顯示
  nodeData_value.value.process = process;
  nodeData_value.value.shapValues = shapValues;
  nodeData_value.value.chartData = result;
  nodeData_value.value.chartReady = true;

  return result;
};

// 初始化處理
onMounted(async () => {
  // console.log("Component[BasicSPIInput] onMounted!", props);

  // 嘗試從共享數據中獲取之前的輸入數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData:", previousData);

  if (previousData?.detail) {
    printLog("載入之前的數據:", previousData);
    nodeData_value.value.partNo =
      previousData.detail.partNo || nodeData_default.partNo;
    nodeData_value.value.startDate =
      previousData.detail.startDate || nodeData_default.startDate;
    nodeData_value.value.endDate =
      previousData.detail.endDate || nodeData_default.endDate;
    nodeData_value.value.processes =
      previousData.detail.processes || nodeData_default.processes;
    nodeData_value.value.defectCode =
      previousData.detail.defectCode || nodeData_default.defectCode;
  }
});
// ==============END 節點自定義區==============

//#region 節點固定方法
// 使用重置邏輯 composable 監聽節點狀態變化
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
  config: {
    debug: false, // 關閉調試模式
  },
});

// 執行節點
const handleRun = async () => {
  // printLog(`DO handleRun!`);
  nodeRef.value.handleRun(processFunction);
};

// 暴露方法給父元件
defineExpose({
  handleRun,
});
//#endregion
</script>

<style scoped>
.result-container {
  max-height: 600px;
  overflow-y: auto;
}
</style>
