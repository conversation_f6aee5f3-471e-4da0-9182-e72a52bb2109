import requests
import json

# 設定 API 端點和參數
url = "http://10.8.32.46:3001/api/v1/workspace/QMS/chat"
headers = {
    "Authorization": "Bearer 3FJ7QRW-DGK46J5-PT2AP8K-C0XWAPY",
    "Content-Type": "application/json"
}
payload = {
  "message": "What is AnythingLLM?",
  "mode": "chat",
  "sessionId": "identifier-to-partition-chats-by-external-id",
  
  "reset": False
}

# 發送請求
response = requests.post(url, headers=headers, data=payload)

# 輸出結果
print(response)
