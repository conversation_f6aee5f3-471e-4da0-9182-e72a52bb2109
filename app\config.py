from pydantic_settings import BaseSettings
from urllib import parse
import logging
from typing import Optional, Dict, List
import json
import os

logger = logging.getLogger(__name__)

# QMS資料庫
QMSDB_SETTING = {
    "host": "**********",
    "port": 3306,
    "username": "qsuser",
    "password": "1q2w3e4R",
    "db": "qsm",
    "charset": "utf8",
}

# KS.TiDB資料庫
KSTIDB_SETTING = {
    "host": "************",
    "port": 3390,
    "username": "qsuser",
    "password": "QsUser@2023",
    "db": "etdb",
}

# KH.StarRocks 資料庫
KHSTARROCKS_SETTING = {
    "host": "**********",
    "user": "flexium",
    "password": "flexium",
    "port": 9030,
    "db": "etdb"
}


class Settings(BaseSettings):
    # API 設置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "統計分析 API 服務"

    # 安全設置
    SECRET_KEY: str = "your-secret-key-here"  # 在生產環境中應該從環境變數讀取
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # API Key 設置
    API_KEYS: Dict[str, str] = {
        "backend-service": "your-api-key-here",  # 在生產環境中應該從環境變數讀取
    }
    API_KEY_HEADER: str = "X-API-Key"

    # 速率限制
    RATE_LIMIT_PER_MINUTE: int = 60

    # 數據限制
    MAX_ARRAY_SIZE: int = 10000  # 最大數組大小

    # 數據驗證
    MIN_SAMPLE_SIZE: int = 3  # 最小樣本大小

    # QMS 資料庫連線
    QMSDB_URL: str = f'mysql+pymysql://{QMSDB_SETTING["username"]}:{parse.quote_plus(QMSDB_SETTING["password"])}@{QMSDB_SETTING["host"]}:{QMSDB_SETTING["port"]}/{QMSDB_SETTING["db"]}'

    # KS.TiDB 資料庫連線
    KSTIDB_URL: str = f'mysql+pymysql://{KSTIDB_SETTING["username"]}:{parse.quote_plus(KSTIDB_SETTING["password"])}@{KSTIDB_SETTING["host"]}:{KSTIDB_SETTING["port"]}/{KSTIDB_SETTING["db"]}'

    # KH.StarRocks 資料庫連線
    KHSTARROCKS_URL: str = f'mysql+pymysql://{KHSTARROCKS_SETTING["user"]}:{parse.quote_plus(KHSTARROCKS_SETTING["password"])}@{KHSTARROCKS_SETTING["host"]}:{KHSTARROCKS_SETTING["port"]}/{KHSTARROCKS_SETTING["db"]}?charset=utf8'

    # Redis 設定
    REDIS_HOST: str = "**********"     # Redis 主機地址
    REDIS_PORT: int = 6379             # Redis 端口
    REDIS_DB: int = 0                  # Redis 數據庫編號
    REDIS_PASSWORD: str | None = None  # Redis 密碼，如果有設置的話
    REDIS_CACHE_PREFIX: str = "sfdaiym"  # Redis 快取鍵前綴

    class Config:
        case_sensitive = True
        env_file = ".env"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # 從環境變量中讀取 API Keys
        api_keys_env = os.getenv("API_KEYS")
        if api_keys_env:
            try:
                self.API_KEYS = json.loads(api_keys_env)
            except json.JSONDecodeError:
                logger.warning("警告: API_KEYS 環境變量格式不正確，應為 JSON 字符串")


settings = Settings()
