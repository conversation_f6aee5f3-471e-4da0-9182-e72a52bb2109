from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from .config import settings
from .models.method_info import get_available_methods, StatisticalMethodInfo
# 統計方法(靜態頁面) Routers 引用
from .routers.statistics.basic_stats import router as basic_stats_router
from .routers.statistics.regression import router as regression_router
from .routers.statistics.correlation import router as correlation_router
from .routers.statistics.dimension_reduction import router as dimension_reduction_router
from .routers.statistics.survival import router as survival_router
# IYM Routers 引用
from .routers.iym.main_method import router as iym_router
from .routers.iym.basic_statistics import router as iym_basic_statistics_router
from .routers.iym.test_statistics import router as iym_test_statistics_router
from .routers.iym.machine_learning import router as iym_ml_router
import logging
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
import time
from typing import List, Dict
from fastapi.routing import APIRoute
from .utils.redis_utils import RedisClient

# 設定日誌
# 先確保 logs 目錄存在
log_dir = Path(__file__).parent / "logs"
log_dir.mkdir(exist_ok=True)

# 定義輸出格式
TIME_FORMAT = "%Y-%m-%d %H:%M"
FORMAT = '%(asctime)s %(filename)s:%(lineno)d [%(levelname)s] %(message)s'

# 建立基本設定
logging.basicConfig(
    level=logging.INFO,
    format=FORMAT,
    datefmt=TIME_FORMAT
)

# 建立 formatter 給檔案處理器使用
formatter = logging.Formatter(fmt=FORMAT, datefmt=TIME_FORMAT)

# 取得 root logger
logger = logging.getLogger()

# 添加檔案處理器
file_handler = TimedRotatingFileHandler(
    filename=log_dir / "error.log",
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)

# 設定檔案處理器只記錄 ERROR 以上級別
file_handler.setLevel(logging.ERROR)

# 設定檔案格式
file_handler.setFormatter(formatter)

# 添加到 logger
logger.addHandler(file_handler)

# 先檢查 Redis 健康狀態
RedisClient.monitor_redis_health()

app = FastAPI(title=settings.PROJECT_NAME,
              openapi_url=f"{settings.API_V1_STR}/openapi.json")

# 添加靜態文件支持
app.mount("/static", StaticFiles(directory="static"), name="static")

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 請求計數器
request_counts = {}

# Middleware 設定


@app.middleware("http")
async def rate_limit(request: Request, call_next):
    # 獲取客戶端 IP
    client_ip = request.client.host
    current_time = time.time()

    # 清理舊的請求記錄
    request_counts[client_ip] = [
        timestamp for timestamp in request_counts.get(client_ip, [])
        if current_time - timestamp < 60
    ]

    # 檢查請求頻率
    if len(request_counts.get(client_ip,
                              [])) >= settings.RATE_LIMIT_PER_MINUTE:
        return JSONResponse(status_code=429,
                            content={"detail": "請求過於頻繁，請稍後再試"})

    # 添加新的請求記錄
    request_counts.setdefault(client_ip, []).append(current_time)

    response = await call_next(request)
    return response

# 靜態說明網頁路由


@app.get("/")
async def root():
    """首頁"""
    return FileResponse('static/index.html')


@app.get("/static/stat_method.html")
async def stat_method():
    """統計方法說明頁面"""
    return FileResponse('static/stat_method.html')


@app.get("/api/v1/methods",
         response_model=List[StatisticalMethodInfo],
         tags=["統計方法"])
async def list_statistical_methods():
    """
    列出所有可用的統計分析方法及其使用情境說明

    Returns:
        List[StatisticalMethodInfo]: 統計方法資訊列表
    """
    return get_available_methods()


# 註冊路由
app.include_router(basic_stats_router,
                   prefix="/api/v1/statistics", tags=["基本統計"])
app.include_router(regression_router,
                   prefix="/api/v1/statistics", tags=["回歸分析"])
app.include_router(correlation_router,
                   prefix="/api/v1/statistics", tags=["相關分析"])
app.include_router(dimension_reduction_router,
                   prefix="/api/v1/statistics", tags=["降維分析"])
app.include_router(survival_router, prefix="/api/v1/statistics", tags=["存活分析"])
app.include_router(iym_router, prefix="/api/v1/iym/main_method", tags=["IYM"])
app.include_router(iym_basic_statistics_router,
                   prefix="/api/v1/iym/basic_statistics", tags=["IYM基礎統計"])
app.include_router(iym_test_statistics_router,
                   prefix="/api/v1/iym/test_statistics", tags=["IYM統計檢定"])
app.include_router(
    iym_ml_router, prefix="/api/v1/iym/machine_learning", tags=["IYM機器學習"])


@app.get("/api/v1/statistics/routes", tags=["API資訊"])
async def get_statistics_routes() -> Dict[str, List[Dict[str, str]]]:
    """
    獲取所有統計相關的API路徑

    Returns:
        Dict[str, List[Dict[str, str]]]: 按標籤分組的API路徑列表
    """
    statistics_routes = {}

    # 獲取所有路由
    routes = app.routes

    # 過濾並組織統計相關的路由
    for route in routes:
        if isinstance(route, APIRoute) and route.tags:  # 確保路由有標籤
            for tag in route.tags:
                if "分析" in tag or "統計" in tag:  # 篩選統計相關的路由
                    if tag not in statistics_routes:
                        statistics_routes[tag] = []

                    statistics_routes[tag].append({
                        "path": route.path,
                        "method": route.methods.pop() if route.methods else "GET",  # 獲取HTTP方法
                        "summary": route.summary or "無描述",
                        "operation_id": route.name
                    })

    return statistics_routes

    # 如果想要用python -m app.main這樣的方式開啟可以這樣寫
# if __name__ == "__main__":
    # import uvicorn
    # uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
