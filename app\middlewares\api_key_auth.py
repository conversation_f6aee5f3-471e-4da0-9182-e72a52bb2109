from fastapi import Request, HTTPException, status, Depends
from fastapi.security.api_key import APIKeyHeader
from ..config import settings
from typing import Optional

# 定義 API Key 頭部
api_key_header = APIKeyHeader(name=settings.API_KEY_HEADER, auto_error=False)

async def get_api_key(api_key_header: Optional[str] = Depends(api_key_header)):
    """
    驗證 API Key
    
    Args:
        api_key_header: 從請求頭中獲取的 API Key
        
    Returns:
        str: 服務 ID
        
    Raises:
        HTTPException: 如果 API Key 無效或缺失
    """
    if api_key_header is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少 API Key",
            headers={"WWW-Authenticate": settings.API_KEY_HEADER},
        )
    
    # 檢查 API Key 是否有效
    for service_id, key in settings.API_KEYS.items():
        if api_key_header == key:
            return service_id
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="無效的 API Key",
        headers={"WWW-Authenticate": settings.API_KEY_HEADER},
    )

# 可選的依賴項，用於需要 API Key 認證的路由
def require_api_key(service_id: str = Depends(get_api_key)):
    """
    要求有效的 API Key
    
    Args:
        service_id: 服務 ID
        
    Returns:
        str: 服務 ID
    """
    return service_id 