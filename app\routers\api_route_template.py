import logging
from fastapi import APIRouter, Query, HTTPException, Body, Depends
from ...middlewares.api_key_auth import require_api_key
from ...utils.redis_utils import RedisClient

# 設定 logger (讓main.py的logger可以抓到此頁面)
logger = logging.getLogger(__name__)

router = APIRouter()

# 路由範例，response須根據方法回傳更改
""" request的Body範例:
# use_redis指名是否使用快取，預設為True，故可不寫
{
    "參數1": "..",
    "參數2": "..",
    "參數3": "..",
    "use_redis": true
}
# 與上方效果相同，因為預設為True，故可不寫
{
    "參數1": "..",
    "參數2": "..",
    "參數3": "..",
}
# 若不使用快取，則將use_redis設為false，但同樣會把輸出結果寫入快取，以利後續調用
{
    "參數1": "..",
    "參數2": "..",
    "參數3": "..",
    "use_redis": false
}
"""


@router.post(
    "/template",
    response_model=dict,
    dependencies=[Depends(require_api_key)],
    summary="API 名稱",
    description="API 描述",
    responses={
        200: {
            "description": "成功達成..事項",
            "content": {
                "application/json": {
                    "example": {"detail": "成功"}
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": ".."}
                }
            }
        },
        500: {
            "description": "伺服器錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": ".."}
                }
            }
        }
    }
)
# 使用decorator調用快取，expire為快取時間，cache_key_prefix為快取鍵前綴(我們通常使用函數名稱)
# 使用Body接收參數，example為參數範例，use_redis為是否使用快取，預設為True，故可不寫
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="template")
async def template(
    request: dict = Body(..., example={
        "參數1": "..",
        "參數2": "..",
        "參數3": "..",
        "use_redis": True
    })
):
    try:
        var1 = request.get("參數1")
        var2 = request.get("參數2")
        var3 = request.get("參數3")

        # 檢查參數
        if not all([var1, var2, var3]):
            error_message = "參數錯誤：缺少必要參數"
            logger.error(error_message)
            raise HTTPException(status_code=400, detail=error_message)

        # 資料庫操作這裡不演示，請自行修改
        data = []

        return data

    except Exception as e:
        error_message = f"伺服器錯誤：{str(e)}"
        # Exception中使用logging.exception記錄錯誤，會自動產生Error並記錄錯誤堆疊
        logging.exception(error_message)
        raise HTTPException(status_code=500, detail=error_message)
