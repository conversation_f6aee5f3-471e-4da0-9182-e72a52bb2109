from fastapi import APIRouter, Body, HTTPException, Depends
import numpy as np
import pandas as pd
from ...utils.redis_utils import RedisClient
import logging
from ...middlewares.api_key_auth import require_api_key
from ...services.data_service import spi_data
from ...services.correlation_matrix import calculate_correlation_matrix

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/mean",
    summary="計算平均值",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的平均值。
    """,
    responses={
        200: {
            "description": "成功計算平均值",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 12.3,
                        "TD": 8.7
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "平均值計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="mean_analysis")
async def mean_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(np.mean(col_data)) if len(
                col_data) > 0 else None
        return result
    except Exception as e:
        logging.exception(f"平均值計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"平均值計算失敗: {str(e)}")


@router.post(
    "/median",
    summary="計算中位數",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的中位數。
    """,
    responses={
        200: {
            "description": "成功計算中位數",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 12.0,
                        "TD": 8.5
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "中位數計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="median_analysis")
async def median_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(np.median(col_data)) if len(
                col_data) > 0 else None
        return result
    except Exception as e:
        logging.exception(f"中位數計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"中位數計算失敗: {str(e)}")


@router.post(
    "/mode",
    summary="計算眾數",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵眾數。
    """,
    responses={
        200: {
            "description": "成功計算眾數",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 11.5,
                        "TD": 8.0
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "眾數計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="mode_analysis")
async def mode_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(col_data.mode().iloc[0]
                                ) if not col_data.mode().empty else None
        return result
    except Exception as e:
        logging.exception(f"眾數計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"眾數計算失敗: {str(e)}")


@router.post(
    "/std",
    summary="計算標準差",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的標準差。
    """,
    responses={
        200: {
            "description": "成功計算標準差",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 2.1,
                        "TD": 1.5
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "標準差計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="std_analysis")
async def std_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(np.std(col_data, ddof=1)) if len(
                col_data) > 0 else None
        return result
    except Exception as e:
        logging.exception(f"標準差計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"標準差計算失敗: {str(e)}")


@router.post(
    "/quartiles",
    summary="計算四分位數",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的第一四分位數(q1)與第三四分位數(q3)。
    """,
    responses={
        200: {
            "description": "成功計算四分位數",
            "content": {
                "application/json": {
                    "example": {
                        "MD": {
                            "q1": 10.2,
                            "q3": 14.1
                        },
                        "TD": {
                            "q1": 7.5,
                            "q3": 9.8
                        }
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "四分位數計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="quartiles_analysis")
async def quartiles_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = {
                "q1": float(np.percentile(col_data, 25)) if len(col_data) > 0 else None,
                "q3": float(np.percentile(col_data, 75)) if len(col_data) > 0 else None
            }
        return result
    except Exception as e:
        logging.exception(f"四分位數計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"四分位數計算失敗: {str(e)}")


@router.post(
    "/max",
    summary="計算最大值",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的最大值。
    """,
    responses={
        200: {
            "description": "成功計算最大值",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 16.5,
                        "TD": 11.2
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "最大值計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="max_analysis")
async def max_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(np.max(col_data)) if len(
                col_data) > 0 else None
        return result
    except Exception as e:
        logging.exception(f"最大值計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"最大值計算失敗: {str(e)}")


@router.post(
    "/min",
    summary="計算最小值",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的最小值。
    """,
    responses={
        200: {
            "description": "成功計算最小值",
            "content": {
                "application/json": {
                    "example": {
                        "MD": 8.0,
                        "TD": 6.0
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "最小值計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="min_analysis")
async def min_analysis(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            result[col] = float(np.min(col_data)) if len(
                col_data) > 0 else None
        return result
    except Exception as e:
        logging.exception(f"最小值計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"最小值計算失敗: {str(e)}")


@router.post(
    "/basic_statistics",
    summary="基礎統計量分析",
    dependencies=[Depends(require_api_key)],
    description="""
    根據傳入的品目、工單和特徵，計算特徵的基礎統計量，包括平均值、標準差、中位數、眾數、最大值、最小值、四分位數等。
    """,
    responses={
        200: {
            "description": "成功計算基礎統計量",
            "content": {
                "application/json": {
                    "example": {
                        "MD": {
                            "mean": 12.3,
                            "std": 2.1,
                            "median": 12.0,
                            "mode": 11.5,
                            "min": 8.0,
                            "max": 16.5,
                            "q1": 10.2,
                            "q3": 14.1
                        },
                        "TD": {
                            "mean": 8.7,
                            "std": 1.5,
                            "median": 8.5,
                            "mode": 8.0,
                            "min": 6.0,
                            "max": 11.2,
                            "q1": 7.5,
                            "q3": 9.8
                        }
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：features 必須是非空的列表"}
                }
            }
        },
        402: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "以下特徵不在數據中"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "基礎統計量分析失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="basic_statistics_analysis")
async def basic_statistics_analysis(request: dict = Body(..., example={
    "resource_type": "SPI",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062",
    "features": [
        "MD", "TD"
    ],
    "use_redis": True
})):
    try:
        resource_type = request.get("resource_type", "SPI")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        if not isinstance(features, list) or len(features) == 0:
            logger.error("參數錯誤：features 必須是非空的列表")
            raise HTTPException(
                status_code=401, detail="參數錯誤：features 必須是非空的列表")
        if resource_type == "SPI":
            data = spi_data(part_no=part_no,
                            work_order=work_order, use_redis=use_redis)
        elif resource_type == "PBC":
            # TODO: 實作 PBC 資料庫查詢
            print("PBC")
        missing_features = [f for f in features if f not in data.columns]
        if missing_features:
            logger.error(f"以下特徵不在數據中: {', '.join(missing_features)}")
            raise HTTPException(
                status_code=402, detail=f"以下特徵不在數據中: {', '.join(missing_features)}")
        stats_result = {}
        for col in features:
            col_data = pd.to_numeric(data[col], errors='coerce').dropna()
            if len(col_data) == 0:
                stats_result[col] = {k: None for k in [
                    "mean", "std", "median", "mode", "min", "max", "q1", "q3"]}
                continue
            stats_result[col] = {
                "mean": float(np.mean(col_data)),
                "std": float(np.std(col_data, ddof=1)),
                "median": float(np.median(col_data)),
                "mode": float(col_data.mode().iloc[0]) if not col_data.mode().empty else None,
                "min": float(np.min(col_data)),
                "max": float(np.max(col_data)),
                "q1": float(np.percentile(col_data, 25)),
                "q3": float(np.percentile(col_data, 75))
            }
        return stats_result
    except Exception as e:
        logging.exception(f"基礎統計量分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"基礎統計量分析失敗: {str(e)}")


@router.post(
    "/correlation_matrix",
    summary="計算相關係數矩陣",
    dependencies=[Depends(require_api_key)],
    responses={
        200: {
            "description": "成功取得相關係數矩陣",
            "content": {
                "application/json": {
                    "example": {
                        "MD": {"MD": 1.0, "TD": 0.12, "PRINT_PRESSURE": -0.03},
                        "TD": {"MD": 0.12, "TD": 1.0, "PRINT_PRESSURE": 0.05},
                        "PRINT_PRESSURE": {"MD": -0.03, "TD": 0.05, "PRINT_PRESSURE": 1.0}
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：features 必須是非空的列表"}
                }
            }
        },
        402: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "以下特徵不在數據中"}
                }
            }
        },
        403: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "數據量不足，無法計算相關係數"}
                }
            }
        },
        404: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "相關係數計算結果包含 NaN，請確認資料量與變數變異性"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "相關係數計算失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="correlation_matrix_analysis")
async def correlation_matrix_analysis(request: dict = Body(..., example={
    "resource_type": "SPI",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "features": [
        "MD", "TD", "PRINT_PRESSURE", "HUMIDITY", "PRINT_SPEED", "TEMPRATURE"
    ],
    "use_redis": True
})):
    try:
        resource_type = request.get("resource_type", "SPI")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        if not isinstance(features, list) or len(features) == 0:
            logger.error("參數錯誤：features 必須是非空的列表")
            raise HTTPException(
                status_code=401, detail="參數錯誤：features 必須是非空的列表")
        if resource_type == "SPI":
            data = spi_data(part_no=part_no,
                            work_order=work_order, use_redis=use_redis)
        elif resource_type == "PBC":
            # TODO: 實作 PBC 資料庫查詢
            print("PBC")
        missing_features = [f for f in features if f not in data.columns]
        if missing_features:
            logger.error(f"以下特徵不在數據中: {', '.join(missing_features)}")
            raise HTTPException(
                status_code=402, detail=f"以下特徵不在數據中: {', '.join(missing_features)}")

        corr_data = data[features].copy()
        for col in corr_data.columns:
            corr_data[col] = pd.to_numeric(corr_data[col], errors='coerce')
        corr_data = corr_data.dropna()
        if len(corr_data) < 2:
            logger.error("數據量不足，無法計算相關係數")
            raise HTTPException(status_code=403, detail="數據量不足，無法計算相關係數")

        corr_matrix = calculate_correlation_matrix(corr_data)
        # 檢查 NaN
        for row in corr_matrix.values():
            if any(pd.isna(v) for v in row.values()):
                logger.error("相關係數計算結果包含 NaN，請確認資料量與變數變異性")
                raise HTTPException(
                    status_code=404, detail="相關係數計算結果包含 NaN，請確認資料量與變數變異性")
        return corr_matrix

    except Exception as e:
        logging.exception(f"相關係數計算失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"相關係數計算失敗: {str(e)}")
