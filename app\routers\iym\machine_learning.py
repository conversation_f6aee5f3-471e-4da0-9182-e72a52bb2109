from fastapi import APIRout<PERSON>, Body, HTTPException, Depends
from ...services.data_service import get_iym_data, spi_data
from ...services.random_forest_continuous_service import train_random_forest_continuous
from ...services.random_forest_category_service import train_random_forest_category
from ...services.xgboost_continuous_service import xgboost_continuous
from ...services.xgboost_category_service import train_xgboost_category
from ...services.model_comparison_service import comparison_algorithms
from ...utils.date_utils import is_valid_date
from ...middlewares.api_key_auth import require_api_key
from ...services.logistic_regression_service import train_logistic_regression
from ...utils.redis_utils import RedisClient
from ...services.lasso_service import train_lasso_model
from ...services.mars_interaction import mars_interaction_analysis
from ...services.decision_tree_interaction import decision_tree_interaction_analysis
from ...services.model_comparison_service import importance_score
from ...services.som_clustering import som_clustering_service
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import xgboost as xgb
from ...services.shap_service import shap_continuous
from ...utils.csv_reader import read_local_csv
from ...utils.ml_utils import target_encode
from collections import defaultdict

import os
import logging
import pandas as pd

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/random_forest",
    summary="隨機森林分析",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的日期範圍、品目、不良項目和製程名稱，進行隨機森林分析。
    使用隨機森林模型分析各製程對不良率的重要性排序。
    """,
    responses={
        200: {
            "description": "成功執行隨機森林分析",
            "content": {"application/json": {
                "example": {
                    "feature_importance_mapping": {
                        "RTR VCP全局面鍍銅": 0.25,
                        "RTR局部銅電剝膜": 0.20
                    },
                    "feature_importance": [
                        {
                            "Feature": "RTR VCP全局面鍍銅",
                            "Importance": 0.25
                        },
                        {
                            "Feature": "RTR局部銅電剝膜",
                            "Importance": 0.20
                        }
                    ],
                    "importance_plot": "base64_encoded_image_string",
                    "model_performance": {
                        "train_score": 0.85,
                        "test_score": 0.82
                    }
                }
            }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        402: {
            "description": "查無資料",
            "content": {
                "application/json": {
                    "example": {"detail": "查無資料"}
                }
            }
        },
        403: {
            "description": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱",
            "content": {
                "application/json": {
                    "example": {"detail": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "隨機森林分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="random_forest_analysis")
async def random_forest_analysis(request: dict = Body(..., example={
    "start_date": "2025-01-01",
    "end_date": "2025-02-28",
    "part_no": "QE2825",
    "defect_code": "S03",
    "process_name": "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合",
    "iterations": 100,
    "use_redis": True
})):
    """隨機森林分析

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目
            - defect_code (str): 不良項目
            - process_name (str): 製程名稱
            - iterations (int): 迭代次數，預設為 100
            - use_redis (bool): 是否使用 Redis 快取，預設為 True

    Returns:
        Dict: 隨機森林分析結果，包含特徵重要性和模型性能指標
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        defect_code = request.get("defect_code")
        process_name = request.get("process_name")
        iterations = request.get("iterations", 100)

        # 檢查必要參數
        if not all([start_date, end_date, part_no, defect_code, process_name]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        _, _, step_process_name_df, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix="IYM 準備資料"
        )

        if wide_data.empty:
            raise HTTPException(status_code=402, detail="查無資料")

        target_column = "defect_rate"
        # 只取線體欄位
        categorical_columns = [
            col for col in wide_data.columns if col.endswith("線體")]
        if not categorical_columns:
            raise HTTPException(
                status_code=403, detail="找不到任何 '線體' 相關欄位，請檢查資料欄位名稱")

        # 目標編碼
        df_encoded, _ = target_encode(
            wide_data, target_column, categorical_columns, smooth=10)
        X = df_encoded[categorical_columns]
        y = df_encoded[target_column]

        # 動態產生 line_mapping，依 step 順序，重複 process_name 才加流水號
        # 取得 step 對應的 process_name
        step_to_process = dict(zip(step_process_name_df['step'].astype(
            str), step_process_name_df['process_name']))
        process_name_count = defaultdict(int)
        process_name_seen = defaultdict(int)
        # 統計每個 process_name 出現幾次
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_count[pname] += 1

        # 產生 line_mapping
        line_mapping = {}
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_seen[pname] += 1
            if process_name_count[pname] == 1:
                new_name = pname
            else:
                idx = process_name_seen[pname]
                new_name = f"{pname}-{idx}"
            line_mapping[col] = new_name

        # 將 X 欄位名稱轉換為中文製程名稱（自動流水號）
        X = X.rename(columns=line_mapping)

        if isinstance(y, pd.DataFrame):
            y = y.iloc[:, 0]
        y = pd.to_numeric(y, errors='coerce')

        # 執行隨機森林分析
        result = train_random_forest_continuous(X, y, iterations=iterations)

        return result

    except Exception as e:
        logging.exception(f"隨機森林分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"隨機森林分析失敗: {str(e)}")


@router.post(
    "/rf_shap_summary",
    summary="隨機森林SHAP分布",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的日期範圍、品目、不良項目和製程名稱，進行隨機森林SHAP summary plot資料分析。
    """,
    responses={
        200: {
            "description": "成功取得隨機森林SHAP分布",
            "content": {"application/json": {
                "example": {
                    "rf_shap_summary": {
                        "feature_names": [
                            "(R232-02)RTR局部銅電剝膜",
                            "(R220-S01)RTR乾膜壓合-1",
                            "(R233-03)RTR線路顯影",
                            "(R233-01)RTR蝕刻-剝膜",
                            "(R223-S01)RTR曝光-2",
                            "(R220-S01)RTR乾膜壓合-2",
                            "(R221-01)RTR 乾膜前處理",
                            "(R212-S01)RTR VCP全局面鍍銅",
                            "(R223-S01)RTR曝光-1"
                        ],
                        "shap_values": [
                            [
                                -0.12410714198527525,
                                0.05975857144429548,
                                -0.01391666429117322,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0
                            ]
                        ],
                        "feature_values": [
                            [
                                0.15622448979591838,
                                0.23051020408163267,
                                0.18701298701298705,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573
                            ]
                        ]
                    }
                }
            }}
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        402: {
            "description": "查無資料",
            "content": {
                "application/json": {
                    "example": {"detail": "查無資料"}
                }
            }
        },
        403: {
            "description": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱",
            "content": {
                "application/json": {
                    "example": {"detail": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "隨機森林SHAP summary分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="rf_shap_summary")
async def rf_shap_summary(request: dict = Body(..., example={
    "start_date": "2025-01-01",
    "end_date": "2025-02-28",
    "part_no": "QE2825",
    "defect_code": "S03",
    "process_name": "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合",
    "use_redis": True
})):
    """隨機森林 SHAP summary plot 資料

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目
            - defect_code (str): 不良項目
            - process_name (str): 製程名稱
            - use_redis (bool): 是否使用 Redis 快取，預設為 True

    Returns:
        Dict: 隨機森林 SHAP summary plot 資料
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        defect_code = request.get("defect_code")
        process_name = request.get("process_name")

        # 檢查必要參數
        if not all([start_date, end_date, part_no, defect_code, process_name]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        _, _, step_process_name_df, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix="IYM 準備資料"
        )

        if wide_data.empty:
            raise HTTPException(status_code=402, detail="查無資料")

        target_column = "defect_rate"
        # 只取線體欄位
        categorical_columns = [
            col for col in wide_data.columns if col.endswith("線體")]
        if not categorical_columns:
            raise HTTPException(
                status_code=403, detail="找不到任何 '線體' 相關欄位，請檢查資料欄位名稱")

        # 目標編碼
        df_encoded, _ = target_encode(
            wide_data, target_column, categorical_columns, smooth=10)
        X = df_encoded[categorical_columns]
        y = df_encoded[target_column]

        # 動態產生 line_mapping，依 step 順序，重複 process_name 才加流水號
        # 取得 step 對應的 process_name
        step_to_process = dict(zip(step_process_name_df['step'].astype(
            str), step_process_name_df['process_name']))
        process_name_count = defaultdict(int)
        process_name_seen = defaultdict(int)
        # 統計每個 process_name 出現幾次
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_count[pname] += 1

        # 產生 line_mapping
        line_mapping = {}
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_seen[pname] += 1
            if process_name_count[pname] == 1:
                new_name = pname
            else:
                idx = process_name_seen[pname]
                new_name = f"{pname}-{idx}"
            line_mapping[col] = new_name

        # 將 X 欄位名稱轉換為中文製程名稱（自動流水號）
        X = X.rename(columns=line_mapping)

        if isinstance(y, pd.DataFrame):
            y = y.iloc[:, 0]
        y = pd.to_numeric(y, errors='coerce')

        rf_model = RandomForestRegressor(
            n_estimators=200,      # 減少樹數量，加快訓練
            min_samples_split=2,   # 增加分裂門檻，加快速度
            n_jobs=-1,             # 多核心加速
            random_state=42
        )
        rf_model.fit(X, y)
        shap_result = shap_continuous(rf_model, X)
        return {"rf_shap_summary": shap_result}

    except Exception as e:
        logging.exception(f"隨機森林 SHAP summary plot 資料分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"隨機森林 SHAP summary plot 資料分析失敗: {str(e)}")


@router.post(
    "/xgboost",
    summary="XGBoost分析",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的日期範圍、品目、不良項目和製程名稱，進行XGBoost分析。
    使用XGBoost模型分析各製程對不良率的影響程度。
    """,
    responses={
        200: {
            "description": "成功執行XGBoost分析",
            "content": {
                "application/json": {
                    "example": {
                        "feature_importance_mapping": {
                            "RTR VCP全局面鍍銅": 0.25,
                            "RTR局部銅電剝膜": 0.20
                        },
                        "feature_importance": [
                            {
                                "Feature": "RTR VCP全局面鍍銅",
                                "Importance": 0.25
                            },
                            {
                                "Feature": "RTR局部銅電剝膜",
                                "Importance": 0.20
                            }
                        ],
                        "importance_plot": "base64_encoded_image_string",
                        "model_performance": {
                            "train_score": 0.85,
                            "test_score": 0.82
                        }
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "XGBoost分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="xgboost_analysis")
async def xgboost_analysis(request: dict = Body(..., example={
    "start_date": "2025-01-01",
    "end_date": "2025-02-28",
    "part_no": "QE2825",
    "defect_code": "S03",
    "process_name": "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合",
    "iterations": 100,
    "use_redis": True
})):
    """XGBoost分析

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目
            - defect_code (str): 不良項目
            - process_name (str): 製程名稱
            - iterations (int): 迭代次數，預設為 100
    Returns:
        Dict: XGBoost分析結果，包含特徵重要性和模型性能指標
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        defect_code = request.get("defect_code")
        process_name = request.get("process_name")
        iterations = request.get("iterations", 100)

        # 檢查必要參數
        if not all([start_date, end_date, part_no, defect_code, process_name]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        _, _, step_process_name_df, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix="IYM 準備資料"
        )

        if wide_data.empty:
            raise HTTPException(status_code=402, detail="查無資料")

        target_column = "defect_rate"
        # 只取線體欄位
        categorical_columns = [
            col for col in wide_data.columns if col.endswith("線體")]
        if not categorical_columns:
            raise HTTPException(
                status_code=403, detail="找不到任何 '線體' 相關欄位，請檢查資料欄位名稱")

        # 目標編碼
        df_encoded, _ = target_encode(
            wide_data, target_column, categorical_columns, smooth=10)
        X = df_encoded[categorical_columns]
        y = df_encoded[target_column]

        # 動態產生 line_mapping，依 step 順序，重複 process_name 才加流水號
        # 取得 step 對應的 process_name
        step_to_process = dict(zip(step_process_name_df['step'].astype(
            str), step_process_name_df['process_name']))
        process_name_count = defaultdict(int)
        process_name_seen = defaultdict(int)
        # 統計每個 process_name 出現幾次
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_count[pname] += 1

        # 產生 line_mapping
        line_mapping = {}
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_seen[pname] += 1
            if process_name_count[pname] == 1:
                new_name = pname
            else:
                idx = process_name_seen[pname]
                new_name = f"{pname}-{idx}"
            line_mapping[col] = new_name

        # 將 X 欄位名稱轉換為中文製程名稱（自動流水號）
        X = X.rename(columns=line_mapping)

        if isinstance(y, pd.DataFrame):
            y = y.iloc[:, 0]
        y = pd.to_numeric(y, errors='coerce')

        # 執行XGBoost分析
        result = xgboost_continuous(X, y.squeeze(), iterations=iterations)

        return result
    except Exception as e:
        logging.exception(f"XGBoost分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"XGBoost分析失敗: {str(e)}")


@router.post(
    "/xgb_shap_summary",
    summary="XGBoost SHAP分布",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的日期範圍、品目、不良項目和製程名稱，進行XGBoost SHAP summary plot資料分析。
    """,
    responses={
        200: {
            "description": "成功取得XGBoost SHAP分布",
            "content": {"application/json": {
                "example": {
                    "xgb_shap_summary": {
                        "feature_names": [
                            "(R232-02)RTR局部銅電剝膜",
                            "(R220-S01)RTR乾膜壓合-1",
                            "(R233-03)RTR線路顯影",
                            "(R233-01)RTR蝕刻-剝膜",
                            "(R223-S01)RTR曝光-2",
                            "(R220-S01)RTR乾膜壓合-2",
                            "(R221-01)RTR 乾膜前處理",
                            "(R212-S01)RTR VCP全局面鍍銅",
                            "(R223-S01)RTR曝光-1"
                        ],
                        "shap_values": [
                            [
                                -0.12410714198527525,
                                0.05975857144429548,
                                -0.01391666429117322,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0
                            ]
                        ],
                        "feature_values": [
                            [
                                0.15622448979591838,
                                0.23051020408163267,
                                0.18701298701298705,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573,
                                0.19571428571428573
                            ]
                        ]
                    }
                }
            }}
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        402: {
            "description": "查無資料",
            "content": {
                "application/json": {
                    "example": {"detail": "查無資料"}
                }
            }
        },
        403: {
            "description": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱",
            "content": {
                "application/json": {
                    "example": {"detail": "找不到任何 '線體' 相關欄位，請檢查資料欄位名稱"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "隨機森林SHAP summary分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="xgb_shap_summary")
async def xgb_shap_summary(request: dict = Body(..., example={
    "start_date": "2025-01-01",
    "end_date": "2025-02-28",
    "part_no": "QE2825",
    "defect_code": "S03",
    "process_name": "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合",
    "use_redis": True
})):
    """XGBoost SHAP summary plot 資料

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目
            - defect_code (str): 不良項目
            - process_name (str): 製程名稱
            - use_redis (bool): 是否使用 Redis 快取，預設為 True

    Returns:
        Dict: XGBoost SHAP summary plot 資料
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        defect_code = request.get("defect_code")
        process_name = request.get("process_name")

        # 檢查必要參數
        if not all([start_date, end_date, part_no, defect_code, process_name]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        _, _, step_process_name_df, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix="IYM 準備資料"
        )

        if wide_data.empty:
            raise HTTPException(status_code=402, detail="查無資料")

        target_column = "defect_rate"
        # 只取線體欄位
        categorical_columns = [
            col for col in wide_data.columns if col.endswith("線體")]
        if not categorical_columns:
            raise HTTPException(
                status_code=403, detail="找不到任何 '線體' 相關欄位，請檢查資料欄位名稱")

        # 目標編碼
        df_encoded, _ = target_encode(
            wide_data, target_column, categorical_columns, smooth=10)
        X = df_encoded[categorical_columns]
        y = df_encoded[target_column]

        # 動態產生 line_mapping，依 step 順序，重複 process_name 才加流水號
        # 取得 step 對應的 process_name
        step_to_process = dict(zip(step_process_name_df['step'].astype(
            str), step_process_name_df['process_name']))
        process_name_count = defaultdict(int)
        process_name_seen = defaultdict(int)
        # 統計每個 process_name 出現幾次
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_count[pname] += 1

        # 產生 line_mapping
        line_mapping = {}
        for col in categorical_columns:
            step = col.split('-')[0]
            pname = step_to_process.get(step, step)
            process_name_seen[pname] += 1
            if process_name_count[pname] == 1:
                new_name = pname
            else:
                idx = process_name_seen[pname]
                new_name = f"{pname}-{idx}"
            line_mapping[col] = new_name

        # 將 X 欄位名稱轉換為中文製程名稱（自動流水號）
        X = X.rename(columns=line_mapping)

        if isinstance(y, pd.DataFrame):
            y = y.iloc[:, 0]
        y = pd.to_numeric(y, errors='coerce')

        xgb_model = xgb.XGBRegressor(
            eval_metric='rmse',
            random_state=42,
            n_estimators=500,
            learning_rate=0.05,
            max_depth=5,
            tree_method='hist',
            n_jobs=-1
        )
        xgb_model.fit(X, y)
        shap_result = shap_continuous(xgb_model, X)
        return {"xgb_shap_summary": shap_result}

    except Exception as e:
        logging.exception(f"XGBoost SHAP summary plot 資料分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"XGBoost SHAP summary plot 資料分析失敗: {str(e)}")


# todo example跟description需修改


@router.post(
    "/xgboost_categorical_for_parameter",
    summary="XGBoost分類模型分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 XGBoost 分類模型分析參數。
    此 API 會對提供的參數進行 XGBoost 分類模型分析，
    計算特徵重要性和模型性能指標。
    """,
    responses={
        200: {
            "description": "成功取得XGBoost分類模型分析結果",
            "content": {
                "application/json": {
                    "example": {
                        "feature_importance": [
                            {
                                "Feature": "TEST_NUMBER",
                                "Importance": 0.25
                            },
                            {
                                "Feature": "solder_life",
                                "Importance": 0.20
                            }
                        ]
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：variables 必須是非空的列表"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "XGBoost分類模型分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="xgboost_categorical_for_parameter")
async def xgboost_categorical_for_parameter(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variables": [
        'TEST_NUMBER',
        'solder_life',
        'PRINT_SPEED',
        'PRINT_PRESSURE',
        'SNAP_OFF_DISTANCE',
        'SNAP_OFF_SPEED',
        'TEMPRATURE',
        'HUMIDITY',
        'TD',
        'MD',
        'tension_mean',
        'tension_range',
        'SQUEEGEE_SUM_COUNT'
    ],
    "target": 'ooc',
    "iterations": 100,
    "use_redis": True
})):
    """XGBoost分類模型分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str, optional): 資源類型，目前只支援 "SPI"/"PBC"，預設為 "SPI"
            - csv_path (str, optional): CSV檔案路徑(相對路徑)
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variables (list): 參數列表

    Returns:
        Dict: XGBoost分類模型分析結果，包含特徵重要性和模型性能指標
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variables = request.get("variables")
        target = request.get("target", "ooc")
        iterations = request.get("iterations", 100)
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            target = 'cluster'
            # 讀取csv檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, variables, target]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(
                    status_code=400,
                    detail="參數錯誤：缺少必要參數，需要csv_path或(part_no、work_order 和 variables)"
                )

            if not isinstance(variables, list) or len(variables) == 0:
                logger.error("參數錯誤：variables 必須是非空的列表")
                raise HTTPException(
                    status_code=401,
                    detail="參數錯誤：variables 必須是非空的列表"
                )

            if resource_type == "SPI":
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
                # 處理 target 欄位為 "ooc" 或 "in_control" 的情況
                if target in data.columns:
                    unique_vals = set(data[target].dropna().astype(
                        str).str.lower().unique())
                    if unique_vals.issubset({"ooc", "in_control"}):
                        data[target] = data[target].astype(
                            str).str.lower().map({"ooc": 1, "in_control": 0})

            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        if csv_path:
            X = data.drop(columns=['work_order', 'panel_no',
                                   'defect_pcs', 'defect_rate', 'cluster'])
        else:
            X = data[variables].copy()

        y = data[target]

        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce')

        result = train_xgboost_category(X, y, iterations=iterations)

        return result
    except Exception as e:
        logging.exception(f"XGBoost分類模型分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"XGBoost分類模型分析失敗: {str(e)}")

# todo example跟description需修改


@router.post(
    "/random_forest_categorical_for_parameter",
    summary="Random Forest 分類模型分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 Random Forest 分類模型分析參數。
    此 API 會對提供的參數進行 Random Forest 分類模型分析，
    計算特徵重要性和模型性能指標。
    """,
    responses={
        200: {
            "description": "成功取得 Random Forest 分類模型分析結果",
            "content": {
                "application/json": {
                    "example": {
                        "feature_importance_mapping": {
                            "TEST_NUMBER": 0.25,
                            "solder_life": 0.20
                        },
                        "feature_importance": [
                            {
                                "Feature": "TEST_NUMBER",
                                "Importance": 0.25
                            },
                            {
                                "Feature": "solder_life",
                                "Importance": 0.20
                            }
                        ]
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：variables 必須是非空的列表"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "Random Forest分類模型分析失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="random_forest_categorical_for_parameter")
async def random_forest_categorical_for_parameter(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variables": [
        'TEST_NUMBER',
        'solder_life',
        'PRINT_SPEED',
        'PRINT_PRESSURE',
        'SNAP_OFF_DISTANCE',
        'SNAP_OFF_SPEED',
        'TEMPRATURE',
        'HUMIDITY',
        'TD',
        'MD',
        'tension_mean',
        'tension_range',
        'SQUEEGEE_SUM_COUNT'
    ],
    "target": 'ooc',
    "iterations": 100,
    "use_redis": True
})):
    """Random Forest 分類模型分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str, optional): 資源類型，目前只支援 "SPI"/"PBC"，預設為 "SPI"
            - csv_path (str, optional): CSV檔案路徑(相對路徑)
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variables (list): 參數列表
            - target (str): 目標變數
            - iterations (int, optional): 迭代次數，預設為 100

    Returns:
        Dict: Random Forest分類模型分析結果，包含特徵重要性和模型性能指標
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variables = request.get("variables")
        target = request.get("target", "ooc")
        iterations = request.get("iterations", 100)
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            target = 'cluster'
            # 讀取csv檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, variables, target]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(
                    status_code=400,
                    detail="參數錯誤：缺少必要參數，需要csv_path或(part_no、work_order 和 variables)"
                )

            if not isinstance(variables, list) or len(variables) == 0:
                logger.error("參數錯誤：variables 必須是非空的列表")
                raise HTTPException(
                    status_code=401,
                    detail="參數錯誤：variables 必須是非空的列表"
                )

            if resource_type == "SPI":
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        if csv_path:
            X = data.drop(columns=['work_order', 'panel_no',
                                   'defect_pcs', 'defect_rate', 'cluster'])
        else:
            X = data[variables].copy()

        y = data[target]

        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce')

        result = train_random_forest_category(X, y, iterations)

        return result
    except Exception as e:
        logging.exception(f"Random Forest分類模型分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Random Forest分類模型分析失敗: {str(e)}")

# todo example跟description需修改


@router.post(
    "/logistic_regression_for_parameter",
    summary="Logistic Regression 分類模型分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 Logistic Regression 分類模型分析參數。
    此 API 會對提供的參數進行 Logistic Regression 分類模型分析，
    計算特徵重要性和模型性能指標。
    """,
    responses={
        200: {
            "description": "成功取得 Logistic Regression 分類模型分析結果",
            "content": {
                "application/json": {
                    "example": {
                        "feature_importance": [
                            {
                                "Feature": "MD",
                                "Importance": 0.0034984895850544927,
                                "Model": "Logistic Regression"
                            },
                            {
                                "Feature": "SQUEEGEE_SUM_COUNT",
                                "Importance": 0.0021025519339889272,
                                "Model": "Logistic Regression"
                            },
                            {
                                "Feature": "TEMPRATURE",
                                "Importance": 0.0012649828408135516,
                                "Model": "Logistic Regression"
                            }
                        ]
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：variables 必須是非空的列表"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "Logistic Regression分類模型分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="logistic_regression_for_parameter")
async def logistic_regression_for_parameter(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variables": [
        'TEST_NUMBER',
        'solder_life',
        'PRINT_SPEED',
        'PRINT_PRESSURE',
        'SNAP_OFF_DISTANCE',
        'SNAP_OFF_SPEED',
        'TEMPRATURE',
        'HUMIDITY',
        'TD',
        'MD',
        'tension_mean',
        'tension_range',
        'SQUEEGEE_SUM_COUNT'
    ],
    "target": 'ooc',
    "iterations": 100,
    "use_redis": True
})):
    """Logistic Regression 分類模型分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str, optional): 資源類型，目前只支援 "SPI"/"PBC"，預設為 "SPI"
            - csv_path (str, optional): CSV檔案路徑(相對路徑)
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variables (list): 參數列表

    Returns:
        Dict: Logistic Regression 分類模型分析結果，包含特徵重要性和模型性能指標
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variables = request.get("variables")
        target = request.get("target", "ooc")
        iterations = request.get("iterations", 100)
        use_redis = request.get("use_redis")
        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            target = 'cluster'
            # 讀取csv檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, variables, target]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(
                    status_code=400,
                    detail="參數錯誤：缺少必要參數，需要csv_path或(part_no、work_order 和 variables)"
                )

            if not isinstance(variables, list) or len(variables) == 0:
                logger.error("參數錯誤：variables 必須是非空的列表")
                raise HTTPException(
                    status_code=401,
                    detail="參數錯誤：variables 必須是非空的列表"
                )

            if resource_type == "SPI":
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        y = data[target]
        if csv_path:
            X = data.drop(columns=['work_order', 'panel_no',
                                   'defect_pcs', 'defect_rate', 'cluster'])
        else:
            X = data[variables].copy()

        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce')

        result = train_logistic_regression(X, y, iterations=iterations)

        return result
    except Exception as e:
        logging.exception(f"Logistic Regression分類模型分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Logistic Regression分類模型分析失敗: {str(e)}")

# todo description需修改，需添加resource_type判斷


@router.post(
    "/lasso_for_parameter",
    summary="Lasso回歸模型分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 Lasso 回歸模型分析參數。
    此 API 會對提供的參數進行 Lasso 回歸模型分析，
    計算特徵重要性和模型性能指標。
    """,
    responses={
        200: {
            "description": "成功取得 Lasso 回歸模型分析結果",
            "content": {
                "application/json": {
                    "example":  [
                        {
                            "Feature": "MD",
                            "Importance": 0.0034984895850544927,
                            "Model": "Lasso"
                        },
                        {
                            "Feature": "SQUEEGEE_SUM_COUNT",
                            "Importance": 0.0021025519339889272,
                            "Model": "Lasso"
                        },
                        {
                            "Feature": "TEMPRATURE",
                            "Importance": 0.0012649828408135516,
                            "Model": "Lasso"
                        },
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "Lasso回歸模型分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="lasso_for_parameter")
async def lasso_for_parameter(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variables": [
        'TEST_NUMBER',
        'solder_life',
        'PRINT_SPEED',
        'PRINT_PRESSURE',
        'SNAP_OFF_DISTANCE',
        'SNAP_OFF_SPEED',
        'TEMPRATURE',
        'HUMIDITY',
        'TD',
        'MD',
        'tension_mean',
        'tension_range',
        'SQUEEGEE_SUM_COUNT'
    ],
    "target": 'ooc',
    "iterations": 100,
    "use_redis": True
})):
    """Lasso回歸模型分析

    Args:
        request (dict): 包含以下參數:
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variables (list): 參數列表
            - target (str): 目標變數

    Returns:
        Dict: Lasso回歸模型分析結果，包含特徵重要性
    """
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variables = request.get("variables")
        target = request.get("target", "ooc")
        iterations = request.get("iterations", 100)
        use_redis = request.get("use_redis")
        # 檢查必要參數
        if not all([part_no, work_order, variables]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(
                status_code=400,
                detail="參數錯誤：缺少必要參數"
            )

        data = spi_data(
            part_no=part_no,
            work_order=work_order,
            use_redis=use_redis
        )

        if target == "ooc":
            # 計算每個工單的 ooc 比例
            data['ooc_rate'] = data.groupby('SERIAL_NUMBER')['ooc'].transform(
                lambda x: (x == 'ooc').mean()
            )
            y = data['ooc_rate']
        else:
            y = data[target]

        X = data[variables].copy()

        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce')

        # 標準化特徵
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        X_scaled = pd.DataFrame(X_scaled, columns=variables)

        # 執行Lasso回歸分析
        result = train_lasso_model(X_scaled, y, iterations=iterations)

        return result
    except Exception as e:
        logging.exception(f"Lasso回歸模型分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Lasso回歸模型分析失敗: {str(e)}")

# todo example需修改


@router.post(
    "/reliability_index",
    summary="可靠性指數評估",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 Random Forest 和 XGBoost 模型分析參數，並計算可靠性指數。
    此 API 會對提供的參數進行 Random Forest 和 XGBoost 模型分析，
    計算特徵重要性和模型性能指標。
    """,
    responses={
        200: {
            "description": "成功取得可靠性指數評估結果",
            "content": {
                "application/json": {
                    "example": {
                        "reliability_index": 0.85,
                        "common_features": ["MD", "TD", "PRINT_PRESSURE"],
                        "execution_time": 0.5
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：feature_importance_1 和 feature_importance_2 必須是列表"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：feature_importance_1 和 feature_importance_2 不能為空列表"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "可靠性指數評估失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="reliability_index_evaluation")
async def reliability_index_evaluation(request: dict = Body(..., example={
        "feature_importance_1": [
        {
            "Feature": "曝光能量_Back side",
            "Importance": 0.002367852013447445,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "曝光能量_Front side",
            "Importance": 0.0023654666302964232,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "Back Side點燈次數",
            "Importance": 0.0008296895751705763,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "Front Side點燈次數",
            "Importance": 0.0004587392184962263,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "Back Side點燈時數",
            "Importance": 0.000327830615240527,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "Front Side點燈時數",
            "Importance": 0.0003121690025879033,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "A1壓力",
            "Importance": 0,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "傳動目前速度",
            "Importance": -1.1955965250557594e-7,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "導電度計",
            "Importance": -1.6167840654546356e-7,
            "Model": "Logistic Regression"
        },
        {
            "Feature": "A2壓力",
            "Importance": -3.416039823335893e-7,
            "Model": "Logistic Regression"
        }
        ],
    "feature_importance_2": [
        {
            "Feature": "落塵量",
            "Importance": 0.21314646117124111,
            "Model": "Random Forest"
        },
        {
            "Feature": "Cycle time",
            "Importance": 0.20235188124424258,
            "Model": "Random Forest"
        },
        {
            "Feature": "Back Side點燈時數",
            "Importance": 0.190133838522791,
            "Model": "Random Forest"
        },
        {
            "Feature": "Front Side點燈時數",
            "Importance": 0.1895006865063263,
            "Model": "Random Forest"
        },
        {
            "Feature": "曝光能量_Back side",
            "Importance": 0.07402024270911538,
            "Model": "Random Forest"
        },
        {
            "Feature": "曝光能量_Front side",
            "Importance": 0.05864411390375448,
            "Model": "Random Forest"
        },
        {
            "Feature": "Back Side點燈次數",
            "Importance": 0.03588891884656557,
            "Model": "Random Forest"
        },
        {
            "Feature": "Front Side點燈次數",
            "Importance": 0.03579227725422967,
            "Model": "Random Forest"
        },
        {
            "Feature": "傳動目前速度",
            "Importance": 0.00003104157984365733,
            "Model": "Random Forest"
        },
        {
            "Feature": "鍍銅槽噴泵4實際流量",
            "Importance": 0.000029719825747539764,
            "Model": "Random Forest"
        }
        ],
    "use_redis": True
})):
    """可靠性指數評估

    Args:
        request (dict): 包含以下參數:
            - rf_feature_importance (List[Dict]): RandomForest模型的特徵重要性列表
            - xgb_feature_importance (List[Dict]): XGBoost模型的特徵重要性列表
            - top_n (int, optional): 考慮的頂部特徵數量，預設為5

    Returns:
        Dict: 可靠性指數評估結果，包含可靠性指數K值、共同特徵列表和執行時間統計
    """
    try:
        feature_importance_1 = request.get("feature_importance_1")
        feature_importance_2 = request.get("feature_importance_2")

        # 檢查必要參數
        if not all([feature_importance_1, feature_importance_2]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(
                status_code=400,
                detail="參數錯誤：缺少必要參數，需要 feature_importance_1 和 feature_importance_2"
            )

        if not isinstance(feature_importance_1, list) or not isinstance(feature_importance_2, list):
            logger.error(
                "參數錯誤：feature_importance_1 和 feature_importance_2 必須是列表")
            raise HTTPException(
                status_code=401,
                detail="參數錯誤：feature_importance_1 和 feature_importance_2 必須是列表"
            )

        if len(feature_importance_1) == 0 or len(feature_importance_2) == 0:
            logger.error(
                "參數錯誤：feature_importance_1 和 feature_importance_2 不能為空列表")
            raise HTTPException(
                status_code=402,
                detail="參數錯誤：feature_importance_1 和 feature_importance_2 不能為空列表"
            )

        # 模型名稱
        model1_name = feature_importance_1[0].get(
            "Model", "Model 1") if feature_importance_1 else "Model 1"
        model2_name = feature_importance_2[0].get(
            "Model", "Model 2") if feature_importance_2 else "Model 2"

        # 特徵信心指數
        reliability_k_value, common_features_df = comparison_algorithms(
            feature_importance_1, feature_importance_2)

        # 準備詳細的特徵分析資料
        # 首先提取出 importance_score 的中間結果以便獲得完整資訊
        importance_score_1 = importance_score(
            feature_importance_1).set_index('Feature')
        importance_score_2 = importance_score(
            feature_importance_2).set_index('Feature')

        # 合併所有特徵的資訊
        all_features = set(importance_score_1.index) | set(
            importance_score_2.index)
        feature_details = []

        for feature in all_features:
            detail = {"Feature": feature}

            # 模型1的數據
            if feature in importance_score_1.index:
                idx1 = importance_score_1.index.get_loc(feature)
                detail["Importance_Model1"] = next(
                    (item["Importance"] for item in feature_importance_1 if item["Feature"] == feature), 0)
                detail["Rank_Model1"] = idx1 + 1  # 轉換為1-based排名
                detail["Score_Model1"] = float(
                    importance_score_1.loc[feature, "score"])
                detail["FinalScore_Model1"] = float(
                    importance_score_1.loc[feature, "final_score"])
            else:
                detail["Importance_Model1"] = 0
                detail["Rank_Model1"] = None
                detail["Score_Model1"] = 0
                detail["FinalScore_Model1"] = 0

            # 模型2的數據
            if feature in importance_score_2.index:
                idx2 = importance_score_2.index.get_loc(feature)
                detail["Importance_Model2"] = next(
                    (item["Importance"] for item in feature_importance_2 if item["Feature"] == feature), 0)
                detail["Rank_Model2"] = idx2 + 1  # 轉換為1-based排名
                detail["Score_Model2"] = float(
                    importance_score_2.loc[feature, "score"])
                detail["FinalScore_Model2"] = float(
                    importance_score_2.loc[feature, "final_score"])
            else:
                detail["Importance_Model2"] = 0
                detail["Rank_Model2"] = None
                detail["Score_Model2"] = 0
                detail["FinalScore_Model2"] = 0

            # 計算總體信心分數
            if feature in common_features_df["Feature"].values:
                detail["IsCommonFeature"] = True
                detail["ConfidenceScore"] = (
                    detail["FinalScore_Model1"] + detail["FinalScore_Model2"]) / 2
            else:
                detail["IsCommonFeature"] = False
                detail["ConfidenceScore"] = 0

            feature_details.append(detail)

        # 按信心分數排序
        feature_details = sorted(
            feature_details, key=lambda x: x["ConfidenceScore"], reverse=True)

        # 整合所有結果
        combined_result = {
            "reliability_index": float(reliability_k_value),
            "common_features": common_features_df["Feature"].tolist(),
            "feature_analysis": feature_details,
            "model_names": {
                "model1": model1_name,
                "model2": model2_name
            }
        }
        return combined_result
    except Exception as e:
        logging.exception(f"可靠性指數評估失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"可靠性指數評估失敗: {str(e)}")


@router.post(
    "/mars_interaction_for_parameter",
    summary="MARS 交互作用分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 MARS (Multivariate Adaptive Regression Splines) 模型分析變量間的交互作用關係。
    此 API 會根據提供的數據，使用 MARS 模型找出影響目標變量的重要交互作用項。
    """,
    responses={
        200: {
            "description": "成功執行MARS交互作用分析",
            "content": {
                "application/json": {
                    "example":
                    [
                        {
                            "feature": "PRINT_SPEED*HUMIDITY",
                            "coefficient": 0.0324,
                            "importance": 1.0
                        },
                        {
                            "feature": "TD*MD",
                            "coefficient": 0.0286,
                            "importance": 0.88
                        },
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：目標變數不是數值型態且非二元分類"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：數據量不足"}
                }
            }
        },
        403: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：MARS分析未找到任何交互作用項"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "MARS互動項分析失敗: R套件錯誤或安裝問題"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="mars_interaction_for_parameter")
async def mars_interaction_for_parameter(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z05100063, Z05300052, Z05300156",
    "features": [
        "TEST_NUMBER",
        "solder_life",
        "PRINT_SPEED",
        "PRINT_PRESSURE",
        "SNAP_OFF_DISTANCE",
        "SNAP_OFF_SPEED",
        "TEMPRATURE",
        "HUMIDITY",
        "TD",
        "MD",
        "tension_mean",
        "tension_range",
        "SQUEEGEE_SUM_COUNT"
    ],
    "target": "ooc",
    "use_redis": True
})):
    """MARS交互作用分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str, optional): 資源類型，目前只支援 "SPI"/"PBC"，預設為 "SPI"
            - csv_path (str, optional): CSV檔案路徑(相對路徑)
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - features (list): 參數列表
            - target (str): 目標變數

    Returns:
        Dict: MARS交互作用分析結果，包含互動項
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        target = request.get("target", "ooc")
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            target = 'cluster'
            # 讀取csv檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, features, target]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

            if resource_type == "SPI":
                # 獲取數據
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        # 只使用指定的變量
        data = data[features + [target]].copy()

        # 轉換數據類型
        for col in data.columns:
            if col != target:
                data[col] = pd.to_numeric(data[col], errors='coerce')
                # 若 target 不是數值，嘗試自動轉換為 0/1
        if not pd.api.types.is_numeric_dtype(data[target]):
            unique_vals = data[target].dropna().unique()
            if len(unique_vals) == 2:
                # 二元分類自動轉換
                mapping = {unique_vals[0]: 0, unique_vals[1]: 1}
                data[target] = data[target].map(mapping)
            else:
                logger.error(
                    f"目標變數 {target} 不是數值型態且非二元分類，無法進行MARS分析")
                raise HTTPException(
                    status_code=401, detail=f"目標變數 {target} 不是數值型態且非二元分類，無法進行MARS分析")

        # 處理空值
        data = data.dropna()

        # 確保至少有一些數據
        if len(data) < 10:  # 設置最小數據量門檻
            logger.error("數據量不足，無法進行分析")
            raise HTTPException(status_code=402, detail="數據量不足，無法進行分析")

        # 執行 MARS 交互作用分析
        result = mars_interaction_analysis(
            data=data,
            target=target,
        )
        # 若結果為空，給出可能原因
        if isinstance(result, list) and len(result) == 0:
            possible_reason = (
                "MARS 分析未找到任何交互作用項。可能原因：\n"
                "1. 變數間無顯著交互作用\n"
                "2. 變數數量太少或資料變異性不足\n"
                "3. 資料高度共線或分布不適合產生交互項\n"
            )
            logger.error(possible_reason)
            raise HTTPException(
                status_code=403, detail=possible_reason)

        return result
    except Exception as e:
        logging.exception(f"MARS交互作用分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"MARS交互作用分析失敗: {str(e)}")


@router.post(
    "/decision_tree_interaction_for_parameter",
    summary="決策樹交互作用分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用決策樹模型分析變量間的交互作用關係。
    此 API 會根據提供的數據，使用決策樹模型找出影響目標變量的重要交互作用規則。
    如果目標變數是類別變數，將自動使用 'class' 方法；如果是連續變數，則使用 'anova' 方法。
    """,
    responses={
        200: {
            "description": "成功執行決策樹交互作用分析",
            "content": {
                "application/json": {
                    "example":
                    {
                        "interaction_paths": [
                            {
                                "Node": "4",
                                "RulePath": "HUMIDITY < 45 & PRINT_SPEED >= 80",
                                "Prediction": 0.025,
                                "Importance": 1.0
                            },
                            {
                                "Node": "10",
                                "RulePath": "HUMIDITY >= 45 & TD >= 15 & MD < 10",
                                "Prediction": 0.018,
                                "Importance": 0.72
                            }
                        ]
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：variables 必須是非空的列表"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：數據量不足"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "決策樹交互作用分析失敗: R套件錯誤或安裝問題"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="decision_tree_interaction_for_parameter")
async def decision_tree_interaction_for_parameter(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variables": [
        "TEST_NUMBER",
        "solder_life",
        "PRINT_SPEED",
        "PRINT_PRESSURE",
        "SNAP_OFF_DISTANCE",
        "SNAP_OFF_SPEED",
        "TEMPRATURE",
        "HUMIDITY",
        "TD",
        "MD",
        "tension_mean",
        "tension_range",
        "SQUEEGEE_SUM_COUNT"
    ],
    "target": "ooc_rate",
    "use_redis": True
})):
    """決策樹交互作用分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str, optional): 資源類型，目前只支援 SPI 與 PBC，預設為 SPI
            - csv_path (str, optional): CSV 檔案路徑 (相對路徑)
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variables (list): 參數列表
            - target (str): 目標變數
            - method (str, optional): 分裂方法，如果未提供則自動根據目標變數類型決定

    Returns:
        List[Dict]: 決策樹交互作用分析結果，包含規則路徑和重要性
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variables = request.get("variables")
        target = request.get("target", "ooc_rate")
        method = request.get("method")
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            target = 'cluster'
            # 讀取csv檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, variables, target]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

            if resource_type == "SPI":
                # 獲取數據
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
                # 處理目標變數：如果是 'ooc_rate' 且數據中有 'ooc' 欄位，則計算 ooc 率
                if target == "ooc_rate" and "ooc" in data.columns:
                    # 計算每個工單的 ooc 比例
                    data['ooc_rate'] = data.groupby('SERIAL_NUMBER')['ooc'].transform(
                        lambda x: (x.astype(str).str.lower() == 'ooc').mean()
                    )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        # 只使用指定的變量
        selected_cols = variables + [target]
        selected_cols = [col for col in selected_cols if col in data.columns]
        data = data[selected_cols].copy()

        # 轉換數據類型
        for col in data.columns:
            if col != target:
                data[col] = pd.to_numeric(data[col], errors='coerce')

        # 處理空值
        data = data.dropna()

        # 確保至少有一些數據
        if len(data) < 10:  # 設置最小數據量門檻
            logger.error("數據量不足，無法進行分析")
            raise HTTPException(status_code=401, detail="數據量不足，無法進行分析")

        # 自動決定使用的方法：如果目標變數有超過3個唯一值，視為連續變數使用 'anova'，否則視為類別變數使用 'class'
        if method is None:
            unique_values = data[target].nunique()
            is_numeric = pd.api.types.is_numeric_dtype(data[target])

            if target in ["ooc", "ooc_rate"] or not is_numeric:
                method = "class"
            elif unique_values <= 3:
                method = "class"
            else:
                method = "anova"

            logger.info(
                f"自動選擇決策樹方法: {method} (唯一值數: {unique_values}, 是數值類型: {is_numeric})")

        # 檢查方法參數
        if method not in ["anova", "class"]:
            logger.error("參數錯誤：method 必須為 'anova' 或 'class'")
            raise HTTPException(
                status_code=402, detail="參數錯誤：method 必須為 'anova' 或 'class'")

        # 執行決策樹交互作用分析
        result = decision_tree_interaction_analysis(
            data=data,
            target=target,
            method=method
        )

        return result

    except Exception as e:
        logging.exception(f"決策樹交互作用分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"決策樹交互作用分析失敗: {str(e)}")


@router.post(
    "/som_clustering",
    summary="使用 SOM 進行資料分群",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 SOM (Self-Organizing Map) 進行資料分群分析。
    根據指定的 part_no、work_order 與 features，進行 SOM 分群，並自動選擇最佳分群 threshold。
    回傳每個座標點的群集、資料數量、OOC比例，以及每筆資料的分群標籤。
    """,
    responses={
        200: {
            "description": "成功取得 SOM 分群結果",
            "content": {
                "application/json": {
                    "example": {
                        "threshold": 0.45,
                        "qe": 0.1234,
                        "num_clusters": 2,
                        "coord_info": {
                            "0_0": {"cluster": 1, "count": 10, "ooc_ratio": 0.2},
                            "0_1": {"cluster": 2, "count": 5, "ooc_ratio": 0.0}
                        },
                        "data": [
                            {"PRINT_SPEED": 100, "PRINT_PRESSURE": 1.2,
                                "TEMPRATURE": 25, "ooc": 1, "som_cluster": 1},
                            {"PRINT_SPEED": 110, "PRINT_PRESSURE": 1.1,
                                "TEMPRATURE": 26, "ooc": 0, "som_cluster": 2}
                        ]
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "SOM分群分析失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="som_clustering")
async def som_clustering(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "features": ["MD", "TD", "PRINT_PRESSURE", "HUMIDITY", "PRINT_SPEED", "TEMPRATURE"],
    "target": "ooc",
    "use_redis": True
})):
    """
    使用 SOM 對指定參數進行分群分析

    Args:
        request (dict): 包含 part_no、work_order、features 欄位
    Returns:
        Dict: 分群標籤與視覺化資訊
    """
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        target = request.get("target", "ooc")
        use_redis = request.get("use_redis")

        if not all([part_no, work_order, features, target]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="缺少必要參數")

        if not isinstance(features, list) or len(features) == 0:
            logger.error("參數錯誤：features 必須是非空的列表")
            raise HTTPException(status_code=401, detail="features 必須是非空的列表")

        data = spi_data(part_no=part_no,
                        work_order=work_order, use_redis=use_redis)

        # 只使用指定的變量
        data = data[features + [target]].copy()

        # ooc 欄位前處理：'ooc'->1, 'in_control'->0，如果已經是 0/1 則不處理
        if "ooc" in data.columns:
            unique_ooc = set(data["ooc"].dropna().unique())
            # 僅當不是純數字0/1時才進行轉換
            if not unique_ooc.issubset({0, 1}):
                data["ooc"] = data["ooc"].map(
                    lambda x: 1 if str(x).lower() == "ooc"
                    else (0 if str(x).lower() == "in_control"
                          else (1 if str(x) == "1" else (0 if str(x) == "0" else x)))
                )

        result = som_clustering_service(
            data_for_parameter=data,
            feature_cols=features,
            target=target,
            map_size=10,
            umatrix_thresholds=[0.4, 0.45, 0.5, 0.55, 0.6],
        )

        return result
    except Exception as e:
        logging.exception(f"SOM分群分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SOM分群分析失敗: {str(e)}")
