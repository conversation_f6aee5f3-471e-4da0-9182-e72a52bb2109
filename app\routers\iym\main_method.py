from fastapi import APIRouter, HTTPException, Body, Depends
from typing import List
from collections import defaultdict
import pandas as pd
from ...utils.database import execute_sql_query
import logging
from ...middlewares.api_key_auth import require_api_key
from ...services.data_service import spi_data, get_iym_data, pbc_data
from ...utils.redis_utils import RedisClient
from ...models.response import YieldResponse
from ...services.yield_analysis import yield_analysis as perform_yield_analysis
from ...services.step_process_level import step_process_level
from ...services.get_list_by_partno import get_list_by_partno as perform_get_list_by_partno
from ...utils.date_utils import is_valid_date

# 設定 logger
logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/yield",
    response_model=YieldResponse,
    dependencies=[Depends(require_api_key)],
    summary="良率分析",
    description="""
    根據指定的日期範圍和品目，計算良率。
    此 API 會從 sf_work_order_trace 表格中查詢資料，
    計算指定日期範圍內的良率。
    """,
    responses={
        200: {
            "description": "良率分析成功",
            "content": {
                "application/json": {
                    "example": {
                        "method": "yield",
                        "success": True,
                        "result": [
                            {
                                "不良大項代碼": "S03",
                                "defect_pcs": 1359,
                                "cumulative_count": 1359,
                                "cumulative_percentage": 86.78
                            },
                        ]
                    }
                }
            }
        },
        400: {
            "description": "參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "日期格式錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        500: {
            "description": "伺服器錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "伺服器錯誤：良率分析失敗"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="yield_analysis")
async def yield_analysis(request: dict = Body(..., example={
    "start_date": "2024-10-01",
    "end_date": "2024-11-01",
    "part_no": "QE2825",
    "use_redis": True
})):
    """良率分析

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目

    Returns:
        YieldResponse: 良率分析結果
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")

        # 檢查必要參數
        if not all([start_date, end_date, part_no]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")
            return cached_data

        result = perform_yield_analysis(start_date=start_date,
                                        end_date=end_date,
                                        part_no=part_no)

        return YieldResponse(method="yield", success=True, result=result)
    except Exception as e:
        logging.exception(f"良率分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# todo: 兩個模式回傳待統一


@router.post(
    "/get_defect_rate",
    summary="Identify Key Stations(根據製程/機台獲取不良率)",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的日期範圍、品目、不良項目和模式(by製程/by機台)，獲取平均不良率和數量。
    計算平均不良率和數量，並按不良率降序排列。
    """,
    responses={
        200: {
            "description": "成功獲取不良率",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "process_name": "(R212-S01)RTR VCP全局面鍍銅",
                            "mean_defect_rate": 2.35,
                            "count": 145
                        }
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "日期格式錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        402: {
            "description": "資料為空",
            "content": {
                "application/json": {
                    "example": {"detail": "資料為空"}
                }
            }
        },
        403: {
            "description": "查無指定製程相關欄位",
            "content": {
                "application/json": {
                    "example": {"detail": "查無指定製程相關欄位"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "獲取不良率失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_defect_rate_by_process")
async def get_defect_rate(request: dict = Body(..., example={
    "start_date": "2024-10-01",
    "end_date": "2024-11-01",
    "part_no": "QE2825",
    "defect_code": "S03",
    "process_name": "(R212-S01)RTR VCP全局面鍍銅",
    "mode": "process",
    "use_redis": True
})):
    """獲取各製程不良率 Identify Key Stations

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目
            - defect_code (str): 不良項目
            - process_name (str): 製程名稱
            - mode (str): 模式(process/machine)
            - use_redis (bool): 是否使用快取

    Returns:
        List[Dict]: 各製程的平均不良率和數量
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        defect_code = request.get("defect_code")
        process_name = request.get("process_name")
        mode = request.get("mode")
        # 檢查必要參數
        if not all([start_date, end_date, part_no, defect_code, process_name, mode]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        # 使用共用資料服務獲取資料，設置必要參數
        work_order_data, _, step_process_name_df, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix=f"獲取不良率，依據模式{mode}",
        )

        # 檢查資料是否為空
        if work_order_data.empty or wide_data.empty:
            logger.error("資料為空")
            raise HTTPException(status_code=402, detail="資料為空")

        # ----------------------
        # 模式一: 製程/步驟/線體
        # ----------------------
        if mode == "process":
            result_list = []

            for _, row in step_process_name_df.iterrows():
                step = row['step']
                process_name_str = row['process_name']
                line_column = f"{step}-線體"

                if line_column not in wide_data.columns:
                    continue

                grouped = wide_data.groupby(line_column).agg({
                    'defect_rate': ['mean', 'count']
                }).reset_index()

                grouped.columns = ['line', 'mean_defect_rate', 'count']
                grouped['process_name'] = process_name_str
                grouped['step'] = step

                for _, line_row in grouped.iterrows():
                    result_list.append({
                        'process_name': line_row['process_name'],
                        'step': line_row['step'],
                        'line': line_row['line'],
                        'mean_defect_rate': float(line_row['mean_defect_rate']),
                        'count': int(line_row['count'])
                    })

            return sorted(result_list, key=lambda x: x['mean_defect_rate'], reverse=True)

        # ----------------------
        # 模式二: 製程機台
        # ----------------------
        elif mode == "machine":
            # 動態欄位名稱處理
            categorical_columns = [
                col for col in wide_data.columns if col.endswith("線體")]

            def get_step(col):
                try:
                    return int(col.split('-')[0])
                except:
                    return 9999
            categorical_columns = sorted(categorical_columns, key=get_step)

            step_to_process = dict(zip(step_process_name_df['step'].astype(
                str), step_process_name_df['process_name']))
            process_name_count = defaultdict(int)
            process_name_seen = defaultdict(int)

            for col in categorical_columns:
                step = col.split('-')[0]
                pname = step_to_process.get(step, step)
                process_name_count[pname] += 1

            line_mapping = {}
            for col in categorical_columns:
                step = col.split('-')[0]
                pname = step_to_process.get(step, step)
                process_name_seen[pname] += 1
                if process_name_count[pname] == 1:
                    new_name = pname
                else:
                    idx = process_name_seen[pname]
                    new_name = f"{pname}-{idx}"
                line_mapping[col] = new_name

            wide_data = wide_data.rename(columns=line_mapping)

            # 尋找指定製程所有欄位
            process_columns = [col for col in wide_data.columns if col ==
                               process_name or col.startswith(f"{process_name}-")]
            if not process_columns:
                raise HTTPException(
                    status_code=403, detail=f"查無指定製程相關欄位: {process_name}")

            # 轉長表做 groupby
            melted = pd.melt(wide_data, id_vars=['work_order', 'defect_rate'], value_vars=process_columns,
                             var_name='process_column', value_name='machine').dropna(subset=['machine'])

            group = melted.groupby('machine')['defect_rate']
            mean_defect_rate = group.mean().sort_values(ascending=False)
            count_defect_rate = group.count().reindex(mean_defect_rate.index)

            stat = []
            for machine, mean_val, count_val in zip(mean_defect_rate.index, mean_defect_rate.values, count_defect_rate.values):
                stat.append({
                    "machine": machine,
                    "mean_defect_rate": float(mean_val),
                    "count": int(count_val)
                })

            return {
                "process_name": process_name,
                "stat": stat
            }

        else:
            raise HTTPException(
                status_code=40, detail="不支援的 mode 參數，請使用 'process' 或 'machine'")

    except Exception as e:
        logging.exception(f"Defect Rate 統計失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Defect Rate 統計失敗: {str(e)}")


@router.post(
    "/process_name_list",
    response_model=List[str],
    dependencies=[Depends(require_api_key)],
    summary="取得製程名稱清單",
    description="""
    根據指定的日期範圍和品目代碼，查詢對應的製程名稱清單。
    此 API 會從 sf_work_order_trace 表格中查詢符合條件的製程名稱。
    可選擇性地指定工單號碼清單來進一步篩選結果。
    """,
    responses={
        200: {
            "description": "成功取得製程名稱清單",
            "content": {
                "application/json": {
                    "example": [
                        "(L211-06)LPSM 微蝕",
                        "(L260-00)LPSM 清潔",
                        "(L260-03)LPSM 預烤"
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "日期格式錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢製程名稱清單失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_process_name_list")
async def get_process_name_list(request: dict = Body(..., example={
    "start_date": "2024-10-01",
    "end_date": "2024-11-01",
    "part_no": "QE2825",
    "workorders": ["H04921909F", "H04918937A"],
    "use_redis": True
})):
    """取得製程名稱清單

    Args:
        request (dict): 包含以下參數:
            - start_date (str): 開始日期，格式為 YYYY-MM-DD
            - end_date (str): 結束日期，格式為 YYYY-MM-DD
            - part_no (str): 品目代碼
            - workorders (list, optional): 工單號碼清單，用於進一步篩選結果

    Returns:
        List[str]: 製程名稱清單
    """
    try:
        start_date = request.get("start_date")
        end_date = request.get("end_date")
        part_no = request.get("part_no")
        workorders = request.get("workorders")

        # 檢查必要參數
        if not all([start_date, end_date, part_no]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if not is_valid_date(start_date) or not is_valid_date(end_date):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        # 利用 SQL 查詢製程名稱清單
        sql = f"""
        SELECT DISTINCT PROCESS_NAME
        FROM sf_work_order_trace
        WHERE PART_NO LIKE '{part_no}%%'
            AND IN_PDLINE_TIME >= '{start_date}'
            AND IN_PDLINE_TIME <= '{end_date} 23:59:59.999'
        """

        # 如果有指定工單號碼清單，則加入 workorder 篩選條件
        if workorders and len(workorders) > 0:
            workorder_values = "', '".join(workorders)
            sql += f" AND workorder IN ('{workorder_values}')"

        sql += " ORDER BY PROCESS_NAME"

        # print(f"get_process_name_list.sql: {sql}")

        # 執行查詢
        results = execute_sql_query(sql, as_dict=True, db="KSTIDB")

        # 提取 PROCESS_NAME 欄位並轉換為列表
        process_names = [row['PROCESS_NAME'] for row in results]

        return process_names

    except Exception as e:
        logging.exception(f"查詢製程名稱清單失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢製程名稱清單失敗: {str(e)}")


@router.post(
    "/variables_list",
    response_model=List[str],
    dependencies=[Depends(require_api_key)],
    summary="取得變數清單",
    description="""
    根據指定的品目代碼和工單號碼，取得變數清單。
    """,
    responses={
        200: {
            "description": "成功取得變數清單",
            "content": {
                "application/json": {
                    "example": [
                        "prog_name",
                        "work_order",
                        "SERIAL_NUMBER",
                        "SN"
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢 SPI 變數清單失敗: 資料處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_variables_list")
async def get_variables_list(
    request: dict = Body(..., example={
        "resource_type": "SPI",
        "part_no": "ZE18113-A",
        "work_order": "Z05300156, Z05100063, Z05300052",
        "use_redis": True
    })
):
    try:
        """取得離散變數清單

        Args:
            request (dict): 包含以下參數:
                - resource_type (str): 資源類型(SPI/PBC)，預設為SPI
                - part_no (str): 品目代碼
                - work_order (str): 工單號碼

        Returns:
            List[str]: 離散變數清單
        """
        resource_type = request.get("resource_type", "SPI")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        use_redis = request.get("use_redis")

        # 檢查參數
        if not all([part_no, work_order]):
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        if resource_type == "SPI":
            data = spi_data(
                part_no=part_no,
                work_order=work_order,
                use_redis=use_redis
            )
        elif resource_type == "PBC":
            # todo: PBC方法待實作
            print("PBC方法待實作")

        column_list = data.columns.tolist()

        return column_list

    except Exception as e:
        logging.exception(f"查詢 SPI 變數清單失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢 SPI 變數清單失敗: {str(e)}")


@router.post(
    "/lot_list_by_partno",
    response_model=List[str],
    dependencies=[Depends(require_api_key)],
    summary="取得 LOT 清單",
    description="""
    根據指定的品目代碼，查詢對應的 LOT 清單。
    此 API 會從 t_flex_smt_param_uploadvalue 表格中查詢符合條件的工單號碼。
    可選擇性地指定日期範圍進行篩選。
    """,
    responses={
        200: {
            "description": "成功取得 LOT 清單",
            "content": {
                "application/json": {
                    "example": ["Z05300156", "Z05100063", "Z05300052"]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "日期格式錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢 LOT 清單失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_lot_list_by_partno")
async def get_lot_list_by_partno(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "start_date": "2025-01-01",
    "end_date": "2025-03-31",
    "use_redis": True
})):
    """取得 LOT 清單

    Args:
        request (dict): 包含以下參數:
            - part_no (str): 品目代碼
            - start_date (str, optional): 開始日期，格式為 YYYY-MM-DD
            - end_date (str, optional): 結束日期，格式為 YYYY-MM-DD

    Returns:
        List[str]: LOT 清單
    """
    try:
        part_no = request.get("part_no")
        start_date = request.get("start_date")
        end_date = request.get("end_date")

        # 檢查必要參數
        if not part_no:
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        # 檢查日期格式
        if (start_date and not is_valid_date(start_date)) or (end_date and not is_valid_date(end_date)):
            logger.error("日期格式錯誤：請使用 YYYY-MM-DD 格式")
            raise HTTPException(
                status_code=401, detail="日期格式錯誤：請使用 YYYY-MM-DD 格式")

        # 基本 SQL 查詢
        sql = f"""
        SELECT DISTINCT work_order
        FROM t_flex_smt_param_uploadvalue
        WHERE PROG_NAME = '{part_no}'
        """

        # 如果有提供日期範圍，則加入日期條件(Annie表示起訖對StartTime即可，EndTime只差幾分鐘)
        if start_date and end_date:
            sql += f" AND DATE_FORMAT(start_time, '%Y-%m-%d') BETWEEN '{start_date}' AND '{end_date}'"

        sql += " ORDER BY work_order"

        # 執行查詢
        results = execute_sql_query(sql, as_dict=True, db="KSTIDB")

        # 提取 work_order 欄位並轉換為列表
        lot_list = [row['work_order'] for row in results]

        return lot_list

    except Exception as e:
        logging.exception(f"查詢 LOT 清單失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢 LOT 清單失敗: {str(e)}")


@router.post(
    "/get_list_by_partno",
    response_model=dict,
    dependencies=[Depends(require_api_key)],
    summary="取得工單號清單、製程+機台清單",
    description="""
    根據指定的品目代碼，查詢對應的工單號清單、製程+機台清單。
    """,
    responses={
        200: {
            "description": "成功取得工單號清單、製程+機台清單",
            "content": {
                "application/json": {
                    "example": {
                        "work_order": [
                            "H05514730",
                            "H05514731",
                            "H05515606",
                            "H05515607"
                        ],
                        "process_machine": {
                            "22-(R220-S01)RTR乾膜壓合": [
                                "(R22I04)RTR 乾膜壓合機-04",
                                "(R22I01)RTR 乾膜壓合機-01",
                                "(R22I05)RTR 乾膜壓合機-05"
                            ],
                            "25-(R223-S01)RTR曝光": [
                                "(R22K01)RTR 雙面平行露光機-01-A",
                                "(R22K02)RTR 雙面平行露光機-01-B",
                                "(R22K05)RTR 雙面平行露光機-03-A",
                                "(R22K07)RTR 雙面平行露光機-04-A",
                                "(R22K08)RTR 雙面平行露光機-04-B"
                            ],
                        }
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "日期格式錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "日期格式錯誤：請使用 YYYY-MM-DD 格式"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢工單號清單、製程+機台清單失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_list_by_partno")
async def get_list_by_partno(request: dict = Body(..., example={
    "resource_type": "PBC",
    "part_no": "QD2001",
    "date_range": ["2025-01-01", "2025-03-31"],
    "use_redis": True
})):
    """取得工單號清單、製程+機台清單

    Args:
        request (dict): 包含以下參數:
            - resource_type (str): 資源類型(SPI/PBC)，預設為PBC
            - part_no (str): 品目代碼
            - date_range (List[str]): 日期範圍，格式為 ['start_date', 'end_date']

    Returns:
        Dict: 工單號清單、製程+機台清單
    """
    try:
        resource_type = request.get("resource_type", "PBC")
        part_no = request.get("part_no")
        date_range = request.get("date_range")
        use_redis = request.get("use_redis")

        # 檢查必要參數
        if not part_no:
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        if resource_type == "SPI":
            data = spi_data(part_no=part_no, use_redis=use_redis)
        elif resource_type == "PBC":
            data, _ = pbc_data(
                part_no=part_no, date_range=date_range, use_redis=use_redis)

        return perform_get_list_by_partno(resource_type, data, part_no)

    except Exception as e:
        logging.exception(f"查詢工單號清單、製程+機台清單失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"查詢工單號清單、製程+機台清單失敗: {str(e)}")


@router.post(
    "/step_process_level",
    response_model=List[dict],
    dependencies=[Depends(require_api_key)],
    summary="取得各製程的水準",
    description="""
    取得各製程的水準(即變數數量)，例如機台數量、人員數量。
    """,
    responses={
        200: {
            "description": "成功取得製程水準清單",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "step_process": "22-(R220-S01)RTR乾膜壓合",
                            "machine_levels": 3,
                            "emp_no_levels": 5,
                        },
                        {
                            "step_process": "25-(R223-S01)RTR曝光",
                            "machine_levels": 5,
                            "emp_no_levels": 6,
                        }
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢製程水準失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_step_process_level")
async def get_step_process_level(request: dict = Body(..., example={
    "resource_type": "PBC",
    "part_no": "QD2001",
    "use_redis": True
})):
    """取得製程水準

    Args:
        request (dict): 包含以下參數:
            - resource_type (str): 資源類型(SPI/PBC)，預設為PBC
            - part_no (str): 品目代碼
            - date_range (List[str]): 日期範圍，格式為 ['start_date', 'end_date']

    Returns:
        List[Dict]: 製程水準清單
    """
    try:
        resource_type = request.get("resource_type", "PBC")
        part_no = request.get("part_no")
        date_range = request.get("date_range")
        use_redis = request.get("use_redis")

        # 檢查必要參數
        if not part_no:
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        if resource_type == "SPI":
            data = spi_data(part_no=part_no, use_redis=use_redis)
        elif resource_type == "PBC":
            data, _ = pbc_data(
                part_no=part_no, date_range=date_range, use_redis=use_redis)

        return step_process_level(data)

    except Exception as e:
        logging.exception(f"查詢製程水準失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢製程水準失敗: {str(e)}")


@router.post(
    "/defect_count_order",
    response_model=dict,
    dependencies=[Depends(require_api_key)],
    summary="取得不良的數量排名",
    description="""
    取得品目指定工單中所有製程的不良數量，並將同不良代碼整併後按數量降序排列。
    """,
    responses={
        200: {
            "description": "成功取得不良數量排名清單",
            "content": {
                "application/json": {
                    "example": {
                        "S03AC": 9803,
                        "MIN_LINE": 6340,
                        "NICK": 6099,
                        "SHORT": 4605,
                        "S04AC": 3573,
                        "PROTRUSION": 3181,
                        "OPEN": 2777,
                        "S04AB": 2590,
                    }
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "查詢不良數量排名失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="get_defect_count_order")
async def get_defect_count_order(request: dict = Body(..., example={
    "resource_type": "PBC",
    "part_no": "QD2001",
    "date_range": ["2025-01-01", "2025-03-31"],
    "use_redis": True
})):
    """取得不良數量排名

    Args:
        request (dict): 包含以下參數:
            - resource_type (str): 資源類型(SPI/PBC)，預設為PBC
            - part_no (str): 品目代碼

    Returns:
        List[Dict]: 不良數量排名清單
    """
    try:
        resource_type = request.get("resource_type", "PBC")
        part_no = request.get("part_no")
        date_range = request.get("date_range")
        use_redis = request.get("use_redis")
        # 檢查必要參數
        if not part_no:
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        if resource_type == "SPI":
            data = spi_data(part_no=part_no, use_redis=use_redis)
        elif resource_type == "PBC":
            _, data = pbc_data(
                part_no=part_no, date_range=date_range, use_redis=use_redis)

        # 取得所有不良代碼欄位（排除 workorder, panel_no, total_defect_pcs, total_pcs, panel_count, defect_rate 等統計欄位）
        exclude_cols = ['workorder', 'panel_no', 'total_defect_pcs',
                        'total_pcs', 'panel_count', 'defect_rate']
        defect_code_cols = [
            col for col in data.columns if col not in exclude_cols]

        # 計算每個不良代碼的總數量
        defect_code_counts = data[defect_code_cols].sum(
        ).sort_values(ascending=False)

        return defect_code_counts.to_dict()

    except Exception as e:
        logging.exception(f"查詢不良數量排名失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢不良數量排名失敗: {str(e)}")
