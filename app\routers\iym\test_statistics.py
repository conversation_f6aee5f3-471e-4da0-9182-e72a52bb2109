from scipy.stats import ttest_ind, levene
from fastapi import APIRouter, Body, HTTPException, Depends
import logging
from app.services.kde_plot_data import generate_kde_for_groups
from ...services.data_service import spi_data
import pandas as pd
import numpy as np
import os
from ...utils.csv_reader import read_local_csv
from ...utils.redis_utils import RedisClient
from ...middlewares.api_key_auth import require_api_key
from ...utils.date_utils import is_valid_date
from ...utils.redis_utils import RedisClient
from ...services.kruskal_wallis_test import perform_ks_test_analysis
from ...services.kde_plot_data import generate_kde_for_groups
from scipy.stats import ttest_1samp, binom_test, ttest_ind, chi2_contingency

router = APIRouter()

logger = logging.getLogger(__name__)


@router.post(
    "/chi_square",
    summary="卡方檢定分析",
    dependencies=[Depends(require_api_key)],
    description="""
    根據指定的品目、工單和所選欄位，進行卡方檢定分析。   
    此 API 會從各資料庫表格中查詢資料，
    並進行卡方檢定分析，找出與不良率相關的因素。
    """,
    responses={
        200: {
            "description": "成功執行卡方檢定分析",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "feature": "FEEDER_NO",
                            "p": 1,
                            "dof": 0,
                            "significant_count": 0,
                            "significant_rate": 0
                        }
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：columns 必須是非空的列表"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "卡方檢定分析失敗: 資料庫查詢錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="chi_square")
async def chi_square(request: dict = Body(..., example={
    "resource_type": "SPI",
    "part_no": "ZE18113-A",
    "work_order": "Z05300156, Z05100063, Z05300052",
    "features": [
        "FEEDER_NO",
        "REEL_NO",
        "SQUEEGEE_NO",
        "PD_SN",
        "CD_SN",
        "PRINT_DIRECTION",
    ],
    "iterations": 100,
    "use_redis": True
})):
    """卡方檢定分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str): 資源類型(SPI/PBC)，預設為SPI
            - part_no (str): 品目
            - work_order (str): 工單
            - features (list): 要分析的欄位列表
            - iterations (int): 重抽樣次數
    Returns:
        List[Dict]: 卡方檢定分析結果
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        part_no = request.get("part_no", "")
        work_order = request.get("work_order", "")
        features = request.get("features", [])
        target = request.get("target", "ooc")
        iterations = request.get("iterations", 100)
        use_redis = request.get("use_redis")
        # 檢查必要參數
        if not all([part_no, work_order, features]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")

        if not isinstance(features, list) or len(features) == 0:
            logger.error("參數錯誤：features 必須是非空的列表")
            raise HTTPException(
                status_code=401, detail="參數錯誤：features 必須是非空的列表")

        if resource_type == "SPI":
            data = spi_data(
                part_no=part_no,
                work_order=work_order,
                use_redis=use_redis
            )
        elif resource_type == "PBC":
            # todo: PBC方法待實作
            print("PBC方法待實作")

        results = []
        significance_counts = {col: 0 for col in features}  # 記錄顯著次數

        for col in features:
            contingency_table = pd.crosstab(data[col], data[target])
            chi2, p, dof, expected = chi2_contingency(contingency_table)
            results.append({
                'feature': col,
                'chi2': chi2,
                'p': p,
                'dof': dof
            })
            # todo: 這邊的ooc要改成寫成活的
            # 進行 iterations 次重抽樣測試
            ooc_size = data[data[target] == 'ooc'].shape[0]
            in_control_data = data[data[target] ==
                                   'in_control']  # 取出 in_control 的資料

            if ooc_size > 0 and len(in_control_data) >= ooc_size:
                for _ in range(iterations):
                    sampled_in_control = in_control_data.sample(
                        n=ooc_size, replace=False, random_state=np.random.randint(10000))
                    sampled_data = pd.concat(
                        [sampled_in_control, data[data[target] == 'ooc']], axis=0)
                    sample_table = pd.crosstab(
                        sampled_data[col], sampled_data[target])

                    if sample_table.shape[1] == 2:  # 確保有足夠的分類
                        chi2_sample, p_sample, _, _ = chi2_contingency(
                            sample_table)
                        if p_sample < 0.05:
                            significance_counts[col] += 1

        results_df = pd.DataFrame(results)
        results_df.sort_values(by='chi2', ascending=False, inplace=True)

        significance_df = pd.DataFrame.from_dict(
            significance_counts, orient='index', columns=['significant_count'])
        significance_df.reset_index(inplace=True)
        significance_df.rename(columns={'index': 'feature'}, inplace=True)

        final_results = results_df.merge(
            significance_df, on='feature', how='left')

        significant_df = final_results[[
            'feature', 'p', 'dof', 'significant_count']]
        significant_df['significant_rate'] = significant_df['significant_count'] / iterations
        significant_df = significant_df.sort_values(
            by='significant_rate', ascending=False)

        result = significant_df.to_dict(orient="records")

        return result
    except Exception as e:
        logging.exception(f"卡方檢定分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"卡方檢定分析失敗: {str(e)}")


@router.post(
    "/kruskal_wallis_test_analysis",
    summary="特徵顯著性H檢定分析",
    dependencies=[Depends(require_api_key)],
    description="""
    使用 Kruskal-Wallis 檢定對特徵進行多次下採樣顯著性分析。    
    此 API 會對提供的特徵進行多次隨機下採樣和 Kruskal-Wallis 檢定，
    計算每個特徵在多次迭代中顯著的比例，以評估特徵的統計顯著性。
    """,
    responses={
        200: {
            "description": "成功執行H檢定分析",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "feature": "solder_life",
                            "significant_count": 100,
                            "significant_rate": 1.0
                        },
                        {
                            "feature": "HUMIDITY",
                            "significant_count": 100,
                            "significant_rate": 1.0
                        },
                        {
                            "feature": "tension_range",
                            "significant_count": 100,
                            "significant_rate": 1.0
                        },
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：iterations 必須是正整數"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：p_threshold 必須在 0 和 1 之間"}
                }
            }
        },
        403: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：數據中不存在目標變數"}
                }
            }
        },
        404: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：以下特徵不在數據中"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "H檢定(Kruskal-Wallis Test)分析失敗: 處理錯誤"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="kruskal_wallis_test_analysis")
async def kruskal_wallis_test_analysis(request: dict = Body(..., example={
    "resource_type": "PBC",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "features": [
        "MD",
        "TD",
        "PRINT_PRESSURE",
        "HUMIDITY",
        "PRINT_SPEED",
        "TEMPRATURE",
        "solder_life"
    ],
    "target": "ooc",
    "iterations": 100,
    "p_threshold": 0.05,
    "use_redis": True
})):
    """H檢定特徵顯著性分析

    Args:
        request (dict): 包含以下參數:
            - resource_type (str): 資源類型，預設為 "SPI"
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - csv_path (str): CSV 檔案路徑 (相對路徑)
            - features (list): 要分析的特徵列表
            - target (str): 目標變數
            - iteration (int): 隨機下採樣的次數
            - p_threshold (float): 顯著性閾值

    Returns:
        Dict: H檢定分析結果，包含顯著特徵列表和顯著性統計
    """
    try:
        resource_type = request.get("resource_type", "SPI")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        csv_path = request.get("csv_path")
        features = request.get("features")
        target = request.get("target", "cluster")
        iterations = request.get("iterations", 100)
        p_threshold = request.get("p_threshold", 0.05)
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)
            # 讀取 CSV 檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, features, target, p_threshold]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
            if resource_type == "SPI":
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        # 檢查迭代次數和顯著性閾值
        if not isinstance(iterations, int) or iterations <= 0:
            logger.error("參數錯誤：iterations 必須是正整數")
            raise HTTPException(
                status_code=401, detail="參數錯誤：iterations 必須是正整數")

        if not isinstance(p_threshold, (int, float)) or p_threshold <= 0 or p_threshold >= 1:
            logger.error("參數錯誤：p_threshold 必須在 0 和 1 之間")
            raise HTTPException(
                status_code=402, detail="參數錯誤：p_threshold 必須在 0 和 1 之間")

        # 確保數據中有 target 列
        if target not in data.columns:
            logger.error(f"數據中不存在目標變數 '{target}'")
            raise HTTPException(
                status_code=403, detail=f"數據中不存在目標變數 '{target}'")

        # 檢查特徵是否都在數據中
        missing_features = [
            feature for feature in features if feature not in data.columns]
        if missing_features:
            logger.error(f"以下特徵不在數據中: {', '.join(missing_features)}")
            raise HTTPException(status_code=404,
                                detail=f"以下特徵不在數據中: {', '.join(missing_features)}")

        # 執行H檢定分析
        result = perform_ks_test_analysis(
            data=data,
            features=features,
            target=target,
            iterations=iterations,
            p_threshold=p_threshold
        )

        return result
    except Exception as e:
        logging.exception(f"H檢定(Kruskal-Wallis Test)分析失敗: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"H檢定(Kruskal-Wallis Test)分析失敗: {str(e)}")


@router.post(
    "/kde_analysis_for_features",
    summary="KDE密度圖數據分析",
    dependencies=[Depends(require_api_key)],
    responses={
        200: {
            "description": "成功取得KDE密度圖數據",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "feature": "PRINT_PRESSURE",
                            "normal": {
                                "x": [0.1, 0.2, 0.3, 0.4],
                                "y": [0.0, 0.5, 1.0, 0.5]
                            },
                            "defect": {
                                "x": [0.1, 0.2, 0.3, 0.4],
                                "y": [0.0, 0.3, 0.8, 0.3]
                            }
                        },
                        {
                            "feature": "solder_life",
                            "normal": {
                                "x": [10, 20, 30, 40],
                                "y": [0.0, 0.2, 0.7, 0.2]
                            },
                            "defect": {
                                "x": [10, 20, 30, 40],
                                "y": [0.0, 0.1, 0.5, 0.1]
                            }
                        }
                    ]
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：features必須是非空的列表"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "以下特徵不在數據中"}
                }
            }
        },
        403: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "數據中不存在目標變數"}
                }
            }
        },
        404: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "數據量不足，無法進行分析"}
                }
            }
        },
        405: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "Normal group標籤 '{normal_group}' 不在目標變數中"}
                }
            }
        },
        406: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "Defect group標籤 '{defect_group}' 不在目標變數中"}
                }
            }
        },
        500: {
            "description": "伺服器內部錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "KDE數據讀取失敗"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="kde_analysis_for_features")
async def kde_analysis_for_features(request: dict = Body(..., example={
    "resource_type": "SPI",
    "csv_path": "data/parameter_data_QE2825.csv",
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "features": [
        "PRINT_PRESSURE",
        "solder_life",
        "MD",
        "PRINT_SPEED",
        "HUMIDITY",
        "tension_range",
        "TD",
        "tension_mean",
        "SQUEEGEE_SUM_COUNT"
    ],
    "target": "ooc",
    "normal_group": "in_control",
    "defect_group": "ooc",
    "use_redis": True
}
)
):
    try:
        resource_type = request.get("resource_type", "SPI")
        csv_path = request.get("csv_path")
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        features = request.get("features")
        target = request.get("target", "ooc")
        normal_group = request.get("normal_group", "in_control")
        defect_group = request.get("defect_group", "ooc")
        use_redis = request.get("use_redis")

        if csv_path:
            # 構建相對路徑
            base_dir = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.dirname(__file__))))
            full_csv_path = os.path.join(base_dir, csv_path)

            # 讀取 CSV 檔案
            data = read_local_csv(full_csv_path)
        else:
            # 檢查必要參數
            if not all([part_no, work_order, features, target, normal_group, defect_group]):
                logger.error("參數錯誤：缺少必要參數")
                raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
            if resource_type == "SPI":
                data = spi_data(
                    part_no=part_no,
                    work_order=work_order,
                    use_redis=use_redis
                )
            elif resource_type == "PBC":
                # todo PBC抓資料方式待補
                print("PBC抓資料方式待補")

        # 只使用指定的變量
        data = data[features + [target]].copy()

        # 必要參數檢查
        if not isinstance(features, list) or len(features) == 0:
            logger.error("參數錯誤：features必須是非空的列表")
            raise HTTPException(
                status_code=401, detail="參數錯誤：features必須是非空的列表")

        # 檢查特徵欄位存在性
        missing_features = [f for f in features if f not in data.columns]
        if missing_features:
            logger.error(f"以下特徵不在數據中: {', '.join(missing_features)}")
            raise HTTPException(
                status_code=402, detail=f"以下特徵不在數據中: {', '.join(missing_features)}")
        if target not in data.columns:
            logger.error(f"數據中不存在目標變數 '{target}'")
            raise HTTPException(
                status_code=403, detail=f"數據中不存在目標變數 '{target}'")

        # 轉型與清洗
        for col in features:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        data = data.dropna(subset=features + [target])
        if len(data) < 5:
            logger.error("數據量不足，無法進行分析")
            raise HTTPException(status_code=404, detail="數據量不足，無法進行分析")

        # 強制 group label 型態與 target 欄位一致
        target_dtype = data[target].dtype
        try:
            normal_group = target_dtype.type(normal_group)
            defect_group = target_dtype.type(defect_group)
        except Exception:
            # 若轉型失敗，保留原值
            pass

        unique_groups = data[target].unique()
        if normal_group not in unique_groups:
            logger.error(f"Normal group標籤 '{normal_group}' 不在目標變數中")
            raise HTTPException(
                status_code=405, detail=f"Normal group標籤 '{normal_group}' 不在目標變數中")
        if defect_group not in unique_groups:
            logger.error(f"Defect group標籤 '{defect_group}' 不在目標變數中")
            raise HTTPException(
                status_code=406, detail=f"Defect group標籤 '{defect_group}' 不在目標變數中")

        kde_result = generate_kde_for_groups(
            data=data,
            target_col=target,
            normal_label=normal_group,
            defect_label=defect_group,
            features=features
        )

        return kde_result
    except Exception as e:
        logging.exception(f"KDE分析錯誤: {str(e)}")
        raise HTTPException(status_code=500, detail=f"KDE數據讀取失敗: {str(e)}")


# 新增單一樣本檢定 平均值/比例------------------------------------------
@router.post(
    "/one_sample_ttest",
    summary="單一樣本平均數t檢定",
    dependencies=[Depends(require_api_key)],
    description="""
    對單一樣本進行平均數t檢定，檢查樣本平均是否與假設值有顯著差異。
    """,
    responses={
        200:  {
            "description": "檢定結果",
            "content": {
                "application/json": {
                    "example": {"p_value": 0.0012, "conclusion": "與假設的平均值有顯著差異 (p < 0.05)"}
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "資料中無此欄位"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "資料量不足，無法進行t檢定"}
                }
            }
        },
        500: {
            "description": "伺服器錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "t檢定失敗"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="one_sample_ttest")
async def one_sample_ttest(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541",
    "variable": "PRINT_PRESSURE",
    "assume_mean": 9.413,
    "use_redis": True
})):
    """
    Args:
        request (dict): 包含以下參數:
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variable (str): 欲檢定的欄位
            - assume_mean (float): 假設母體平均數
    Returns:
        dict: p值、結論
    """
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variable = request.get("variable")
        popmean = request.get("assume_mean")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, variable, popmean]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        if variable not in data.columns:
            logger.error(f"資料中無此欄位: {variable}")
            raise HTTPException(status_code=401, detail=f"資料中無此欄位: {variable}")
        values = pd.to_numeric(data[variable], errors="coerce").dropna()
        if len(values) < 2:
            logger.error("資料量不足，無法進行t檢定")
            raise HTTPException(status_code=402, detail="資料量不足，無法進行t檢定")
        _, p_value = ttest_1samp(values, popmean)
        conclusion = "與假設的平均值有顯著差異 (p < 0.05)" if p_value < 0.05 else "與假設的平均值無顯著差異 (p >= 0.05)"
        return {"p_value": p_value, "conclusion": conclusion}
    except Exception as e:
        logging.exception(f"t檢定失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"t檢定失敗: {str(e)}")


@router.post(
    "/one_sample_proportion_test",
    summary="單一樣本比例檢定",
    dependencies=[Depends(require_api_key)],
    description="""
    對單一樣本進行比例檢定，檢查樣本比例是否與假設比例有顯著差異。
    """,
    responses={
        200: {
            "description": "檢定結果",
            "content": {
                "application/json": {
                    "example": {"p_value": 0.0012, "conclusion": "與假設的平均值有顯著差異 (p < 0.05)"}
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "資料中無此欄位"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "success 必須介於 0 與 n 之間"}
                }
            }
        },
        403: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "proportion 必須介於 0 與 1 之間"}
                }
            }
        },
        500: {
            "description": "伺服器錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "比例檢定失敗"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="one_sample_proportion_test")
async def one_sample_proportion_test(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541",
    "target_variable": "ooc",
    "prportion_value": "ooc",
    "assume_proportion": 0.1,
    "alternative": "two-sided",
    "use_redis": True
})):
    """
    Args:
        request (dict): 包含以下參數:
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - target_variable (str): 欲檢定的欄位（如 ooc）
            - prportion_value: 欲計算為比例的值（如 "ooc"）
            - assume_proportion (float): 假設比例
            - alternative (str): 'two-sided', 'greater', 'less'
    Returns:
        dict: p值
    """
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variable = request.get("target_variable")
        success_value = request.get("prportion_value")
        prop = request.get("assume_proportion")
        alternative = request.get("alternative", "two-sided")
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, variable, success_value, prop, alternative]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="參數錯誤：缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        if variable not in data.columns:
            logger.error(f"資料中無此欄位: {variable}")
            raise HTTPException(status_code=401, detail=f"資料中無此欄位: {variable}")
        n = data[variable].notna().sum()
        success = (data[variable] == success_value).sum()
        if not (0 <= success <= n):
            logger.error("success 必須介於 0 與 n 之間")
            raise HTTPException(
                status_code=402, detail="success 必須介於 0 與 n 之間")
        if not (0 < prop < 1):
            logger.error("proportion 必須介於 0 與 1 之間")
            raise HTTPException(
                status_code=403, detail="proportion 必須介於 0 與 1 之間")
        p_value = binom_test(success, n, prop, alternative=alternative)
        conclusion = "顯著差異 (p < 0.05)" if p_value < 0.05 else "無顯著差異 (p >= 0.05)"
        return {"p_value": p_value, "conclusion": conclusion}
    except Exception as e:
        logging.exception(f"比例檢定失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"比例檢定失敗: {str(e)}")


# 兩獨立樣本T檢定-----------------------------------------


@router.post(
    "/two_sample_ttest",
    summary="兩樣本平均數t檢定",
    dependencies=[Depends(require_api_key)],
    description="""
    對兩組獨立樣本進行平均數t檢定，檢查兩組樣本平均是否有顯著差異，並檢定變異數同質性，若有差異則回傳信賴區間。
    """,
    responses={
        200: {
            "description": "檢定結果",
            "content": {
                "application/json": {
                    "example": {"p_value": 0.04244590584171029,
                                "conclusion": "兩組平均數有顯著差異 (p < 0.05)",
                                "levene_p_value": 4.95262111428294e-7,
                                "variance_conclusion": "兩組變異數不同質 (p < 0.05)",
                                "mean_diff": -0.027099344510014944,
                                "confidence_interval": [
                                    -0.05327501560142764,
                                    -0.0009236734186022465
                                ]}
                }
            }
        },
        400: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "參數錯誤：缺少必要參數"}
                }
            }
        },
        401: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "資料中無指定欄位"}
                }
            }
        },
        402: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "兩組資料量皆需大於1"}
                }
            }
        },
        403: {
            "description": "請求參數錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "兩組變異數不同質 (p < 0.05)"}
                }
            }
        },
        500: {
            "description": "伺服器錯誤",
            "content": {
                "application/json": {
                    "example": {"detail": "兩樣本t檢定失敗"}
                }
            }
        }
    }
)
@RedisClient.cached_body_api(expire=86400, cache_key_prefix="two_sample_ttest")
async def two_sample_ttest(request: dict = Body(..., example={
    "part_no": "ZE18113-A",
    "work_order": "Z04C00541, Z05100062, Z05100063, Z05300052, Z05300156",
    "variable": "PRINT_PRESSURE",
    "group_variable": "ooc",
    "group1_value": "in_control",
    "group2_value": "ooc",
    "equal_var": True,
    "use_redis": True
})):
    """
    Args:
        request (dict): 包含以下參數:
            - part_no (str): 品目
            - work_order (str): 工單號碼
            - variable (str): 欲檢定的欄位
            - group_variable (str): 分組欄位
            - group1_value: 第一組的分組值
            - group2_value: 第二組的分組值
            - equal_var (bool): 兩組變異數是否相等（預設True）
    Returns:
        dict: p值、結論、變異數同質性檢定、信賴區間（如有差異）
    """
    try:
        part_no = request.get("part_no")
        work_order = request.get("work_order")
        variable = request.get("variable")
        group_variable = request.get("group_variable")
        group1_value = request.get("group1_value")
        group2_value = request.get("group2_value")
        equal_var = request.get("equal_var", True)
        use_redis = request.get("use_redis")
        if not all([part_no, work_order, variable, group_variable, group1_value, group2_value, equal_var]):
            logger.error("參數錯誤：缺少必要參數")
            raise HTTPException(status_code=400, detail="缺少必要參數")
        data = spi_data(part_no=part_no, work_order=work_order,
                        use_redis=use_redis)
        if variable not in data.columns or group_variable not in data.columns:
            logger.error("資料中無指定欄位")
            raise HTTPException(status_code=401, detail="資料中無指定欄位")
        group1 = pd.to_numeric(
            data[data[group_variable] == group1_value][variable], errors="coerce").dropna()
        group2 = pd.to_numeric(
            data[data[group_variable] == group2_value][variable], errors="coerce").dropna()
        if len(group1) < 2 or len(group2) < 2:
            logger.error("兩組資料量皆需大於1")
            raise HTTPException(status_code=402, detail="兩組資料量皆需大於1")
        # 變異數同質性檢定
        levene_stat, levene_p = levene(group1, group2)
        var_conclusion = "兩組變異數同質 (p >= 0.05)" if levene_p >= 0.05 else "兩組變異數不同質 (p < 0.05)"
        stat, p_value = ttest_ind(group1, group2, equal_var=equal_var)
        conclusion = "兩組平均數有顯著差異 (p < 0.05)" if p_value < 0.05 else "兩組平均數無顯著差異 (p >= 0.05)"
        result = {
            "p_value": p_value,
            "conclusion": conclusion,
            "levene_p_value": levene_p,
            "variance_conclusion": var_conclusion
        }
        # 若有顯著差異，計算信賴區間（95%）
        if p_value < 0.05:
            import scipy.stats as stats
            mean_diff = group1.mean() - group2.mean()
            n1, n2 = len(group1), len(group2)
            s1, s2 = group1.std(ddof=1), group2.std(ddof=1)
            if equal_var:
                pooled_var = (((n1 - 1) * s1 ** 2) +
                              ((n2 - 1) * s2 ** 2)) / (n1 + n2 - 2)
                se = (pooled_var * (1 / n1 + 1 / n2)) ** 0.5
                df = n1 + n2 - 2
            else:
                se = (s1 ** 2 / n1 + s2 ** 2 / n2) ** 0.5
                df = (s1 ** 2 / n1 + s2 ** 2 / n2) ** 2 / (
                    ((s1 ** 2 / n1) ** 2) / (n1 - 1) +
                    ((s2 ** 2 / n2) ** 2) / (n2 - 1)
                )
            t_crit = stats.t.ppf(0.975, df)
            ci_low = mean_diff - t_crit * se
            ci_high = mean_diff + t_crit * se
            result["mean_diff"] = mean_diff
            result["confidence_interval"] = [ci_low, ci_high]
        return result
    except Exception as e:
        logging.exception(f"兩樣本t檢定失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"兩樣本t檢定失敗: {str(e)}")
