from fastapi import APIRouter, HTTPException
from ...models.request import CorrelationAnalysisRequest
from ...models.response import CorrelationAnalysisResponse, ErrorResponse
from ...services.correlation import perform_correlation_analysis

router = APIRouter()


@router.post("/correlation", response_model=CorrelationAnalysisResponse)
async def correlation_analysis(request: CorrelationAnalysisRequest):
    try:
        x_values = request.data.get("x", [])
        y_values = request.data.get("y", [])
        if not x_values or not y_values:
            raise HTTPException(status_code=400, detail="缺少必要的數據：x 或 y")
        result = perform_correlation_analysis(x_values, y_values)
        return {
            "method": "correlation_analysis",
            "success": True,
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
