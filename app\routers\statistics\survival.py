from fastapi import APIRouter, HTTPException
from ...models.request import SurvivalAnalysisRequest
from ...models.response import SurvivalAnalysisResponse, ErrorResponse
from ...services.survival import perform_survival_analysis

router = APIRouter(tags=["存活分析"])


@router.post("/survival", response_model=SurvivalAnalysisResponse)
async def survival_analysis(request: SurvivalAnalysisRequest):
    try:
        times = request.data.get("times", [])
        events = request.data.get("events", [])
        groups = request.data.get("groups", [])
        group_names = request.data.get("group_names", [])
        if not times or not events:
            raise HTTPException(
                status_code=400, detail="缺少必要的數據：times 或 events")
        result = perform_survival_analysis(times, events, groups, group_names)
        return {
            "method": "survival_analysis",
            "success": True,
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
