import pandas as pd
import logging

logger = logging.getLogger(__name__)


def calculate_correlation_matrix(data: pd.DataFrame) -> dict:
    """
    計算相關係數矩陣

    Args:
        data (pd.DataFrame): 欲計算相關係數的資料，僅包含數值型特徵欄位
        method (str): 相關係數計算方法，可選 "pearson", "spearman", "kendall"

    Returns:
        dict: 相關係數矩陣（巢狀字典格式，適合直接回傳 API）
    """
    corr = data.corr().fillna(0)
    # 轉成巢狀 dict，方便 FastAPI 回傳
    corr_dict = corr.to_dict()
    return corr_dict
