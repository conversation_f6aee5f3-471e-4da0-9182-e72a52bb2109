import pandas as pd
import numpy as np
import re
import json
from functools import reduce
from ..utils.database import execute_sql_query
from ..utils.redis_utils import RedisClient
from ..utils.date_utils import is_valid_date
from typing import Dict, List, Any, Optional, Tuple
import logging
import time
from fastapi import HTTPException
from sklearn.model_selection import train_test_split
from ..utils.date_utils import format_execution_time
from ..utils.ml_utils import calculate_control_limits, remove_outliers, target_encode
from .data_service_config import ColumnConfig, AnalysisConfig

logger = logging.getLogger(__name__)


# def get_data(
#     resource_type: str,
#     start_date: str,
#     end_date: str,
#     part_no: str,
#     work_order: Optional[str] = None,
#     process_name: Optional[str] = None,
#     defect_code: Optional[str] = None,
# ) -> Any:
#     """
#     之後的理想寫法，泛用的資料獲取函式，目前先不採用，等之後處理好統一的輸入輸出格式再套用
#     """
#     try:
#         # 檢查輸入參數
#         if not all([part_no, resource_type]):
#             raise TypeError("get_data 參數不齊全")

#         # 檢查輸入參數的格式
#         if not is_valid_date(start_date) or not is_valid_date(end_date):
#             raise ValueError("日期格式錯誤，應為YYYY-MM-DD")

#         # 處理工單列表
#         if work_order:
#             work_order_list = "','".join(sorted(wo.strip()
#                                                 for wo in work_order.split(",")))
#         else:
#             work_order_list = None

#         # 根據resource_type獲取資料
#         resource_type = resource_type.upper()
#         if resource_type == "PBC":
#             return pbc_data(part_no=part_no, work_order=work_order_list, specify_defect_code=defect_code)
#         elif resource_type == "SPI":
#             return spi_data(part_no, work_order_list)
#         else:
#             raise ValueError(f"未知的 resource_type: {resource_type}")
#     except Exception as e:
#         logger.error(f"get_data 發生錯誤: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"get_data 發生錯誤: {str(e)}")


"""
待改:
在main_method的get_defect_rate中有
在machine_learning的rf、rf_shap、xgboost、xgboost_shap中有使用
"""


def get_iym_data(
    start_date: str,
    end_date: str,
    part_no: str,
    process_name: Optional[str] = None,
    defect_code: Optional[str] = None,
    log_prefix: str = "資料獲取",
    pivot_columns: Optional[List[str]] = None,
    specific_steps: Optional[List[str]] = None
) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    獲取IYM分析所需的資料

    Args:
        start_date (str): 開始日期 (YYYY-MM-DD)
        end_date (str): 結束日期 (YYYY-MM-DD)
        part_no (str): 品目
        process_name (Optional[str]): 製程名稱，多個製程用逗號分隔
        defect_code (Optional[str]): 不良項目代碼
        log_prefix (str): 日誌前綴，用於識別不同分析的日誌
        pivot_columns (Optional[List[str]]): 指定要轉換的欄位列表
        specific_steps (Optional[List[str]]): 指定要保留的步驟列表

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
            (工單資料, 不良資料, 製程與步驟對應表, 寬表資料)
    """
    try:
        # 處理製程名稱列表
        process_name_filter = ""
        if process_name:
            process_name_list = "','".join(
                pn.strip() for pn in process_name.split(","))
            process_name_filter = f"AND process_name in ('{process_name_list}')"

        # 取得工單主表資料
        work_order_sql = f"""
        SELECT
            a.*,
            materials.material_no AS 材料,
            materials.material_batch AS 材料批號
        FROM
            (SELECT
                id,
                workorder AS work_order,
                process_name,
                pdline_name AS 線體,
                step,
                in_pdline_time,
                out_pdline_time,
                emp_no AS 人員工號,
                job_title AS 人員職稱,
                materials
            FROM sf_work_order_trace
            WHERE
                part_no LIKE '{part_no}%%'
                AND date_format(IN_PDLINE_TIME, '%Y-%m-%d') BETWEEN '{start_date}' AND '{end_date}'
                {process_name_filter}
            ORDER BY
                WORKORDER,
                STEP
            ) AS a
        LEFT JOIN
            (SELECT
                trace_id,
                material_no,
                material_batch,
                use_time,
                production_date,
                shipping_date,
                validity_period,
                retest_result,
                origin,
                recovery_time,
                recovery_times,
                usage_time,
                takeout_time,
                usage_count
            FROM sf_work_order_materials
            ) AS materials
        ON a.id = materials.trace_id
        """
        logger.debug(f'{log_prefix} - 工單查詢 SQL: {work_order_sql}')

        # 執行查詢
        work_order_data = pd.DataFrame(execute_sql_query(
            work_order_sql, as_dict=True, db="KHSTARROCKS"))
        logger.info(f'{log_prefix} - 工單資料筆數: {len(work_order_data)}')

        if work_order_data.empty:
            logger.warning(f'{log_prefix} - 無工單資料')
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        # 獲取唯一工單列表
        work_order_list = "','".join(np.unique(work_order_data['work_order']))

        # 取得不良資料
        defect_code_filter = ""
        if defect_code:
            defect_code_filter = f"AND 不良大項代碼 in ('{defect_code}')"

        defect_sql = f"""
        SELECT
            工單號碼 as work_order,
            round(sum(不良PCS數)/max(檢查PCS數)*100, 2) as defect_rate
        FROM qms_station_defect_detail
        WHERE
            工單號碼 in ('{work_order_list}')
            AND 大站名稱 in ('AOI 工程','RTR 線檢 AOI')
            AND 站點名稱 != 'AOI 完工站'
            {defect_code_filter}
        GROUP BY 工單號碼 {', 不良大項代碼' if defect_code else ''}
        """
        logger.debug(f'{log_prefix} - 不良資料查詢 SQL: {defect_sql}')

        # 執行查詢
        defect_data = pd.DataFrame(execute_sql_query(
            defect_sql, as_dict=True, db="QMSDB"))
        logger.info(f'{log_prefix} - 不良資料筆數: {len(defect_data)}')

        if defect_data.empty:
            logger.warning(f'{log_prefix} - 無不良資料')
            return work_order_data, pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        # 建立step和process_name的關聯set
        step_process_name_df = work_order_data.groupby(
            'step')['process_name'].apply(lambda x: set(x)).reset_index()
        step_process_name_df['process_name'] = step_process_name_df['process_name'].apply(
            lambda x: list(x)[0])

        # 去重複
        work_order_data = work_order_data.drop_duplicates(
            subset=['work_order', 'step'])
        logger.info(f'{log_prefix} - 去重複後共幾筆資料: {len(work_order_data)}')

        # 檢查是否有足夠的資料進行透視
        if len(work_order_data) == 0:
            logger.warning(f'{log_prefix} - 工單資料筆數為0，無法進行透視')
            return work_order_data, defect_data, step_process_name_df, pd.DataFrame()

        # 實例化AnalysisConfig Class
        analysis_config = AnalysisConfig()

        # 抓取CONFIG的預設轉換欄位
        pivot_columns = analysis_config.default_pivot_columns
        logger.debug(f'{log_prefix} - pivot_columns: {pivot_columns}')

        # 轉成寬表
        wide_data = pd.pivot_table(
            work_order_data,
            index='work_order',
            columns='step',
            values=pivot_columns,
            aggfunc='first'
        )
        wide_data = work_order_data.pivot(index='work_order', columns='step',
                                          values=pivot_columns)
        logger.info(f'{log_prefix} - 轉成寬表共幾筆資料: {len(wide_data)}')

        # 展平多層索引列
        wide_data.columns = [f"{col[1]}-{col[0]}" for col in wide_data.columns]
        wide_data = wide_data.reset_index()

        # 如果指定了特定步驟，只保留這些步驟的資料
        if specific_steps:
            # 獲取所有符合指定步驟的列
            selected_columns = ['work_order']  # 保留 work_order 列
            selected_columns.extend([col for col in wide_data.columns if any(
                f"{step}" in col for step in specific_steps)])
            # 取除重複
            selected_columns = list(set(selected_columns))
            logger.debug(
                f'{log_prefix} - 選取的欄位selected_columns: {selected_columns}')
            wide_data = wide_data[selected_columns]

        # 合併不良資料
        wide_data = pd.merge(wide_data, defect_data, on=[
                             'work_order'], how='left')
        wide_data['defect_rate'] = wide_data['defect_rate'].fillna(0)

        return work_order_data, defect_data, step_process_name_df, wide_data

    except Exception as e:
        logger.error(f"{log_prefix} - 資料獲取失敗: {str(e)}")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


# 暫時無用，先保留


def get_iym_machine_parameter(
    start_date: str,
    end_date: str,
    part_no: str,
    machine_name: str
) -> pd.DataFrame:
    """
    取得IYM機台參數資料

    Args:
        start_date (str): 開始日期 (YYYY-MM-DD)
        end_date (str): 結束日期 (YYYY-MM-DD)
        part_no (str): 品目
        machine_name (str): 線體/機台名稱

    Returns:
        pd.DataFrame: 查詢結果
    """
    try:
        sql = f"""
        WITH param_data AS (
            SELECT
                d.work_order,
                d.panel_no,
                d.in_pdline_time,
                d.out_pdline_time,
                d.pdline_name,
                d.parameter_item AS TagName,
                d.parameter_name
            FROM T_PANEL_RESUME_DETAIL d
            JOIN sf_work_order_trace t
                ON d.work_order = t.workorder
                AND d.in_pdline_time = t.IN_PDLINE_TIME
            WHERE t.part_no LIKE '{part_no}%'
                AND t.IN_PDLINE_TIME BETWEEN '{start_date}' AND '{end_date}'
                AND d.pdline_name = '{machine_name}'
        ),
        runtime_avg AS (
            SELECT
                h.TagName,
                h.datetime,
                h.value
            FROM T_FLEX_RUNTIME_HISTORY h
            WHERE h.datetime BETWEEN '{start_date}' AND '{end_date}'
        )
        SELECT
            p.work_order,
            p.panel_no,
            p.in_pdline_time,
            p.out_pdline_time,
            p.pdline_name,
            p.TagName,
            p.parameter_name,
            AVG(h.value) AS avg_value
        FROM param_data p
        JOIN runtime_avg h
            ON h.TagName = p.TagName
            AND h.datetime BETWEEN p.in_pdline_time AND p.out_pdline_time
        GROUP BY
            p.work_order,
            p.panel_no,
            p.in_pdline_time,
            p.out_pdline_time,
            p.pdline_name,
            p.TagName,
            p.parameter_name
        """
        logger.debug(f'get_iym_machine_parameter SQL: {sql}')
        df = pd.DataFrame(execute_sql_query(
            sql, as_dict=True, db="KHSTARROCKS"))
        logger.debug("查詢結果欄位:", df.columns)
        logger.debug("查詢結果預覽:", df.head())
        if df.empty:
            logger.warning("get_iym_machine_parameter - 查詢結果為空")
            return pd.DataFrame()
        variables_df = df[['work_order', 'panel_no',
                           'parameter_name', 'avg_value']]
        pivot_variables_df = variables_df.pivot(
            index=['work_order', 'panel_no'],
            columns='parameter_name',
            values='avg_value'
        ).reset_index()
        pivot_variables_df.fillna(0, inplace=True)
        logger.debug("pivot_variables_df.columns:",
                     pivot_variables_df.columns)

        panel_list = pivot_variables_df['panel_no'].unique().tolist()
        logger.debug("panel_list:", panel_list)
        # 查詢每個 panel_no 的不良數
        if panel_list:
            # 組成 SQL IN 條件（確保每個 panel 被單引號包起來）
            panel_in = ",".join(f"'{p}'" for p in panel_list)

            defect_sql = f"""
            SELECT
                panel_no,
                SUM(pcs_no) AS defect_count
            FROM sf_work_order_panel_defect
            WHERE panel_no IN ({panel_in})
            GROUP BY panel_no
            """

            # 執行 SQL 並轉為 DataFrame
            defect_df = pd.DataFrame(
                execute_sql_query(defect_sql, as_dict=True, db="KHSTARROCKS")
            )
        else:
            defect_df = pd.DataFrame(columns=['panel_no', 'defect_count'])

        logger.debug(defect_df.head())
        # 合併不良數，查無資料則為 0
        machine_variable_result_df = pd.merge(
            pivot_variables_df,
            defect_df,
            how='left',
            on='panel_no'
        )
        machine_variable_result_df['defect_count'] = machine_variable_result_df['defect_count'].fillna(
            0).astype(int)

        return machine_variable_result_df
    except Exception as e:
        logger.error(f"get_iym_machine_parameter - 查詢失敗: {str(e)}")
        return pd.DataFrame()

# 暫時無用，先保留


def prepare_ml_data(
    start_date: str,
    end_date: str,
    part_no: str,
    process_name: str,
    defect_code: str,
    log_prefix: str = "機器學習資料準備"
) -> Tuple[pd.DataFrame, Dict[str, Any], Dict[str, str]]:
    """
    準備機器學習分析所需的資料

    Args:
        start_date (str): 開始日期 (YYYY-MM-DD)
        end_date (str): 結束日期 (YYYY-MM-DD)
        part_no (str): 品目
        process_name (str): 製程名稱
        defect_code (str): 不良項目代碼
        log_prefix (str): 日誌前綴

    Returns:
        Tuple[pd.DataFrame, Dict[str, Any], Dict[str, str]]:
            (處理後的資料, 訓練資料字典, 執行時間統計)
    """
    execution_times = {}
    total_start_time = time.time()

    try:
        # 準備數據===========================================
        data_start_time = time.time()

        # 目標變數
        TARGET_COLUMN = "defect_rate"

        # 實例化ColumnConfig Class
        column_config = ColumnConfig()

        # 根據part_no選擇配置
        if "QE2825" in part_no:
            required_columns = column_config.qe2825_columns
            name_mappings = column_config.qe2825_mapping
        elif "QN1366" in part_no:
            required_columns = column_config.qn1366_columns
            name_mappings = column_config.qn1366_mapping

        # 獲取資料
        _, _, _, wide_data = get_iym_data(
            start_date=start_date,
            end_date=end_date,
            part_no=part_no,
            process_name=process_name,
            defect_code=defect_code,
            log_prefix="隨機森林分析",
            pivot_columns=['線體'],
            specific_steps=required_columns
        )

        # 記錄資料獲取耗時
        execution_times['data_fetch'] = time.time() - data_start_time
        logger.info(
            f'{log_prefix} - 資料獲取耗時: {execution_times["data_fetch"]:.2f} 秒')

        # 資料預處理===========================================
        preprocess_start_time = time.time()

        wide_data['category'] = wide_data['defect_rate'].apply(
            lambda x: 'Defect' if x > np.median(wide_data['defect_rate']) else 'Normal')
        wide_data.dropna(inplace=True)

        # 檢查資料是否為空
        if wide_data.empty:
            logger.error('wide_data is empty')
            raise HTTPException(status_code=400, detail="無法獲取足夠的數據進行分析")

        # 檢查所需列是否存在
        missing_columns = [
            col for col in required_columns if col not in wide_data.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"缺少必要的列: {', '.join(missing_columns)}"
            )

        # 選擇所需列
        analysis_data = wide_data[['work_order'] +
                                  required_columns + ['defect_rate']].copy()
        logger.debug(
            f'{log_prefix} - analysis_data shape: {analysis_data.shape}')

        # 重命名欄位
        rename_dict = {'work_order': 'workorder'}
        rename_dict.update(name_mappings)
        analysis_data.rename(columns=rename_dict, inplace=True)

        # 記錄資料預處理耗時
        execution_times['preprocess'] = time.time() - preprocess_start_time
        logger.info(
            f'{log_prefix} - 資料預處理耗時: {execution_times["preprocess"]:.2f} 秒')

        # 獲取RTR相關列
        feature_start_time = time.time()
        rtr_columns = [col for col in analysis_data.columns if "RTR" in col]
        execution_times['feature_extraction'] = time.time() - \
            feature_start_time
        logger.info(
            f'{log_prefix} - 獲取RTR相關列耗時: {execution_times["feature_extraction"]:.2f} 秒')

        # 目標編碼：將類別特徵轉換為數值，用於模型訓練。
        df_encoded, _ = target_encode(
            analysis_data, TARGET_COLUMN, rtr_columns, smooth=0)
        # encoding_dict: 目標編碼平均值的映射字典。

        # 分割訓練集與測試集
        split_start_time = time.time()
        X_train, X_test, y_train, y_test = train_test_split(
            df_encoded[rtr_columns],
            df_encoded[TARGET_COLUMN],
            test_size=0.2,
            random_state=42
        )
        execution_times['data_split'] = time.time() - split_start_time
        logger.info(
            f'{log_prefix} - 資料分割耗時: {execution_times["data_split"]:.2f} 秒')

        # 計算總執行時間
        execution_times['total'] = time.time() - total_start_time
        logger.info(f'{log_prefix} - 總執行時間: {execution_times["total"]:.2f} 秒')

        # 格式化執行時間
        formatted_execution_times = {
            step: format_execution_time(duration)
            for step, duration in execution_times.items()
        }

        # 記錄執行時間
        for step, duration in formatted_execution_times.items():
            logger.info(f'{log_prefix} - {step} 執行時間: {duration}')

        # 準備返回的訓練資料字典
        train_data = {
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'feature_columns': rtr_columns
        }

        return analysis_data, train_data, formatted_execution_times

    except Exception as e:
        logger.error(f"{log_prefix} - 資料準備失敗: {str(e)}")
        raise

# 空板資料獲取函式


def pbc_data(
    part_no: str,
    work_order: Optional[str] = None,
    specify_defect_code: Optional[List[str]] = None,
    date_range: Optional[List[str]] = None,
    use_redis: bool = True,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    獲取空板資料 (PBC - Printed Board Circuit)

    Args:
        part_no (str): 品目編號
        work_order (str): 工單號碼，多個工單用逗號分隔
        specify_defect_code (List[str]): 指定不良項目代碼，字串陣列
        date_range (List[str]): 日期範圍，格式為 ['start_date', 'end_date']
        use_redis (bool): 是否使用 Redis 快取

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: 處理後的空板資料
        (workorder_process_defect_table, workorder_panel_defect_table)
        workorder_process_defect_table: 工單製程不良資料
        workorder_panel_defect_table: 工單面板不良資料
    """
    try:
        if use_redis:
            # 建立 Redis 快取鍵，包含所有參數
            cache_key = f"pbc_data_{part_no}_{work_order}_{specify_defect_code}"
            redis_client = RedisClient(cache_key)

            # 嘗試從 Redis 獲取快取資料
            cached_data = redis_client.get()
            if cached_data:
                try:
                    # 解析快取的 JSON 資料
                    cached_dict = json.loads(cached_data)

                    # 將 JSON 資料轉換回 DataFrame
                    workorder_process_defect_table = pd.read_json(
                        cached_dict['workorder_process_defect_table'])
                    workorder_panel_defect_table = pd.read_json(
                        cached_dict['workorder_panel_defect_table'])

                    logger.info(f'pbc_data - 從 Redis 快取載入資料成功')
                    return workorder_process_defect_table, workorder_panel_defect_table
                except Exception as e:
                    logger.warning(
                        f'pbc_data - Redis 快取資料解析失敗: {str(e)}，重新查詢資料庫')

        # 查詢工單追蹤資料
        if date_range:
            date_range_str = f"AND A.in_pdline_time BETWEEN '{date_range[0]}' AND '{date_range[1]}'"
        else:
            date_range_str = ""

        sql = f"""
        SELECT DISTINCT
            A.workorder,
            A.part_no,
            A.in_pdline_time,
            A.out_pdline_time,
            A.process_name,
            A.machine_desc,
            A.emp_no,
            A.emp_name,
            A.JOB_title,
            A.step
        FROM
            sf_work_order_trace A,
            sf_work_order_trace B
        WHERE
            A.part_no LIKE '{part_no}%'
            AND A.workorder = B.workorder
            AND B.workorder LIKE 'H%'
            AND B.process_name LIKE '%240%'
            {date_range_str}
        ORDER BY
            A.workorder,
            A.step
        """
        logger.debug(f'pbc_data SQL: {sql}')

        # 執行查詢
        iym_df_table = pd.DataFrame(execute_sql_query(
            sql, as_dict=True, db="KHSTARROCKS"))
        logger.debug(f'pbc_data shape: {iym_df_table.shape}')

        if iym_df_table.empty:
            logger.warning("pbc_data - 查詢結果為空")
            return pd.DataFrame(), pd.DataFrame()

        # 建立 step_process 欄位
        iym_df_table['step_process'] = iym_df_table['step'].astype(
            str) + '-' + iym_df_table['process_name']

        # 找出 RTR 盲檢和 RTR 線檢的索引
        blind_check_mask = iym_df_table['step_process'].str.contains(
            'RTR 盲檢', na=False)
        line_check_mask = iym_df_table['step_process'].str.contains(
            'RTR 線檢', na=False)

        if not blind_check_mask.any() or not line_check_mask.any():
            logger.warning("pbc_data - 找不到 RTR 盲檢或 RTR 線檢步驟")
            return pd.DataFrame(), pd.DataFrame()

        start_idx = iym_df_table[blind_check_mask].index[0]
        end_idx = iym_df_table[line_check_mask].index[0]

        # 取得中間的製程步驟
        process = iym_df_table.loc[start_idx +
                                   1:end_idx-1, 'step_process'].unique()
        iym_df_table = iym_df_table[iym_df_table['step_process'].isin(process)]

        # 取得機台列表和工單列表
        machine_list = iym_df_table['machine_desc'].unique()
        iym_df_table_workorder = iym_df_table['workorder'].unique()

        # 找出拆單母工單
        mask = iym_df_table['workorder'].str.contains(r'[a-zA-Z]$', na=False)
        result = iym_df_table.loc[mask, 'workorder'].unique()
        split_workorder_mother = [
            re.sub(r'[a-zA-Z]$', '', wo) for wo in result]
        split_workorder_mother = pd.Series(
            split_workorder_mother).drop_duplicates().tolist()

        # 定義要進行透視的欄位
        pivot_fields = {
            'machine_desc': 'machine',
            'emp_no': 'emp_no',
            'emp_name': 'emp_name',
            'JOB_title': 'JOB_title',
            'in_pdline_time': 'in_pdline_time',
            'out_pdline_time': 'out_pdline_time'
        }

        # 建立透視表功能函式
        def create_pivot_table(df, fields):
            """建立透視表"""
            pivot_tables = {}
            for col, prefix in fields.items():
                pivot = df.pivot_table(
                    index=['workorder'],
                    columns='step_process',
                    values=col,
                    aggfunc='first'
                )
                pivot.columns = [f"{prefix}_{c}" for c in pivot.columns]
                pivot_tables[prefix] = pivot.reset_index()

            # 合併所有透視表
            result = reduce(
                lambda left, right: pd.merge(
                    left, right, on='workorder', how='outer'),
                [pivot_tables[prefix] for prefix in fields.values()]
            )
            result.columns.name = None  # 移除列名
            return result

        # 執行透視表處理
        if not split_workorder_mother:
            # 沒有拆工單，直接做透視
            iym_df_table_pivot = create_pivot_table(iym_df_table, pivot_fields)
        else:
            # 有拆工單，需要處理母工單資料
            # 建立工單字串
            if len(split_workorder_mother) == 1:
                workorder_str = f"('{split_workorder_mother[0]}')"
            else:
                workorder_str = str(tuple(split_workorder_mother))

            # 查詢母工單資料
            sql = f"""
            SELECT DISTINCT
                A.workorder,
                A.part_no,
                A.in_pdline_time,
                A.out_pdline_time,
                A.process_name,
                A.machine_desc,
                A.emp_no,
                A.emp_name,
                A.JOB_title,
                A.step
            FROM sf_work_order_trace A
            WHERE A.workorder IN {workorder_str}
            """
            logger.debug(f'母工單查詢 SQL: {sql}')

            split_workorder_mother_table = pd.DataFrame(
                execute_sql_query(sql, as_dict=True, db="KHSTARROCKS")
            )
            split_workorder_mother_table['step_process'] = (
                split_workorder_mother_table['step'].astype(str) +
                '-' + split_workorder_mother_table['process_name']
            )
            split_workorder_mother_table = split_workorder_mother_table[
                split_workorder_mother_table['step_process'].isin(process)
            ]

            # 建立工單透視表
            iym_df_table_pivot = create_pivot_table(iym_df_table, pivot_fields)

            # 建立母工單透視表
            mother_pivot_tables = {}
            for col, prefix in pivot_fields.items():
                pivot = split_workorder_mother_table.pivot_table(
                    index=['workorder'],
                    columns='step_process',
                    values=col,
                    aggfunc='first'
                )
                pivot.columns = [f"{prefix}_{c}" for c in pivot.columns]
                mother_pivot_tables[prefix] = pivot.reset_index()

            split_workorder_mother_table_pivot = reduce(
                lambda left, right: pd.merge(
                    left, right, on='workorder', how='outer'),
                [mother_pivot_tables[prefix]
                    for prefix in pivot_fields.values()]
            )
            split_workorder_mother_table_pivot = split_workorder_mother_table_pivot.rename(
                columns={'workorder': 'mother_workorder'}
            )

            # 針對每個欄位補植
            for col in split_workorder_mother_table_pivot.columns:
                if col == 'mother_workorder':
                    continue
                iym_df_table_pivot['mother_workorder'] = iym_df_table_pivot['workorder'].str.replace(
                    r'[A-Z]+$', '', regex=True
                )
                temp = iym_df_table_pivot[['mother_workorder']].merge(
                    split_workorder_mother_table_pivot[[
                        'mother_workorder', col]],
                    on='mother_workorder',
                    how='left'
                )
                iym_df_table_pivot[col] = iym_df_table_pivot[col].combine_first(
                    temp[col])

            # 移除臨時欄位
            iym_df_table_pivot = iym_df_table_pivot.drop(
                columns=['mother_workorder'])

        # 重新排序欄位
        step_process = sorted(set(
            col.split('_', 1)[1]
            for col in iym_df_table_pivot.columns
            if '_' in col and not col.startswith('workorder')
        ))

        field_order = [
            'machine', 'in_pdline_time', 'out_pdline_time',
            'emp_no', 'emp_name', 'JOB_title'
        ]

        new_columns = ['workorder']
        for step in step_process:
            for field in field_order:
                col_name = f"{field}_{step}"
                if col_name in iym_df_table_pivot.columns:
                    new_columns.append(col_name)

        iym_df_table_pivot = iym_df_table_pivot[new_columns]

        # 加入材料資料
        sql = f"""
        SELECT 
            A.workorder,
            A.process_name,
            A.machine_desc,
            A.STEP,
            B.material_no,
            B.material_batch
        FROM etdb.sf_work_order_trace A
        LEFT JOIN etdb.sf_work_order_materials B 
            ON A.id = B.trace_id    
        WHERE 
            A.workorder IN {tuple(iym_df_table_workorder)}
            AND A.machine_desc IN {tuple(machine_list)}
        ORDER BY 
            A.workorder, 
            A.STEP
        """

        materials = pd.DataFrame(execute_sql_query(
            sql, as_dict=True, db="KHSTARROCKS"))
        materials = materials.rename(columns={'STEP': 'step'})
        materials['step_process'] = materials['step'].astype(
            str) + '-' + materials['process_name']

        # 建立材料透視表
        material_no_pivot = materials.pivot_table(
            index='workorder',
            columns='step_process',
            values='material_no',
            aggfunc='first'
        )
        material_no_pivot.columns = [
            f"material_no_{c}" for c in material_no_pivot.columns]
        material_no_pivot = material_no_pivot.reset_index()

        material_batch_pivot = materials.pivot_table(
            index='workorder',
            columns='step_process',
            values='material_batch',
            aggfunc='first'
        )
        material_batch_pivot.columns = [
            f"material_batch_{c}" for c in material_batch_pivot.columns]
        material_batch_pivot = material_batch_pivot.reset_index()

        # 合併到主表
        iym_df_table_pivot = reduce(
            lambda left, right: pd.merge(
                left, right, on='workorder', how='left'),
            [iym_df_table_pivot, material_no_pivot, material_batch_pivot]
        )

        # 最終欄位排序和清理
        full_field_order = [
            'machine', 'in_pdline_time', 'out_pdline_time',
            'material_no', 'material_batch', 'emp_no', 'emp_name', 'JOB_title'
        ]

        new_columns = ['workorder']
        for step in step_process:
            for field in full_field_order:
                col_name = f"{field}_{step}"
                if col_name in iym_df_table_pivot.columns:
                    new_columns.append(col_name)

        iym_df_table_pivot = iym_df_table_pivot[new_columns]

        # 將 machine_ 前綴的欄位名稱改為只剩 step_process 名稱
        iym_df_table_pivot.columns = [
            col.replace('machine_', '') if col.startswith('machine_') else col
            for col in iym_df_table_pivot.columns
        ]

        # 取得不良資料
        sql = f"""
        SELECT 
        workorder, 
        panel_no, 
        defect_code, 
        defect_count
        FROM (
            SELECT
                d.id,
                d.workorder,
                d.part_no,
                t.panel_no,
                t.defect_code,
                t.defect_count,
            ROW_NUMBER() OVER (
                PARTITION BY d.id, d.workorder, d.part_no, t.panel_no, t.defect_code
            ORDER BY t.defect_count DESC
            ) AS rn
            FROM sf_work_order_trace d
                JOIN sf_work_order_panel_defect_summary t
                ON d.id = t.trace_id
            WHERE d.workorder IN {tuple(iym_df_table_workorder)}
        ) AS ranked_defects
        WHERE rn = 1
        """
        defect_table = pd.DataFrame(execute_sql_query(
            sql, as_dict=True, db="KHSTARROCKS"))
        defect_table_pivot = defect_table.pivot_table(
            index=['workorder', 'panel_no'],
            columns='defect_code',
            values='defect_count',
            aggfunc='sum',
            fill_value=0
        ).reset_index()
        defect_table_pivot.columns.name = None  # 移除列名
        defect_table_pivot['total_defect_pcs'] = defect_table_pivot.sum(
            axis=1, numeric_only=True)

        # 拉取各取數量資訊
        sql = f"""
        SELECT distinct 
            a.workorder,
            b.total_pcs,
            b.panel_count
        FROM sf_work_order_trace a
        JOIN sf_work_order_defect b
            ON a.id = b.trace_id
        WHERE a.workorder IN {tuple(iym_df_table_workorder)}
        order by a.workorder
        """
        pcs_info = pd.DataFrame(execute_sql_query(
            sql, as_dict=True, db="KHSTARROCKS"))
        pcs_info = pcs_info.rename(columns={'total_pcs': 'total_pcs',
                                            'panel_count': 'panel_count'})
        iym_df_table_pivot = pd.merge(iym_df_table_pivot, pcs_info, on=[
            'workorder'], how='left')

        workorder_defect_table = pd.merge(
            defect_table_pivot,
            pcs_info,
            on='workorder',
            how='left'
        )
        workorder_defect_table['defect_rate'] = workorder_defect_table['total_defect_pcs'] / \
            workorder_defect_table['total_pcs']
        workorder_panel_defect_table = workorder_defect_table

        if specify_defect_code:
            # 有指定不良項目
            specify_defect_table = defect_table[defect_table['defect_code'].fillna(
                '').str.startswith(tuple(specify_defect_code))]
            specify_defect_table_pivot = specify_defect_table.pivot_table(
                index=['workorder', 'panel_no'],
                columns='defect_code',
                values='defect_count',
                aggfunc='sum',
                fill_value=0
            ).reset_index()
            specify_defect_table_pivot.columns.name = None  # 移除列名
            specify_defect_table_pivot['total_defect_count'] = specify_defect_table_pivot.iloc[:, 2:].sum(
                axis=1)
            specify_defect_table_pivot = pd.merge(
                specify_defect_table_pivot,
                pcs_info,
                on='workorder',
                how='left'
            )
            specify_defect_table_pivot.rename(
                columns={'total_pcs': 'workorder_total_pcs', }, inplace=True)
            specify_defect_table_pivot['panel_pcs'] = specify_defect_table_pivot['workorder_total_pcs'] / \
                specify_defect_table_pivot['panel_count']
            specify_defect_table_pivot['defect_rate'] = specify_defect_table_pivot['total_defect_count'] / \
                specify_defect_table_pivot['panel_pcs']

            workorder_specify_defect_table_pivot = specify_defect_table_pivot.groupby('workorder').agg({
                'total_defect_count': 'sum',
                'workorder_total_pcs': 'mean',
            }).reset_index()
            workorder_specify_defect_table_pivot['defect_rate'] = workorder_specify_defect_table_pivot[
                'total_defect_count'] / workorder_specify_defect_table_pivot['workorder_total_pcs']
            workorder_panel_defect_table = specify_defect_table_pivot
            workorder_defect_table = workorder_specify_defect_table_pivot

        else:
            # 沒有指定，使用全部不良
            defect_table_pivot['total_defect_pcs'] = defect_table_pivot.sum(
                axis=1, numeric_only=True)
            workorder_defect_table = defect_table_pivot.groupby('workorder').agg({
                'total_defect_pcs': 'sum',
            }).reset_index()
            workorder_defect_table = pd.merge(
                workorder_defect_table,
                pcs_info,
                on='workorder',
                how='left'
            )
            workorder_defect_table['defect_rate'] = workorder_defect_table['total_defect_pcs'] / \
                workorder_defect_table['total_pcs']

        workorder_process_defect_table = pd.merge(iym_df_table_pivot,
                                                  workorder_defect_table,
                                                  on='workorder',
                                                  how='left'
                                                  )

        logger.info(
            f'pbc_data - 最終資料筆數: {len(workorder_process_defect_table)}')
        workorder_process_defect_table = workorder_process_defect_table.replace(
            {np.nan: None})

        if use_redis:
            # 將結果存入 Redis
            try:
                cache_data = {
                    'workorder_process_defect_table': workorder_process_defect_table.to_json(orient='records'),
                    'workorder_panel_defect_table': workorder_panel_defect_table.to_json(orient='records')
                }
                redis_client.set(json.dumps(cache_data))
                logger.info(f'pbc_data - 資料已存入 Redis 快取')
            except Exception as e:
                logger.warning(f'pbc_data - Redis 快取儲存失敗: {str(e)}')

        return workorder_process_defect_table, workorder_panel_defect_table

    except Exception as e:
        logger.exception(f"pbc_data - 處理失敗: {str(e)}")
        return pd.DataFrame(), pd.DataFrame()


# SPI資料獲取函式


def spi_data(
    part_no: str,
    work_order: str,
    use_redis: bool = True,
):
    work_order_list = "','".join(sorted(wo.strip()
                                        for wo in work_order.split(",")))
    side = part_no.split("-")[1]

    if use_redis:
        redis_client = RedisClient(
            f"spi_data_{part_no}_{work_order_list}")

        # 嘗試從 Redis 獲取快取資料`
        cached_data = redis_client.get()
        if cached_data:
            return pd.read_json(cached_data)

    # 取得SPI資料
    sql = f"""
    SELECT
        prog_name,
        work_order,
        SERIAL_NUMBER,
        SN,comp_type,
        POSITION,
        HIGH_VALUE,
        VOLUME_VALUE,
        AREA_VALUE,
        start_time,
        end_time,
        create_time
    FROM t_flex_smt_param_uploadvalue
    WHERE
        work_order in ('{work_order_list}')
    """
    logger.debug(f'取得SPI資料 SQL: {sql}')
    spi_data = pd.DataFrame(execute_sql_query(sql, as_dict=True, db="KSTIDB"))
    logger.debug(f'spi_data shape: {spi_data.shape}')

    # 取得鋼網資料
    stencil_sql = f"""
    SELECT *
    FROM qms_spi_maverick_ai
    WHERE work_order in ('{work_order_list}')
    """
    stencil_data = pd.DataFrame(execute_sql_query(
        stencil_sql, as_dict=True, db="KSTIDB"))

    spi_data = pd.merge(spi_data, stencil_data, on=['work_order'], how='left')
    spi_data['solder_life'] = (
        spi_data['IN_TIME'] - spi_data['BEGINTEMPERTIME']).dt.total_seconds()

    # 取得載具蓋片資料
    sn_carrier_sql = f"""
    SELECT *
    FROM t_flex_sn_carrier
    WHERE
        PROCESS_NAME = 'SMT投入-{side}'
        AND work_order IN ('{work_order_list}')
    """
    sn_carrier = pd.DataFrame(execute_sql_query(
        sn_carrier_sql, as_dict=True, db="KSTIDB"))
    sn_carrier = sn_carrier.drop(columns=['SERIAL_NUMBER1', 'PD_SN1', 'UPDATE_TIME',
                                          'CREATE_TIME', 'OUT_PROCESS_TIME', 'PROCESS_NAME', 'WORK_ORDER'])

    spi_data = pd.merge(spi_data, sn_carrier, on=['SERIAL_NUMBER'], how='left')

    # 取得印刷資料
    print_sql = """
    SELECT *
    FROM qms_spi_maverick_aiprint
    """
    print_data = pd.DataFrame(execute_sql_query(
        print_sql, as_dict=True, db="KSTIDB"))

    print_data = print_data.rename(columns={'PANEL_NO': 'SERIAL_NUMBER'})
    spi_data = pd.merge(spi_data, print_data, on=[
        'SERIAL_NUMBER', 'MACHINE_CODE'], how='left')

    # 取得刮刀資料
    scraper_sql = """
    SELECT
        p1.SCRAPER_NO,
        p1.work_order,
        p1.MACHINE_CODE,
        p1.SUM_COUNT
    FROM qms_spi_maverick_aiscraper p1
    INNER JOIN (
        SELECT
            SCRAPER_NO,
            work_order,
            MAX(CREATE_TIME) AS only_CREATE_TIME
        FROM qms_spi_maverick_aiscraper
        GROUP BY
            SCRAPER_NO,
            work_order,
            MACHINE_CODE
    ) p2 ON p1.SCRAPER_NO = p2.SCRAPER_NO
        AND p1.work_order = p2.work_order
        AND p1.CREATE_TIME = p2.only_CREATE_TIME
    """
    scraper_data = pd.DataFrame(execute_sql_query(
        scraper_sql, as_dict=True, db="KSTIDB"))

    aiscraper_dataset1 = scraper_data.rename(
        columns={'SCRAPER_NO': 'SQUEEGEE_FRONT_NO', 'SUM_COUNT': 'FRONT_SQUEEGEE_SUM_COUNT'})
    spi_data = pd.merge(spi_data, aiscraper_dataset1, on=[
        'work_order', 'MACHINE_CODE', 'SQUEEGEE_FRONT_NO'], how='left')

    aiscraper_dataset2 = scraper_data.rename(
        columns={'SCRAPER_NO': 'SQUEEGEE_RAER_NO', 'SUM_COUNT': 'BACK_SQUEEGEE_SUM_COUNT'})
    spi_data = pd.merge(spi_data, aiscraper_dataset2, on=[
        'work_order', 'MACHINE_CODE', 'SQUEEGEE_RAER_NO'], how='left')

    spi_data['SERIAL_NUMBER'] = spi_data['SERIAL_NUMBER'].astype(str)
    tension_columns = ['POINTA', 'POINTB', 'POINTC', 'POINTD', 'POINTE']
    spi_data['tension_mean'] = spi_data[tension_columns].mean(axis=1)
    spi_data['tension_range'] = spi_data[tension_columns].max(
        axis=1) - spi_data[tension_columns].min(axis=1)

    spi_data['SQUEEGEE_SUM_COUNT'] = np.where(
        spi_data['PRINT_DIRECTION'] == 'FORWARD',
        spi_data['FRONT_SQUEEGEE_SUM_COUNT'],
        spi_data['BACK_SQUEEGEE_SUM_COUNT']
    )

    spi_data['SQUEEGEE_NO'] = np.where(
        spi_data['PRINT_DIRECTION'] == 'FORWARD',
        spi_data['SQUEEGEE_FRONT_NO'],
        spi_data['SQUEEGEE_RAER_NO']
    )

    spi_data.drop(columns=['UPDATE_TIME', 'OUT_TIME', 'SQUEEGEE_FRONT_NO', 'SQUEEGEE_RAER_NO',
                           'FRONT_SQUEEGEE_SUM_COUNT', 'BACK_SQUEEGEE_SUM_COUNT', 'POINTA',
                           'POINTB', 'POINTC', 'POINTD', 'POINTE', 'create_time', 'PRINT_TIME',
                           'AUTO_CLEAN', 'GAP'], inplace=True)

    final_data = calculate_control_limits(
        spi_data, target_col='VOLUME_VALUE', std_multiplier=3)

    final_data['ooc'] = final_data['control_status'].apply(
        lambda x: 'ooc' if x != 'in_control' else 'in_control')
    final_data.drop(columns=['control_status'], inplace=True)
    final_data.dropna(inplace=True)

    if use_redis:
        # 將結果存入 Redis
        redis_client.set(final_data.to_json(orient='records'))

    return final_data


# 測試用例
if __name__ == "__main__":
    # 請根據實際資料填入參數
    start_date = "2025-01-01"
    end_date = "2025-02-28"
    part_no = "QE2825"
    machine_name = "(R22B)RTR 乾膜前處理線-02-B#E2"

    df = get_iym_machine_parameter(start_date, end_date, part_no, machine_name)
    logger.debug(df.head())
