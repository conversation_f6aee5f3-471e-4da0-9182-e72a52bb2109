from typing import List
from dataclasses import dataclass, field

"""
*因為是service，沒有req、res，或驗證的需求，所以不寫成pydantic model，用dataclass保持輕量化就好。

dataclass是python語法糖，用@dataclass decorator方式宣告，
他可以自動生成__init__、__repr__方法，還可以設定預設值(使用field)。
內部變數有兩種寫法，根據寫法會產生不同變數--
class變數: (寫法是"不明確定義型別")
    例如下方的ColumnConfig寫法。
    是靜態的，甚至不用實例化就可以使用，在所有調用者間共享，常用來寫不會改動的常數(因為共用)。
instance變數: (寫法是"明確定義型別")
    例如下方的AnalysisConfig寫法。
    非靜態，必須實例化後才能使用，在所有調用者間不共享，常用來寫會改動的變數。
    *field(default_factory=函數) 是官方提供的安全寫法，要傳入函數，常用lambda簡寫。


外部調用使用範例:
from .data_service_config import ColumnConfig, AnalysisConfig

實例化-
column_config = ColumnConfig() #非必要，因為class變數不須實例化也可調用
analysis_config = AnalysisConfig() # 使用預設值
custom_analysis_config = AnalysisConfig(
    default_pivot_columns=["工站", "時間"],
    outlier_std_multiplier=2.5,
    test_size=0.1
) # 使用自定義值

使用-
required_columns = column_config.qe2825_columns
pivot_columns = analysis_config.default_pivot_columns
custom_pivot_columns = custom_analysis_config.default_pivot_columns
"""

# ColumnConfig寫成常數，因為不會改動


@dataclass
class ColumnConfig:
    # QE2825 配置
    qe2825_columns = [
        '26-線體', '29-線體', '32-線體', '35-線體',
        '39-線體', '42-線體', '43-線體']
    qe2825_mapping = {
        '26-線體': 'RTR VCP全局面鍍銅',
        '29-線體': 'RTR局部銅電剝膜',
        '32-線體': 'RTR乾膜前處理',
        '35-線體': 'RTR乾膜壓合',
        '39-線體': 'RTR曝光',
        '42-線體': 'RTR線路顯影',
        '43-線體': 'RTR蝕刻-剝膜'
    }

    # QN1366 配置
    qn1366_columns = [
        '66-線體', '69-線體', '72-線體', '75-線體', '78-線體', '79-線體']
    qn1366_mapping = {
        '66-線體': 'RTR局部銅電剝膜',
        '69-線體': 'RTR 乾膜前處理(粗化)',
        '72-線體': 'RTR 真空乾膜壓合',
        '75-線體': 'RTR 線路DI',
        '78-線體': 'RTR線路顯影',
        '79-線體': 'RTR蝕刻-剝膜'
    }

# AnalysisConfig寫成instance變數，因為每個地方的分析設定大概率會不同


@dataclass
class AnalysisConfig:
    default_pivot_columns: List[str] = field(default_factory=lambda: [
        '線體', 'in_pdline_time', 'out_pdline_time',
        '人員工號', '人員職稱', '材料批號'
    ])
