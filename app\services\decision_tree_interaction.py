import pandas as pd
import numpy as np
from typing import Dict, Any, List
import logging
import time
import os
from io import StringIO
from ..utils.date_utils import format_execution_time
from ..utils.encodeAscii2Utf8 import fix_chinese_encoding

logger = logging.getLogger(__name__)

# 判斷是否為開發模式（1=開發模式，0=正式模式）
DEVELOPMENT_MODE = os.environ.get("DEVELOPMENT_MODE", "0") == "1"

if not DEVELOPMENT_MODE:
    import rpy2.robjects as ro
    from rpy2.robjects import pandas2ri
    # 啟用 pandas 與 R 的轉換功能
    pandas2ri.activate()
    print("已啟用 rpy2 和 pandas2ri，準備進行決策樹交互作用分析")


def decision_tree_interaction_analysis(data: pd.DataFrame,
                                       target: str = "ooc_rate",
                                       method: str = "anova") -> Dict[str, Any]:
    """
    使用決策樹分析變量之間的交互作用關係

    Args:
        data (pd.DataFrame): 輸入數據
        target (str): 目標變量名稱
        method (str): 分裂方法，'anova' 用於連續變數，'class' 用於類別變數

    Returns:
        Dict[str, Any]: 包含交互作用路徑和執行時間的字典
    """
    try:
        if DEVELOPMENT_MODE:
            logger.info("開發模式啟用，跳過 rpy2/R 分析，直接回傳 mock 結果")
            # 回傳一個簡單的 mock 結果
            return [{
                "Node": "1",
                "RulePath": "(mock) A > 1 & B < 2",
                "Prediction": 0.5,
                "Importance": 1.0
            }]

        # 添加詳細的數據檢查和除錯信息
        logger.info(f"開始決策樹交互作用分析，數據形狀: {data.shape}")
        logger.info(f"目標變量: {target}")
        logger.info(f"方法: {method}")

        # 檢查數據基本信息
        logger.info(f"數據列名: {list(data.columns)}")
        logger.info(f"數據類型:\n{data.dtypes}")
        logger.info(f"數據前5行:\n{data.head()}")

        # 檢查是否有無限值或異常值
        logger.info(
            f"是否有無限值: {np.isinf(data.select_dtypes(include=[np.number])).any().any()}")
        logger.info(f"是否有NaN值: {data.isna().any().any()}")

        # 檢查字符串列的特殊字符
        string_columns = data.select_dtypes(include=['object']).columns
        for col in string_columns:
            # 只顯示前10個
            logger.info(f"列 '{col}' 的唯一值: {data[col].unique()[:10]}")

        # 數據清理：處理可能的問題
        data_clean = data.copy()

        # 1. 移除包含特殊字符的列名
        problematic_cols = []
        for col in data_clean.columns:
            if any(char in col for char in [' ', '-', '(', ')', '&', '|', '*', '/', '\\', '"', "'"]):
                problematic_cols.append(col)
                logger.warning(f"發現問題列名: {col}")

        if problematic_cols:
            logger.info(f"移除問題列名: {problematic_cols}")
            data_clean = data_clean.drop(columns=problematic_cols)

        # 2. 確保目標變量存在
        if target not in data_clean.columns:
            logger.error(f"目標變量 '{target}' 不存在於數據中")
            raise ValueError(f"目標變量 '{target}' 不存在於數據中")

        # 3. 處理數值列的無限值和異常值
        numeric_cols = data_clean.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col != target:
                # 將無限值替換為NaN
                data_clean[col] = data_clean[col].replace(
                    [np.inf, -np.inf], np.nan)
                # 使用中位數填充NaN
                median_val = data_clean[col].median()
                if not pd.isna(median_val):
                    data_clean[col] = data_clean[col].fillna(median_val)

        # 4. 移除包含NaN的行
        data_clean = data_clean.dropna()

        logger.info(f"清理後數據形狀: {data_clean.shape}")

        # 5. 檢查清理後的數據
        if len(data_clean) < 10:
            logger.error("清理後數據量不足，無法進行分析")
            raise ValueError("清理後數據量不足，無法進行分析")

        # 6. 嘗試轉換為R物件前的最後檢查
        logger.info("準備轉換為R物件...")
        logger.info(f"最終數據類型:\n{data_clean.dtypes}")

        try:
            # 將數據傳入 R 環境
            r_data = pandas2ri.py2rpy(data_clean)
            logger.info("成功轉換為R物件")
        except Exception as rpy_error:
            logger.error(f"R物件轉換失敗: {str(rpy_error)}")
            logger.error(f"錯誤詳情: {type(rpy_error).__name__}")

            # 嘗試更嚴格的數據清理
            logger.info("嘗試更嚴格的數據清理...")

            # 只保留數值列和目標變量
            numeric_cols = data_clean.select_dtypes(
                include=[np.number]).columns.tolist()
            if target in data_clean.columns:
                numeric_cols.append(target)

            data_strict = data_clean[numeric_cols].copy()
            data_strict = data_strict.dropna()

            logger.info(f"嚴格清理後數據形狀: {data_strict.shape}")

            if len(data_strict) < 10:
                raise ValueError("嚴格清理後數據量仍然不足")

            # 再次嘗試轉換
            print("data_strict", data_strict.head(5))
            r_data = pandas2ri.py2rpy(data_strict)
            logger.info("嚴格清理後成功轉換為R物件")

        ro.globalenv['df'] = r_data

        # 避免 R 的 locale 中文錯亂
        ro.r('Sys.setlocale("LC_ALL", "C")')

        # R 腳本：建模 + 輸出節點規則資料表
        r_script = f'''
        # 跨平台的函式庫路徑設定
        get_lib_path <- function() {{
            os_type <- Sys.info()["sysname"]
            if (os_type == "Windows") {{
                lib_path <- file.path(Sys.getenv("USERPROFILE"), "R", "win-library")
            }} else {{
                lib_path <- file.path(Sys.getenv("HOME"), "R", "library")
            }}
            return(lib_path)
        }}
        
        user_lib <- get_lib_path()
        dir.create(user_lib, showWarnings = FALSE, recursive = TRUE)
        .libPaths(c(user_lib, .libPaths()))
        options(repos = c(CRAN = "https://cloud.r-project.org"))
        
        packages <- c("dplyr", "rpart", "caret")

        for (package_name in packages) {{
            if (!require(package_name, character.only = TRUE)) {{
                try(install.packages(package_name,
                    lib = user_lib,
                    dependencies = TRUE,
                    quiet = TRUE,
                    ask = FALSE))
                library(package_name, character.only = TRUE)
            }}
        }}

        tree_path_analysis <- function(data, target = "{target}", method = "{method}", seed = 42) {{
            set.seed(seed)
            
            # 數據分割
            idx <- createDataPartition(data[[target]], p = 0.8, list = FALSE)
            train <- data[idx, ]
            test <- data[-idx, ]

            # 訓練決策樹模型
            tree_model <- try(rpart(formula = as.formula(paste(target, "~ .")), 
                                  data = train, 
                                  method = method,
                                  control = rpart.control(cp = 0.01)),
                           silent = TRUE)
            
            # 檢查是否有錯誤
            if (inherits(tree_model, "try-error")) {{
                return(data.frame(Node = character(0), 
                                 RulePath = character(0), 
                                 Prediction = numeric(0),
                                 Importance = numeric(0)))
            }}

            # 轉換為路徑資料框
            as_path_df <- function(model) {{
                frame <- model$frame
                if (nrow(frame) <= 1) {{
                    return(data.frame(Node = character(0), 
                                     RulePath = character(0), 
                                     Prediction = numeric(0),
                                     Importance = numeric(0)))
                }}
                
                splits <- rownames(frame)
                result <- data.frame(
                    Node = character(),
                    RulePath = character(), 
                    Prediction = numeric(),
                    stringsAsFactors = FALSE
                )
                
                for (node in splits) {{
                    path <- path.rpart(model, nodes = as.numeric(node), print.it = FALSE)[[1]]
                    if (length(path) > 1) {{  # 確保不是根節點
                        rule_text <- paste(path[-1], collapse = " & ")  # 移除 root 並串接
                        prediction <- frame[node, "yval"]
                        
                        result <- rbind(result, data.frame(
                            Node = node,
                            RulePath = rule_text,
                            Prediction = prediction,
                            stringsAsFactors = FALSE
                        ))
                    }}
                }}
                
                # 篩選葉節點
                leaf_nodes <- rownames(model$frame[model$frame$var == "<leaf>", ])
                leaf_paths <- result[result$Node %in% leaf_nodes, ]
                
                # 根據預測值的絕對偏差計算重要性
                mean_pred <- mean(leaf_paths$Prediction)
                leaf_paths$Importance <- abs(leaf_paths$Prediction - mean_pred)
                
                # 標準化重要性得分
                max_importance <- max(leaf_paths$Importance)
                if (max_importance > 0) {{
                    leaf_paths$Importance <- leaf_paths$Importance / max_importance
                }}
                
                # 排序結果
                leaf_paths <- leaf_paths[order(leaf_paths$Importance, decreasing = TRUE), ]
                
                return(leaf_paths)
            }}

            result_df <- as_path_df(tree_model)
            return(result_df)
        }}

        result_df <- try(tree_path_analysis(
            data = df,
            target = "{target}",
            method = "{method}"
        ), silent = TRUE)

        # 檢查是否發生錯誤
        if (inherits(result_df, "try-error")) {{
            result_df <- data.frame(
                Node = character(0), 
                RulePath = character(0), 
                Prediction = numeric(0),
                Importance = numeric(0)
            )
        }}
        '''

        # 執行 R 腳本
        ro.r(r_script)
        logger.info("決策樹交互作用R腳本執行成功")

        # 取回結果
        result_df = ro.globalenv['result_df']
        path_df = pandas2ri.rpy2py(result_df)

        if 'RulePath' in path_df.columns:
            path_df['RulePath'] = path_df['RulePath'].apply(
                fix_chinese_encoding)

        result = path_df.to_dict('records')

        return result

    except Exception as e:
        logger.error(f"決策樹交互作用分析失敗: {str(e)}")
        logger.error(f"錯誤類型: {type(e).__name__}")
        logger.error(f"錯誤詳情: {str(e)}")
        raise Exception(f"決策樹交互作用分析失敗: {str(e)}")
