import pandas as pd
import re


def get_list_by_partno(resource_type: str, table: pd.DataFrame, part_no: str):
    """
    根據品目篩選其工單號清單、製程+機台清單

    Args:
        resource_type (str): 資源類型(SPI, PBC)
        table (pd.DataFrame): 資料(要是pivot table，這裡是使用iym_df_table_pivot)
        part_no (str): 品目

    Returns:
        Dict: 工單號清單、製程+機台清單
    """
    if resource_type == "SPI":
        # todo 之後整併SPI近進來
        print("SPI")
    elif resource_type == "PBC":
        # 創立一個dict
        result = {}
        # 取得所有工單號
        work_order_list = table['workorder'].unique().tolist()
        result['work_order'] = work_order_list
        # 取得所有 step_process 名稱
        step_processes = sorted(set(
            re.match(
                r'^(?:emp_no_|emp_name_|JOB_title_)?(\d+-[^_]+)', col).group(1)
            for col in table.columns
            if re.match(r'^(?:emp_no_|emp_name_|JOB_title_)?\d+-', col)
        ))
        result['process_machine'] = {}
        for step_processe in step_processes:
            # 取得所有機台
            machine_list = table[step_processe].unique().tolist()
            result['process_machine'][step_processe] = machine_list

        return result
