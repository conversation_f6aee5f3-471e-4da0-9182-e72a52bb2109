from scipy.stats import gaussian_kde
import numpy as np
import pandas as pd


def get_kde_data(data: pd.DataFrame, column: str, grid_points: int = 1000):
    values = data[column].dropna().values
    # 若資料不足兩筆或全都一樣，無法計算 KDE
    if len(values) < 2 or np.all(values == values[0]):
        return {"x": [], "y": []}
    kde = gaussian_kde(values)
    x_vals = np.linspace(min(values), max(values), grid_points)
    y_vals = kde.evaluate(x_vals)
    return {
        "x": x_vals.tolist(),
        "y": y_vals.tolist()
    }


def generate_kde_for_groups(data: pd.DataFrame, target_col: str, normal_label: str, defect_label: str, features: list) -> dict:
    normal_data = data[data[target_col] == normal_label]
    defect_data = data[data[target_col] == defect_label]
    # print("data[target] unique:", data[target_col].unique())
    # print("normal_group:", normal_label, "defect_group:", defect_label)
    # print("features:", features)
    # print("data.head():", data.head())

    result = []
    for feature in features:
        result.append({
            "feature": feature,
            "normal": get_kde_data(normal_data, feature),
            "defect": get_kde_data(defect_data, feature)
        })
    return result  # 已經是 dict 結構，可直接回傳給前端
