import numpy as np
import pandas as pd
from scipy.stats import kruskal
from imblearn.under_sampling import RandomUnderSampler
import logging

logger = logging.getLogger(__name__)


def perform_ks_test_analysis(data: pd.DataFrame,
                             features: list,
                             target: str = "ooc",
                             iterations: int = 100,
                             p_threshold: float = 0.05) -> dict:
    """
    執行多次 Kruskal-Wallis 檢定來評估特徵的統計顯著性

    Args:
        data (pd.DataFrame): 輸入數據
        features (list): 要分析的特徵列表
        target (str): 目標變量名稱
        iteration (int): 隨機下採樣的次數
        p_threshold (float): 顯著性閾值

    Returns:
        dict: 包含特徵顯著性計數和比例的分析結果
    """
    try:
        logger.info(f"開始執行H檢定分析，特徵數: {len(features)}，迭代次數: {iterations}")

        # 檢查數據有效性
        if len(data) == 0:
            logger.error("輸入數據為空")
            raise ValueError("輸入數據為空")

        # 檢查目標變量是否在數據中
        if target not in data.columns:
            logger.error(f"目標變量 '{target}' 不在數據中")
            raise ValueError(f"目標變量 '{target}' 不在數據中")

        # 檢查特徵是否都在數據中
        missing_features = [
            feature for feature in features if feature not in data.columns]
        if missing_features:
            logger.error(f"以下特徵不在數據中: {missing_features}")
            raise ValueError(f"以下特徵不在數據中: {missing_features}")

        # 確保特徵為數值類型
        df = data.copy()
        for feature in features:
            df[feature] = pd.to_numeric(df[feature], errors='coerce')

        # 處理 NaN 值
        df = df.dropna(subset=features + [target])

        # 記錄各特徵的顯著次數
        significant_counts = {feature: 0 for feature in features}

        # 檢查哪些特徵在整個數據集中具有變異性
        no_variance_features = []
        for feature in features:
            unique_values = df[feature].unique()
            if len(unique_values) == 1:
                logger.warning(
                    f"特徵 '{feature}' 在整個數據集中沒有變異性，所有值都是 {unique_values[0]}")
                no_variance_features.append(feature)

        # 進行多次隨機下採樣
        for i in range(iterations):
            try:
                # 準備下採樣
                X = df[features]
                y = df[target]

                # 進行隨機下採樣，平衡各類別樣本
                rus = RandomUnderSampler(random_state=i)
                X_resampled, y_resampled = rus.fit_resample(X, y)

                # 重新組合特徵與目標變量
                df_resampled = pd.DataFrame(X_resampled, columns=features)
                df_resampled[target] = y_resampled

                # 進行 Kruskal-Wallis 檢定
                for feature in features:
                    # 跳過已知沒有變異性的特徵
                    if feature in no_variance_features:
                        continue

                    unique_labels = np.unique(y_resampled)
                    if len(unique_labels) < 2:
                        logger.warning(f"迭代 {i+1}: 標籤類別少於2個，跳過此特徵的檢定")
                        continue

                    # 為每個類別收集數據
                    groups = [df_resampled[df_resampled[target] ==
                                           label][feature] for label in unique_labels]

                    # 檢查每組是否有足夠的數據
                    valid_groups = [
                        group for group in groups if len(group) > 0]
                    if len(valid_groups) < 2:
                        logger.warning(
                            f"迭代 {i+1}: 特徵 {feature} 的有效組數少於2個，跳過此特徵")
                        continue

                    # 檢查數據變異性
                    all_values = np.concatenate(valid_groups)
                    if len(np.unique(all_values)) == 1:
                        logger.warning(
                            f"迭代 {i+1}: 特徵 {feature} 的所有數值都相同，跳過此特徵")
                        continue

                    # 執行 Kruskal-Wallis 檢定
                    stat, p_value = kruskal(*valid_groups)

                    # 若顯著，則計數加1
                    if p_value < p_threshold:
                        significant_counts[feature] += 1

                if (i+1) % 10 == 0:
                    logger.info(f"完成 {i+1}/{iterations} 次迭代")

            except Exception as e:
                logger.error(f"迭代 {i+1} 中發生錯誤: {str(e)}")
                continue

        # 計算顯著比例
        significant_rates = {}
        for feature in features:
            if feature in no_variance_features:
                significant_rates[feature] = 0.0
            else:
                significant_rates[feature] = significant_counts[feature] / \
                    iterations

        try:
            # 直接將列表轉換為DataFrame並排序後轉為字典
            return pd.DataFrame([{
                'feature': feature,
                'significant_count': significant_counts.get(feature, 0),
                'significant_rate': significant_rates.get(feature, 0.0),
            } for feature in features])\
                .sort_values(by='significant_rate', ascending=False)\
                .to_dict('records')
        except Exception as e:
            logger.error(f"H檢定(Kruskal-Wallis Test)分析 處理結果時出錯: {str(e)}")
            raise Exception(f"H檢定(Kruskal-Wallis Test)分析 處理結果時出錯: {str(e)}")
    except Exception as e:
        logger.error(f"H檢定(Kruskal-Wallis Test)分析失敗: {str(e)}")
        raise Exception(f"H檢定(Kruskal-Wallis Test)分析失敗: {str(e)}")
