import pandas as pd
import numpy as np
from sklearn.linear_model import Lasso
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from typing import Dict, Any
import base64
from io import BytesIO
import logging
import time
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time

logger = logging.getLogger(__name__)


def train_lasso_model(X: pd.DataFrame, y: pd.Series, alpha: float = 0.001, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 Lasso 回歸模型

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標
        alpha (float): Lasso 正則化參數
        iterations (int): 迭代次數

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    # 初始化特徵重要性和性能指標
    lasso_importances = np.zeros(X.shape[1])
    mse_scores = []
    logger.info(f"訓練 Lasso 模型，迭代次數: {iterations}")

    for i in range(iterations):
        # 分割資料集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=i)

        # 訓練 Lasso 模型
        lasso_model = Lasso(alpha=alpha, random_state=42, max_iter=5000)
        lasso_model.fit(X_train, y_train)

        # 預測並計算 MSE
        y_test_pred = lasso_model.predict(X_test)
        mse = np.mean((y_test - y_test_pred) ** 2)
        mse_scores.append(mse)

        # 累加特徵重要性
        lasso_importances += np.abs(lasso_model.coef_)

    # 計算平均特徵重要性
    lasso_importances /= iterations

    # 計算平均 MSE
    avg_mse = np.mean(mse_scores)
    logger.info(f"Lasso 模型訓練完成，平均 MSE: {avg_mse}")

    # 整理特徵重要性資料
    feature_names = X.columns
    lasso_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': lasso_importances, 'Model': 'Lasso'})
    lasso_top_features = lasso_df.sort_values(
        by='Importance', ascending=False).head(10)

    return lasso_top_features.to_dict(orient='records')
