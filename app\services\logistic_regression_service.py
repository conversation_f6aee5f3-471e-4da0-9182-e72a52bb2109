import warnings
import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from typing import Dict, Any
import time
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time
from sklearn.metrics import recall_score
from imblearn.under_sampling import RandomUnderSampler
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import logging

logger = logging.getLogger(__name__)

warnings.filterwarnings("ignore")


def train_logistic_regression(X: pd.DataFrame, y: pd.Series, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 羅吉斯回歸 模型（加速版）

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標
        iterations (int): 訓練迭代次數

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    rus = RandomUnderSampler(random_state=42)
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    logreg_importances = np.zeros(X.shape[1])
    accuracy_scores_logreg = []
    logger.info(f"訓練 Logistic Regression 模型，迭代次數: {iterations}")

    # 加速：減少迭代次數、減少特徵數量（如有需要）、使用更快的 solver
    for i in range(iterations):
        x_train, x_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=i, stratify=y
        )
        x_train_resampled, y_train_resampled = rus.fit_resample(
            x_train, y_train)

        logreg_model = LogisticRegression(
            max_iter=iterations,
            solver='saga',        # saga 對大數據較快
            random_state=42,
            n_jobs=-1             # 多核心加速
        )
        logreg_model.fit(x_train_resampled, y_train_resampled)
        logreg_pred = logreg_model.predict(x_test)
        recall_logreg = recall_score(y_test, logreg_pred, pos_label=1)
        accuracy_scores_logreg.append(recall_logreg)
        logreg_importances += logreg_model.coef_[0] * recall_logreg

    sum_accuracy = np.sum(accuracy_scores_logreg)
    if sum_accuracy != 0:
        logreg_importances /= sum_accuracy
    else:
        logreg_importances = np.zeros_like(logreg_importances)

    feature_names = X.columns
    logreg_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': logreg_importances, 'Model': 'Logistic Regression'})
    # 重要性由大排到小
    logreg_df = logreg_df.sort_values(by='Importance', ascending=False)
    logreg_top10 = logreg_df.head(10)
    logger.info(f"Logistic Regression 模型訓練完成，重要性前10特徵: {logreg_top10}")
    return logreg_top10.to_dict(orient='records')
