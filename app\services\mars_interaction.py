import os
import pandas as pd
from typing import Dict, Any, List
import logging
import time
from ..utils.encodeAscii2Utf8 import fix_chinese_encoding

logger = logging.getLogger(__name__)

# 判斷是否為開發模式（1=開發模式，0=正式模式）
DEVELOPMENT_MODE = os.environ.get("DEVELOPMENT_MODE", "0") == "1"

if not DEVELOPMENT_MODE:
    import rpy2.robjects as ro
    from rpy2.robjects import pandas2ri
    # 啟用 pandas 與 R 的轉換功能
    pandas2ri.activate()
    print("已啟用 rpy2 和 pandas2ri，準備進行 MARS 交互作用分析")


def mars_interaction_analysis(
        data: pd.DataFrame,
        target: str = "ooc") -> Dict[str, Any]:
    """
    使用 MARS (Multivariate Adaptive Regression Splines) 模型分析變量間的交互作用關係

    Args:
        data (pd.DataFrame): 輸入數據
        target (str): 目標變量名稱

    Returns:
        Dict[str, Any]: {
            "interaction_terms": List[Dict[str, Any]],
            "execution_time": str
        }
    """
    start_time = time.time()
    try:
        if DEVELOPMENT_MODE:
            logger.info("開發模式啟用，跳過 rpy2/R 分析，直接回傳 mock 結果")
            # 回傳一個簡單的 mock 結果
            return [{
                "feature": "(mock) A*B",
                "coefficient": 0.88,
                "importance": 1.0
            }]

        # 將數據傳入 R 環境
        r_data = pandas2ri.py2rpy(data)
        ro.globalenv['df'] = r_data

        # 避免 R 的 locale 中文錯亂
        ro.r('Sys.setlocale("LC_ALL", "C")')

        # R 腳本
        r_script = f'''
        # 跨平台的函式庫路徑設定
        get_lib_path <- function() {{
            os_type <- Sys.info()["sysname"]
            if (os_type == "Windows") {{
                lib_path <- file.path(Sys.getenv("USERPROFILE"), "R", "win-library")
            }} else {{
                lib_path <- file.path(Sys.getenv("HOME"), "R", "library")
            }}
            return(lib_path)
        }}

        user_lib <- get_lib_path()
        dir.create(user_lib, showWarnings = FALSE, recursive = TRUE)
        .libPaths(c(user_lib, .libPaths()))
        options(repos = c(CRAN = "https://cloud.r-project.org"))

        packages <- c("dplyr", "earth", "caret", "ggplot2")
        for (package_name in packages) {{
            if (!require(package_name, character.only = TRUE)) {{
                try(install.packages(package_name,
                    lib = user_lib,
                    dependencies = TRUE,
                    quiet = TRUE,
                    ask = FALSE))
                library(package_name, character.only = TRUE)
            }}
        }}
        options(scipen=999)

        mars_interaction_analysis <- function(df, target = "{target}", seed = 42) {{
            data <- df
            set.seed(seed)
            idx <- createDataPartition(data[[target]], p = 0.8, list = FALSE)
            train <- data[idx, ]
            formula <- as.formula(paste(target, "~ ."))
            mars_model <- earth(formula, data = train, degree = 2, trace = 1)
            print(summary(mars_model))
            all_coeffs <- mars_model$coefficients
            interaction_terms <- grep("\\\\*", rownames(all_coeffs), value = TRUE)
            interaction_coeffs <- all_coeffs[interaction_terms, , drop = FALSE]
            if (length(interaction_terms) == 0) {{
                return(data.frame(feature = character(0), coefficient = numeric(0), importance = numeric(0)))
            }}
            sorted_idx <- order(abs(interaction_coeffs[, 1]), decreasing = TRUE)
            sorted_interactions <- interaction_coeffs[sorted_idx, , drop = FALSE]
            
            interaction_table <- data.frame(
                feature = rownames(sorted_interactions),
                coefficient = sorted_interactions[, 1],
                stringsAsFactors = FALSE
            )
            rownames(interaction_table) <- seq_len(nrow(interaction_table))
            # 使用對數標準化來處理不同位數的係數
            abs_coeffs <- abs(interaction_table$coefficient)
            
            if (length(unique(abs_coeffs)) > 1) {{
                log_abs_coeffs <- log10(abs_coeffs + 1e-10)
                interaction_table$importance <- (log_abs_coeffs - min(log_abs_coeffs)) / 
                                              (max(log_abs_coeffs) - min(log_abs_coeffs))
                
                # 排除重要性過低的交互作用項
                important_threshold <- 0.001  # 可調整閾值
                interaction_table <- interaction_table[interaction_table$importance >= important_threshold, ]
                
                # 重新排序
                interaction_table <- interaction_table[order(interaction_table$importance, decreasing = TRUE), ]
                rownames(interaction_table) <- seq_len(nrow(interaction_table))
                
            }} else {{
                interaction_table$importance <- 1.0
            }}
            
            return(interaction_table)
        }}

        result_df <- try(mars_interaction_analysis(
            df = df,
            target = "{target}",
        ), silent = TRUE)

        # 檢查是否發生錯誤
        if (inherits(result_df, "try-error")) {{
            result_df <- data.frame(
                feature = character(0), 
                coefficient = numeric(0),
                importance = numeric(0)
            )
        }}
        '''
        # 執行 R 腳本
        ro.r(r_script)
        logger.info("MARS交互作用R腳本執行成功")

        # 取回結果
        result_df = ro.globalenv['result_df']
        interaction_df = pandas2ri.rpy2py(result_df)
        logger.info(f"MARS交互作用分析結果: {interaction_df}")

        # *** 只需要添加這一行 ***
        if 'feature' in interaction_df.columns:
            interaction_df['feature'] = interaction_df['feature'].apply(
                fix_chinese_encoding)

        # 整理結果
        interaction_terms = interaction_df.to_dict('records')
        logger.info(f"MARS交互作用分析結果: {interaction_terms}")

        return interaction_terms

    except Exception as e:
        logger.error(f"MARS交互作用分析失敗: {str(e)}")
        raise Exception(f"MARS交互作用分析失敗: {str(e)}")
