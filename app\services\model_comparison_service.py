import pandas as pd
import numpy as np
import logging
import time
from typing import Dict, Any, List, Tuple
from ..utils.ml_utils import importance_score, calculate_RI_K
from ..utils.date_utils import format_execution_time

logger = logging.getLogger(__name__)


def process_rf_xgb_reliability_index(
    rf_feature_importance: List[Dict[str, Any]],
    xgb_feature_importance: List[Dict[str, Any]],
    top_n: int = 5
) -> Dict[str, Any]:
    """
    計算 RandomForest 和 XGBoost 模型之間的可靠性指數

    Args:
        rf_feature_importance (List[Dict[str, Any]]): RandomForest 模型的特徵重要性列表
        xgb_feature_importance (List[Dict[str, Any]]): XGBoost 模型的特徵重要性列表
        top_n (int, optional): 考慮的頂部特徵數量。預設為 5

    Returns:
        Dict[str, Any]: 包含可靠性指數和共同特徵的結果
    """
    execution_times = {}
    total_start_time = time.time()

    try:
        # 將特徵重要性列表轉換為 DataFrame
        process_start_time = time.time()
        rf_df = pd.DataFrame(rf_feature_importance)
        xgb_df = pd.DataFrame(xgb_feature_importance)

        # 處理可能的空資料情況
        if rf_df.empty or xgb_df.empty:
            logger.warning("收到的特徵重要性列表為空")
            return {
                "reliability_index": 0.0,
                "common_features": [],
                "rf_importance_score": [],
                "xgb_importance_score": [],
                "execution_times": {"total": format_execution_time(time.time() - total_start_time)}
            }

        # 確保 DataFrame 包含必要的欄位
        if "Feature" not in rf_df.columns or "Importance" not in rf_df.columns:
            logger.error("RandomForest 特徵重要性缺少必要欄位: Feature 或 Importance")
            raise ValueError("RandomForest 特徵重要性格式錯誤")

        if "Feature" not in xgb_df.columns or "Importance" not in xgb_df.columns:
            logger.error("XGBoost 特徵重要性缺少必要欄位: Feature 或 Importance")
            raise ValueError("XGBoost 特徵重要性格式錯誤")

        # 獲取頂部特徵，確保不超過可用的特徵數量
        rf_top_n = rf_df.sort_values(
            by='Importance', ascending=False).head(min(top_n, len(rf_df)))
        xgb_top_n = xgb_df.sort_values(
            by='Importance', ascending=False).head(min(top_n, len(xgb_df)))

        # 計算重要性分數
        rf_importance_score = importance_score(rf_top_n).set_index('Feature')
        xgb_importance_score = importance_score(xgb_top_n).set_index('Feature')
        execution_times['data_processing'] = time.time() - process_start_time

        # 計算可靠性指數 K
        ri_start_time = time.time()
        ri_k, common_features = calculate_RI_K(
            rf_importance_score, xgb_importance_score)
        execution_times['ri_calculation'] = time.time() - ri_start_time

        # 計算總執行時間
        execution_times['total'] = time.time() - total_start_time

        # 格式化執行時間
        formatted_execution_times = {
            step: format_execution_time(duration)
            for step, duration in execution_times.items()
        }

        # 記錄執行時間
        for step, duration in formatted_execution_times.items():
            logger.info(f'RF-XGB可靠性指數評估 - {step} 執行時間: {duration}')

        # 如果沒有共同特徵，記錄警告
        if common_features.empty:
            logger.warning("兩個模型沒有共同的重要特徵")

        # 轉換 common_features 為列表以便 JSON 序列化
        common_features_list = common_features['Feature'].tolist(
        ) if not common_features.empty else []

        # 返回結果
        return {
            "reliability_index": float(ri_k),
            # "common_features": common_features_list,
            "rf_importance_score": rf_importance_score.reset_index().to_dict('records'),
            "xgb_importance_score": xgb_importance_score.reset_index().to_dict('records'),
            "execution_times": formatted_execution_times
        }

    except Exception as e:
        logger.error(f"RF-XGB可靠性指數評估失敗: {str(e)}")
        raise Exception(f"RF-XGB可靠性指數評估失敗: {str(e)}")


def importance_score(data):
    data_df = pd.DataFrame(data, columns=['Feature'])
    data_df['score'] = [1, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    data_df = final_score(data_df)
    data_df.reset_index(drop=True, inplace=True)
    return data_df


def final_score(df):
    feature_count = df.shape[0]
    df['final_score'] = [0] * feature_count
    df.reset_index(drop=False, inplace=True)
    for i in range(feature_count):
        if i < 3:
            df.loc[i, 'final_score'] = df['score'][i] * \
                0.8 / sum(df['score'][:3])
        else:
            df.loc[i, 'final_score'] = df['score'][i] * \
                0.2 / sum(df['score'][3:])

    df.set_index('index', inplace=True)
    return df


def calculate_RI_K(df1, df2):
    K = 0
    result = []
    for i in range(df1.shape[0]):
        if df1.index[i] in df2.index:
            result.append(df1.index[i])
            K += (df1['final_score'][i] + df2['final_score']
                  [df2.index.get_loc(df1.index[i])]) / 2
    result = pd.DataFrame(result, columns=['Feature'])
    return K, result


def comparison_algorithms(
    feature_importance_1: List[Dict[str, Any]],
    feature_importance_2: List[Dict[str, Any]],
) -> Dict[str, Any]:

    # 特徵信心指數
    importance_score_1 = importance_score(
        feature_importance_1).set_index('Feature')
    importance_score_2 = importance_score(
        feature_importance_2).set_index('Feature')

    result, result_list = calculate_RI_K(
        importance_score_1, importance_score_2)

    return result, result_list
