import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from typing import Dict, Any
import logging
import time
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time
from sklearn.metrics import recall_score
from imblearn.under_sampling import RandomUnderSampler
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder

logger = logging.getLogger(__name__)


def train_random_forest_category(X: pd.DataFrame, y: pd.Series, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 Random Forest 模型（加速版，保持格式與名稱不變）

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    rus = RandomUnderSampler(random_state=42)
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)

    rf_importances = np.zeros(X.shape[1])
    accuracy_scores_rf = []

    # 加速：減少迭代次數、減少樹數量、減少特徵數量（如有需要）、多核心
    for i in range(iterations):
        x_train, x_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=i, stratify=y
        )
        x_train_resampled, y_train_resampled = rus.fit_resample(
            x_train, y_train)

        rf_model = RandomForestClassifier(
            n_estimators=200,      # 減少樹數量，加快訓練
            min_samples_split=2,  # 增加分裂門檻 5，加快速度
            n_jobs=-1,            # 多核心加速
            random_state=42
        )
        rf_model.fit(x_train_resampled, y_train_resampled)
        rf_pred = rf_model.predict(x_test)
        recall_rf = recall_score(y_test, rf_pred, pos_label=1)
        accuracy_scores_rf.append(recall_rf)
        rf_importances += rf_model.feature_importances_ * recall_rf

    sum_accuracy = np.sum(accuracy_scores_rf)
    if sum_accuracy != 0:
        rf_importances /= sum_accuracy
    else:
        rf_importances = np.zeros_like(rf_importances)

    feature_names = X.columns
    rf_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': rf_importances, 'Model': 'Random Forest'})
    rf_top10 = rf_df.sort_values(by='Importance', ascending=False).head(10)
    logger.info(f"Random Forest 模型訓練完成，重要性前10特徵: {rf_top10}")

    return rf_top10.to_dict(orient='records')
