import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from typing import Dict, Any
import logging
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time
from sklearn.metrics import r2_score
from sklearn.model_selection import train_test_split

logger = logging.getLogger(__name__)


def train_random_forest_continuous(X: pd.DataFrame, y: pd.Series, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 Random Forest 連續型回歸模型（加速版，保持格式與名稱不變）

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標
        iterations (int): 訓練迭代次數

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    rf_importances = np.zeros(X.shape[1])
    r2_scores_rf = []

    for i in range(iterations):
        x_train, x_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=i
        )

        rf_model = RandomForestRegressor(
            n_estimators=200,      # 減少樹數量，加快訓練
            min_samples_split=2,   # 增加分裂門檻，加快速度
            n_jobs=-1,             # 多核心加速
            random_state=42
        )
        rf_model.fit(x_train, y_train)
        rf_pred = rf_model.predict(x_test)
        r2_rf = r2_score(y_test, rf_pred)
        r2_scores_rf.append(r2_rf)
        rf_importances += rf_model.feature_importances_  # 不加權

    rf_importances /= iterations  # 取平均

    feature_names = X.columns
    rf_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': rf_importances, 'Model': 'Random Forest'})
    rf_top10 = rf_df.sort_values(by='Importance', ascending=False).head(10)
    logger.info(f"Random Forest 模型訓練完成，重要性前10特徵: {rf_top10}")

    return rf_top10.to_dict(orient='records')
