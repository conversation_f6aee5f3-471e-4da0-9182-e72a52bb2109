import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from typing import Tuple, List, Dict, Any
import matplotlib.pyplot as plt
import seaborn as sns
import base64
from io import BytesIO
import logging
import time
from ..utils.date_utils import format_execution_time

logger = logging.getLogger(__name__)


def train_random_forest(X_train: pd.DataFrame, y_train: pd.Series) -> RandomForestRegressor:
    """
    訓練隨機森林模型

    Args:
        X_train (pd.DataFrame): 訓練特徵
        y_train (pd.Series): 訓練目標

    Returns:
        RandomForestRegressor: 訓練好的模型
    """
    # rf_model = RandomForestRegressor(
    #     n_estimators=100,
    #     max_depth=None,
    #     min_samples_split=2,
    #     min_samples_leaf=1,
    #     random_state=42
    # )
    rf_model = RandomForestRegressor(
        n_estimators=500, max_depth=6, random_state=40, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    return rf_model


def plot_feature_importance(imp_df: pd.DataFrame, title: str = "Feature Importance") -> str:
    """
    繪製特徵重要性圖並返回base64編碼的圖像

    Args:
        imp_df (pd.DataFrame): 包含特徵和重要性的數據框
        title (str): 圖表標題

    Returns:
        str: base64編碼的圖像
    """
    # 設置中文字型
    plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei',
                                       'Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    plt.figure(figsize=(10, 6))
    sns.barplot(data=imp_df.sort_values("Importance", ascending=True),
                x="Importance", y="Feature")
    plt.title(title)
    plt.xlabel("Importance Score")
    plt.ylabel("Features")

    buffer = BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close()

    return image_base64


def process_random_forest_analysis(train_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    執行隨機森林分析流程

    Args:
        train_data (Dict[str, Any]): 訓練資料字典，包含 X_train, X_test, y_train, y_test, feature_columns

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    execution_times = {}
    total_start_time = time.time()

    try:
        # 訓練模型
        model_start_time = time.time()
        logger.info(
            f"訓練隨機森林模型，訓練資料: {train_data['X_train']}, 訓練目標: {train_data['y_train']}")
        rf_model = train_random_forest(
            train_data['X_train'], train_data['y_train'])
        execution_times['model_training'] = time.time() - model_start_time

        # 計算特徵重要性
        importance_start_time = time.time()
        imp_rf = pd.DataFrame({
            "Feature": train_data['feature_columns'],
            "Importance": rf_model.feature_importances_
        })

        # 生成特徵重要性圖
        importance_plot = plot_feature_importance(
            imp_rf, "Random Forest Feature Importance")
        execution_times['importance_calculation'] = time.time() - \
            importance_start_time

        # 計算模型性能指標
        metrics_start_time = time.time()
        train_score = rf_model.score(
            train_data['X_train'], train_data['y_train'])
        test_score = rf_model.score(train_data['X_test'], train_data['y_test'])
        execution_times['metrics_calculation'] = time.time() - \
            metrics_start_time

        # 計算總執行時間
        execution_times['total'] = time.time() - total_start_time

        # 格式化執行時間
        formatted_execution_times = {
            step: format_execution_time(duration)
            for step, duration in execution_times.items()
        }

        # 記錄執行時間
        for step, duration in formatted_execution_times.items():
            logger.info(f'隨機森林分析 - {step} 執行時間: {duration}')

        # 整理結果
        result = {
            "feature_importance_mapping": imp_rf.set_index('Feature')['Importance'].to_dict(),
            "feature_importance": imp_rf.sort_values("Importance", ascending=False).to_dict('records'),
            "importance_plot": importance_plot,
            "model_performance": {
                "train_score": train_score,
                "test_score": test_score
            },
            "execution_times": formatted_execution_times
        }

        return result

    except Exception as e:
        logger.error(f"隨機森林分析失敗: {str(e)}")
        raise Exception(f"隨機森林分析失敗: {str(e)}")
