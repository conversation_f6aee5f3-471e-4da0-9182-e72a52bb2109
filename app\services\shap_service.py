import shap
import numpy as np
import pandas as pd
from typing import Dict, Any


def shap_continuous(model, X: pd.DataFrame) -> Dict[str, Any]:
    """
    SHAP summary_plot 所需資料格式。
    回傳每筆 SHAP 值、原始特徵值與排序後的特徵名稱清單。
    """
    explainer = shap.Explainer(model, X)
    shap_values = explainer(X)

    shap_array = shap_values.values  # shape: (n_samples, n_features)
    feature_array = X.values         # shape: (n_samples, n_features)
    feature_names = list(X.columns)

    # 按照 shap 值絕對值平均值排序
    mean_shap = np.abs(shap_array).mean(axis=0)
    sorted_idx = np.argsort(mean_shap)[::-1]  # 排序由大到小

    sorted_feature_names = [feature_names[i] for i in sorted_idx]
    sorted_shap_values = shap_array[:, sorted_idx].tolist()
    sorted_feature_values = feature_array[:, sorted_idx].tolist()

    return {
        "feature_names": sorted_feature_names,
        # shape: (n_samples, n_features)
        "shap_values": sorted_shap_values,
        # shape: (n_samples, n_features)
        "feature_values": sorted_feature_values
    }
