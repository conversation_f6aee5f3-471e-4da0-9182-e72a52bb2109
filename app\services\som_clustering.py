from minisom import MiniSom
from sklearn.preprocessing import LabelEncoder, StandardScaler
from scipy.ndimage import label
import numpy as np
import pandas as pd
from typing import Dict, Any, List
from fastapi.encoders import jsonable_encoder
import logging

logger = logging.getLogger(__name__)


def som_clustering_service(
    data_for_parameter: pd.DataFrame,
    feature_cols: List[str],
    target: str,
    map_size: int,
    sigma: float = 1.0,
    learning_rate: float = 0.5,
    training_iterations: int = 1000,
    umatrix_thresholds: List[float] = [0.45, 0.5, 0.55],
    normalize: bool = True,
    random_seed: int = 42
) -> Dict[str, Any]:
    """
    訓練 SOM 並根據多個 threshold 選擇最佳分群，回傳每個座標點的群集、資料數量、OOC比例
    """
    np.random.seed(random_seed)

    logger.info("Step 1: start data preparation")

    # 前處理
    clustering_data = data_for_parameter[feature_cols].copy()

    # Label encoding for all columns
    encoders = {}
    for col in clustering_data.columns:
        encoders[col] = LabelEncoder()
        clustering_data[col] = encoders[col].fit_transform(
            clustering_data[col].astype(str))

    scaler = StandardScaler()
    X = scaler.fit_transform(
        clustering_data) if normalize else clustering_data.values

    som = MiniSom(map_size, map_size, X.shape[1], sigma=sigma,
                  learning_rate=learning_rate, random_seed=random_seed)
    som.random_weights_init(X)

    logger.info("Step 2: start SOM training")
    som.train_batch(X, training_iterations, verbose=False)
    logger.info("SOM training done")

    logger.info("Step 3: calculating U-Matrix")
    umatrix = som.distance_map()
    logger.info("U-Matrix done")

    winners = np.array([som.winner(x) for x in X])

    best_result = None
    best_qe = float('inf')

    for th in umatrix_thresholds:
        logger.info(f"Trying threshold: {th}")
        binary_umatrix = umatrix > th
        labeled_array, num_clusters = label(binary_umatrix)
        if num_clusters < 2:
            continue  # 需至少兩群

        qe = np.mean([np.linalg.norm(x - som.get_weights()[bmu[0], bmu[1]])
                     for x, bmu in zip(X, winners)])
        if qe < best_qe:
            best_qe = qe
            best_result = {
                "threshold": th,
                "num_clusters": num_clusters,
                "labeled_array": labeled_array,
                "qe": qe
            }
        logger.info("Best threshold found")

    if best_result is None:
        return {
            "error": "無法找到合適的分群 threshold"
        }

    # 每個資料點的分群標籤
    cluster_labels = [int(best_result["labeled_array"][x, y])
                      for x, y in winners]
    df_result = data_for_parameter.copy()
    df_result["som_cluster"] = cluster_labels

    # 確保 ooc 欄位為 0/1 數值
    if target in df_result.columns:
        df_result[target] = df_result[target].apply(
            lambda x: 1 if str(x).lower() == "ooc" or str(x) == "1" else 0)

    # 每個座標點的群集、資料數量、OOC比例
    coord_info = {}
    count_map = np.zeros((map_size, map_size))
    ooc_count_map = np.zeros((map_size, map_size))

    for i, (x, y) in enumerate(winners):
        count_map[x, y] += 1
        if target in df_result.columns:
            ooc_val = df_result[target].iloc[i]
            ooc_count_map[x, y] += 1 if ooc_val == 1 else 0

    for x in range(map_size):
        for y in range(map_size):
            cluster_id = int(best_result["labeled_array"][x, y])
            count = int(count_map[x, y])
            ooc_ratio = float(
                ooc_count_map[x, y] / count) if count > 0 else None
            if ooc_ratio is None:
                ooc_ratio = 0.0

            coord_info[f"{x}_{y}"] = {
                "cluster": cluster_id,
                "count": count,
                "ooc_ratio": ooc_ratio
            }

    logger.info("All done, ready to return")
    return jsonable_encoder({
        "threshold": float(best_result["threshold"]),
        "qe": float(best_result["qe"]),
        "num_clusters": int(best_result["num_clusters"]),
        "coord_info": coord_info,
        "som_cluster": cluster_labels
    })
