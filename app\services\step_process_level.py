import re
import pandas as pd


def step_process_level(iym_df_table_pivot: pd.DataFrame):
    """
    取得製程水準

    Args:
        iym_df_table_pivot (pd.DataFrame): 資料

    Returns:
        List[Object]: 製程水準清單
    """
    # 取得所有 step_process 名稱
    step_processes = sorted(set(
        re.match(
            r'^(?:emp_no_|emp_name_|JOB_title_)?(\d+-[^_]+)', col).group(1)
        for col in iym_df_table_pivot.columns
        if re.match(r'^(?:emp_no_|emp_name_|JOB_title_)?\d+-', col)
    ))
    rows = []
    for step_proc in step_processes:
        # 站點
        site_col = step_proc if step_proc in iym_df_table_pivot.columns else None
        # 人員
        emp_col = f'emp_no_{step_proc}' if f'emp_no_{step_proc}' in iym_df_table_pivot.columns else None
        # 職級
        # job_col = f'JOB_title_{step_proc}' if f'JOB_title_{step_proc}' in iym_df_table_pivot.columns else None

        row = {'step_process': step_proc}
        if site_col:
            row['machine_levels'] = iym_df_table_pivot[site_col].nunique(
                dropna=True)
        else:
            row['machine_levels'] = None
        if emp_col:
            row['emp_no_levels'] = iym_df_table_pivot[emp_col].nunique(
                dropna=True)
        else:
            row['emp_no_levels'] = None
        # if job_col:
        #     row['JOB_title_levels'] = iym_df_table_pivot[job_col].nunique(dropna=True)
        # else:
        #     row['JOB_title_levels'] = None
        rows.append(row)

    step_process_summary = pd.DataFrame(rows)
    return step_process_summary.to_dict(orient='records')
