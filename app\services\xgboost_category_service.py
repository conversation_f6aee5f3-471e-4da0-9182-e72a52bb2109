import pandas as pd
import numpy as np
import xgboost as xgb
from typing import Dict, Any
import logging
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time
from sklearn.metrics import recall_score
from imblearn.under_sampling import RandomUnderSampler
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')


logger = logging.getLogger(__name__)


def train_xgboost_category(X: pd.DataFrame, y: pd.Series, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 XGBoost 模型（加速訓練，不用 early stopping）

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標
        iterations (int): 訓練迭代次數

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    rus = RandomUnderSampler(random_state=42)
    xgb_importances = np.zeros(X.shape[1])
    accuracy_scores_xgb = []
    logger.info(f"訓練 XGBoost 模型，迭代次數: {iterations}")
    for i in range(iterations):
        x_train, x_test, y_train, y_test = train_test_split(
            X.astype('float32'), y, test_size=0.2, random_state=i, stratify=y
        )
        x_train_resampled, y_train_resampled = rus.fit_resample(
            x_train, y_train)

        xgb_params = dict(
            eval_metric='logloss',
            random_state=42,
            n_estimators=1200,           # 減少樹數量，加快訓練
            learning_rate=0.01,        # 提高學習率，加快收斂
            max_depth=10,               # 減少樹深，加快訓練
            tree_method='hist',
            n_jobs=-1,
            use_label_encoder=False
        )

        xgb_model = xgb.XGBClassifier(**xgb_params)
        xgb_model.fit(
            x_train_resampled, y_train_resampled,
            eval_set=[(x_test, y_test)],
            verbose=False
        )
        xgb_pred = xgb_model.predict(x_test)
        recall_xgb = recall_score(y_test, xgb_pred, pos_label=1)
        accuracy_scores_xgb.append(recall_xgb)
        xgb_importances += xgb_model.feature_importances_ * recall_xgb

    sum_accuracy = np.sum(accuracy_scores_xgb)
    if sum_accuracy != 0:
        xgb_importances /= sum_accuracy
    else:
        xgb_importances = np.zeros_like(xgb_importances)

    feature_names = X.columns
    xgb_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': xgb_importances, 'Model': 'XGBoost'})
    xgb_top10 = xgb_df.sort_values(by='Importance', ascending=False).head(10)
    logger.info(f"XGBoost 模型訓練完成，重要性前10特徵: {xgb_top10}")
    return xgb_top10.to_dict(orient='records')
