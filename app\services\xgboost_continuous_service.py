import pandas as pd
import numpy as np
import xgboost as xgb
from typing import Dict, Any
import logging
from matplotlib.font_manager import FontProperties
from ..utils.date_utils import format_execution_time
from sklearn.metrics import r2_score
from sklearn.model_selection import train_test_split

import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


def xgboost_continuous(X: pd.DataFrame, y: pd.Series, iterations: int = 100) -> Dict[str, Any]:
    """
    訓練 XGBoost 連續型回歸模型（加速訓練，不用 early stopping）

    Args:
        X (pd.DataFrame): 訓練特徵
        y (pd.Series): 訓練目標
        iterations (int): 訓練迭代次數

    Returns:
        Dict[str, Any]: 分析結果，包含特徵重要性和模型性能指標
    """
    if isinstance(y, pd.DataFrame):
        y = y.iloc[:, 0]
    y = np.asarray(y).reshape(-1)

    xgb_importances = np.zeros(X.shape[1])
    r2_scores_xgb = []
    logger.info(f"訓練 XGBoost 模型，迭代次數: {iterations}")
    for i in range(iterations):
        x_train, x_test, y_train, y_test = train_test_split(
            X.astype('float32'), y, test_size=0.2, random_state=i
        )

        xgb_params = dict(
            eval_metric='rmse',
            random_state=42,
            n_estimators=500,
            learning_rate=0.05,
            max_depth=5,
            tree_method='hist',
            n_jobs=-1
        )

        xgb_model = xgb.XGBRegressor(**xgb_params)
        xgb_model.fit(
            x_train, y_train,
            eval_set=[(x_test, y_test)],
            verbose=False
        )
        xgb_pred = xgb_model.predict(x_test)
        r2 = r2_score(y_test, xgb_pred)
        r2_scores_xgb.append(r2)
        xgb_importances += xgb_model.feature_importances_  # 不加權

    xgb_importances /= iterations  # 取平均

    feature_names = X.columns
    xgb_df = pd.DataFrame(
        {'Feature': feature_names, 'Importance': xgb_importances, 'Model': 'XGBoost'})
    xgb_top10 = xgb_df.sort_values(by='Importance', ascending=False).head(10)
    logger.info(f"XGBoost 模型訓練完成，重要性前10特徵: {xgb_top10}")
    return xgb_top10.to_dict(orient='records')
