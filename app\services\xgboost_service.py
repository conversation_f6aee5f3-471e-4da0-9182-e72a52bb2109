import pandas as pd
import numpy as np
import xgboost as xgb
from typing import Dict, Any
import logging
import time
from ..utils.date_utils import format_execution_time

logger = logging.getLogger(__name__)


def is_gpu_available() -> bool:
    """
    檢測系統是否有可用的GPU
    """
    try:
        X = np.random.random((10, 5)).astype('float32')
        y = np.random.random(10).astype('float32')
        test_model = xgb.XGBRegressor(tree_method='gpu_hist')
        test_model.fit(X, y)
        return True
    except Exception as e:
        logger.error(f"檢測 GPU 是否可用時發生錯誤: {str(e)}")
        return False


def train_xgboost(X_train: pd.DataFrame, y_train: pd.Series) -> xgb.XGBRegressor:
    """
    訓練 XGBoost 模型（自動偵測GPU並加速）
    """
    X_train = X_train.astype('float32')
    if not isinstance(y_train, (int, float)):
        y_train = y_train.astype('float32')
    use_gpu = is_gpu_available()
    logger.info(f"使用 GPU: {use_gpu}")
    xgb_model = xgb.XGBRegressor(
        n_estimators=100,           # 減少樹數量
        learning_rate=0.1,          # 提高學習率
        max_depth=4,                # 減少樹深
        subsample=0.8,
        colsample_bytree=0.8,
        tree_method='gpu_hist' if use_gpu else 'hist',
        random_state=40,
        n_jobs=-1
    )
    xgb_model.fit(X_train, y_train)
    logger.info(f"XGBoost 模型訓練完成")
    return xgb_model


def process_xgboost_analysis(train_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    執行 XGBoost 分析流程
    """
    execution_times = {}
    total_start_time = time.time()

    try:
        # 1. 訓練模型
        model_start_time = time.time()
        logger.info(
            f"訓練 XGBoost 模型，訓練資料: {train_data['X_train']}, 訓練目標: {train_data['y_train']}")
        xgb_model = train_xgboost(train_data['X_train'], train_data['y_train'])
        execution_times['model_training'] = time.time() - model_start_time

        # 2. 計算特徵重要性
        importance_start_time = time.time()
        imp_xgb = pd.DataFrame({
            "Feature": train_data['feature_columns'],
            "Importance": xgb_model.feature_importances_
        })
        execution_times['importance_calculation'] = time.time() - \
            importance_start_time

        # 3. 計算模型性能指標
        metrics_start_time = time.time()
        train_score = xgb_model.score(
            train_data['X_train'], train_data['y_train'])
        test_score = xgb_model.score(
            train_data['X_test'], train_data['y_test'])
        execution_times['metrics_calculation'] = time.time() - \
            metrics_start_time

        # 計算總執行時間
        execution_times['total'] = time.time() - total_start_time

        # 格式化執行時間
        formatted_execution_times = {
            step: format_execution_time(duration)
            for step, duration in execution_times.items()
        }

        # 記錄執行時間
        for step, duration in formatted_execution_times.items():
            logger.info(f'XGBoost分析 - {step} 執行時間: {duration}')

        # 整理結果（保持原格式與變數名稱）
        result = {
            "feature_importance_mapping": imp_xgb.set_index('Feature')['Importance'].to_dict(),
            "feature_importance": imp_xgb.sort_values("Importance", ascending=False).to_dict('records'),
            "importance_plot": None,  # 不再產生圖
            "model_performance": {
                "train_score": train_score,
                "test_score": test_score
            },
            "execution_times": formatted_execution_times
        }

        return result

    except Exception as e:
        logger.error(f"XGBoost分析失敗: {str(e)}")
        raise Exception(f"XGBoost分析失敗: {str(e)}")
