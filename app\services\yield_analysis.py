from typing import List, Dict, Any, Optional
from ..utils.database import execute_sql_query, pandas_sql_query
from ..models.response import YieldAnalysisResult
import logging

logger = logging.getLogger(__name__)


def yield_analysis(
    start_date: str,
    end_date: str,
    part_no: str,
) -> List[YieldAnalysisResult]:
    """執行良率分析
    Args:
        start_date (str): 開始日期
        end_date (str): 結束日期
        part_no (str): 品目

    Returns:
        List[YieldAnalysisResult]: 良率分析結果
    """

    # 查詢不良品統計
    defect_sql = f"""
    SELECT
        不良大項代碼,
        sum(不良PCS數) as defect_pcs
    FROM qms_station_defect_detail
    WHERE 品目 = '{part_no}'
        and 大站名稱 in ('AOI 工程','RTR 線檢 AOI')
        and 站點名稱 != 'AOI 完工站'
        and 登錄日期 between '{start_date}' and '{end_date}'
    GROUP BY 不良大項代碼
    ORDER BY defect_pcs DESC
    """

    try:
        # 執行查詢
        defect_details = execute_sql_query(
            defect_sql, as_dict=True, db="QMSDB")

        # 計算總不良數
        total_defects = sum(item['defect_pcs'] for item in defect_details)

        # 計算累積量和累積百分比
        cumulative_count = 0
        yield_results = []

        for item in defect_details:
            cumulative_count += item['defect_pcs']
            cumulative_percentage = (
                cumulative_count / total_defects * 100) if total_defects > 0 else 0

            yield_results.append(
                YieldAnalysisResult(
                    不良大項代碼=item['不良大項代碼'],
                    defect_pcs=item['defect_pcs'],
                    cumulative_count=cumulative_count,
                    cumulative_percentage=round(cumulative_percentage, 2)
                )
            )

        return yield_results

    except Exception as e:
        logger.error(f"良率分析失敗: {str(e)}")
        raise Exception(f"良率分析失敗: {str(e)}")
