import pandas as pd
import os
import logging

logger = logging.getLogger(__name__)


def read_local_csv(filepath: str, encoding: str = "utf-8") -> pd.DataFrame:
    """
    讀取本地CSV檔案

    Args:
        filepath (str): CSV檔案的完整路徑
        encoding (str): 檔案編碼，預設為utf-8

    Returns:
        pd.DataFrame: 讀取後的資料表

    Raises:
        FileNotFoundError: 若檔案不存在
        pd.errors.ParserError: 若CSV格式錯誤
    """
    if not os.path.isfile(filepath):
        logger.error(f"找不到檔案: {filepath}")
        raise FileNotFoundError(f"找不到檔案: {filepath}")
    try:
        df = pd.read_csv(filepath, encoding=encoding)
        logger.info(f"讀取CSV成功: {filepath}")
        return df
    except Exception as e:
        logger.error(f"讀取CSV失敗: {str(e)}")
        raise RuntimeError(f"讀取CSV失敗: {str(e)}")
