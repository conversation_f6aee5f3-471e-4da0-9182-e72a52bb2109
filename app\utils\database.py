from typing import List, Dict, Any, Optional
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session
from ..config import settings
import logging

logger = logging.getLogger(__name__)

def get_db_engine(db: str = "QMSDB") -> Engine:
    """獲取資料庫引擎
    
    Args:
        db (str, optional): 資料庫名稱，可選值為 "QMSDB" 或 "KSTIDB". Defaults to "QMSDB".
    
    Returns:
        Engine: 資料庫引擎
    """
    if db == "QMSDB":
        return create_engine(settings.QMSDB_URL)
    elif db == "KSTIDB":
        return create_engine(settings.KSTIDB_URL)
    elif db == "KHSTARROCKS":
        return create_engine(settings.KHSTARROCKS_URL)
    else:
        logger.error(f"不支援的資料庫: {db}，僅支援 QMSDB 或 KSTIDB")
        raise ValueError(f"不支援的資料庫: {db}，僅支援 QMSDB 或 KSTIDB")

def get_db_session(db: str = "QMSDB") -> Session:
    """獲取資料庫會話
    
    Args:
        db (str, optional): 資料庫名稱，可選值為 "QMSDB" 或 "KSTIDB". Defaults to "QMSDB".
    
    Returns:
        Session: 資料庫會話
    """
    engine = get_db_engine(db)
    return Session(engine)

def execute_sql_query(sql: str, as_dict: bool = False, db: str = "QMSDB") -> List[Dict[str, Any]]:
    """執行 SQL 查詢
    
    Args:
        sql (str): SQL 查詢語句
        as_dict (bool, optional): 是否返回字典格式. Defaults to False.
        db (str, optional): 資料庫名稱，可選值為 "QMSDB" 或 "KSTIDB". Defaults to "QMSDB".
    
    Returns:
        List[Dict[str, Any]]: 查詢結果
    """
    try:
        logger.info(f"執行 SQL 查詢，資料庫: {db}!")
        with get_db_session(db) as session:
            result = session.execute(text(sql))
            if as_dict:
                return [dict(row._mapping) for row in result]
            return result.fetchall()
    except Exception as e:
        logger.error(f"資料庫查詢錯誤: {str(e)}")
        raise Exception(f"資料庫查詢錯誤: {str(e)}")

def pandas_sql_query(sql: str, db: str = "QMSDB") -> pd.DataFrame:
    """使用 pandas 執行 SQL 查詢
    
    Args:
        sql (str): SQL 查詢語句
        db (str, optional): 資料庫名稱，可選值為 "QMSDB" 或 "KSTIDB". Defaults to "QMSDB".
    
    Returns:
        pd.DataFrame: 查詢結果的 DataFrame
    """
    logger.info(f"執行 SQL 查詢，資料庫: {db}!")
    try:
        engine = get_db_engine(db)
        return pd.read_sql_query(sql, engine)
    except Exception as e:
        logger.error(f"Pandas 資料庫查詢錯誤: {str(e)}")
        raise Exception(f"Pandas 資料庫查詢錯誤: {str(e)}") 