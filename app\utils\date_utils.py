from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def is_valid_date(date_string: str, date_format: str = '%Y-%m-%d') -> bool:
    """驗證日期字串是否符合指定格式
    
    Args:
        date_string (str): 要驗證的日期字串
        date_format (str, optional): 日期格式. 預設為 '%Y-%m-%d'
        
    Returns:
        bool: 日期格式是否有效
    """
    try:
        datetime.strptime(date_string, date_format)
        return True
    except ValueError as e:
        logger.error(f"日期格式驗證錯誤: {str(e)}")
        return False 

def format_execution_time(seconds: float) -> str:
    """將秒數轉換為 HH:mm:ss.SSS 格式
    
    Args:
        seconds (float): 執行時間（秒）
        
    Returns:
        str: 格式化後的時間字串，格式為 HH:mm:ss.SSS
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}" 