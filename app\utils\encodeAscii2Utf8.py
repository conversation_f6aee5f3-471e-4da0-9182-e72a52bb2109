import re


def fix_chinese_encoding(text):
    """簡單修復中文編碼問題"""
    if not isinstance(text, str):
        return str(text)

    # 將 \351\273\236 這樣的八進制轉換為中文
    def replace_octal(match):
        try:
            return chr(int(match.group(1), 8))
        except:
            return match.group(0)

    # 處理 \\351 和 \351 兩種格式
    result = re.sub(r'\\\\(\d{3})', replace_octal, text)
    result = re.sub(r'\\(\d{3})', replace_octal, result)

    # 嘗試修復編碼
    try:
        result = result.encode('latin1').decode('utf-8')
    except:
        pass

    return result
