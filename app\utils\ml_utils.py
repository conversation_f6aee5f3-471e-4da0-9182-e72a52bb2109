from typing import List, Tuple, Dict
import pandas as pd
import numpy as np
from typing import List, Dict, <PERSON><PERSON>


def final_score(df):
    """
    計算特徵的最終得分，基於其重要性分數
    前3個特徵佔80%的權重，其餘特徵佔20%的權重。
    若特徵數量少於3個，則平均分配80%的權重。

    Args:
        df (pd.DataFrame): 包含特徵和得分的數據框

    Returns:
        pd.DataFrame: 添加了final_score列的數據框
    """
    df['final_score'] = [0] * df.shape[0]
    df.reset_index(drop=False, inplace=True)

    total_features = df.shape[0]

    # 如果特徵數量少於3個，則每個特徵都被視為"前幾名"
    if total_features <= 3:
        for i in range(total_features):
            df.loc[i, 'final_score'] = df['score'][i] / \
                sum(df['score'][:total_features])
    else:
        # 前3個特徵佔80%的權重
        for i in range(3):
            df.loc[i, 'final_score'] = df['score'][i] * \
                0.8 / sum(df['score'][:3])

        # 其餘特徵佔20%的權重
        if total_features > 3:
            for i in range(3, total_features):
                df.loc[i, 'final_score'] = df['score'][i] * \
                    0.2 / sum(df['score'][3:])

    df.set_index('index', inplace=True)
    return df


def importance_score(data):
    """
    計算特徵重要性分數

    Args:
        data (pd.DataFrame): 包含Feature和Importance的數據框

    Returns:
        pd.DataFrame: 添加了score和final_score的數據框
    """
    data_df = data.copy()
    # 如果只有一列，假設是Feature列，則需要將其轉換為DataFrame
    if isinstance(data, list):
        data_df = pd.DataFrame(data, columns=['Feature'])

    # 線性分配權重從1到0.5
    data_df['score'] = np.linspace(1, 0.5, len(data_df))

    # 計算最終得分
    data_df = final_score(data_df)
    data_df.reset_index(drop=True, inplace=True)
    return data_df


def calculate_RI_K(df1, df2):
    """
    計算可靠性指數K和共同特徵

    Args:
        df1 (pd.DataFrame): 第一個模型的特徵重要性DataFrame，帶有final_score列，以Feature為索引
        df2 (pd.DataFrame): 第二個模型的特徵重要性DataFrame，帶有final_score列，以Feature為索引

    Returns:
        tuple: (K值, 共同特徵DataFrame)
    """
    K = 0.0
    result = []

    for feature in df1.index:
        if feature in df2.index:
            result.append(feature)
            K += (df1.loc[feature, 'final_score'] +
                  df2.loc[feature, 'final_score']) / 2

    result_df = pd.DataFrame(result, columns=['Feature'])
    return K, result_df


def calculate_control_limits(df, target_col='Height', std_multiplier=3):
    """
    計算管制界線並標註數據點的狀態

    參數:
    df: 原始DataFrame
    target_col: 要計算管制界線的目標欄位
    std_multiplier: 標準差倍數，預設為3

    回傳:
    添加control_status欄位的DataFrame
    """
    # 複製原始資料
    df_result = df.copy()

    # 先移除離群值以計算管制界線
    df_clean = remove_outliers(df, [target_col])

    # 計算平均值和標準差
    mean = df_clean[target_col].mean()
    std = df_clean[target_col].std()

    # 計算管制界線
    ucl = mean + std_multiplier * std  # 上管制界線
    lcl = mean - std_multiplier * std  # 下管制界線

    # 標註每個數據點的狀態
    conditions = [
        (df_result[target_col] > ucl),
        (df_result[target_col] < lcl),
        (df_result[target_col].between(lcl, ucl))
    ]
    choices = ['upper', 'lower', 'in_control']

    df_result['control_status'] = np.select(conditions, choices)

    return df_result


def remove_outliers(df, columns):
    """
    使用1.5倍IQR方法移除離群值

    參數:
    df: 要處理的DataFrame
    columns: 要移除離群值的欄位名稱列表

    回傳:
    移除離群值後的DataFrame
    """
    df_clean = df.copy()

    for col in columns:
        df_clean[col] = df_clean[col].astype(float)
        # 計算Q1、Q3和IQR
        Q1 = df_clean[col].quantile(0.25)
        Q3 = df_clean[col].quantile(0.75)
        IQR = Q3 - Q1

        # 設定上下界
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        # 移除離群值
        df_clean = df_clean[(df_clean[col] >= lower_bound)
                            & (df_clean[col] <= upper_bound)]

    return df_clean


def target_encode(
    df: pd.DataFrame,
    target_column: str,
    categorical_columns: List[str],
    smooth: int = 10
) -> Tuple[pd.DataFrame, Dict[str, Dict]]:
    """
    對類別特徵進行目標編碼，可選擇是否平滑處理

    Args:
        df (pd.DataFrame): 輸入數據框
        target_column (str): 目標變量列名
        categorical_columns (List[str]): 需要編碼的類別特徵列名列表
        smooth (int): 平滑參數，若設為0表示無平滑

    Returns:
        Tuple[pd.DataFrame, Dict]: 編碼後的數據框和每個欄位對應的編碼映射字典
    """
    df_encoded = df.copy()
    global_mean = df[target_column].mean()
    encoding_dict = {}

    for col in categorical_columns:
        stats = df.groupby(col)[target_column].agg(['mean', 'count'])
        if smooth > 0:
            stats['smooth_mean'] = (
                stats['mean'] * stats['count'] + global_mean * smooth) / (stats['count'] + smooth)
            mapping = stats['smooth_mean']
        else:
            mapping = stats['mean']

        df_encoded[col] = df[col].map(mapping).fillna(global_mean)
        encoding_dict[col] = mapping.to_dict()

    return df_encoded, encoding_dict
