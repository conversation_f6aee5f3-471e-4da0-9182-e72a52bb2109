import redis
import json
import functools
import hashlib
from typing import Callable, Optional
from fastapi.encoders import jsonable_encoder
from ..config import settings
import logging

logger = logging.getLogger(__name__)


class RedisClient:
    _connection = None

    @classmethod
    def get_connection(cls):
        """獲取 Redis 連接實例"""
        if cls._connection is None:
            cls._connection = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True
            )
        return cls._connection

    def __init__(self, key):
        """初始化 Redis 客戶端

        Args:
            key (str): Redis key，如 f"xgb_cat:{part_no}_{work_order}"
        """
        self._redis = self.get_connection()
        self._key = f"{settings.REDIS_CACHE_PREFIX}:{key}"
        logger.debug(f"Redis 客戶端初始化! key: {self._key}")

    def get(self, default=None):
        """獲取數據"""
        try:
            data = self._redis.get(self._key)
            logger.debug(f"Redis 讀取資料成功 key: {self._key}, data: {data}")
            return json.loads(data) if data else default
        except Exception as e:
            logger.error(f"Redis 讀取錯誤: {str(e)}")
            return default

    def set(self, value, expire=86400):
        """存儲數據 預設過期時間為 86400 秒 (24 小時)"""
        try:
            encoded = jsonable_encoder(value)
            self._redis.set(self._key, json.dumps(
                encoded, ensure_ascii=False), ex=expire)
            logger.debug(f"Redis 寫入資料成功 key: {self._key}, value: {encoded}")
            return True
        except Exception as e:
            logger.error(f"Redis 寫入錯誤: {str(e)}")
            return False

    def delete(self):
        """刪除數據"""
        try:
            self._redis.delete(self._key)
            logger.info(f"Redis 刪除資料成功 key: {self._key}")
            return True
        except Exception as e:
            logger.error(f"Redis 刪除錯誤: {str(e)}")
            return False

    @classmethod
    def cached_body_api(
        cls,
        expire: int = 86400,
        cache_key_prefix: Optional[str] = None
    ):
        """
        快取裝飾器 - 使用通用雜湊生成唯一快取鍵

        Args:
            expire: 快取過期時間（秒），預設 24 小時
            cache_key_prefix: 快取鍵前綴，預設使用函數名稱

        使用範例:
            @RedisClient.cached_body_api(expire=3600, cache_key_prefix="mean_api")
            async def mean_analysis(request: dict = Body(...)):
                # 業務邏輯...
                pass
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                # 從參數中提取 request
                request = cls._extract_request_from_args(args, kwargs)

                if not request:
                    logger.warning(f"無法從參數中提取 request，跳過快取: {func.__name__}")
                    return await func(*args, **kwargs)

                # 檢查是否使用快取（預設使用快取，設置 use_redis=False 只會跳過讀取快取）
                use_redis = request.get('use_redis', True)

                # 生成快取鍵
                cache_key = cls._generate_hash_cache_key(
                    func.__name__,
                    request,
                    cache_key_prefix
                )

                # 使用 RedisClient 實例
                redis_client = cls(cache_key)

                # 如果允許使用快取，嘗試獲取快取的結果
                if use_redis:
                    cached_result = redis_client.get()
                    if cached_result is not None:
                        logger.info(
                            f"Redis 快取命中: {func.__name__} - {cache_key}")
                        return cached_result
                else:
                    logger.info(f"跳過讀取 Redis 快取: {func.__name__}")

                # 執行原函數
                logger.info(f"執行函數並快取結果: {func.__name__} - {cache_key}")
                result = await func(*args, **kwargs)

                # 無論是否使用快取，都儲存結果到快取中
                redis_client.set(result, expire=expire)

                return result
            return wrapper
        return decorator

    @staticmethod
    def _extract_request_from_args(args, kwargs) -> Optional[dict]:
        """從參數中提取 request 物件，並對字典中的列表進行排序

        Returns:
            Optional[dict]: 處理後的請求字典，只有純值列表類型會被排序
        """
        def sort_lists_in_dict(d: dict) -> dict:
            """遞迴處理字典中的列表，只對純值列表進行排序"""
            result = {}
            for key, value in d.items():
                if isinstance(value, list):
                    # 檢查列表是否只包含基本類型（str、int、float等）
                    if all(isinstance(x, (str, int, float, bool)) for x in value):
                        result[key] = sorted(value)
                    else:
                        # 如果列表包含複雜類型（如字典），保持原始順序
                        result[key] = value
                elif isinstance(value, dict):
                    result[key] = sort_lists_in_dict(value)
                else:
                    result[key] = value
            return result

        # 方法1: 從 kwargs 中找到 dict 類型的參數
        for key, value in kwargs.items():
            if isinstance(value, dict):
                return sort_lists_in_dict(value)

        # 方法2: 從 args 中找到 dict 類型的參數
        for arg in args:
            if isinstance(arg, dict):
                return sort_lists_in_dict(arg)

        return None

    @staticmethod
    def _generate_hash_cache_key(
        func_name: str,
        request: dict,
        cache_key_prefix: Optional[str] = None
    ) -> str:
        """
        生成雜湊快取鍵 - 確保唯一性和安全性

        Args:
            func_name: 函數名稱
            request: 請求參數字典
            cache_key_prefix: 自定義前綴

        Returns:
            生成的快取鍵，格式: {prefix}_{hash}
        """
        try:
            # 使用提供的前綴或函數名稱
            prefix = cache_key_prefix if cache_key_prefix else func_name

            # 移除 use_redis 參數，因為它不應該影響快取鍵
            request_for_hash = {k: v for k,
                                v in request.items() if k != 'use_redis'}

            # 生成一致的 JSON 字符串
            # sort_keys=True 確保相同內容產生相同雜湊
            # ensure_ascii=False 正確處理中文等 Unicode 字符
            # separators=(',', ':') 移除額外空格，使雜湊更緊湊
            request_str = json.dumps(
                request_for_hash,
                sort_keys=True,
                ensure_ascii=False,
                separators=(',', ':')
            )

            # 使用 SHA-256 生成雜湊（比 MD5 更安全，碰撞風險更低）
            request_hash = hashlib.sha256(
                request_str.encode('utf-8')).hexdigest()

            # 使用前16位雜湊值（碰撞風險仍然極低，但鍵更短）
            short_hash = request_hash[:16]

            cache_key = f"{prefix}_{short_hash}"

            # 記錄詳細資訊用於除錯（僅在 DEBUG 模式）
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"快取鍵生成 - 函數: {func_name}")
                logger.debug(f"原始參數: {request_for_hash}")
                logger.debug(f"JSON字符串: {request_str}")
                logger.debug(f"生成快取鍵: {cache_key}")

            return cache_key

        except Exception as e:
            # 如果雜湊生成失敗，使用備用方案
            logger.warning(f"快取鍵生成失敗，使用備用方案: {e}")
            fallback_key = f"{func_name}_fallback_{hash(str(request))}"
            return fallback_key

    @classmethod
    def monitor_redis_health(cls):
        """監控 Redis 健康狀態"""
        try:
            redis_conn = cls.get_connection()
            info = redis_conn.info()

            health_info = {
                'redis_version': info.get('redis_version'),
                'uptime_in_seconds': info.get('uptime_in_seconds'),
                'connected_clients': info.get('connected_clients'),
                'used_memory_human': info.get('used_memory_human'),
                'maxmemory_human': info.get('maxmemory_human', 'unlimited'),
                'maxmemory_policy': info.get('maxmemory_policy'),
                'evicted_keys': info.get('evicted_keys', 0),  # 重要：被淘汰的 key 數量
                'expired_keys': info.get('expired_keys', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0)
            }

            # 檢查是否有記憶體壓力
            if health_info['evicted_keys'] > 0:
                logger.warning(
                    f"Redis 已淘汰 {health_info['evicted_keys']} 個 key！")

            logger.info(f"Redis 健康狀態: {health_info}")
            return health_info

        except Exception as e:
            logger.error(f"Redis 健康檢查失敗: {str(e)}")
            return None

    @classmethod
    def clear_cache_by_pattern(cls, pattern: str):
        """
        根據模式清除快取

        Args:
            pattern: Redis key 模式，如 "mean_api_*"

        Example:
            RedisClient.clear_cache_by_pattern("mean_api_*")
        """
        try:
            redis_conn = cls.get_connection()
            full_pattern = f"{settings.REDIS_CACHE_PREFIX}:{pattern}"
            keys = redis_conn.keys(full_pattern)

            if keys:
                deleted_count = redis_conn.delete(*keys)
                logger.info(f"清除快取成功，刪除 {deleted_count} 個鍵，模式: {full_pattern}")
                return deleted_count
            else:
                logger.info(f"沒有找到匹配的快取鍵，模式: {full_pattern}")
                return 0

        except Exception as e:
            logger.error(f"清除快取失敗: {str(e)}")
            return 0

    @classmethod
    def get_cache_info(cls, pattern: str = "*"):
        """
        獲取快取資訊

        Args:
            pattern: Redis key 模式，預設為所有鍵

        Returns:
            dict: 包含快取統計資訊
        """
        try:
            redis_conn = cls.get_connection()
            full_pattern = f"{settings.REDIS_CACHE_PREFIX}:{pattern}"
            keys = redis_conn.keys(full_pattern)

            cache_info = {
                "total_keys": len(keys),
                "keys": keys[:10] if len(keys) > 10 else keys,  # 只顯示前10個
                "pattern": full_pattern
            }

            if len(keys) > 10:
                cache_info["note"] = f"只顯示前10個鍵，總共有 {len(keys)} 個"

            return cache_info

        except Exception as e:
            logger.error(f"獲取快取資訊失敗: {str(e)}")
            return {"error": str(e)}
