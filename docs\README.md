# 專案管理模組

## 模組目的

專案管理模組用於管理系統中的專案，包括創建、編輯、刪除和查看專案。此模組提供了卡片和表格兩種視圖模式，方便用戶以不同方式瀏覽專案列表。

## 目錄結構

```
frontend/src/views/projects/
├── README.md                 # 本文檔
├── index.vue                 # 專案列表主視圖
├── detail.vue                # 專案詳情視圖
└── components/               # 元件目錄
    └── ProjectTable.vue      # 專案表格視圖元件
```

## 元件說明

### index.vue

專案列表主視圖，負責：

- 顯示專案列表（支援卡片和表格兩種視圖模式）
- 提供篩選功能（按狀態篩選）
- 提供新增、編輯、刪除專案的功能
- 包含卡片視圖的實現
- 整合表格視圖元件

### detail.vue

專案詳情視圖，負責顯示單個專案的詳細資訊和相關操作。

### ProjectTable.vue

專案表格視圖元件，負責：

- 以表格形式展示專案列表
- 顯示專案基本資訊（名稱、描述、狀態、創建時間、創建者）
- 提供查看、編輯、刪除專案的操作按鈕

## 視圖模式

本模組支援兩種視圖模式：

### 卡片視圖

- 以卡片形式展示專案，每個卡片包含專案的基本資訊
- 提供視覺化的狀態標識（不同顏色的頂部邊框）
- 支援卡片懸停效果和動畫
- 包含新增專案的卡片

### 表格視圖

- 以表格形式展示專案，每行代表一個專案
- 提供更緊湊的資訊展示方式
- 適合需要快速瀏覽多個專案的場景

## 使用方式

在主視圖中，用戶可以：

1. 使用頂部的篩選下拉選單按狀態篩選專案
2. 點擊視圖切換按鈕在卡片和表格視圖之間切換
3. 點擊「新增專案」按鈕創建新專案
4. 點擊專案卡片或表格中的「查看」按鈕查看專案詳情
5. 管理員可以編輯和刪除專案

## 功能改進

本模組的主要改進包括：

1. **視圖模式切換**：新增卡片和表格兩種視圖模式，提升用戶體驗
2. **元件拆分**：將表格視圖拆分為獨立元件，提高代碼可維護性
3. **代碼組織優化**：使用 Vue 3 Composition API 和 `<script setup>` 語法，提高代碼可讀性
4. **命名規範**：遵循最佳實踐，使用 `handle` 前綴命名事件處理函數

## 未來改進

1. 增強搜索功能，支援按名稱、描述等字段搜索
2. 增加更多排序選項
3. 支援批量操作（批量刪除、批量更新狀態等）
4. 增加專案標籤功能
5. 優化移動端顯示效果
