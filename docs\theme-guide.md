# 主題色彩系統使用指南

## 基本概念

我們的專案已經實現了一個基於 CSS 變數的主題色彩系統，使得在整個應用中統一管理和修改顏色變得更加容易。這個系統主要由兩部分組成：

1. **CSS 變數的定義**：在 `style.css` 中定義全局顏色變數
2. **使用方式**：通過 Tailwind 類別或直接引用變數來應用顏色

## 顏色變數

我們定義了以下核心顏色變數：

```css
:root {
  /* 全局顏色變數 */
  --color-light-bg: #ffffff; /* 亮色模式下的背景顏色 */
  --color-dark-bg: #1a1a1a; /* 暗黑模式下的背景顏色 */
  --color-light-text: #333333; /* 亮色模式下的文字顏色 */
  --color-dark-text: #e5e7eb; /* 暗黑模式下的文字顏色 */
}
```

同時，我們根據當前主題模式設置了應用級別的變數：

```css
html.dark {
  --app-bg-color: var(--color-dark-bg);
  --app-text-color: var(--color-dark-text);
}

html {
  --app-bg-color: var(--color-light-bg);
  --app-text-color: var(--color-light-text);
}
```

## 使用方式

### 1. 使用 Tailwind 類別

我們已經在 Tailwind 配置中擴展了背景和文字顏色，您可以使用以下方式：

#### 方法一：使用自定義類別 (需要構建後才能使用)

- `bg-dark-mode`：暗黑模式下的背景顏色
- `bg-light-mode`：亮色模式下的背景顏色
- `text-dark-mode`：暗黑模式下的文字顏色
- `text-light-mode`：亮色模式下的文字顏色

```html
<div class="bg-light-mode dark:bg-dark-mode">
  <span class="text-light-mode dark:text-dark-mode">這段文字會隨主題變化</span>
</div>
```

#### 方法二：使用 Tailwind 原生類別 (開發過程中推薦使用)

在開發過程中，為避免 Tailwind 編譯錯誤，可以先使用原生類別：

```html
<div class="bg-white dark:bg-gray-900">
  <span class="text-gray-800 dark:text-gray-100">暗黑模式時文字顏色會變化</span>
</div>
```

> 注意：為了向下兼容，舊版本的 `bg-mode` 和 `text-mode` 類別仍然可用，但建議在新的開發中使用更加直觀的命名方式。

### 2. 直接使用 CSS 變數

對於需要直接操作 CSS 的場景，可以直接使用 CSS 變數：

```css
.my-component {
  background-color: var(--color-light-bg); /* 亮色模式背景 */
  color: var(--color-light-text); /* 亮色模式文字 */
}

html.dark .my-component {
  background-color: var(--color-dark-bg); /* 暗黑模式背景 */
  color: var(--color-dark-text); /* 暗黑模式文字 */
}
```

或者在 Vue 組件中：

```html
<template>
  <div
    :style="{ 
    backgroundColor: isDark ? 'var(--color-dark-bg)' : 'var(--color-light-bg)',
    color: isDark ? 'var(--color-dark-text)' : 'var(--color-light-text)'
  }">
    內容
  </div>
</template>
<script setup>
  import { useThemeMode } from "@/composables/useThemeMode";

  // 只需要 isDark
  const { isDark } = useThemeMode();

  // 或者同時需要 isDark 和 toggleTheme
  const { isDark, toggleTheme } = useThemeMode();
</script>
```

### 3. 在 JavaScript 中使用

如果需要在 JavaScript 中獲取這些顏色值：

```js
// 獲取暗黑模式背景色
const darkBgColor = getComputedStyle(document.documentElement)
  .getPropertyValue("--color-dark-bg")
  .trim();

// 獲取亮色模式背景色
const lightBgColor = getComputedStyle(document.documentElement)
  .getPropertyValue("--color-light-bg")
  .trim();
```

## 修改主題顏色

若要更改主題顏色，只需修改 `style.css` 中的根變數值：

```css
:root {
  --color-dark-bg: #121212; /* 將暗黑模式背景色從 #1a1a1a 改為 #121212 */
  --color-light-bg: #f8f9fa; /* 將亮色模式背景色從 #ffffff 改為 #f8f9fa */
}
```

這將自動應用到所有使用該變數的地方，無需修改各個組件。

## Tailwind 兼容性注意事項

為了讓自定義類別在 Tailwind 中正常工作，我們已經：

1. 在 `tailwind.config.js` 中擴展了主題顏色
2. 在 `style.css` 中將自定義類別放入 `@layer utilities` 中

如果在開發過程中遇到 Tailwind 編譯錯誤（類似於 "The class does not exist"），可以嘗試：

1. 重啟開發服務器（`npm run dev`）
2. 暫時使用 Tailwind 原生類別（如 `bg-white` 代替 `bg-light-mode`）
3. 如果問題仍然存在，確保您的 Tailwind 設置正確並且能夠識別自定義類別

## 批量更新主題類別

我們提供了一個腳本來幫助批量更新舊的主題類別（如 `dark:bg-gray-800`）為新的類別（`dark:bg-dark-mode`）：

```bash
# 如果還沒有安裝必要依賴，請先執行
npm run update-theme-install

# 然後執行主題類別更新腳本
npm run update-theme
```

此腳本會自動搜尋並替換專案中的舊類別名稱，並在終端中顯示詳細的替換報告。

### 疑難排解

如果遇到執行腳本時出現錯誤，可以嘗試以下解決方法：

1. 確保已安裝最新版本的依賴：

   ```bash
   npm install glob@latest replace-in-file@latest --save-dev
   ```

2. 如果看到 `replaceInFile is not a function` 錯誤，請檢查 `replace-in-file` 的版本是否與腳本兼容。

3. 對於 Node.js 版本相關問題，建議使用 Node.js v16 或更高版本。

## 最佳實踐

1. 對於新組件，在開發階段優先使用 Tailwind 原生類別（如 `bg-white` 和 `dark:bg-gray-900`）
2. 在專案準備發布時，可以使用更新腳本將原生類別替換為自定義類別（如 `bg-light-mode` 和 `dark:bg-dark-mode`）
3. 如需添加新的主題相關顏色，請在 `:root` 中定義新變數，並在 `tailwind.config.js` 中擴展它
4. 避免在組件中直接使用硬編碼的顏色值，特別是與主題相關的顏色
5. Element Plus 組件的顏色配置應通過 CSS 變數覆蓋其預設值
