<!DOCTYPE html>
<html lang="zh-Hant">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/favorite.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>IYM 良率分析系統</title>
    <style>
      /* 全局載入樣式 */
      #app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        transition: opacity 0.5s;
        opacity: 1;
        visibility: visible;
      }
      html.dark #app-loading {
        background-color: #1a1a1a;
      }

      /* 支援暗黑模式 */
      /* @media (prefers-color-scheme: dark) {
        #app-loading {
          background-color: #1a1a1a;
        }
        #app-loading .loading-text {
          color: #ffffff;
        }
      } */

      /* 載入中圖示 */
      .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
      }

      .loading-spinner:after {
        content: " ";
        display: block;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 3px solid #409eff;
        border-color: #409eff transparent #409eff transparent;
        animation: loading-spinner 1.2s linear infinite;
      }

      @keyframes loading-spinner {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-container {
        text-align: center;
      }

      .loading-text {
        font-size: 16px;
        color: #409eff;
        font-weight: 600;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Helvetica, Arial, sans-serif;
        min-height: 20px;
      }

      /* 確保應用容器在載入期間不可見 */
      #app {
        opacity: 0;
        transition: opacity 0.5s;
      }

      /* 載入完成後應用容器可見 */
      #app.loaded {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <!-- 初始載入畫面，放在 body 直接子元素位置，確保最高優先級顯示 -->
    <div id="app-loading">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">載入中...</div>
      </div>
    </div>

    <!-- 應用容器 -->
    <div id="app"></div>

    <script
      type="module"
      src="/src/main.js"></script>
    <script>
      // 檢查存儲的主題設定並應用
      document.addEventListener("DOMContentLoaded", function () {
        const theme = localStorage.getItem("theme");
        if (theme === "dark") {
          document.documentElement.classList.add("dark");
          document.getElementById("app-loading").style.backgroundColor =
            "#1a1a1a";
        }
      });

      // 防止未初始化完成時顯示應用內容
      window.addEventListener("load", function () {
        const appElement = document.getElementById("app");
        // 延遲添加 loaded 類，確保初始載入畫面有足夠時間處理應用初始化
        setTimeout(function () {
          if (appElement) {
            appElement.classList.add("loaded");
          }
        }, 200);
      });
    </script>
  </body>
</html>
