{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "update-theme": "node scripts/update-theme-classes.js", "update-theme-dev": "node scripts/update-theme-classes.js --dev", "update-theme-install": "npm install glob replace-in-file --save-dev"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.42.1", "@vue-flow/minimap": "^1.5.2", "@vue-flow/node-resizer": "^1.4.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^12.7.0", "axios": "^1.7.9", "chart.js": "^4.4.8", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.5.3", "element-tiptap": "^2.2.1", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "katex": "^0.16.21", "lucide-vue-next": "^0.363.0", "markdown-it": "^14.1.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-katex": "^2.0.3", "markdown-it-task-lists": "^2.1.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "pdfjs-dist": "^4.10.38", "pinia": "^3.0.1", "pptx-preview": "^1.0.3", "pptxgenjs": "^3.12.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-demi": "^0.14.6", "vue-echarts": "^7.0.3", "vue-files-preview": "^1.0.38", "vue-json-pretty": "^2.4.0", "vue-json-viewer": "^3.0.4", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^5.0.3", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001697", "glob": "^11.0.2", "path": "^0.12.7", "postcss": "^8.5.1", "replace-in-file": "^8.3.0", "sass": "^1.85.0", "tailwindcss": "^3.4.17", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^5.1.0"}}