{"name": "sfda-iym-backend", "version": "1.1.0", "description": "SFDA IYM Backend Server", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint .", "format": "prettier --write .", "prisma:seed": "node prisma/seed.js", "import-data": "node ./scripts/dataImporter.js", "import-and-seed": "node ./scripts/runImportAndSeed.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@prisma/client": "^5.10.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "compressing": "^1.10.1", "cors": "^2.8.5", "dotenv": "^16.4.4", "ejs": "^3.1.10", "express": "^4.18.2", "express-validator": "^7.0.1", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.0.0", "highlight.js": "^11.11.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "ldapjs": "^3.0.7", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "pdf-lib": "^1.17.1", "sequelize": "^6.31.1", "sharp": "^0.33.5", "sqlite3": "^5.1.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.12", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.2.5", "prisma": "^5.22.0", "supertest": "^6.3.4"}, "prisma": {"seed": "node prisma/seed.js"}}