-- 建立問題回報表（IssueTicket）
CREATE TABLE IF NOT EXISTS `IssueTicket` (
    `id` VARCHAR(191) NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `content` TEXT NOT NULL,
    `type` VARCHAR(50) NOT NULL,
    `category` VARCHAR(50) NOT NULL,
    `systemCode` VARCHAR(20) NOT NULL DEFAULT 'IYM',
    `priority` VARCHAR(20) NOT NULL,
    `status` VARCHAR(20) NOT NULL DEFAULT 'open',
    `screenshot` VARCHAR(255) NULL,
    `comment` TEXT NULL,
    `reporterId` VARCHAR(191) NOT NULL,
    `assigneeId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`),
    INDEX `IssueTicket_reporterId_idx`(`reporterId`),
    INDEX `IssueTicket_assigneeId_idx`(`assigneeId`),
    INDEX `IssueTicket_status_idx`(`status`),
    INDEX `IssueTicket_systemCode_idx`(`systemCode`),
    CONSTRAINT `IssueTicket_reporterId_fkey` FOREIGN KEY (`reporterId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT `IssueTicket_assigneeId_fkey` FOREIGN KEY (`assigneeId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
);

-- 使用 Prisma 時，需要將以下 SQL 插入到 _prisma_migrations 表中，記錄這次遷移
-- INSERT INTO `_prisma_migrations` (`id`, `checksum`, `finished_at`, `migration_name`, `logs`, `rolled_back_at`, `started_at`, `applied_steps_count`)
-- VALUES
-- (UUID(), '202b5920dd8ddad7dcc4b1d6c9b3e09c5d97a2c6ca92e2bcf4fd4a23f0fa3d6f', NOW(), '20231125000000_add_issue_ticket', NULL, NULL, NOW(), 1);
