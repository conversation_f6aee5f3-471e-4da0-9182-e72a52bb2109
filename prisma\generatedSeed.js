// 此檔案由dataImporter.js自動生成，請勿直接修改
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function cleanupData(prisma) {
  // 刪除所有現有數據
  await prisma.dataSnapshot.deleteMany();
  await prisma.userFavorite.deleteMany();
  await prisma.issueTicket.deleteMany();
  await prisma.project.deleteMany();
  await prisma.systemCode.deleteMany();
  await prisma.rolePermission.deleteMany();
  await prisma.userRole.deleteMany();
  await prisma.flowNodeDefinition.deleteMany();
  await prisma.permission.deleteMany();
  await prisma.role.deleteMany();
  await prisma.user.deleteMany();
}

async function main() {
  try {
    console.log('開始清理現有數據...');
    await cleanupData(prisma);
    console.log('數據清理完成');


    console.log('開始創建role數據...');
    const roleData = [
  {
    "id": "255b24da-4ba5-427b-92eb-5743675451dc",
    "name": "SUPERADMIN",
    "description": "超級管理員，擁有所有權限且不受限制"
  },
  {
    "id": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "name": "POWERUSER",
    "description": "進階用戶，可以創建和管理工作流程"
  },
  {
    "id": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "name": "ADMIN",
    "description": "系統管理員，擁有所有權限"
  },
  {
    "id": "b223fe30-810a-4e86-a23c-ca12b56a9c43",
    "name": "READER",
    "description": "一般用戶，只能查看和執行工作流程"
  }
];
    
    for (const item of roleData) {
      await prisma.role.create({
        data: item
      });
    }
    console.log('role數據創建完成');

    console.log('開始創建permission數據...');
    const permissionData = [
  {
    "id": "18409acd-40d8-4b52-ad8b-4cf89b20fc17",
    "name": "ASSIGN_ROLES",
    "description": "分配角色"
  },
  {
    "id": "1c7404e1-6316-47f4-bc18-9e9c28b105d8",
    "name": "VIEW_ROLES",
    "description": "查看角色"
  },
  {
    "id": "206ef254-8670-4d80-9a44-001081778cba",
    "name": "VIEW_PROJECTS",
    "description": "查看專案列表"
  },
  {
    "id": "2cfb1a13-62ad-4767-bd7c-bd5a527aa917",
    "name": "VIEW_NODE_DEFINITIONS",
    "description": "查看節點定義"
  },
  {
    "id": "4005cb78-48f5-4845-8744-69e3808a72a3",
    "name": "VIEW_PERMISSIONS",
    "description": "查看權限"
  },
  {
    "id": "4065ccdd-5ed3-462e-b2a2-65881c1fceb0",
    "name": "DELETE_PROJECTS",
    "description": "刪除專案"
  },
  {
    "id": "5115de91-492a-4bbe-8dd4-2271de71f927",
    "name": "SYSTEM_ADMIN",
    "description": "系統管理權限"
  },
  {
    "id": "57943d6e-c479-42fb-8194-5bdc97802108",
    "name": "EDIT_PROJECTS",
    "description": "編輯專案"
  },
  {
    "id": "83c99f70-51f1-4457-829b-6b3cc83b6fae",
    "name": "MANAGE_NODE_DEFINITIONS",
    "description": "管理節點定義"
  },
  {
    "id": "87ac30f5-da17-41e7-85e7-cbaa1f88f0c3",
    "name": "CREATE_PROJECTS",
    "description": "創建新專案"
  },
  {
    "id": "9b84fa15-c918-4137-b3c6-156afd1f2e8b",
    "name": "MANAGE_ROLES",
    "description": "管理角色"
  }
];
    
    for (const item of permissionData) {
      await prisma.permission.create({
        data: item
      });
    }
    console.log('permission數據創建完成');

    console.log('開始創建user數據...');
    const userData = [
  {
    "id": "0494ecc7-55f6-4ad8-a503-6854f9510f7b",
    "username": "power002",
    "email": "<EMAIL>",
    "password": "$2a$10$ydFPtXJofOMYeMdoG3/GaOuZY1jD.hU4SZqElZIICffhZUbQ9Sfwy",
    "avatar": "1745721419881-849851234.png",
    "isActive": true
  },
  {
    "id": "1e6fb238-10ea-4786-9f55-481068d1f378",
    "username": "qs",
    "email": "<EMAIL>",
    "password": "$2a$10$0rQZ.2CSPNlbhD3O3imJEucH0OKdF4NLttwR8jEeVa90hNQtaONba",
    "avatar": null,
    "isActive": true
  },
  {
    "id": "36a6f1c7-e9ff-4803-ae98-1b3726612216",
    "username": "reader003",
    "email": "<EMAIL>",
    "password": "$2a$10$dh6MAWB3mMvBKIjo5DMgVOflu3L46eBh/Kwo2htmujcG9cc78V3WK",
    "avatar": "👨‍💻",
    "isActive": true
  },
  {
    "id": "3e0c7c3c-9984-40dd-ab55-d4f152259e91",
    "username": "蕭傳璋",
    "email": "<EMAIL>",
    "password": "$2a$10$I8wX/NsLvHI60NqneWxEhOXOF0P.rmZhn56GIUwoKz.4AIasS9jsu",
    "avatar": "1745308137846-983687493.png",
    "isActive": true
  },
  {
    "id": "4adc1164-32c4-459e-a6ec-1436922c8a24",
    "username": "naomi_xiang",
    "email": "<EMAIL>",
    "password": "$2a$10$banbrb1p7ckjTT5bR7QivOjJW8gyUF8G9j.DHFo/c51zTWadCcp.W",
    "avatar": null,
    "isActive": true
  },
  {
    "id": "4bcfc3c5-10a9-4c52-8849-8afabb0843b7",
    "username": "power003",
    "email": "<EMAIL>",
    "password": "$2a$10$mrAjJAkSooY4GAeK1z3ts.fLf0sPIbak2jaw4yJvKxQWB5OS8BFsq",
    "avatar": "👨‍🔧",
    "isActive": true
  },
  {
    "id": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "username": "admin001",
    "email": "<EMAIL>",
    "password": "$2a$10$5Hr4wESefYk6wSR.jZ7on.bhV51NKzmC5m0hmyG9OKn0.HWkCfagm",
    "avatar": "1745721366738-135079997.png",
    "isActive": true
  },
  {
    "id": "760defb6-bc58-40ed-be43-dec961950bac",
    "username": "qs1",
    "email": "<EMAIL>",
    "password": "$2a$10$2bhF7moflMeM.qHXBw4Xf.uEEcWhdSs4Vvtsf6fwju9MHFnw4BHWe",
    "avatar": "1745721312714-125950553.png",
    "isActive": true
  },
  {
    "id": "7cf35f58-c2bf-47b8-b06c-12d1d853ccfc",
    "username": "admin",
    "email": "<EMAIL>",
    "password": "admin123",
    "avatar": "1745721280296-839209666.png",
    "isActive": true
  },
  {
    "id": "85a371e8-bca9-42da-a7e0-45f7778b5749",
    "username": "john_hsiao",
    "email": "<EMAIL>",
    "password": "$2a$10$LZWmcIrdp99.kyjCICCiuunKk.cQ4m8M2sma54/VrHaeEdgZW1wli",
    "avatar": "1745975853669_wuze86.png",
    "isActive": true
  },
  {
    "id": "8ef206ce-f9f9-4090-b91d-9412c5881895",
    "username": "reader002",
    "email": "<EMAIL>",
    "password": "$2a$10$h5T1GDd3aj9D/eD2rhMTAeUl4ZnIQd/uNyIxJ9THpz0YhMVLlK/zS",
    "avatar": "👩‍💻",
    "isActive": true
  },
  {
    "id": "9300172f-d435-4ea8-b4ff-d981cda98a69",
    "username": "reader001",
    "email": "<EMAIL>",
    "password": "$2a$10$dVPHdIev9OzRs2wN3DBa3.Rl4CVPoxNiucgxhuKdJppWo.pi9r5Ji",
    "avatar": "👨‍💻",
    "isActive": true
  },
  {
    "id": "a1e3557d-56dc-4158-bd06-70b00b112d30",
    "username": "admin002",
    "email": "<EMAIL>",
    "password": "$2a$10$BlCE.qeJ.KbIEff5oRtBc.kA5n4Dyn29iK4ZAEntU.8nlhfuMUf/a",
    "avatar": "1745721396742-128444568.png",
    "isActive": true
  },
  {
    "id": "a960d8d4-f361-4b2c-a91d-765d889bf032",
    "username": "reader005",
    "email": "<EMAIL>",
    "password": "$2a$10$xksPYsszTJUPhhkbWNh/FOTpdkMcxcBlt8Y.UQD0Xo29p/Iha.IqO",
    "avatar": "👨‍💻",
    "isActive": true
  },
  {
    "id": "ab95bf6c-e2b7-4449-9520-79c0e4be7cfd",
    "username": "reader004",
    "email": "<EMAIL>",
    "password": "$2a$10$KAnaJM0CILl8j1FbfbhgT.xMgDN.uybzdaq9Jr7R6a1iREXF3hHnK",
    "avatar": "👩‍💻",
    "isActive": true
  },
  {
    "id": "d7410c84-5cb2-497b-8993-af957634c667",
    "username": "paisu_pai",
    "email": "<EMAIL>",
    "password": "$2a$10$wqvpJUwNlBNKQThpp8nOGuaYESdo7RTvwavCgVHKuMHRA6AVXQMlS",
    "avatar": "1745721298374-153083359.png",
    "isActive": true
  },
  {
    "id": "dde93b27-c181-4447-b63f-5bd9045eb8fc",
    "username": "power001",
    "email": "<EMAIL>",
    "password": "$2a$10$EB/XN09dqLyrky512lMQhOzsfwUkmtWvppynYv2S5BnZaaAqQie5e",
    "avatar": "1745721431319-653010293.png",
    "isActive": true
  },
  {
    "id": "de934bfb-01c1-4477-adbf-63698b87a628",
    "username": "dinoin_chen",
    "email": "<EMAIL>",
    "password": "$2a$10$DzDvFNc6ABbIRvKAOUcLlOGdZvBDhAHuRszNJ0C9/jMqUBk15CT5G",
    "avatar": null,
    "isActive": true
  }
];
    
    for (const item of userData) {
      // 用戶資料
      await prisma.user.create({ 
        data: item 
      });
    }
    console.log('user數據創建完成');

    console.log('開始創建userRole數據...');
    const userRoleData = [
  {
    "id": "004d560a-76fa-4e37-8299-4b9af5c6a517",
    "userId": "de934bfb-01c1-4477-adbf-63698b87a628",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc"
  },
  {
    "id": "05fa26f9-7894-4c6b-bd12-3f831cf27992",
    "userId": "a960d8d4-f361-4b2c-a91d-765d889bf032",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "28c43459-cf64-49ba-9408-a5ff9c4adde9",
    "userId": "0494ecc7-55f6-4ad8-a503-6854f9510f7b",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561"
  },
  {
    "id": "45a6b239-1983-4552-97b8-a0d3c49c8e75",
    "userId": "d7410c84-5cb2-497b-8993-af957634c667",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "4c9aa115-698d-4233-ad80-6b0fa6fc7550",
    "userId": "9300172f-d435-4ea8-b4ff-d981cda98a69",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "537c9999-e241-497f-ae34-2ca8c9cdc21e",
    "userId": "760defb6-bc58-40ed-be43-dec961950bac",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "56930371-9c0c-40ea-9164-2560469c7899",
    "userId": "3e0c7c3c-9984-40dd-ab55-d4f152259e91",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc"
  },
  {
    "id": "74f8fe87-b434-4488-80bb-d629b91868de",
    "userId": "a1e3557d-56dc-4158-bd06-70b00b112d30",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6"
  },
  {
    "id": "7872f240-d553-418b-846a-52292e5c0998",
    "userId": "8ef206ce-f9f9-4090-b91d-9412c5881895",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "7d2cfc38-8166-4b9c-a96f-88435e2195f9",
    "userId": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6"
  },
  {
    "id": "898c1764-f5a2-4ead-a9d0-edc971782e1a",
    "userId": "4bcfc3c5-10a9-4c52-8849-8afabb0843b7",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561"
  },
  {
    "id": "9810affc-17d9-4f7e-b783-4b85faf6df56",
    "userId": "dde93b27-c181-4447-b63f-5bd9045eb8fc",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561"
  },
  {
    "id": "9e590f11-2308-46e4-832d-d03b2db5198f",
    "userId": "36a6f1c7-e9ff-4803-ae98-1b3726612216",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "a111edb7-2c7a-4717-b749-cc88c1ee4086",
    "userId": "1e6fb238-10ea-4786-9f55-481068d1f378",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "c02c1461-e927-42c1-a156-c31ec8bb57f1",
    "userId": "85a371e8-bca9-42da-a7e0-45f7778b5749",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc"
  },
  {
    "id": "d29ea0fa-1af3-4bbd-92ce-c110e8e094dd",
    "userId": "4adc1164-32c4-459e-a6ec-1436922c8a24",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  },
  {
    "id": "e3528044-84d4-44ac-a1fd-e3e4d69c339f",
    "userId": "ab95bf6c-e2b7-4449-9520-79c0e4be7cfd",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43"
  }
];
    
    for (const item of userRoleData) {
      // 用戶角色關聯
      await prisma.userRole.create({
        data: {
          id: item.id,
          user: { connect: { id: item.userId } },
          role: { connect: { id: item.roleId } }
        }
      });
    }
    console.log('userRole數據創建完成');

    console.log('開始創建rolePermission數據...');
    const rolePermissionData = [
  {
    "id": "084a7ec2-369c-4fa3-8749-f9a4c9d47430",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "4065ccdd-5ed3-462e-b2a2-65881c1fceb0"
  },
  {
    "id": "0bf40a02-8372-4e56-ba69-4de45fff6324",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "5115de91-492a-4bbe-8dd4-2271de71f927"
  },
  {
    "id": "1753e262-24f7-428b-8959-f3ebf879052e",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "57943d6e-c479-42fb-8194-5bdc97802108"
  },
  {
    "id": "1a016f6d-fc5c-437b-9dec-9ba6a14c7f2d",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "4005cb78-48f5-4845-8744-69e3808a72a3"
  },
  {
    "id": "1ebbbb08-878b-468b-b51b-f2482162f482",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "permissionId": "206ef254-8670-4d80-9a44-001081778cba"
  },
  {
    "id": "22404558-dbcd-49b7-b590-13996f1e62ba",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "1c7404e1-6316-47f4-bc18-9e9c28b105d8"
  },
  {
    "id": "23a1a728-209b-4b73-afa7-5376d15311ab",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "18409acd-40d8-4b52-ad8b-4cf89b20fc17"
  },
  {
    "id": "2a63c3b7-d9fe-4043-a365-cfd357cccd77",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "206ef254-8670-4d80-9a44-001081778cba"
  },
  {
    "id": "350ebae3-050b-4bcd-b166-730e23a1b31b",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "9b84fa15-c918-4137-b3c6-156afd1f2e8b"
  },
  {
    "id": "39e448e7-3c3c-445f-907d-397cbd66cb2d",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "2cfb1a13-62ad-4767-bd7c-bd5a527aa917"
  },
  {
    "id": "4ea6f77a-2059-4156-b64d-57eca337c702",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "206ef254-8670-4d80-9a44-001081778cba"
  },
  {
    "id": "52659384-4a32-44f5-8fcc-409a3adbb687",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "2cfb1a13-62ad-4767-bd7c-bd5a527aa917"
  },
  {
    "id": "6447c9c0-8afd-4df0-9dc9-6bdffadbff13",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "83c99f70-51f1-4457-829b-6b3cc83b6fae"
  },
  {
    "id": "6889c518-7b61-4684-a798-b1559abda875",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "permissionId": "57943d6e-c479-42fb-8194-5bdc97802108"
  },
  {
    "id": "6c811b2c-a2d2-49a1-90bf-c33bcffb7ecd",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "18409acd-40d8-4b52-ad8b-4cf89b20fc17"
  },
  {
    "id": "8317eb64-8a98-4e5d-bb4e-fc4b0c4cd9aa",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "87ac30f5-da17-41e7-85e7-cbaa1f88f0c3"
  },
  {
    "id": "83c7047e-5a8c-4170-9834-726145bebc67",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "4065ccdd-5ed3-462e-b2a2-65881c1fceb0"
  },
  {
    "id": "8703d2c0-2538-4945-92d2-69560068663d",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "4005cb78-48f5-4845-8744-69e3808a72a3"
  },
  {
    "id": "a12c4235-5135-4140-b647-b0f855bb467e",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43",
    "permissionId": "206ef254-8670-4d80-9a44-001081778cba"
  },
  {
    "id": "ab484cd0-3b78-45b9-8c95-52bf99a17f9f",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "permissionId": "2cfb1a13-62ad-4767-bd7c-bd5a527aa917"
  },
  {
    "id": "b51bdc79-fdb0-45d9-a2cf-9837223e31d5",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "9b84fa15-c918-4137-b3c6-156afd1f2e8b"
  },
  {
    "id": "ba5c7669-839c-451a-b612-f8ff272061ba",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "permissionId": "87ac30f5-da17-41e7-85e7-cbaa1f88f0c3"
  },
  {
    "id": "c1f34b49-0b1c-45fa-8221-54295549cb04",
    "roleId": "646ed4a8-f6b7-4ac6-8dcb-20f4fcbc8561",
    "permissionId": "83c99f70-51f1-4457-829b-6b3cc83b6fae"
  },
  {
    "id": "d6b5a83a-9805-4b41-acf0-f833efcff560",
    "roleId": "b223fe30-810a-4e86-a23c-ca12b56a9c43",
    "permissionId": "2cfb1a13-62ad-4767-bd7c-bd5a527aa917"
  },
  {
    "id": "dc183da6-e65d-4baf-93a4-1f376bd0b8c4",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "1c7404e1-6316-47f4-bc18-9e9c28b105d8"
  },
  {
    "id": "e6f85971-c7dd-4d8a-8d6f-77001c6d5e36",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "83c99f70-51f1-4457-829b-6b3cc83b6fae"
  },
  {
    "id": "ec2ffbc7-719d-402e-ae35-70cfdb042501",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "87ac30f5-da17-41e7-85e7-cbaa1f88f0c3"
  },
  {
    "id": "fcdb431a-087a-44eb-81be-f996daf6d56c",
    "roleId": "a32c4ba2-7c03-4d4e-bcc8-622cfced69d6",
    "permissionId": "5115de91-492a-4bbe-8dd4-2271de71f927"
  },
  {
    "id": "fe1be1f6-1abe-47a2-aed7-d786fe5244fc",
    "roleId": "255b24da-4ba5-427b-92eb-5743675451dc",
    "permissionId": "57943d6e-c479-42fb-8194-5bdc97802108"
  }
];
    
    for (const item of rolePermissionData) {
      // 角色權限關聯
      await prisma.rolePermission.create({
        data: {
          id: item.id,
          role: { connect: { id: item.roleId } },
          permission: { connect: { id: item.permissionId } }
        }
      });
    }
    console.log('rolePermission數據創建完成');

    console.log('開始創建systemCode數據...');
    const systemCodeData = [
  {
    "systemCode": "DOC",
    "systemName": "文件管理系統",
    "systemDescription": "Document Management System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "EQP",
    "systemName": "設備管理系統",
    "systemDescription": "Equipment Management System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "FIN",
    "systemName": "財務系統",
    "systemDescription": "Finance System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "HRM",
    "systemName": "人資系統",
    "systemDescription": "Human Resource Management",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "INV",
    "systemName": "庫存系統",
    "systemDescription": "Inventory System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "IYM",
    "systemName": "量測系統",
    "systemDescription": "In-line Yield Management System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "MFG",
    "systemName": "製造系統",
    "systemDescription": "Manufacturing System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "systemCode": "QAS",
    "systemName": "品質保證系統",
    "systemDescription": "Quality Assurance System",
    "isEnabled": true,
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  }
];
    
    for (const item of systemCodeData) {
      await prisma.systemCode.create({
        data: item
      });
    }
    console.log('systemCode數據創建完成');

    console.log('開始創建project數據...');
    const projectData = [
  {
    "id": "10780e79-2bfe-444f-86e9-d13cf3209d7c",
    "projectNumber": "QAS_20250324_2NHKO",
    "systemCode": "QAS",
    "name": "客戶滿意度改善計劃",
    "description": "基於客訴分析制定客戶滿意度改善方案",
    "status": "draft",
    "createdBy": "a1e3557d-56dc-4158-bd06-70b00b112d30",
    "updatedBy": "4bcfc3c5-10a9-4c52-8849-8afabb0843b7"
  },
  {
    "id": "20938b55-3e21-4a36-bf8d-7ae092149137",
    "projectNumber": "IYM_20250328_ODPGB",
    "systemCode": "IYM",
    "name": "關鍵因子查找",
    "description": "良率影響關鍵因子分析查找",
    "status": "active",
    "createdBy": "3e0c7c3c-9984-40dd-ab55-d4f152259e91",
    "updatedBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "id": "3fa7b32c-e177-4bfd-998f-8c69574559ae",
    "projectNumber": "IYM_20250324_Q3S0Y",
    "systemCode": "IYM",
    "name": "2024年Q1客訴分析專案",
    "description": "分析第一季度客戶反饋，找出主要問題點並提出改善建議",
    "status": "active",
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "id": "413a35af-844c-43ed-bca0-2ad7cb101257",
    "projectNumber": "QAS_20250324_4VHLA",
    "systemCode": "QAS",
    "name": "產品品質改善追蹤",
    "description": "追蹤並分析產品品質相關的客訴案件",
    "status": "active",
    "createdBy": "dde93b27-c181-4447-b63f-5bd9045eb8fc",
    "updatedBy": "dde93b27-c181-4447-b63f-5bd9045eb8fc"
  },
  {
    "id": "4264cef5-28d9-409b-a724-e724cc50575c",
    "projectNumber": "DOC_20250324_PMXEA",
    "systemCode": "DOC",
    "name": "2023年度客訴報告",
    "description": "彙整2023年度所有客訴數據並生成分析報告\n使用程式語言實現\nJavaScript 實現：\n\n使用 setTimeout 函數逐字顯示文字\n可以調整打字速度、停頓和錯誤率\nPython 實現：\n\n使用 time.sleep() 函數控制輸出速度\n可以在終端機或 GUI 應用程式中實現",
    "status": "completed",
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
  },
  {
    "id": "5096b02b-3db4-41c5-91e3-9c5d33a6fec6",
    "projectNumber": "DOC_20250324_6PJEY",
    "systemCode": "DOC",
    "name": "客訴處理SOP優化",
    "description": "根據客訴處理數據優化標準作業流程",
    "status": "completed",
    "createdBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683",
    "updatedBy": "5fd59bfc-d7ed-432c-9fe6-dab520c87683"
  },
  {
    "id": "7370d074-edbf-4db9-bb2d-8b938c006a61",
    "projectNumber": "MFG_20250324_X09QC",
    "systemCode": "MFG",
    "name": "包裝改善專案",
    "description": "基於客訴回饋優化產品包裝",
    "status": "draft",
    "createdBy": "4bcfc3c5-10a9-4c52-8849-8afabb0843b7",
    "updatedBy": "4bcfc3c5-10a9-4c52-8849-8afabb0843b7"
  },
  {
    "id": "7cd51e69-539a-4443-afeb-d3c1abe2d5ef",
    "projectNumber": "QAS_20250324_75V3K",
    "systemCode": "QAS",
    "name": "新產品客訴追蹤",
    "description": "追蹤新產品上市後的客戶反饋",
    "status": "draft",
    "createdBy": "dde93b27-c181-4447-b63f-5bd9045eb8fc",
    "updatedBy": "dde93b27-c181-4447-b63f-5bd9045eb8fc"
  },
  {
    "id": "a37002db-308d-4d1c-9368-fc471499802d",
    "projectNumber": "QAS_20250324_XJ7TP",
    "systemCode": "QAS",
    "name": "客服回應時效分析",
    "description": "分析客服團隊對客訴的回應時效",
    "status": "active",
    "createdBy": "0494ecc7-55f6-4ad8-a503-6854f9510f7b",
    "updatedBy": "a1e3557d-56dc-4158-bd06-70b00b112d30"
  },
  {
    "id": "bdde49bb-4f7d-4d1e-9c1f-db552b5df095",
    "projectNumber": "QAS_20250324_LLRST",
    "systemCode": "QAS",
    "name": "運送品質監控",
    "description": "監控並分析運送過程中的產品損壞案例",
    "status": "active",
    "createdBy": "a1e3557d-56dc-4158-bd06-70b00b112d30",
    "updatedBy": "dde93b27-c181-4447-b63f-5bd9045eb8fc"
  },
  {
    "id": "cbe9d447-39f5-47df-b162-0b01743635d2",
    "projectNumber": "HRM_20250324_QXMN2",
    "systemCode": "HRM",
    "name": "跨部門協作效率分析",
    "description": "分析客訴處理過程中的跨部門協作效率",
    "status": "active",
    "createdBy": "0494ecc7-55f6-4ad8-a503-6854f9510f7b",
    "updatedBy": "0494ecc7-55f6-4ad8-a503-6854f9510f7b"
  },
  {
    "id": "ea3a951a-d8c5-4c12-a966-a126ed9ce929",
    "projectNumber": "IYM_20250429_DKUAV",
    "systemCode": "IYM",
    "name": "test",
    "description": "test的說明",
    "status": "draft",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749",
    "updatedBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  }
];
    
    for (const item of projectData) {
      await prisma.project.create({
        data: item
      });
    }
    console.log('project數據創建完成');

    console.log('開始創建flowNodeDefinition數據...');
    const flowNodeDefinitionData = [
  {
    "id": "10c79d65-0062-4fa0-8b11-34d2cda8a91b",
    "category": "data-process",
    "name": "特徵顯著性KS檢定分析",
    "description": "使用Kruskal-Wallis檢定分析參數顯著性",
    "icon": "BookUp2",
    "componentName": "KruskalWallisTestAnalysisProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "2b97fba4-3027-4cf4-9ed6-734f8ade8070",
    "category": "data-process",
    "name": "Random Forest",
    "description": "隨機森林分析",
    "icon": "Trees",
    "componentName": "RandomForestProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "2da6ae2b-84a2-43b8-abea-2ec5de7401a0",
    "category": "data-process",
    "name": "KDE密度圖數據分析",
    "description": "觀察連續型隨機變數分布的趨勢",
    "icon": "LineChart",
    "componentName": "KdeAnalysisForFeaturesProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "37179d94-56cb-4e0d-8648-300736723ca5",
    "category": "data-process",
    "name": "Logistic Regression參數分析",
    "description": "使用Logistic Regression分析參數重要性",
    "icon": "Regex",
    "componentName": "LogisticRegressionForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "39b90219-c899-41d2-8a8b-892f5af79e1a",
    "category": "data-process",
    "name": "MARS interaction for parameter",
    "description": "呈現自變數之間的交互作用對應變數的影響。",
    "icon": "Globe2",
    "componentName": "MarsInteractionForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "4dd87d50-5203-49ed-bca0-dbef7783197c",
    "category": "data-process",
    "name": "個人發展計劃生成(測試)X",
    "description": "整合績效和技能分析結果，生成個人發展計劃",
    "icon": "Squirrel",
    "componentName": "DevelopmentPlanProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": "TESTTESTXXX"
  },
  {
    "id": "4e354f0d-88ac-4bf8-95ed-0ac478a0b809",
    "category": "data-process",
    "name": "入庫良率分析",
    "description": "分析入庫良率",
    "icon": "Store",
    "componentName": "YieldProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "4fbf9266-96ab-4ff0-b5b2-f48851366f53",
    "category": "data-input",
    "name": "SPI關鍵因子分析 基本輸入(品目/起訖日)",
    "description": "用於輸入品目、起始日期、結束日期三項基礎資料",
    "icon": "FileInput",
    "componentName": "BasicSPIInput",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "51047afc-4c7e-411b-9b0a-0c8b24a5d2f1",
    "category": "data-process",
    "name": "XGBoost",
    "description": "梯度增強演算法",
    "icon": "SortAsc",
    "componentName": "XGBoostProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "609fd490-93c9-4c39-b404-c3abbb4b3f8b",
    "category": "data-process",
    "name": "XGBoost參數分析",
    "description": "使用XGBoost分析參數重要性",
    "icon": "BarChartHorizontal",
    "componentName": "XGBoostForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "6d1b342a-5046-4fe3-8516-b1fb4cc468b6",
    "category": "data-process",
    "name": "可靠性指數評估",
    "description": "使用XGBoost分析參數重要性",
    "icon": "PanelTopClose",
    "componentName": "ReliabilityIndexProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "77a5ffdf-b195-4d67-b23f-5ceaea3cf532",
    "category": "data-process",
    "name": "Random Forest參數分析",
    "description": "使用Random Forest分析參數重要性",
    "icon": "Trees",
    "componentName": "RandomForestForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "7a94cf19-3b33-4be7-9e8c-0e2183168cbb",
    "category": "data-process",
    "name": "Key Process Parameter",
    "description": "影響不良的製程關鍵因子",
    "icon": "KeySquare",
    "componentName": "SummarySPIProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "7b305022-9442-4877-8b85-225479c847d3",
    "category": "data-process",
    "name": "選取卡方離散變數",
    "description": "將根據選擇的卡方離散變數進行接續的分析",
    "icon": "BoxSelect",
    "componentName": "SelectChiSquareDiscreteVariablesProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "82a7c007-689a-403e-b58b-6ba3e00a8b9a",
    "category": "data-process",
    "name": "卡方圖分析(SPI)",
    "description": "根據品目、工單及卡方離散變數，進行卡方分析，找出影響不良的關鍵因素",
    "icon": "AlignStartHorizontal",
    "componentName": "ChiSquareSPIProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "8980dee7-9e15-4d0a-9f91-d4bcb5ed5221",
    "category": "data-process",
    "name": "卡方圖分析(不良原因)",
    "description": "根據選擇的不良原因，進行卡方分析，找出影響不良的關鍵因素",
    "icon": "AlignStartHorizontal",
    "componentName": "ChiSquareForDefectProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": "說明說明XXX"
  },
  {
    "id": "bbdb59c1-7832-49d9-9dc1-203413c043e3",
    "category": "data-input",
    "name": "客訴單號選擇器(測試)",
    "description": "用於選擇客訴單號進行分析",
    "icon": "Speech",
    "componentName": "ComplaintInput",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "c067ec8a-3a03-48c8-a394-bdd27ad420e0",
    "category": "data-process",
    "name": "技能缺口分析(測試)",
    "description": "分析員工技能與職位要求的差距，提供培訓建議",
    "icon": "UserSquare",
    "componentName": "SkillAnalysisProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "c55b6d56-1e86-4573-bcda-edd495278e33",
    "category": "data-input",
    "name": "員工數據導入(測試)",
    "description": "用於導入員工績效評估數據、工作時間記錄、培訓參與記錄等",
    "icon": "UserPlus2",
    "componentName": "EmployeeDataInput",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "c8d1eed2-2464-44bb-9850-1516a3e40205",
    "category": "data-input",
    "name": "基本輸入(品目/起訖日/製程)",
    "description": "用於輸入品目、起始日期、結束日期、製程四項基礎資料",
    "icon": "MapPinned",
    "componentName": "BasicInput",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "cda6f4a0-a0d0-4d87-b27c-77390972d1e9",
    "category": "data-process",
    "name": "選取連續變數",
    "description": "將根據選擇的連續變數進行接續的分析",
    "icon": "ALargeSmall",
    "componentName": "SelectContinuousVariablesProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "dc91eb6f-8e34-414f-9231-47bf6520c533",
    "category": "data-process",
    "name": "Lasso參數分析",
    "description": "同時進行特徵選擇和正則化（數學）的迴歸分析",
    "icon": "AlignStartVertical",
    "componentName": "LassoForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "de045168-eb1d-4054-b747-de168ab27658",
    "category": "data-process",
    "name": "AI 訊息處理(純好玩的)",
    "description": "模擬 AI 生成文本的打字效果，可自定義輸出速度和思考延遲",
    "icon": "VenetianMask",
    "componentName": "AiProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "de39735c-c862-43b8-85cc-d9010d517858",
    "category": "data-process",
    "name": "決策樹交互作用分析",
    "description": "以決策樹分析參數之間的互動關係",
    "icon": "TreePine",
    "componentName": "DecisionTreeInteractionForParamProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "f46c7034-389b-4fa6-940d-1365473be242",
    "category": "data-process",
    "name": "選取不良原因",
    "description": "將根據選擇的不良原因，預備進行接續的分析",
    "icon": "UserCheck",
    "componentName": "SelectDefectCodeFormYieldProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "f4ab5079-6058-4abb-b41d-453fe162311d",
    "category": "data-process",
    "name": "範例處理節點",
    "description": "示範 取得上下文、全域變數，顯示結果寫入上下文及全域變數",
    "icon": "BookTemplate",
    "componentName": "TemplateProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  },
  {
    "id": "ff0aa0f4-d9ca-4875-b67d-9b25926f70a6",
    "category": "data-process",
    "name": "績效趨勢分析(測試)",
    "description": "分析員工過去6個季度的績效變化趨勢",
    "icon": "Bike",
    "componentName": "PerformanceAnalysisProcess",
    "componentPath": "business",
    "assigneeType": null,
    "config": "{}",
    "uiConfig": "{}",
    "handles": "{}",
    "createdBy": null,
    "updatedBy": null,
    "helpContent": null
  }
];
    
    for (const item of flowNodeDefinitionData) {
      await prisma.flowNodeDefinition.create({
        data: item
      });
    }
    console.log('flowNodeDefinition數據創建完成');

    console.log('開始創建userFavorite數據...');
    const userFavoriteData = [
  {
    "type": "flow",
    "resourceId": "412cc285-5241-4a7b-a6a9-3737d2abdbcb",
    "path": "/flow-instances/412cc285-5241-4a7b-a6a9-3737d2abdbcb",
    "createdAt": "2025-05-12T01:02:07.798Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  },
  {
    "type": "flow",
    "resourceId": "4af335d2-fd9c-41d7-aaef-ffa6745f9163",
    "path": "/flow-instances/4af335d2-fd9c-41d7-aaef-ffa6745f9163",
    "createdAt": "2025-05-14T17:55:23.215Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  },
  {
    "type": "flow",
    "resourceId": "6297d8dd-c99a-4c4b-9b7d-f79a2b5fe5f2",
    "path": "/flow-instances/6297d8dd-c99a-4c4b-9b7d-f79a2b5fe5f2",
    "createdAt": "2025-05-08T01:02:20.813Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "flow",
    "resourceId": "79025bb4-ee46-4c00-92e5-bd899a2c34c1",
    "path": "/flow-instances/79025bb4-ee46-4c00-92e5-bd899a2c34c1",
    "createdAt": "2025-05-08T01:02:10.188Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "flow",
    "resourceId": "82d7d01f-5f04-4022-9310-bb3993deb413",
    "path": "/flow-instances/82d7d01f-5f04-4022-9310-bb3993deb413",
    "createdAt": "2025-05-08T00:57:51.909Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "project",
    "resourceId": "10780e79-2bfe-444f-86e9-d13cf3209d7c",
    "path": "/projects/10780e79-2bfe-444f-86e9-d13cf3209d7c",
    "createdAt": "2025-05-07T18:59:02.715Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "project",
    "resourceId": "20938b55-3e21-4a36-bf8d-7ae092149137",
    "path": "/projects/20938b55-3e21-4a36-bf8d-7ae092149137",
    "createdAt": "2025-05-08T19:53:07.617Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  },
  {
    "type": "project",
    "resourceId": "bdde49bb-4f7d-4d1e-9c1f-db552b5df095",
    "path": "/projects/bdde49bb-4f7d-4d1e-9c1f-db552b5df095",
    "createdAt": "2025-05-07T22:36:41.932Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "project",
    "resourceId": "ea3a951a-d8c5-4c12-a966-a126ed9ce929",
    "path": "/projects/ea3a951a-d8c5-4c12-a966-a126ed9ce929",
    "createdAt": "2025-05-07T19:06:51.079Z",
    "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "type": "report",
    "resourceId": "213ae84e-a83a-4592-9280-69dba24a2403",
    "path": "/report/213ae84e-a83a-4592-9280-69dba24a2403",
    "createdAt": "2025-05-12T18:19:08.420Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  },
  {
    "type": "report",
    "resourceId": "4af335d2-fd9c-41d7-aaef-ffa6745f9163",
    "path": "/report/4af335d2-fd9c-41d7-aaef-ffa6745f9163",
    "createdAt": "2025-05-14T18:46:21.529Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  },
  {
    "type": "report",
    "resourceId": "82d7d01f-5f04-4022-9310-bb3993deb413",
    "path": "/report/82d7d01f-5f04-4022-9310-bb3993deb413",
    "createdAt": "2025-05-13T19:47:22.268Z",
    "createdBy": "de934bfb-01c1-4477-adbf-63698b87a628"
  }
];
    
    for (const item of userFavoriteData) {
      await prisma.userFavorite.create({
        data: item
      });
    }
    console.log('userFavorite數據創建完成');

    console.log('開始創建issueTicket數據...');
    const issueTicketData = [
  {
    "id": "26b73454-29b5-4cce-b736-75d95e6926ef",
    "title": "附檔",
    "content": "在預覽附檔時，在畫面右側列出專案內所有的附件(參考 midjourney)",
    "type": "feature",
    "category": "frontend",
    "systemCode": "IYM",
    "priority": "medium",
    "status": "open",
    "screenshot": "1b1eb671-0fe1-43cd-aeb6-307e0ce927cf.png",
    "comment": null,
    "reporterId": "85a371e8-bca9-42da-a7e0-45f7778b5749"
  },
  {
    "id": "58689b6e-0497-45cf-94ae-34c6dd756568",
    "title": "三的",
    "content": "豐三的\n## Test\n### 12345",
    "type": "bug",
    "category": "frontend",
    "systemCode": "IYM",
    "priority": "medium",
    "status": "open",
    "screenshot": null,
    "comment": null,
    "reporterId": "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
  },
  {
    "id": "5dc8ed7b-edad-4609-b88e-8bbe607e45b4",
    "title": "BaseNode 問題",
    "content": "## 節點高度\n## 連接線無法刪除(管理員限定)",
    "type": "bug",
    "category": "frontend",
    "systemCode": "IYM",
    "priority": "medium",
    "status": "open",
    "screenshot": "5e10c2cc-6da8-4677-ba84-189eb4ec2c92.png",
    "comment": null,
    "reporterId": "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
  },
  {
    "id": "67943e14-5ed2-4963-b0fb-755145cae4c0",
    "title": "index.vue 編輯鈕統一風格",
    "content": "使用 el-button-group 來統一按鈕樣式",
    "type": "feature",
    "category": "frontend",
    "systemCode": "IYM",
    "priority": "medium",
    "status": "in_progress",
    "screenshot": "67c92eec-f817-437e-9d94-3dfcfb385e41.png",
    "comment": null,
    "reporterId": "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
  },
  {
    "id": "973da3a8-d81e-461d-bcfa-af69c5fec75d",
    "title": "卡方分析結果範例關鍵信息",
    "content": "卡方分析結果通常包含以下關鍵信息：\n\n1. **卡方值 (χ²)**: 例如 χ² = 15.73\n   - 這是觀察值與期望值差異的總和\n\n2. **自由度 (df)**: 例如 df = 3\n   - 計算方式通常是 (列數-1) × (行數-1)\n\n3. **顯著性水平 (p值)**: 例如 p = 0.0013\n   - 如果 p < 0.05，通常表示結果具有統計顯著性\n\n4. **效應量**: 例如 Cramer's V = 0.31\n   - 表示關聯強度的大小\n\n5. **列聯表**: 顯示各類別的觀察頻次和期望頻次\n   - 包含行變數和列變數的交叉分布\n\n6. **標準化殘差**: 例如 2.5, -1.8 等\n   - 指出哪些單元格對卡方值貢獻最大\n\n7. **結論解釋**: 例如「拒絕虛無假設，兩變數間存在顯著關聯」\n8. fffff\n\n這些信息幫助研究者判斷兩個分類變數之間是否存在統計學上的顯著關聯。",
    "type": "bug",
    "category": "frontend",
    "systemCode": "IYM",
    "priority": "medium",
    "status": "open",
    "screenshot": "5773c7fb-6cfd-476d-8b02-33c93e3f6136.png,8384bb20-6dac-4e1a-a851-da5fcff27534.png,06295a1c-9fd3-4c1d-8cd1-049db56427a5.png,3a34bba4-c548-46a6-a6be-070ce1b6ba37.png",
    "comment": null,
    "reporterId": "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
  }
];
    
    for (const item of issueTicketData) {
      await prisma.issueTicket.create({
        data: item
      });
    }
    console.log('issueTicket數據創建完成');

    console.log('所有數據已成功導入');
  } catch (error) {
    console.error('數據導入出錯:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
