# 資料庫遷移與初始化

## 資料庫遷移

本目錄包含資料庫遷移腳本，這些腳本由Prisma自動生成。

## 資料庫初始化

可以使用以下兩個檔案進行資料庫初始化：

1. `seed.js`：原始的資料庫種子檔案，包含基本的系統設定數據
2. `generatedSeed.js`：由備份JSON檔案自動生成的種子檔案（如果有執行資料匯入工具）

## 使用方法

### 使用原始seed.js初始化

```bash
npx prisma db seed
```

或

```bash
npm run prisma:seed
```

### 使用生成的generatedSeed.js初始化

如果已經通過資料匯入工具生成了`generatedSeed.js`檔案，可以直接執行它：

```bash
node ./prisma/generatedSeed.js
```

## 備份與還原流程

1. 備份：使用資料庫工具將資料匯出為JSON檔案，保存在`prisma/backup`目錄下
2. 還原：執行資料匯入工具將備份檔案轉換為seed腳本，然後執行該腳本

```bash
# 步驟1：匯入資料（生成generatedSeed.js）
npm run import-data

# 步驟2：執行生成的seed腳本
node ./prisma/generatedSeed.js
```

## 注意事項

- 執行seed腳本會清空資料庫中的現有資料，請謹慎操作
- 生成的seed腳本會按照正確的順序插入資料，以確保資料的依賴關係正確
