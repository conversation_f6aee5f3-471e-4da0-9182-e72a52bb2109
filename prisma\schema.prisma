// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  //shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model User {
  /// 使用者唯一識別碼
  id        String   @id @default(uuid())
  /// 使用者名稱，用於登入
  username  String   @unique
  /// 電子郵件地址，用於通知和找回密碼
  email     String   @unique
  /// 加密後的密碼
  password  String
  /// 使用者頭像圖片路徑
  avatar    String?
  /// 帳號是否啟用
  isActive  Boolean  @default(true)
  /// 帳號創建時間
  createdAt DateTime @default(now())
  /// 最後更新時間
  updatedAt DateTime @updatedAt

  // RBAC 關聯
  userRoles UserRole[]

  // 專案關聯
  createdProjects Project[] @relation("CreatedProjects")
  updatedProjects Project[] @relation("UpdatedProjects")

  // uploadedFiles FileNode[] @relation("UploadedFiles")

  // 新增關聯
  createdFlowNodeDefinitions FlowNodeDefinition[] @relation("CreatedFlowNodeDefinitions")
  updatedFlowNodeDefinitions FlowNodeDefinition[] @relation("UpdatedFlowNodeDefinitions")

  // 新增關聯
  createdSystemCodes SystemCode[] @relation("CreatedSystemCodes")
  updatedSystemCodes SystemCode[] @relation("UpdatedSystemCodes")

  // 新增 Flow 相關關聯
  createdFlowTemplates FlowTemplate[] @relation("CreatedFlowTemplates")
  updatedFlowTemplates FlowTemplate[] @relation("UpdatedFlowTemplates")
  createdFlowInstances FlowInstance[] @relation("CreatedFlowInstances")
  updatedFlowInstances FlowInstance[] @relation("UpdatedFlowInstances")
  createdFlowDocuments FlowDocument[] @relation("CreatedFlowDocuments")

  // 新增 Issue 相關關聯
  reportedIssues IssueTicket[] @relation("ReportedIssues")
  
  // 新增 UserFavorite 關聯
  userFavorites UserFavorite[] @relation("UserFavorites")

  userDataSnapshots DataSnapshot[] @relation("UserDataSnapshots")

  @@index([email])
  @@map("User")
}

model Permission {
  /// 權限唯一識別碼
  id          String   @id @default(uuid())
  /// 權限名稱，如 CREATE_PROJECT、MANAGE_USERS
  name        String   @unique
  /// 權限描述
  description String?
  /// 創建時間
  createdAt   DateTime @default(now())
  /// 最後更新時間
  updatedAt   DateTime @updatedAt

  // RBAC 關聯
  rolePermissions RolePermission[]

  @@map("Permission")
}

model Role {
  /// 角色唯一識別碼
  id          String   @id @default(uuid())
  /// 角色名稱，如 ADMIN、USER、MANAGER
  name        String   @unique
  /// 角色描述
  description String?
  /// 創建時間
  createdAt   DateTime @default(now())
  /// 最後更新時間
  updatedAt   DateTime @updatedAt

  // RBAC 關聯
  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@map("Role")
}

model UserRole {
  /// 使用者角色關聯唯一識別碼
  id        String   @id @default(uuid())
  /// 使用者ID
  userId    String
  /// 角色ID
  roleId    String
  /// 創建時間
  createdAt DateTime @default(now())
  /// 最後更新時間
  updatedAt DateTime @updatedAt

  // 關聯
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("UserRole")
}

model RolePermission {
  /// 角色權限關聯唯一識別碼
  id           String   @id @default(uuid())
  /// 角色ID
  roleId       String
  /// 權限ID
  permissionId String
  /// 創建時間
  createdAt    DateTime @default(now())
  /// 最後更新時間
  updatedAt    DateTime @updatedAt

  // 關聯
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("RolePermission")
}

model Project {
  /// 專案唯一識別碼
  id            String   @id @default(uuid())
  /// 專案編號（自動生成，格式：systemCode_YYYYMMDD_HHMMSS_XXXXX）
  projectNumber String   @unique
  /// 系統代碼
  systemCode    String
  /// 專案名稱
  name          String
  /// 專案描述
  description   String?
  /// 專案狀態（active 進行中、completed 已完成、cancelled 已取消, temporary 臨時專案, 給節點測試時使用
  status        String   @default("active")
  /// 創建者ID
  createdBy     String
  /// 最後更新者ID
  updatedBy     String
  /// 創建時間
  createdAt     DateTime @default(now())
  /// 最後更新時間
  updatedAt     DateTime @updatedAt
  

  // 關聯
  creator      User           @relation("CreatedProjects", fields: [createdBy], references: [id])
  updater      User           @relation("UpdatedProjects", fields: [updatedBy], references: [id])
  flowInstances FlowInstance[]
  flowDocuments FlowDocument[]
  dataSnapshots DataSnapshot[]

  @@map("Project")
}

// 數據快照(將每次執行的結果儲存)
model DataSnapshot {
  /// 數據快照唯一識別碼
  id             String   @id @default(uuid())

  projectId      String @db.VarChar(150)
  /// 關聯的流程實例ID
  flowInstanceId String @db.VarChar(150)

  projectDetail Json?     @default(dbgenerated("(JSON_OBJECT())"))
  nodes          Json?    @default(dbgenerated("(JSON_OBJECT())"))
  /// 邊緣數據
  edges          Json?    @default(dbgenerated("(JSON_OBJECT())"))
  /// 節點詳細數據
  nodeData       Json?    @default(dbgenerated("(JSON_OBJECT())"))
  /// 上下文數據
  context        Json?    @default(dbgenerated("(JSON_OBJECT())"))
  /// 快照描述
  description    String?
  /// 創建者ID
  createdBy      String
  /// 創建時間
  createdAt      DateTime @default(now())

  project      Project      @relation(fields: [projectId], references: [id])
  flowInstance FlowInstance @relation("FlowInstanceDataSnapshots", fields: [flowInstanceId], references: [id])
  creator      User         @relation("UserDataSnapshots", fields: [createdBy], references: [id])


  @@map("DataSnapshot")
}

//TODO: 棄用
// model FileNode {
//   /// 檔案節點唯一識別碼
//   id             String   @id @default(uuid())

//   projectId      String
  
//   /// 所屬工作流程實例ID
//   flowInstanceId String
//   /// 檔案名稱
//   fileName       String
//   /// 原始檔案名稱
//   originalName   String
//   /// 檔案存儲路徑
//   fileUrl        String
//   /// 檔案大小（bytes）
//   fileSize       Int
//   /// 縮圖路徑
//   thumbnailPath  String?
//   /// 檔案類型（結案報告、不良照片等）
//   fileType       String
//   /// 檔案狀態（pending、processing、completed、error）
//   status         String   @default("pending")
//   /// X軸座標位置
//   positionX      Int
//   /// Y軸座標位置
//   positionY      Int
//   /// 節點樣式（JSON字串）
//   style          String? // JSON string
//   /// 檔案描述
//   description    String?
//   /// 上傳者ID
//   uploadedBy     String
//   /// 創建時間
//   createdAt      DateTime @default(now())
//   /// 最後更新時間
//   updatedAt      DateTime @updatedAt

//   // 關聯
//   flowInstance FlowInstance @relation(fields: [flowInstanceId], references: [id])
//   uploader     User         @relation("UploadedFiles", fields: [uploadedBy], references: [id])

//   @@map("FileNode")
// }

model ApiLog {
  /// API日誌唯一識別碼
  id             String   @id @default(uuid())
  /// 關聯的節點實例ID
  nodeInstanceId String
  /// API端點
  apiEndpoint    String
  /// 請求方法（GET、POST等）
  requestMethod  String
  /// 請求內容（JSON字串）
  requestBody    String? // JSON string
  /// 響應內容（JSON字串）
  responseBody   String? // JSON string
  /// HTTP狀態碼
  statusCode     Int
  /// 執行時間（毫秒）
  executionTime  Int
  /// 創建時間
  createdAt      DateTime @default(now())

  @@map("ApiLog")
}

model SystemCode {
  /// 系統代碼，作為主鍵
  systemCode        String   @id
  /// 系統名稱
  systemName        String
  /// 系統描述
  systemDescription String?
  /// 是否啟用
  isEnabled         Boolean  @default(true)
  /// 創建時間
  createdAt         DateTime @default(now())
  /// 最後更新時間
  updatedAt         DateTime @updatedAt
  /// 創建者ID
  createdBy         String
  /// 最後更新者ID
  updatedBy         String

  // 關聯
  creator User @relation("CreatedSystemCodes", fields: [createdBy], references: [id])
  updater User @relation("UpdatedSystemCodes", fields: [updatedBy], references: [id])

  @@map("SystemCode")
}

model FlowNodeDefinition {
  /// 主鍵
  id            String   @id @default(uuid())
  /// 業務分類
  category      String   @db.VarChar(150)
  /// 節點名稱
  name          String   @db.VarChar(150)
  /// 節點描述
  description   String   @db.VarChar(255)
  /// 節點詳細說明
  helpContent   String?  @db.Text
  /// 節點圖示
  icon          String?  @db.VarChar(50)
  /// Vue 組件名稱
  componentName String   @db.VarChar(150)
  /// Vue 組件路徑（選填）
  componentPath String?  @db.VarChar(150)

  // 參與者類型 (man, robot)
  assigneeType String? @db.VarChar(50)

  /// 預設配置
  config        Json     @default(dbgenerated("(JSON_OBJECT())"))
  /// UI 配置
  uiConfig      Json     @default(dbgenerated("(JSON_OBJECT())"))
  /// 連接點配置
  handles       Json     @default(dbgenerated("(JSON_OBJECT())"))
  /// 創建時間
  createdAt     DateTime @default(now())
  /// 創建者ID
  createdBy     String?
  /// 最後更新時間
  updatedAt     DateTime @updatedAt
  /// 更新者ID
  updatedBy     String?

  // 關聯
  templates FlowTemplate[]
  creator   User?          @relation("CreatedFlowNodeDefinitions", fields: [createdBy], references: [id])
  updater   User?          @relation("UpdatedFlowNodeDefinitions", fields: [updatedBy], references: [id])

  @@map("FlowNodeDefinition")
}

model FlowTemplate {
  /// 主鍵
  id          String   @id @default(uuid())
  /// 模板名稱
  name        String   @db.VarChar(150)
  /// 模板描述
  description String   @db.VarChar(255)
  /// 模板類型
  type        String   @db.VarChar(50)
  /// 版本號
  version     String   @db.VarChar(20)
  /// 模板狀態（inactive、active、deleted）
  status      String   @default("inactive")
  /// 節點定義
  nodes       Json     @default(dbgenerated("(JSON_ARRAY())"))
  /// 邊緣定義
  edges       Json     @default(dbgenerated("(JSON_ARRAY())"))
  /// 模板配置
  metadata    Json?
  /// 縮圖（base64 或 URL）
  thumbnail   String?  @db.LongText
  /// 是否已刪除（軟刪除）
  isDeleted   Boolean  @default(false)
  /// 創建者ID
  createdBy   String?
  /// 更新者ID
  updatedBy   String?
  /// 創建時間
  createdAt   DateTime @default(now())
  /// 最後更新時間
  updatedAt   DateTime @updatedAt

  // 關聯
  nodeDefinitions FlowNodeDefinition[]
  instances       FlowInstance[]
  creator         User?                @relation("CreatedFlowTemplates", fields: [createdBy], references: [id])
  updater         User?                @relation("UpdatedFlowTemplates", fields: [updatedBy], references: [id])

  @@map("FlowTemplate")
}

/// 工作流程實例
model FlowInstance {
  /// 流程實例唯一識別碼
  id String @id @default(uuid())

  /// 關聯的專案ID
  projectId  String
  /// 關聯的模板ID
  templateId String

  /// 自定義說明
  description String? @db.VarChar(300)
  
  /// 實例狀態 (draft，active，completed，cancelled)
  status String @default("draft")

  /// 節點定義（包含位置、配置等）//NOTE: 直接對應 vue-flow 的 nodes
  /// 示例：
  /// {
  ///   id: string          // 節點唯一識別碼
  ///   type: string        // 節點類型
  ///   position: {x, y}    // 節點位置
  ///   data: {            // 節點數據
  ///     label: string     // 節點標籤
  ///     config: object    // 節點配置
  ///   }
  /// }[]
  nodes Json @default(dbgenerated("(JSON_ARRAY())"))

  /// 連線定義（包含來源和目標節點的連接點）//NOTE: 直接對應 vue-flow 的 edges
  /// 示例：
  /// {
  ///   id: string          // 連線唯一識別碼
  ///   source: string      // 來源節點ID
  ///   target: string      // 目標節點ID
  ///   sourceHandle: string // 來源連接點
  ///   targetHandle: string // 目標連接點
  /// }[]
  edges Json @default(dbgenerated("(JSON_ARRAY())"))

  /// 執行上下文（存儲節點間的數據傳遞）
  ///示例:
  ///{"sharedData": {"node_1745897129122": {"detail": {"output": {"employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "nodeType": "EmployeeDataInputNode", "completed": true, "timestamp": "2025-05-02T04:16:34.270Z", "employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "nodeId": "node_1745897129122", "nodeName": "員工數據導入", "timestamp": "2025-05-02T04:16:34.314Z"}, "node_1745897134286": {"detail": {"goals": [{"title": "提升核心技能", "targetDate": "2024-12-31", "description": "在未來6個月內提升核心技能水平，達到職位要求"}, {"title": "掌握進階技能", "targetDate": "2025-06-30", "description": "在未來12個月內掌握必要的進階技能"}, {"title": "提升績效表現", "targetDate": "2024-09-30", "description": "在下一季度提升績效評分至優秀水平"}], "notes": ["定期與主管進行發展計劃進度檢討", "保持學習記錄和成果證明", "及時調整計劃以應對變化", "積極參與團隊分享和知識傳遞"], "metrics": [{"name": "核心技能達標率", "type": "success", "target": "80%", "description": "核心技能達到職位要求的比例"}, {"name": "進階技能掌握度", "type": "warning", "target": "60%", "description": "進階技能的掌握程度"}, {"name": "績效評分", "type": "success", "target": "4.5/5", "description": "季度績效評分"}], "actionPlans": [{"title": "核心技能培訓", "timeline": "2024-07-01 至 2024-12-31", "resources": ["內部培訓課程", "線上學習平台", "導師指導"], "description": "參加公司內部培訓課程和線上學習平台"}, {"title": "進階技能學習", "timeline": "2024-10-01 至 2025-06-30", "resources": ["專案實踐機會", "技術研討會", "專業書籍"], "description": "參與專案實踐和技術研討會"}, {"title": "績效提升計劃", "timeline": "2024-07-01 至 2024-09-30", "resources": ["主管指導", "績效反饋", "工作改進工具"], "description": "制定具體的工作改進計劃，定期與主管溝通"}], "employeeInfo": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "generatedTime": "2025-05-02T04:16:39.530Z"}, "nodeId": "node_1745897134286", "nodeName": "個人發展計劃生成", "timestamp": "2025-05-02T04:16:39.588Z"}, "node_1745897140407": {"detail": {"analysisTime": "2025-05-02T04:16:36.869Z", "coreSkillGaps": [{"gap": 5, "skill": "Vue.js", "current": 75, "required": 80}, {"gap": 10, "skill": "Node.js", "current": 65, "required": 75}], "skillStrengths": [], "developmentPath": [{"skill": "Vue.js 進階課程", "description": "完成 Vue.js 進階課程，從 75% 提升至 80% 水平"}, {"skill": "Node.js 進階課程", "description": "完成 Node.js 進階課程，從 65% 提升至 75% 水平"}, {"skill": "微服務架構 基礎課程", "description": "完成 微服務架構 基礎課程，從 40% 提升至 60% 水平"}], "advancedSkillGaps": [{"gap": 20, "skill": "微服務架構", "current": 40, "required": 60}, {"gap": 20, "skill": "雲端部署", "current": 45, "required": 65}, {"gap": 20, "skill": "DevOps", "current": 35, "required": 55}], "trainingSuggestions": ["建議優先提升核心技能：Vue.js、Node.js", "建議逐步提升進階技能：微服務架構、雲端部署、DevOps"]}, "nodeId": "node_1745897140407", "nodeName": "技能缺口分析", "timestamp": "2025-05-02T04:16:36.946Z"}, "node_1745897146596": {"detail": {"trend": "上升", "maxScore": 92, "minScore": 85, "stability": "一般", "analysisTime": "2025-05-02T04:16:38.416Z", "averageScore": 89, "recommendations": [], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "nodeId": "node_1745897146596", "nodeName": "績效趨勢分析", "timestamp": "2025-05-02T04:16:38.466Z"}}, "statistics": {"errorNodes": 0, "totalNodes": 4, "lastUpdated": "2025-05-02T04:16:39.605Z", "completedNodes": 3, "executionProgress": 75}, "globalVariables": {"employeeId": "EMP001", "employeeData": {"output": {"employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "nodeType": "EmployeeDataInputNode", "completed": true, "timestamp": "2025-05-02T04:16:34.270Z", "employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "skillAnalysis": {"analysisTime": "2025-05-02T04:16:36.869Z", "coreSkillGaps": [{"gap": 5, "skill": "Vue.js", "current": 75, "required": 80}, {"gap": 10, "skill": "Node.js", "current": 65, "required": 75}], "skillStrengths": [], "developmentPath": [{"skill": "Vue.js 進階課程", "description": "完成 Vue.js 進階課程，從 75% 提升至 80% 水平"}, {"skill": "Node.js 進階課程", "description": "完成 Node.js 進階課程，從 65% 提升至 75% 水平"}, {"skill": "微服務架構 基礎課程", "description": "完成 微服務架構 基礎課程，從 40% 提升至 60% 水平"}], "advancedSkillGaps": [{"gap": 20, "skill": "微服務架構", "current": 40, "required": 60}, {"gap": 20, "skill": "雲端部署", "current": 45, "required": 65}, {"gap": 20, "skill": "DevOps", "current": 35, "required": 55}], "trainingSuggestions": ["建議優先提升核心技能：Vue.js、Node.js", "建議逐步提升進階技能：微服務架構、雲端部署、DevOps"]}, "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "developmentPlan": {"goals": [{"title": "提升核心技能", "targetDate": "2024-12-31", "description": "在未來6個月內提升核心技能水平，達到職位要求"}, {"title": "掌握進階技能", "targetDate": "2025-06-30", "description": "在未來12個月內掌握必要的進階技能"}, {"title": "提升績效表現", "targetDate": "2024-09-30", "description": "在下一季度提升績效評分至優秀水平"}], "notes": ["定期與主管進行發展計劃進度檢討", "保持學習記錄和成果證明", "及時調整計劃以應對變化", "積極參與團隊分享和知識傳遞"], "metrics": [{"name": "核心技能達標率", "type": "success", "target": "80%", "description": "核心技能達到職位要求的比例"}, {"name": "進階技能掌握度", "type": "warning", "target": "60%", "description": "進階技能的掌握程度"}, {"name": "績效評分", "type": "success", "target": "4.5/5", "description": "季度績效評分"}], "actionPlans": [{"title": "核心技能培訓", "timeline": "2024-07-01 至 2024-12-31", "resources": ["內部培訓課程", "線上學習平台", "導師指導"], "description": "參加公司內部培訓課程和線上學習平台"}, {"title": "進階技能學習", "timeline": "2024-10-01 至 2025-06-30", "resources": ["專案實踐機會", "技術研討會", "專業書籍"], "description": "參與專案實踐和技術研討會"}, {"title": "績效提升計劃", "timeline": "2024-07-01 至 2024-09-30", "resources": ["主管指導", "績效反饋", "工作改進工具"], "description": "制定具體的工作改進計劃，定期與主管溝通"}], "employeeInfo": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "generatedTime": "2025-05-02T04:16:39.530Z"}, "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}], "performanceAnalysis": {"trend": "上升", "maxScore": 92, "minScore": 85, "stability": "一般", "analysisTime": "2025-05-02T04:16:38.416Z", "averageScore": 89, "recommendations": [], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}}
  context Json @default(dbgenerated("(JSON_OBJECT())"))

  /// 節點數據（存儲每個節點執行後的數據）
  /// 示例：
  /// [{"id": "node_1745897129122", "result": {"nodeId": "node_1745897129122", "output": {"employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "nodeName": "員工數據導入", "nodeType": "EmployeeDataInputNode", "completed": true, "timestamp": "2025-05-02T04:16:34.325Z", "employeeId": "EMP001", "employeeDetail": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}}, "lastUpdate": "2025-05-02T04:16:34.325Z"}, {"id": "node_1745897134286", "result": {"goals": [{"title": "提升核心技能", "targetDate": "2024-12-31", "description": "在未來6個月內提升核心技能水平，達到職位要求"}, {"title": "掌握進階技能", "targetDate": "2025-06-30", "description": "在未來12個月內掌握必要的進階技能"}, {"title": "提升績效表現", "targetDate": "2024-09-30", "description": "在下一季度提升績效評分至優秀水平"}], "notes": ["定期與主管進行發展計劃進度檢討", "保持學習記錄和成果證明", "及時調整計劃以應對變化", "積極參與團隊分享和知識傳遞"], "nodeId": "node_1745897134286", "metrics": [{"name": "核心技能達標率", "type": "success", "target": "80%", "description": "核心技能達到職位要求的比例"}, {"name": "進階技能掌握度", "type": "warning", "target": "60%", "description": "進階技能的掌握程度"}, {"name": "績效評分", "type": "success", "target": "4.5/5", "description": "季度績效評分"}], "nodeName": "個人發展計劃生成", "timestamp": "2025-05-02T04:16:39.614Z", "actionPlans": [{"title": "核心技能培訓", "timeline": "2024-07-01 至 2024-12-31", "resources": ["內部培訓課程", "線上學習平台", "導師指導"], "description": "參加公司內部培訓課程和線上學習平台"}, {"title": "進階技能學習", "timeline": "2024-10-01 至 2025-06-30", "resources": ["專案實踐機會", "技術研討會", "專業書籍"], "description": "參與專案實踐和技術研討會"}, {"title": "績效提升計劃", "timeline": "2024-07-01 至 2024-09-30", "resources": ["主管指導", "績效反饋", "工作改進工具"], "description": "制定具體的工作改進計劃，定期與主管溝通"}], "employeeInfo": {"id": "EMP001", "joinDate": "2023-01-01", "position": "資深工程師", "department": "研發部", "workingHours": [{"hours": 176, "month": "2023-01"}, {"hours": 168, "month": "2023-02"}, {"hours": 184, "month": "2023-03"}], "trainingRecords": [{"date": "2023-03-15", "hours": 8, "course": "Vue.js 進階"}, {"date": "2023-06-20", "hours": 16, "course": "TypeScript 實戰"}], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "generatedTime": "2025-05-02T04:16:39.530Z"}, "lastUpdate": "2025-05-02T04:16:39.614Z"}, {"id": "node_1745897140407", "result": {"nodeId": "node_1745897140407", "nodeName": "技能缺口分析", "timestamp": "2025-05-02T04:16:36.958Z", "analysisTime": "2025-05-02T04:16:36.869Z", "coreSkillGaps": [{"gap": 5, "skill": "Vue.js", "current": 75, "required": 80}, {"gap": 10, "skill": "Node.js", "current": 65, "required": 75}], "skillStrengths": [], "developmentPath": [{"skill": "Vue.js 進階課程", "description": "完成 Vue.js 進階課程，從 75% 提升至 80% 水平"}, {"skill": "Node.js 進階課程", "description": "完成 Node.js 進階課程，從 65% 提升至 75% 水平"}, {"skill": "微服務架構 基礎課程", "description": "完成 微服務架構 基礎課程，從 40% 提升至 60% 水平"}], "advancedSkillGaps": [{"gap": 20, "skill": "微服務架構", "current": 40, "required": 60}, {"gap": 20, "skill": "雲端部署", "current": 45, "required": 65}, {"gap": 20, "skill": "DevOps", "current": 35, "required": 55}], "trainingSuggestions": ["建議優先提升核心技能：Vue.js、Node.js", "建議逐步提升進階技能：微服務架構、雲端部署、DevOps"]}, "lastUpdate": "2025-05-02T04:16:36.958Z"}, {"id": "node_1745897146596", "result": {"trend": "上升", "nodeId": "node_1745897146596", "maxScore": 92, "minScore": 85, "nodeName": "績效趨勢分析", "stability": "一般", "timestamp": "2025-05-02T04:16:38.482Z", "analysisTime": "2025-05-02T04:16:38.416Z", "averageScore": 89, "recommendations": [], "performanceRecords": [{"score": 85, "quarter": "2023-Q1"}, {"score": 88, "quarter": "2023-Q2"}, {"score": 90, "quarter": "2023-Q3"}, {"score": 92, "quarter": "2023-Q4"}]}, "lastUpdate": "2025-05-02T04:16:38.482Z"}]
  nodeData Json? @default(dbgenerated("(JSON_OBJECT())"))

  /// 節點上下文（存儲節點執行結果）//TODO: 棄用
  /// 示例：
  /// {
  ///   [nodeId: string]: {
  ///     input: any        // 節點輸入數據
  ///     output: any       // 節點輸出數據
  ///     executionTime: number // 執行時間（秒）
  ///   }
  /// }
  nodeContext Json? @default(dbgenerated("(JSON_OBJECT())"))

  /// 節點狀態（記錄每個節點的執行狀態）
  /// 示例：
  /// [{"id": "node_1745897129122", "error": null, "status": "completed", "lastUpdate": "2025-05-02T04:16:34.325Z"}, {"id": "node_1745897134286", "error": null, "status": "completed", "lastUpdate": "2025-05-02T04:16:39.614Z"}, {"id": "node_1745897140407", "error": null, "status": "completed", "lastUpdate": "2025-05-02T04:16:36.958Z"}, {"id": "node_1745897146596", "error": null, "status": "completed", "lastUpdate": "2025-05-02T04:16:38.482Z"}]
  nodeStates Json? @default(dbgenerated("(JSON_OBJECT())"))

  /// 執行日誌 //TODO: 現在是儲存節點的 executionHistory, 應該要再調整
  /// 示例：
  /// {
  ///   type: string        // SYSTEM, NODE, ERROR
  ///   nodeId?: string     // 相關節點ID
  ///   message: string     // 日誌信息
  ///   timestamp: string   // 時間戳
  ///   data?: any         // 額外數據
  /// }[]
  logs Json? @default(dbgenerated("(JSON_ARRAY())"))

  /// 最後執行的節點ID（用於恢復執行）!TODO: 備著，應該用不上
  //#TODO: remove lastNodeId String?

  /// 錯誤信息（當 status 為 failed 時）
  error String?

  /// 開始執行時間
  startedAt DateTime?
  /// 完成時間
  endedAt   DateTime?
  /// 暫停時間
  //pausedAt  DateTime?

  /// 創建者ID
  createdBy String
  /// 更新者ID
  updatedBy String
  /// 創建時間
  createdAt DateTime @default(now())
  /// 更新時間
  updatedAt DateTime @updatedAt

  // 關聯
  project   Project        @relation(fields: [projectId], references: [id])
  template  FlowTemplate   @relation(fields: [templateId], references: [id])
  //fileNodes FileNode[]
  creator   User           @relation("CreatedFlowInstances", fields: [createdBy], references: [id])
  updater   User           @relation("UpdatedFlowInstances", fields: [updatedBy], references: [id])
  dataSnapshots DataSnapshot[] @relation("FlowInstanceDataSnapshots")
  documents FlowDocument[]

  @@index([projectId])
  @@index([templateId])
  @@index([status])
  @@index([createdBy])
  @@map("FlowInstance")
}

model FlowDocument {
  /// 主鍵
  id         String   @id @default(uuid())
  /// 關聯的專案ID
  projectId  String
  /// 關聯的工作流實例ID
  instanceId String
  /// 關聯的節點ID
  nodeId     String?
  /// 文檔類型（report、image、attachment）
  docType    String
  /// 文檔名稱
  name       String
  /// 存儲路徑
  url        String
  /// 文檔元數據
  metadata   Json?
  /// 創建者ID
  createdBy  String?
  /// 創建時間
  createdAt  DateTime @default(now())

  // 關聯
  project  Project      @relation(fields: [projectId], references: [id])
  instance FlowInstance @relation(fields: [instanceId], references: [id])
  creator  User?        @relation("CreatedFlowDocuments", fields: [createdBy], references: [id])

  @@map("FlowDocument")
}

model OrgEmployee {
  /// 帳號代碼
  accountGuid            String    @id @map("account_guid") @db.VarChar(50)
  /// group 部門關聯
  groupId                String?   @map("group_id") @db.VarChar(50)
  /// groupName 部門名
  groupName              String?   @map("group_name") @db.VarChar(50)
  /// groupCode 部門代碼
  groupCode              String?   @map("group_code") @db.VarChar(50)
  /// name 姓名
  name                   String?   @db.VarChar(50)
  /// nickname 暱稱
  nickname               String?   @db.VarChar(50)
  /// accountMapping 帳號對應欄位
  accountMapping         String?   @map("account_mapping") @db.VarChar(20)
  /// password 密碼
  password               String?   @db.VarChar(25)
  /// email 信箱
  email                  String?   @db.VarChar(100)
  /// passwordInvalidAttempts 密碼試錯次數
  passwordInvalidAttempts Int?      @map("password_invalid_attempts") @db.TinyInt
  /// pwResetReason 密碼重置設定原因
  pwResetReason          Int?      @map("pw_reset_reason") @db.TinyInt
  /// isPasswordReset 是否重設密碼
  isPasswordReset        Boolean?  @map("is_password_reset") @db.Bit(1)
  /// isLockedOut 是否鎖定
  isLockedOut            Boolean?  @map("is_locked_out") @db.Bit(1)
  /// activityDate 帳號使用日期
  activityDate           DateTime? @map("activity_date") @db.DateTime(3)
  /// expireDate 帳號到期日
  expireDate             DateTime? @map("expire_date") @db.DateTime(3)
  /// lastActivityDate 最後使用日期
  lastActivityDate       DateTime? @map("last_activity_date") @db.DateTime(3)
  /// lastLockedOutDate 最後鎖定日期
  lastLockedOutDate      DateTime? @map("last_locked_out_date") @db.DateTime(3)
  /// lastPasswordChangeDate 最後密碼變更日期
  lastPasswordChangeDate DateTime? @map("last_password_change_date") @db.DateTime(3)
  /// isSuspended 是否暫停
  isSuspended            Boolean?  @map("is_suspended") @db.Bit(1)
  /// lastSuspendedDate 最後停用日期
  lastSuspendedDate      DateTime? @map("last_suspended_date") @db.DateTime(3)
  /// isUpdatePersonalInfo 是否更新個人資訊
  isUpdatePersonalInfo   Boolean?  @map("is_update_personal_info") @db.Bit(1)
  /// lastUpdatePersonalInfoDate 記錄最後一次更新
  lastUpdatePersonalInfoDate String?   @map("last_update_personal_info_date") @db.VarChar(50)
  /// userType 帳號類別
  userType               String?   @map("user_type") @db.VarChar(50)
  /// cardNo 人員卡片號碼
  cardNo                 String?   @map("card_no") @db.VarChar(50)
  /// employeeNo 員工編號
  employeeNo             String?   @map("employee_no") @db.VarChar(50) @unique
  /// domain AD網域
  domain                 String?   @db.VarChar(50)
  /// account AD帳號
  account                String?   @db.VarChar(50)
  /// caSerialNum CA憑證號碼
  caSerialNum            String?   @map("ca_serial_num") @db.VarChar(50)
  /// isUsbAuth 是否USB認證
  isUsbAuth              Boolean?  @map("is_usb_auth") @db.Bit(1)
  /// usbKey USB密碼
  usbKey                 String?   @map("usb_key") @db.VarChar(50)
  /// lang 語系
  lang                   String?   @db.VarChar(50)
  /// emailA 電子郵件 1
  emailA                 String?   @map("email_a") @db.VarChar(50)
  /// emailB 電子郵件 2
  emailB                 String?   @map("email_b") @db.VarChar(50)
  /// emailC 電子郵件 3
  emailC                 String?   @map("email_c") @db.VarChar(50)
  /// emailD 電子郵件 4
  emailD                 String?   @map("email_d") @db.VarChar(50)
  /// address 地址
  address                String?   @db.VarChar(50)
  /// arriveDate 到職日
  arriveDate             DateTime? @map("arrive_date") @db.DateTime(3)
  /// leaveDate 離職日
  leaveDate              DateTime? @map("leave_date") @db.DateTime(3)
  /// birthday 生日
  birthday               DateTime? @db.Date
  /// sex 性別
  sex                    String?   @db.VarChar(5)
  /// telephone 電話
  telephone              String?   @map("telphone") @db.VarChar(50)
  /// extNum 分機
  extNum                 String?   @map("ext_num") @db.VarChar(50)
  /// mobile 手機
  mobile                 String?   @db.VarChar(50)
  /// photo 照片
  photo                  String?   @db.VarChar(50)
  /// signature 簽名
  signature              String?   @db.VarChar(50)
  /// line LINE帳號
  line                   String?   @db.VarChar(50)
  /// skype SKYPE帳號
  skype                  String?   @db.VarChar(50)
  /// wechat WECHAT帳號
  wechat                 String?   @db.VarChar(50)
  /// updatedAt 最後同步時間
  updatedAt              DateTime? @map("updated_at") @db.DateTime(3)
  /// titleName 職稱
  titleName              String?   @map("title_name") @db.VarChar(100)
  /// titleRank 數字越小職等越大
  titleRank              Int?      @map("title_rank") @db.TinyInt

  @@map("org_employee")
}

model OrgGroup {
  /// 01 groupId  群組代碼
  groupId       String    @id @map("group_id") @db.VarChar(50)
  /// 02 groupType 群組類別（Department...）
  groupType     String?   @map("group_type") @db.VarChar(10)
  /// 03 groupName 群組名稱
  groupName     String?   @map("group_name") @db.VarChar(50)
  /// 04 parentGroupId 父群組代碼
  parentGroupId String?   @map("parent_group_id") @db.VarChar(50)
  /// 05 lft 左排序
  lft           Int?      @map("lft") @db.TinyInt
  /// 06 rgt 右排序
  rgt           Int?      @map("rgt") @db.TinyInt
  /// 07 lev 階層
  lev           Int?      @map("lev") @db.TinyInt
  /// 08 groupCode 群組代號
  groupCode     String?   @map("group_code") @db.VarChar(50)
  /// 09 active 是否啟用
  active        Boolean?  @map("active") @db.Bit(1)
  /// 10 companyId 公司別/ID
  companyId     String?   @map("company_id") @db.VarChar(50)
  /// 11 updatedAt 更新時間
  updatedAt     DateTime? @map("updated_at") @db.DateTime(3)

  @@map("org_group")
}

model IssueTicket {
  /// 問題單唯一識別碼
  id          String   @id @default(uuid())
  /// 問題標題
  title       String   @db.VarChar(255)
  /// 問題內容 (Markdown 格式)
  content     String   @db.Text
  /// 問題類型 (bug/feature/task)
  type        String   @db.VarChar(50)
  /// 問題分類 (frontend/backend/analysis)
  category    String   @db.VarChar(50)
  /// 系統代碼 (預設為 IYM)
  systemCode  String   @default("IYM") @db.VarChar(20)
  /// 優先級 (low/medium/high/critical)
  priority    String   @db.VarChar(20)
  /// 問題狀態 (open/in_progress/resolved/closed)
  status      String   @default("open") @db.VarChar(20)
  /// 截圖檔案路徑
  screenshot  String?  @db.VarChar(255)
  /// 評論/備註
  comment     String?  @db.Text
  /// 回報者ID
  reporterId  String
  /// 建立時間
  createdAt   DateTime @default(now())
  /// 更新時間
  updatedAt   DateTime @updatedAt

  // 關聯
  reporter  User  @relation("ReportedIssues", fields: [reporterId], references: [id])

  @@index([reporterId])
  @@index([status])
  @@index([systemCode])
  @@map("IssueTicket")
}

/// 使用者關注資源表
model UserFavorite {
  /// 資源類型 (project/flow/report)
  type       String   @db.VarChar(100)
  /// 資源ID (projectId/flowInstanceId/reportId)
  resourceId String   @db.VarChar(100)
  /// URL路徑
  path       String   @db.VarChar(200)
  /// 創建時間
  createdAt  DateTime @default(now())
  /// 創建者ID
  createdBy  String   @db.VarChar(191)

  // 關聯
  user User @relation("UserFavorites", fields: [createdBy], references: [id], onDelete: Cascade)

  @@id([type, resourceId, createdBy])
  @@unique([type, resourceId], name: "userfavorite_un")
  @@index([createdBy])
  @@index([type])
  @@map("userfavorite")
}