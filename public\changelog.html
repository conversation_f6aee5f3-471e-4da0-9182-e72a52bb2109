<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFDA IYM 前端更新日誌</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            color: #2c3e50;
            font-size: 15px;
        }
        
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #409eff;
            border-bottom: 2px solid #eaeaea;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 28px;
        }

        h2 {
            color: #606266;
            margin-top: 30px;
            font-size: 24px;
        }

        h3 {
            font-size: 20px;
        }

        .version {
            background-color: #f0f9eb;
            border-left: 4px solid #67c23a;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 15px;
        }

        .version h3 {
            color: #67c23a;
            margin: 0 0 10px 0;
        }

        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .tech-item {
            background-color: #ecf5ff;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            font-size: 15px;
        }

        .feature-list li {
            margin: 10px 0;
            padding-left: 25px;
            position: relative;
        }

        .feature-list li:before {
            content: "✓";
            color: #67c23a;
            position: absolute;
            left: 0;
        }

        .warning {
            background-color: #fef0f0;
            border-left: 4px solid #f56c6c;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            color: #f56c6c;
            font-size: 15px;
        }

        .info {
            background-color: #f4f4f5;
            border-left: 4px solid #909399;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 15px;
        }

        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            font-size: 14px;
        }

        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 13px;
            margin-right: 5px;
        }

        .tag.new {
            background-color: #f0f9eb;
            color: #67c23a;
        }

        .tag.fix {
            background-color: #fef0f0;
            color: #f56c6c;
        }

        .tag.update {
            background-color: #ecf5ff;
            color: #409eff;
        }

        ul {
            font-size: 15px;
        }

        p {
            font-size: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SFDA IYM 前端更新日誌</h1>
        
        <h2>系統需求</h2>
        <div class="info">
            <h3>環境要求</h3>
            <ul>
                <li>Node.js >= 16.0.0</li>
                <li>npm >= 8.0.0</li>
                <li>現代瀏覽器（Chrome、Firefox、Safari、Edge）最新版本</li>
                <li>建議螢幕解析度 >= 1920x1080</li>
            </ul>
        </div>

        <h2>快速開始</h2>
        <div class="info">
            <h3>安裝與運行</h3>
            <div class="code">
                # 安裝依賴<br>
                npm install<br><br>
                # 開發環境運行<br>
                npm run dev<br><br>
                # 生產環境構建<br>
                npm run build
            </div>
        </div>

        <h2>技術棧</h2>
        <div class="tech-stack">
            <div class="tech-item">Vue 3.4.15</div>
            <div class="tech-item">Vite 5.1.0</div>
            <div class="tech-item">Pinia 3.0.1</div>
            <div class="tech-item">Element Plus 2.5.3</div>
            <div class="tech-item">Vue Flow 1.42.1</div>
            <div class="tech-item">Tailwind CSS 3.4.17</div>
        </div>

        <h2>最新功能</h2>
        <div class="version">
            <h3>v1.3.1 (2025-03-22)</h3>
            <div class="feature-list">
                <li><span class="tag update">更新</span>優化 RBAC 用戶管理介面</li>
                <ul>
                    <li>改進頭像上傳功能
                        <ul>
                            <li>添加紅色警告提示</li>
                            <li>強化操作確認機制</li>
                            <li>優化錯誤處理流程</li>
                        </ul>
                    </li>
                    <li>優化用戶體驗
                        <ul>
                            <li>統一錯誤提示樣式</li>
                            <li>改進視覺反饋效果</li>
                            <li>提升操作流暢度</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <h3>注意事項</h3>
            <p>頭像上傳功能更新後，請注意：</p>
            <ul>
                <li>上傳的頭像將立即生效</li>
                <li>此操作無法復原</li>
                <li>僅支援 JPG、PNG、GIF 格式</li>
                <li>檔案大小不能超過 5MB</li>
            </ul>
        </div>

        <h2>歷史版本</h2>
        <div class="version">
            <h3>v1.3.0 (2025-02-20)</h3>
            <ul class="feature-list">
                <li><span class="tag new">新增</span>改進角色分配功能，支援直接在用戶列表中編輯角色</li>
                <li><span class="tag new">新增</span>添加雙擊編輯功能，提升操作效率</li>
                <li><span class="tag update">更新</span>優化角色標籤顯示，使用不同顏色區分角色類型</li>
                <li><span class="tag update">更新</span>改進用戶資料編輯體驗，郵箱設為唯一標識</li>
            </ul>
        </div>

        <div class="version">
            <h3>v1.2.0 (2025-02-15)</h3>
            <ul class="feature-list">
                <li><span class="tag new">新增</span>RBAC 權限管理系統</li>
                <li><span class="tag update">更新</span>升級核心依賴版本</li>
                <li><span class="tag update">更新</span>優化工作流程設計器性能</li>
                <li><span class="tag update">更新</span>改進用戶介面體驗</li>
            </ul>
        </div>

        <div class="version">
            <h3>v1.1.0 (2025-02-13)</h3>
            <ul class="feature-list">
                <li><span class="tag new">新增</span>API 呼叫節點類型</li>
                <li><span class="tag update">更新</span>優化節點樣式配置</li>
                <li><span class="tag update">更新</span>改進工作流程範本管理</li>
                <li><span class="tag fix">修復</span>修復已知問題</li>
            </ul>
        </div>

        <div class="version">
            <h3>v1.0.0 (2025-02-01)</h3>
            <ul class="feature-list">
                <li><span class="tag new">新增</span>基礎功能實現</li>
                <li><span class="tag new">新增</span>工作流程範本設計</li>
                <li><span class="tag new">新增</span>用戶管理系統</li>
                <li><span class="tag new">新增</span>專案管理功能</li>
            </ul>
        </div>

        <h2>常見問題</h2>
        <div class="info">
            <h3>Q: 如何處理跨域問題？</h3>
            <p>A: 在開發環境中，通過 Vite 的 proxy 配置處理；在生產環境中，需要後端配置 CORS。</p>

            <h3>Q: 如何添加新的節點類型？</h3>
            <p>A: 在 nodeTypes.js 中定義新的節點類型，並在 CustomNode.vue 中添加對應的渲染邏輯。</p>

            <h3>Q: 如何配置開發環境？</h3>
            <p>A: 在 .env.development 文件中配置以下環境變數：</p>
            <div class="code">
                VITE_API_URL=http://localhost:3001/api<br>
                VITE_APP_TITLE=SFDA IYM<br>
                VITE_APP_DESCRIPTION=Improve Your Manufacturing
            </div>
        </div>

        <h2>聯繫方式</h2>
        <div class="info">
            <ul>
                <li>技術支援：<EMAIL></li>
                <li>問題回報：<EMAIL></li>
                <li>官方網站：https://example.com</li>
            </ul>
        </div>
    </div>
</body>
</html> 