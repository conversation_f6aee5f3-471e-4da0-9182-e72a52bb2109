# 資料匯入工具

## 資料匯入工具說明

此資料夾包含資料匯入工具，用於將資料庫備份的JSON檔案轉換為可執行的seed.js代碼。

### dataImporter.js000

這個腳本會讀取 `backend/prisma/backup` 目錄下的JSON備份檔案，並生成一個 `generatedSeed.js` 檔案，該檔案可以用來初始化資料庫。

### runImportAndSeed.js

這個腳本提供一鍵式解決方案，會按順序執行資料匯入和資料庫初始化兩個步驟。

## 使用方法

### 單獨執行資料匯入（不初始化資料庫）

1. 確保 `backend/prisma/backup` 目錄下有備份的JSON檔案，檔案命名格式為 `表名稱_2025xxx.json`
2. 執行以下命令：

```bash
npm run import-data
```

3. 腳本將會生成 `backend/prisma/generatedSeed.js` 檔案

### 執行生成的種子檔案（初始化資料庫）

```bash
node backend/prisma/generatedSeed.js
```

### 一鍵匯入與初始化（推薦使用）

執行以下命令可以一次完成匯入和初始化：

```bash
npm run import-and-seed
```

這個命令會自動執行以下兩個步驟：

1. 從備份JSON檔案生成seed檔案
2. 執行生成的seed檔案初始化資料庫

## 支援的資料表

目前支援以下資料表的轉換：

- user\_\_: 用戶資料
- role\_\_: 角色資料
- userrole: 用戶角色關聯
- permission: 權限資料
- rolepermission: 角色權限關聯
- project: 專案資料
- systemcode: 系統代碼
- flownodedefinition: 流程節點定義
- datasnapshot: 資料快照
- userfavorite: 用戶收藏
- issueticket: 問題單

## 注意事項

- 匯入過程會先清空資料庫中的所有資料，請謹慎使用
- 匯入順序已經設定，以確保資料的依賴關係正確
- 如需新增其他資料表，請修改 `dataImporter.js` 中的 `TABLE_MAPPING` 和 `MODEL_ORDER`
