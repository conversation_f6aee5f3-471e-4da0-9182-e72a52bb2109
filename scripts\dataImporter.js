const fs = require("fs");
const path = require("path");
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

// 讀取備份目錄
const BACKUP_DIR = path.join(__dirname, "../prisma/backup");
const OUTPUT_FILE = path.join(__dirname, "../prisma/generatedSeed.js");

// 資料表映射，用於建立Prisma模型與備份JSON檔案的對應關係
const TABLE_MAPPING = {
  user__: "user",
  role__: "role",
  userrole: "userRole",
  permission: "permission",
  rolepermission: "rolePermission",
  project: "project",
  systemcode: "systemCode",
  flownodedefinition: "flowNodeDefinition",
  datasnapshot: "dataSnapshot",
  userfavorite: "userFavorite",
  issueticket: "issueTicket",
  // org_group: "orgGroup",
  // org_employee: "orgEmployee",
};

// 模型資料關係，用於確保資料按正確順序插入
const MODEL_ORDER = [
  "role",
  "permission",
  "user",
  "userRole",
  "rolePermission",
  "systemCode",
  "project",
  "flowNodeDefinition",
  "dataSnapshot",
  "userFavorite",
  "issueTicket",
  // "orgEmployee",
  // "orgGroup",
];

// 讀取特定資料表備份檔案的函數
function readBackupFile(tableName) {
  try {
    // 尋找匹配的檔案名稱（不區分大小寫）
    const files = fs.readdirSync(BACKUP_DIR);
    const backupFileName = files.find(
      (file) =>
        file.toLowerCase().startsWith(tableName.toLowerCase()) &&
        file.includes("_2025")
    );

    if (!backupFileName) {
      console.log(`找不到${tableName}的備份檔案`);
      return null;
    }

    const filePath = path.join(BACKUP_DIR, backupFileName);
    const fileContent = fs.readFileSync(filePath, "utf8");
    const data = JSON.parse(fileContent);

    // 返回資料陣列
    const key = Object.keys(data)[0];

    // 檢查資料大小，如果過大則限制處理的數量
    let dataArray = data[key];

    // 特別處理大型表格
    if (tableName === "org_employee" || tableName === "org_group") {
      console.log(
        `處理大型表格 ${tableName}，原始數據數量: ${dataArray.length}`
      );

      // 只取前100筆資料處理
      if (dataArray.length > 100) {
        console.log(`資料筆數過多，將只處理前100筆`);
        dataArray = dataArray.slice(0, 100);
      }
    }

    return dataArray;
  } catch (error) {
    console.error(`讀取${tableName}備份檔案時出錯:`, error);
    return null;
  }
}

// 日期格式轉換函數
function formatDateTime(dateTimeStr) {
  if (!dateTimeStr) return null;

  try {
    // 檢查是否已經是ISO格式
    if (dateTimeStr.includes("T") && dateTimeStr.includes("Z")) {
      return dateTimeStr;
    }

    // 處理MySQL/SQLite日期時間格式 (YYYY-MM-DD HH:MM:SS.SSS)
    const date = new Date(dateTimeStr);
    if (isNaN(date.getTime())) {
      // 如果無法解析，嘗試手動解析
      const parts = dateTimeStr.split(/[- :\.]/);
      if (parts.length >= 6) {
        // 手動構建日期
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // 月份從0開始
        const day = parseInt(parts[2]);
        const hour = parseInt(parts[3]);
        const minute = parseInt(parts[4]);
        const second = parseInt(parts[5]);
        const millisecond = parts[6] ? parseInt(parts[6]) : 0;

        const newDate = new Date(
          year,
          month,
          day,
          hour,
          minute,
          second,
          millisecond
        );
        return newDate.toISOString();
      }
      return null;
    }

    return date.toISOString();
  } catch (error) {
    console.error(`日期格式轉換錯誤: ${dateTimeStr}`, error);
    return null;
  }
}

// 將 snake_case 轉換為 camelCase
function snakeToCamel(str) {
  return str.replace(/_([a-z])/g, function (match, group1) {
    return group1.toUpperCase();
  });
}

// 處理欄位名稱轉換
function transformFieldNames(item, modelName) {
  const transformedItem = {};

  // 特殊欄位轉換映射
  const fieldMappings = {
    // 使用者收藏表欄位映射
    userFavorite: {
      resourceid: "resourceId",
      createdat: "createdAt",
      createdby: "createdBy",
    },
    // 組織員工表欄位映射
    orgEmployee: {
      account_guid: "accountGuid",
      group_id: "groupId",
      group_name: "groupName",
      group_code: "groupCode",
      account_mapping: "accountMapping",
      password_invalid_attempts: "passwordInvalidAttempts",
      pw_reset_reason: "pwResetReason",
      is_password_reset: "isPasswordReset",
      is_locked_out: "isLockedOut",
      activity_date: "activityDate",
      expire_date: "expireDate",
      last_activity_date: "lastActivityDate",
      last_locked_out_date: "lastLockedOutDate",
      last_password_change_date: "lastPasswordChangeDate",
      is_suspended: "isSuspended",
      last_suspended_date: "lastSuspendedDate",
      is_update_personal_info: "isUpdatePersonalInfo",
      last_update_personal_info_date: "lastUpdatePersonalInfoDate",
      user_type: "userType",
      card_no: "cardNo",
      employee_no: "employeeNo",
      ca_serial_num: "caSerialNum",
      is_usb_auth: "isUsbAuth",
      usb_key: "usbKey",
      email_a: "emailA",
      email_b: "emailB",
      email_c: "emailC",
      email_d: "emailD",
      arrive_date: "arriveDate",
      leave_date: "leaveDate",
      telphone: "telephone",
      ext_num: "extNum",
      updated_at: "updatedAt",
      title_name: "titleName",
      title_rank: "titleRank",
    },
    // 組織群組表欄位映射
    orgGroup: {
      group_id: "groupId",
      group_type: "groupType",
      group_name: "groupName",
      parent_group_id: "parentGroupId",
      group_code: "groupCode",
      company_id: "companyId",
      updated_at: "updatedAt",
    },
  };

  // 日期時間欄位名稱列表
  const dateTimeFields = [
    "createdAt",
    "updatedAt",
    "createdat",
    "updatedat",
    "startedAt",
    "endedAt",
    "birthday",
    "arriveDate",
    "leaveDate",
    "activityDate",
    "expireDate",
    "lastActivityDate",
    "lastLockedOutDate",
    "lastPasswordChangeDate",
    "lastSuspendedDate",
    "activity_date",
    "expire_date",
    "last_activity_date",
    "last_locked_out_date",
    "last_password_change_date",
    "last_suspended_date",
    "arrive_date",
    "leave_date",
    "updated_at",
  ];

  // 獲取當前模型的欄位映射
  const fieldMapping = fieldMappings[modelName] || {};

  // 轉換欄位
  for (const key in item) {
    // 檢查是否存在特定欄位映射
    let targetKey;
    if (fieldMapping[key]) {
      targetKey = fieldMapping[key];
    } else if (key.includes("_")) {
      // 如果沒有特定的映射但字段中有下劃線，則嘗試自動將 snake_case 轉換為 camelCase
      targetKey = snakeToCamel(key);
    } else {
      // 使用原欄位名
      targetKey = key;
    }

    let value = item[key];

    // 處理日期欄位
    const lowerKey = targetKey.toLowerCase();
    if (
      dateTimeFields.includes(targetKey) ||
      dateTimeFields.some((field) => lowerKey === field.toLowerCase())
    ) {
      if (value && typeof value === "string") {
        value = formatDateTime(value);
      }
    }

    transformedItem[targetKey] = value;
  }

  return transformedItem;
}

// 處理資料轉換的函數
function processDataItem(item, modelName) {
  // 移除不需要的欄位
  delete item.createdAt;
  delete item.updatedAt;

  // 處理欄位名稱及格式轉換
  item = transformFieldNames(item, modelName);

  // 處理特定模型的欄位轉換
  if (modelName === "user") {
    // 將 isActive 從數字轉換為布爾值
    if (item.isActive !== undefined) {
      item.isActive = Boolean(item.isActive);
    }
  }

  // 處理通用的布爾值欄位轉換
  for (const key in item) {
    // 如果欄位名稱含有 'is' 或 'has'，且值為 0 或 1，則轉換為布爾值
    if (
      (key.startsWith("is") || key.startsWith("has")) &&
      (item[key] === 0 || item[key] === 1)
    ) {
      item[key] = Boolean(item[key]);
    }
  }

  return item;
}

// 生成適合seed.js的代碼片段
function generateSeedCode() {
  let seedCode = `// 此檔案由dataImporter.js自動生成，請勿直接修改
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function cleanupData(prisma) {
  // 刪除所有現有數據
  await prisma.dataSnapshot.deleteMany();
  await prisma.userFavorite.deleteMany();
  await prisma.issueTicket.deleteMany();
  await prisma.project.deleteMany();
  await prisma.systemCode.deleteMany();
  await prisma.rolePermission.deleteMany();
  await prisma.userRole.deleteMany();
  await prisma.flowNodeDefinition.deleteMany();
  await prisma.permission.deleteMany();
  await prisma.role.deleteMany();
  await prisma.user.deleteMany();
}

async function main() {
  try {
    console.log('開始清理現有數據...');
    await cleanupData(prisma);
    console.log('數據清理完成');

`;

  // 按模型順序處理資料
  for (const modelName of MODEL_ORDER) {
    // 獲取對應的備份檔案表名
    const tableName = Object.keys(TABLE_MAPPING).find(
      (key) => TABLE_MAPPING[key] === modelName
    );
    if (!tableName) continue;

    const backupData = readBackupFile(tableName);
    if (!backupData || backupData.length === 0) continue;

    // 對每個資料項進行處理，包括類型轉換和移除不必要欄位
    const processedData = backupData.map((item) =>
      processDataItem({ ...item }, modelName)
    );

    seedCode += `
    console.log('開始創建${modelName}數據...');
    const ${modelName}Data = ${JSON.stringify(processedData, null, 2)};
    
    for (const item of ${modelName}Data) {
`;

    // 根據不同模型進行特殊處理
    if (modelName === "user") {
      seedCode += `      // 用戶資料
      await prisma.user.create({ 
        data: item 
      });
`;
    } else if (modelName === "userRole") {
      seedCode += `      // 用戶角色關聯
      await prisma.userRole.create({
        data: {
          id: item.id,
          user: { connect: { id: item.userId } },
          role: { connect: { id: item.roleId } }
        }
      });
`;
    } else if (modelName === "rolePermission") {
      seedCode += `      // 角色權限關聯
      await prisma.rolePermission.create({
        data: {
          id: item.id,
          role: { connect: { id: item.roleId } },
          permission: { connect: { id: item.permissionId } }
        }
      });
`;
    } else {
      seedCode += `      await prisma.${modelName}.create({
        data: item
      });
`;
    }

    seedCode += `    }
    console.log('${modelName}數據創建完成');
`;
  }

  seedCode += `
    console.log('所有數據已成功導入');
  } catch (error) {
    console.error('數據導入出錯:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
`;

  return seedCode;
}

// 執行主函數
function run() {
  try {
    console.log("開始生成seed.js代碼...");
    const seedCode = generateSeedCode();
    fs.writeFileSync(OUTPUT_FILE, seedCode);
    console.log(`生成的代碼已保存到${OUTPUT_FILE}`);
  } catch (error) {
    console.error("生成失敗:", error);
  }
}

run();
