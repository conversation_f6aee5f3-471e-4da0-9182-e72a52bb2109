const { exec } = require("child_process");
const path = require("path");

// 腳本執行路徑
const importer = path.join(__dirname, "./dataImporter.js");
const generatedSeed = path.join(__dirname, "../prisma/generatedSeed.js");

console.log("🔄 開始資料匯入流程...");

// 執行資料匯入腳本
console.log("📥 步驟1：執行資料匯入，生成seed檔案");
exec(`node ${importer}`, (error, stdout, stderr) => {
  if (error) {
    console.error(`❌ 資料匯入出錯: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`❌ 匯入錯誤: ${stderr}`);
    return;
  }

  console.log(stdout);
  console.log("✅ 匯入完成，seed檔案已生成");

  // 執行生成的seed檔案
  console.log("📥 步驟2：執行生成的seed檔案，初始化資料庫");
  exec(`node ${generatedSeed}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ 資料庫初始化出錯: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`❌ 初始化錯誤: ${stderr}`);
      return;
    }

    console.log(stdout);
    console.log("✅ 資料庫初始化完成");
    console.log("🎉 資料匯入與初始化流程成功完成！");
  });
});
