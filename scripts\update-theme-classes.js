#!/usr/bin/env node

/**
 * 批量更新暗黑模式CSS類別的腳本
 *
 * 使用方法:
 * 1. 安裝依賴: npm install glob replace-in-file --save-dev
 * 2. 執行腳本: node scripts/update-theme-classes.js
 */

import { globSync } from "glob";
import * as replaceInFile from "replace-in-file";
import path from "path";
import { fileURLToPath } from "url";

// 獲取當前文件的目錄路徑
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 顏色映射配置 - 開發模式使用的替換（將自定義類別替換為原生類別）
const devModeReplacements = [
  // 替換背景色類別
  {
    from: /\bbg-light-mode\b/g,
    to: "bg-white",
  },
  {
    from: /\bdark:bg-dark-mode\b/g,
    to: "dark:bg-gray-900",
  },
  {
    from: /\btext-light-mode\b/g,
    to: "text-gray-800",
  },
  {
    from: /\bdark:text-dark-mode\b/g,
    to: "dark:text-gray-100",
  },
];

// 顏色映射配置 - 生產模式使用的替換（將原生類別替換為自定義類別）
const prodModeReplacements = [
  // 替換背景色類別
  {
    from: /\bbg-white\s+dark:bg-gray-900\b/g,
    to: "bg-light-mode dark:bg-dark-mode",
  },
  {
    from: /\bbg-white\s+dark:bg-\[#1a1a1a\]\b/g,
    to: "bg-light-mode dark:bg-dark-mode",
  },
  {
    from: /\bbg-white\s+dark:bg-gray-800\b/g,
    to: "bg-light-mode dark:bg-dark-mode",
  },
  {
    from: /\bbg-gray-50\s+dark:bg-gray-900\b/g,
    to: "bg-gray-50 dark:bg-dark-mode",
  },
  {
    from: /\bbg-gray-50\s+dark:bg-gray-800\b/g,
    to: "bg-gray-50 dark:bg-dark-mode",
  },
  {
    from: /\bbg-gray-100\s+dark:bg-gray-800\b/g,
    to: "bg-gray-100 dark:bg-dark-mode",
  },
  {
    from: /\bdark:bg-gray-900\b/g,
    to: "dark:bg-dark-mode",
  },
  {
    from: /\bdark:bg-gray-800\b/g,
    to: "dark:bg-dark-mode",
  },
  {
    from: /\bdark:bg-\[#1a1a1a\]\b/g,
    to: "dark:bg-dark-mode",
  },
  {
    from: /\bdark:bg-mode\b/g,
    to: "dark:bg-dark-mode",
  },
  // 替換文字色類別
  {
    from: /\btext-gray-800\s+dark:text-gray-100\b/g,
    to: "text-light-mode dark:text-dark-mode",
  },
  {
    from: /\btext-gray-800\s+dark:text-gray-200\b/g,
    to: "text-light-mode dark:text-dark-mode",
  },
  {
    from: /\bdark:text-gray-100\b/g,
    to: "dark:text-dark-mode",
  },
  {
    from: /\bdark:text-gray-200\b/g,
    to: "dark:text-dark-mode",
  },
  {
    from: /\bdark:text-gray-300\b/g,
    to: "dark:text-dark-mode",
  },
  {
    from: /\bdark:text-mode\b/g,
    to: "dark:text-dark-mode",
  },
];

// 需要被忽略的目錄和文件
const ignorePatterns = [
  "node_modules",
  "dist",
  ".git",
  "*.md",
  "*.json",
  "*.log",
];

// 檢查命令行參數，決定用哪種替換模式
const args = process.argv.slice(2);
const mode = args.includes("--dev") ? "dev" : "prod";
const replacements =
  mode === "dev" ? devModeReplacements : prodModeReplacements;

async function main() {
  try {
    console.log(`🔍 以 ${mode === "dev" ? "開發" : "生產"} 模式執行替換...`);
    console.log("🔍 找尋需要更新的文件...");

    // 找到所有 Vue 文件
    const projectRoot = path.resolve(process.cwd());
    const sourceDir = path.join(projectRoot, "src");
    const vueFiles = globSync(`${sourceDir}/**/*.vue`, {
      ignore: ignorePatterns.map((p) => `**/${p}`),
    });

    console.log(`📁 找到 ${vueFiles.length} 個 Vue 文件`);

    // 批量執行替換
    for (const replacement of replacements) {
      console.log(`🔄 執行替換: ${replacement.from} -> ${replacement.to}`);

      const results = await replaceInFile.replaceInFile({
        files: vueFiles,
        from: replacement.from,
        to: replacement.to,
        countMatches: true,
      });

      let totalChanges = 0;
      results.forEach((result) => {
        if (result.hasChanged) {
          totalChanges += result.numMatches || 0;
          console.log(
            `✅ 已更新: ${result.file} (${result.numMatches || 0} 處)`
          );
        }
      });

      console.log(`🔢 共替換 ${totalChanges} 處`);
    }

    console.log("✨ 所有更新完成!");
    if (mode === "dev") {
      console.log("💡 提示：已轉換為開發模式的 Tailwind 原生類別。");
      console.log(
        "   在專案準備發布時，請執行 `npm run update-theme` 轉換回主題類別。"
      );
    } else {
      console.log("💡 提示：已轉換為生產模式的自定義主題類別。");
    }
  } catch (error) {
    console.error("❌ 錯誤:", error);
    process.exit(1);
  }
}

main();
