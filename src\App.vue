<template>
  <el-config-provider
    :locale="zhTw"
    :dark="isDark">
    <component :is="layout">
      <router-view />
    </component>
  </el-config-provider>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";
import MainLayout from "./layouts/MainLayout.vue";
import BlankLayout from "./layouts/BlankLayout.vue";
import DocLayout from "./layouts/DocLayout.vue";
import { useThemeMode } from "@/composables/useThemeMode";
import zhTw from "element-plus/dist/locale/zh-tw.mjs";

// 取得主題
const { isDark } = useThemeMode();

// 獲取布局類型
const route = useRoute();
const layout = computed(() => {
  if (route.meta.layout === "blank") return BlankLayout;
  if (route.meta.layout === "doc") return DocLayout;
  return MainLayout;
});
</script>

<style>
/* 移除 FontAwesome 引入 */
</style>
