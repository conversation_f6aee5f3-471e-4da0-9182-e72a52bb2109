import request from "@/api/request";

/**
 * 獲取用戶關注列表
 * @param {String} createdBy - 用戶ID
 * @returns {Promise} - 返回獲取的關注列表
 */
export const getFavorites = (createdBy) => {
  return request.get(`/favorites`, {
    createdBy,
  });
};

/**
 * 新增關注項目
 * @param {Object} data - 關注資料
 * @param {String} data.type - 資源類型 (project/flow/report)
 * @param {String} data.resourceId - 資源ID
 * @param {String} data.name - 用於頁面顯示的名稱
 * @param {String} data.path - URL 路徑
 * @param {String} data.createdBy - 用戶ID
 * @returns {Promise} - 返回新增結果
 */
export const addFavorite = (data) => {
  return request.post(`/favorites`, data);
};

/**
 * 刪除關注項目
 * @param {String} params.type - 資源類型
 * @param {String} params.resourceId - 資源ID
 * @returns {Promise} - 返回刪除結果
 */
export const deleteFavorite = (params) => {
  return request.delete(`/favorites/${params.type}/${params.resourceId}`, {});
};

/**
 * 獲取關注項目詳細資訊
 * @param {Array} items - 關注項目列表
 * @returns {Promise} - 返回詳細資訊
 */
export const getFavoriteDetails = (items) => {
  return request.post(`/favorites/details`, { items });
};
