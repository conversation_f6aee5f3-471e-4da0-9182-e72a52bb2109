import request from "@/api/request";

// 只保留更新冷卻機制
let lastUpdateData = null;
let lastUpdateTime = 0;
const UPDATE_COOLDOWN = 300; // 更新冷卻時間（毫秒）

// 獲取流程實例列表
export const getFlowInstances = (params) => {
  // 確保 simplified 參數是字符串
  const queryParams = {
    ...params,
    simplified: params.simplified?.toString() || "true",
  };

  return request({
    url: "/flow-instances",
    method: "get",
    params: queryParams,
  });
};

// 獲取單個流程實例
export const getFlowInstanceById = (instanceId, params = {}) => {
  return request({
    url: `/flow-instances/${instanceId}`,
    method: "get",
    params,
  });
};

// 創建流程實例
export const createFlowInstance = (data) => {
  return request({
    url: "/flow-instances",
    method: "post",
    data: {
      projectId: data.projectId,
      templateId: data.templateId,
      nodes: data.nodes,
      edges: data.edges,
    },
  });
};

/**
 * 更新流程實例
 * @param {string} id - 流程實例ID
 * @param {object} data - 更新數據
 * @returns {Promise} - 請求Promise
 */
export const updateFlowInstance = (id, data) => {
  // 檢查是否為位置更新 (_dataUpdate)
  if (data._dataUpdate) {
    return request({
      url: `/flow-instances/${id}`,
      method: "put",
      data: data,
      params: { _isDataUpdate: true },
    });
  }

  // 對於結構更新，實現冷卻時間和請求合併
  const now = Date.now();
  if (lastUpdateData && now - lastUpdateTime < UPDATE_COOLDOWN) {
    console.log("flow.js", `流程實例更新過於頻繁，合併請求: ${id}`);
    lastUpdateData = {
      ...lastUpdateData,
      ...data,
      nodes: data.nodes || lastUpdateData.nodes,
      edges: data.edges || lastUpdateData.edges,
    };

    // 返回一個延遲解析的Promise
    return new Promise((resolve) => {
      const waitTime = UPDATE_COOLDOWN - (now - lastUpdateTime);
      setTimeout(() => {
        // 在冷卻時間結束後使用最新的合併數據發送請求
        const mergedData = { ...lastUpdateData };
        lastUpdateData = null; // 清空暫存數據

        request({
          url: `/flow-instances/${id}`,
          method: "put",
          data: mergedData,
          params: mergedData._isDataUpdate
            ? { _isDataUpdate: true }
            : undefined,
        })
          .then(resolve)
          .catch((error) => {
            console.error("flow.js", `流程實例更新失敗: ${id}`, error);
            resolve({ error: error, data: null });
          });
      }, waitTime);
    });
  }

  // 記錄本次更新
  lastUpdateTime = now;
  lastUpdateData = { ...data };

  // 發送請求
  return request({
    url: `/flow-instances/${id}`,
    method: "put",
    data: data,
    params: data._isDataUpdate ? { _isDataUpdate: true } : undefined,
  }).then((response) => {
    lastUpdateData = null; // 成功後清空暫存
    return response;
  });
};

// 刪除流程實例
export const deleteFlowInstance = (instanceId, force = false) => {
  console.log(`flow.js API 刪除流程實例 ${instanceId}，force=${force}`);
  const params = force ? { force: true } : undefined;
  console.log("flow.js API 請求參數:", { params });

  return request({
    url: `/flow-instances/${instanceId}`,
    method: "delete",
    params: params,
  });
};

// 開始執行流程實例
export const startFlowInstance = (instanceId) => {
  console.log(`flow.js API 開始執行流程實例 ${instanceId}`);
  return request({
    url: `/flow-instances/${instanceId}/start`,
    method: "put",
  });
};

// 這些功能已被刪除，因為我們的工作流不支持暫停、恢復和停止操作
// 每個節點必須執行完成

// 執行節點
export const executeNode = (instanceId, nodeId, data) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/${nodeId}/execute`,
    method: "post",
    data,
  });
};

// 獲取實例日誌
export const getInstanceLogs = (instanceId) => {
  return request({
    url: `/flow-instances/${instanceId}/logs`,
    method: "get",
  });
};

// 獲取節點日誌
export const getNodeLogs = (instanceId, nodeId) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/${nodeId}/logs`,
    method: "get",
  });
};

// 獲取節點狀態
export const getNodeState = (instanceId, nodeId) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/${nodeId}/state`,
    method: "get",
  });
};

// 獲取節點上下文
export const getNodeContext = (instanceId, nodeId) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/${nodeId}/context`,
    method: "get",
  });
};

// 更新節點上下文
export const updateNodeContext = (instanceId, nodeId, data) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/${nodeId}/context`,
    method: "put",
    data,
  });
};

// 獲取流程實例統計信息
export const getInstanceStats = (instanceId) => {
  return request({
    url: `/flow-instances/${instanceId}/stats`,
    method: "get",
  });
};

// 重置流程實例
export const resetInstance = (instanceId) => {
  return request({
    url: `/flow-instances/${instanceId}/reset`,
    method: "post",
  });
};

// 批量獲取節點狀態
export const getNodesStates = (instanceId, nodeIds) => {
  return request({
    url: `/flow-instances/${instanceId}/nodes/states`,
    method: "get",
    params: { nodeIds: nodeIds.join(",") },
  });
};

export const getFlowNodeDefinitions = () => {
  return request({
    url: "/flow-node-definitions",
    method: "get",
  });
};

// 獲取節點說明內容
export const getFlowNodeHelpContent = (nodeDefinitionId) => {
  // 按ID獲取
  return request({
    url: `/flow-node-definitions/${nodeDefinitionId}/help-content`,
    method: "get",
  });
};

export const createFlowNodeDefinition = (data) => {
  return request({
    url: "/flow-node-definitions",
    method: "post",
    data,
  });
};

export const updateFlowNodeDefinition = (id, data) => {
  return request({
    url: `/flow-node-definitions/${id}`,
    method: "put",
    data,
  });
};

export const deleteFlowNodeDefinition = (id) => {
  return request({
    url: `/flow-node-definitions/${id}`,
    method: "delete",
  });
};

// FlowTemplate API
export const getFlowTemplates = () => {
  return request({
    url: "/flow-templates",
    method: "get",
  });
};

export const getFlowTemplateById = (id) => {
  return request({
    url: `/flow-templates/${id}`,
    method: "get",
  });
};

export const createFlowTemplate = (data) => {
  return request({
    url: "/flow-templates",
    method: "post",
    data,
  });
};

export const updateFlowTemplate = (id, data) => {
  console.log("API 更新流程模板", id, data);
  return request({
    url: `/flow-templates/${id}`,
    method: "put",
    data,
  });
};

export const deleteFlowTemplate = (id, permanent = false) => {
  return request({
    url: `/flow-templates/${id}`,
    method: "delete",
    params: { permanent },
  });
};

// NOTE: 發布工作流程範本功能已經停用
// /**
//  * 發布工作流程範本
//  * @param {string} id - 範本 ID
//  * @returns {Promise<Object>} - 發布後的範本
//  */
// export const publishFlowTemplate = (id) =>
//   request.put(`/flow-templates/${id}/publish`);

// /**
//  * 取消發布工作流程範本
//  * @param {string} id - 範本 ID
//  * @returns {Promise<Object>} - 取消發布後的範本
//  */
// export const unpublishFlowTemplate = (id) =>
//   request.put(`/flow-templates/${id}/unpublish`);

// 恢復已刪除的流程實例
export const restoreFlowInstance = (instanceId) => {
  return request({
    url: `/flow-instances/${instanceId}/restore`,
    method: "post",
  });
};
