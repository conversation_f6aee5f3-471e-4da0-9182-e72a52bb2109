/**
 * 問題工單 API
 */
import request from "@/api/request";
/**
 * 獲取所有問題工單
 * @param {Object} params - 查詢參數
 * @returns {Promise} - 請求結果
 */
export function getIssues(params = {}) {
  return request({
    url: "/issues",
    method: "get",
    params,
  });
}

/**
 * 獲取單個問題工單詳情
 * @param {String} id - 問題工單ID
 * @returns {Promise} - 請求結果
 */
export function getIssueById(id) {
  return request({
    url: `/issues/${id}`,
    method: "get",
  });
}

/**
 * 創建問題工單
 * @param {Object} data - 問題工單資料 (可能包含文件)
 * @returns {Promise} - 請求結果
 */
export function createIssue(data) {
  // 檢查是否包含文件，如果包含則使用 FormData
  if (data.screenshot && data.screenshot instanceof File) {
    const formData = new FormData();

    // 添加所有其他字段
    Object.keys(data).forEach((key) => {
      if (key === "screenshot" && data[key] instanceof File) {
        formData.append("screenshot", data[key]);
      } else if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, data[key]);
      }
    });

    return request({
      url: "/issues",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 如果沒有文件，直接發送 JSON
  return request({
    url: "/issues",
    method: "post",
    data,
  });
}

/**
 * 更新問題工單
 * @param {String} id - 問題工單ID
 * @param {Object} data - 更新資料 (可能包含文件)
 * @returns {Promise} - 請求結果
 */
export function updateIssue(id, data) {
  // 檢查是否包含文件，如果包含則使用 FormData
  if (data.screenshot && data.screenshot instanceof File) {
    const formData = new FormData();

    // 添加所有其他字段
    Object.keys(data).forEach((key) => {
      if (key === "screenshot" && data[key] instanceof File) {
        formData.append("screenshot", data[key]);
      } else if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, data[key]);
      }
    });

    return request({
      url: `/issues/${id}`,
      method: "put",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 如果沒有文件，直接發送 JSON
  return request({
    url: `/issues/${id}`,
    method: "put",
    data,
  });
}

/**
 * 刪除問題工單
 * @param {String} id - 問題工單ID
 * @returns {Promise} - 請求結果
 */
export function deleteIssue(id) {
  return request({
    url: `/issues/${id}`,
    method: "delete",
  });
}

/**
 * 上傳截圖
 * @param {File} file - 要上傳的文件
 * @returns {Promise} - 請求結果
 */
export function uploadScreenshot(file) {
  const formData = new FormData();
  formData.append("screenshot", file);

  return request({
    url: "/issues/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 更新問題工單狀態
 * @param {String} id - 問題工單ID
 * @param {String} status - 新狀態
 * @returns {Promise} - 請求結果
 */
export function updateIssueStatus(id, status) {
  return request({
    url: `/issues/${id}`,
    method: "put",
    data: { status },
  });
}

/**
 * 指派問題工單
 * @param {String} id - 問題工單ID
 * @param {String} assigneeId - 負責人ID
 * @returns {Promise} - 請求結果
 */
export function assignIssue(id, assigneeId) {
  return request({
    url: `/issues/${id}`,
    method: "put",
    data: { assigneeId },
  });
}
