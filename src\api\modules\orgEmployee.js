import request from "@/api/request";
/**
 * 搜尋員工列表
 * @param {Object} params - 搜尋參數
 * @param {number} params.page - 頁碼
 * @param {number} params.pageSize - 每頁筆數
 * @param {string} [params.name] - 姓名
 * @param {string} [params.employeeNo] - 員工編號
 * @param {string} [params.groupId] - 部門 ID
 * @param {string} [params.sortBy] - 排序欄位
 * @param {string} [params.sortOrder] - 排序方向
 * @returns {Promise} 員工列表
 */
export function searchEmployees(params) {
  return request({
    url: "/org-employees/search",
    method: "get",
    params,
  });
}

/**
 * 根據帳號代碼獲取員工資訊
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 員工資訊
 */
export function getEmployeeByAccountGuid(accountGuid) {
  return request({
    url: `/org-employees/account/${accountGuid}`,
    method: "get",
  });
}

/**
 * 根據員工編號獲取員工資訊
 * @param {string} employeeNo - 員工編號
 * @returns {Promise} 員工資訊
 */
export function getEmployeeByEmployeeNo(employeeNo) {
  return request({
    url: `/org-employees/employee-no/${employeeNo}`,
    method: "get",
  });
}

/**
 * 根據部門 ID 獲取員工列表
 * @param {string} groupId - 部門 ID
 * @returns {Promise} 員工列表
 */
export function getEmployeesByGroup(groupId) {
  return request({
    url: `/org-employees/group/${groupId}`,
    method: "get",
  });
}

/**
 * 創建員工
 * @param {Object} data - 員工資料
 * @returns {Promise} 創建結果
 */
export function createEmployee(data) {
  return request({
    url: "/org-employees",
    method: "post",
    data,
  });
}

/**
 * 更新員工資訊
 * @param {string} accountGuid - 帳號代碼
 * @param {Object} data - 員工資料
 * @returns {Promise} 更新結果
 */
export function updateEmployee(accountGuid, data) {
  return request({
    url: `/org-employees/${accountGuid}`,
    method: "put",
    data,
  });
}

/**
 * 刪除員工
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 刪除結果
 */
export function deleteEmployee(accountGuid) {
  return request({
    url: `/org-employees/${accountGuid}`,
    method: "delete",
  });
}

/**
 * 鎖定員工帳號
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 鎖定結果
 */
export function lockEmployee(accountGuid) {
  return request({
    url: `/org-employees/${accountGuid}/lock`,
    method: "post",
  });
}

/**
 * 解鎖員工帳號
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 解鎖結果
 */
export function unlockEmployee(accountGuid) {
  return request({
    url: `/org-employees/${accountGuid}/unlock`,
    method: "post",
  });
}

/**
 * 停用員工帳號
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 停用結果
 */
export function suspendEmployee(accountGuid) {
  return request({
    url: `/org-employees/${accountGuid}/suspend`,
    method: "post",
  });
}

/**
 * 啟用員工帳號
 * @param {string} accountGuid - 帳號代碼
 * @returns {Promise} 啟用結果
 */
export function activateEmployee(accountGuid) {
  return request({
    url: `/org-employees/${accountGuid}/activate`,
    method: "post",
  });
}
