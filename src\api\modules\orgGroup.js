import request from "@/api/request";
/**
 * 獲取所有部門列表
 * @returns {Promise} 部門列表
 */
export function getOrgGroups() {
  return request({
    url: "/org-groups",
    method: "get",
  });
}

/**
 * 根據 ID 獲取部門資訊
 * @param {string} groupId - 部門 ID
 * @returns {Promise} 部門資訊
 */
export function getOrgGroupById(groupId) {
  return request({
    url: `/org-groups/${groupId}`,
    method: "get",
  });
}

/**
 * 獲取部門樹狀結構
 * @param {Object} params - 查詢參數
 * @param {string} [params.parentId] - 父節點 ID
 * @returns {Promise} 部門樹狀結構
 */
export function getOrgGroupTree(params) {
  return request({
    url: "/org-groups/tree",
    method: "get",
    params,
  });
}

/**
 * 創建部門
 * @param {Object} data - 部門資料
 * @returns {Promise} 創建結果
 */
export function createOrgGroup(data) {
  return request({
    url: "/org-groups",
    method: "post",
    data,
  });
}

/**
 * 更新部門
 * @param {string} groupId - 部門 ID
 * @param {Object} data - 部門資料
 * @returns {Promise} 更新結果
 */
export function updateOrgGroup(groupId, data) {
  return request({
    url: `/org-groups/${groupId}`,
    method: "put",
    data,
  });
}

/**
 * 刪除部門
 * @param {string} groupId - 部門 ID
 * @returns {Promise} 刪除結果
 */
export function deleteOrgGroup(groupId) {
  return request({
    url: `/org-groups/${groupId}`,
    method: "delete",
  });
}
