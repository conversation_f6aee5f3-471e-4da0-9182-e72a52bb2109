import axios from "axios";
import { ElMessage } from "element-plus";
import router from "@/router";
import { getApiBaseUrl } from "@/utils/url";
import { useUserStore } from "@/stores/user";
import { v4 as uuidv4 } from "uuid";

/**
 * 生成安全的檔案名
 * @param {File} file - 原始檔案
 * @returns {string} 安全的檔案名
 */
const generateSafeFileName = (file) => {
  // 獲取檔案副檔名
  const ext = file.name.split(".").pop().toLowerCase();
  // 生成時間戳
  const timestamp = new Date().getTime();
  // 生成 6 位隨機字串
  const randomStr = Math.random().toString(36).substring(2, 8);
  // 組合新檔案名：時間戳_隨機字串.副檔名
  return `${timestamp}_${randomStr}.${ext}`;
};

// 創建 axios 實例
const service = axios.create({
  baseURL: getApiBaseUrl(),
  // timeout: 15000, // 請求超時時間
  timeout: 0,
});

// 請求攔截器
service.interceptors.request.use(
  (config) => {
    // 如果請求URL以/external開頭，則將URL中的/external/和後續部分進行編碼
    // 給第三方api專用
    if (config.url.startsWith("/external")) {
      let url = config.url;
      url = url.replace("/external/", "");
      url = encodeURIComponent(url);
      config.url = "/external/" + url;
    }
    // 為每個請求添加唯一ID以便追蹤
    const requestId = uuidv4().substring(0, 8);
    config.requestId = requestId;

    // 將請求ID添加到自定義請求頭中
    config.headers["X-Request-ID"] = requestId;

    // 記錄請求開始
    // if (config.method === "put" && config.url.includes("flow-instances")) {
    //   console.log(
    //     "request.js",
    //     `[${requestId}] 開始請求 ${config.method.toUpperCase()} ${config.url}`,
    //     config.params && config.params._isDataUpdate
    //       ? { isDataUpdate: true }
    //       : { dataLength: JSON.stringify(config.data).length }
    //   );
    // }

    // 直接從 localStorage 獲取 token
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    // 如果是 FormData，不要設置 Content-Type，讓瀏覽器自動設置
    if (!(config.data instanceof FormData)) {
      config.headers["Content-Type"] = "application/json";
    }
    return config;
  },
  (error) => {
    console.error("請求錯誤:", error);
    return Promise.reject(error);
  }
);

// 響應攔截器
service.interceptors.response.use(
  (response) => {
    // 記錄請求完成
    const requestId = response.config.requestId;
    if (
      response.config.method === "put" &&
      response.config.url.includes("flow-instances")
    ) {
      console.log(
        "request.js",
        `[${requestId}] 請求完成 ${response.config.method.toUpperCase()} ${
          response.config.url
        }`,
        {
          status: response.status,
          time: `${response.headers["x-response-time"] || "N/A"}ms`,
        }
      );
    }

    return response.data;
  },
  async (error) => {
    // 記錄請求失敗
    if (error.config) {
      const requestId = error.config.requestId;
      if (
        error.config.method === "put" &&
        error.config.url.includes("flow-instances")
      ) {
        console.error(
          "request.js",
          `[${requestId}] 請求失敗 ${error.config.method.toUpperCase()} ${
            error.config.url
          }`,
          {
            status: error.response?.status || "NETWORK_ERROR",
            message: error.response?.data?.message || error.message,
          }
        );
      }
    }

    console.error("響應錯誤:", error);

    // 處理取消的請求
    if (axios.isCancel(error)) {
      // console.log("request.js", "請求被取消", { url: error.config?.url });
      return Promise.reject({ cancelled: true, message: "請求被取消" });
    }

    // 如果請求配置中設置了 silent 為 true，則不顯示錯誤消息
    if (!error.config?.silent) {
      // 處理錯誤響應
      const message = error.response?.data?.message || "請求失敗";
      ElMessage.error({
        message,
        duration: 3000,
        showClose: true,
      });
    }

    // 當獲得401錯誤時，無論是哪種請求都導向登入頁面
    if (error.response?.status === 401) {
      const userStore = useUserStore();
      // 清除 token 和用戶信息
      localStorage.removeItem("token");
      userStore.$reset();

      // 只在非登入頁時重定向
      if (router.currentRoute.value.path !== "/login") {
        router.push({
          path: "/login",
          query: { redirect: router.currentRoute.value.fullPath },
        });
      }
    }

    return Promise.reject(error);
  }
);

// 封裝請求方法
export const request = {
  get: (url, params) => service.get(url, { params }),
  post: (url, data, config) => {
    return service.post(url, data, config);
  },
  put: (url, data, config = {}) => {
    // 只記錄非流程更新的請求或特別設置需要記錄的請求
    if (!url.includes("flow-instances") || config.logRequest) {
      console.log("request.js", `發送 PUT 請求: ${url}`, {
        dataType: typeof data,
        isDataUpdate: config.params?._isDataUpdate,
      });
    }
    return service.put(url, data, config);
  },
  delete: (url) => service.delete(url),
  upload: (url, file) => {
    const formData = new FormData();
    // 使用安全的檔案名
    const safeFileName = generateSafeFileName(file);
    formData.append("file", file, safeFileName);
    // 添加原始檔案名，以便後端記錄
    formData.append("originalName", file.name);

    return service.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
  download: (url, params) =>
    service.get(url, {
      params,
      responseType: "blob",
    }),
};

export default service;
