const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const swaggerUi = require("swagger-ui-express");
const swaggerJsdoc = require("swagger-jsdoc");
const { errorHandler } = require("./middlewares/errorHandler");
const path = require("path");
const { PrismaClient } = require("@prisma/client");
const loggerMiddleware = require("./middlewares/loggerMiddleware");
const { logger } = require("./utils/logger");
require("dotenv").config({
  path: path.resolve(__dirname, "../.env"),
  override: true,
});

const app = express();
const prisma = new PrismaClient();

// 中間件設置
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    crossOriginEmbedderPolicy: false,
    // 添加 CSP 設定
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        // 允許在 iframe 中嵌入您的內容
        frameAncestors: ["'self'", "*"], // 或者替換 '*' 為特定的域名
        // 其他可能需要的設定
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:"],
        connectSrc: ["'self'"],
      },
    },
  })
);
app.use(cors());
app.use(express.json({ limit: "80mb" })); // 增加限制
app.use(express.urlencoded({ extended: true, limit: "80mb" })); // 同時調整 urlencoded 的限制
// app.use(express.json({ limit: false })); // 移除限制(會跳錯)
// app.use(express.urlencoded({ extended: true, limit: false })); // 移除限制(會跳錯)
app.use(morgan("dev"));

// 應用日誌中間件
app.use(loggerMiddleware);

// 靜態檔案服務
app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

// Swagger 設置
const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "SFDA IYM API",
      version: "1.0.0",
      description: "SFDA IYM API 文檔",
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 3001}`,
      },
    ],
    tags: [
      {
        name: "Auth",
        description: "用戶認證",
      },
      {
        name: "Users",
        description: "用戶管理",
      },
      {
        name: "RBAC",
        description: "RBAC 權限控制",
      },
      {
        name: "Projects",
        description: "專案管理",
      },
      {
        name: "FlowInstances",
        description: "工作流程實例",
      },
      {
        name: "FlowNodeDefinitions",
        description: "工作流程節點定義",
      },
      {
        name: "FlowTemplates",
        description: "工作流程模板",
      },
      {
        name: "FlowDocuments",
        description: "工作流程文件",
      },
      {
        name: "Report",
        description: "報表相關 API",
      },
      {
        name: "Issues",
        description: "問題回報與待辦事項",
      },
      {
        name: "ExternalAPI",
        description: "外部 API 代理",
      },
      {
        name: "OrgEmployees",
        description: "組織員工管理",
      },
      {
        name: "OrgGroups",
        description: "組織群組管理",
      },
    ],
  },
  apis: ["./src/routes/*.js"],
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// 導入路由
const authRoutes = require("./routes/authRoutes");
const userRoutes = require("./routes/userRoutes");
const rbacRoutes = require("./routes/rbacRoutes");
const projectRoutes = require("./routes/projectRoutes");
const flowNodeDefinitionRoutes = require("./routes/flowNodeDefinitionRoutes");
const flowTemplateRoutes = require("./routes/flowTemplateRoutes");
const flowInstanceRoutes = require("./routes/flowInstanceRoutes");
const flowDocumentRoutes = require("./routes/flowDocumentRoutes");
const externalApiRoutes = require("./routes/externalApiRoutes");
const gitRoutes = require("./routes/gitRoutes");
const issueRoutes = require("./routes/issueRoutes");
const userFavoriteRoutes = require("./routes/userFavoriteRoutes");
const orgEmployeeRoutes = require("./routes/orgEmployeeRoutes");
const orgGroupRoutes = require("./routes/orgGroupRoutes");
// const iymExternalApiRoutes = require("./routes/iymExternalApiRoutes");
const reportRoutes = require("./routes/report");

// 註冊路由
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/rbac", rbacRoutes);
app.use("/api/projects", projectRoutes);
app.use("/api/flow-node-definitions", flowNodeDefinitionRoutes);
app.use("/api/flow-templates", flowTemplateRoutes);
app.use("/api/flow-instances", flowInstanceRoutes);
app.use("/api/flow-documents", flowDocumentRoutes);
app.use("/api/external", externalApiRoutes);
app.use("/api/git", gitRoutes);
app.use("/api/issues", issueRoutes);
app.use("/api/favorites", userFavoriteRoutes);
app.use("/api/org-employees", orgEmployeeRoutes);
app.use("/api/org-groups", orgGroupRoutes);
app.use("/api/report", reportRoutes);

// 健康檢查端點
app.get("/api/health", async (req, res) => {
  try {
    // 檢查資料庫連線
    await prisma.$queryRaw`SELECT 1`;

    // 如果資料庫連線正常，返回成功狀態
    res.status(200).json({
      status: "ok",
      database: "connected",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      message: "服務正常運行中",
    });
  } catch (error) {
    // 如果資料庫連線失敗，返回錯誤狀態
    logger.error("健康檢查 - 資料庫連線失敗:", error);
    res.status(503).json({
      status: "error",
      database: "disconnected",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      message: "資料庫連線失敗",
      error: error.message,
    });
  }
});

// 錯誤處理中間件
app.use(errorHandler);

// 全局錯誤處理中間件
app.use((err, req, res, next) => {
  const { logger } = require("./utils/logger");
  logger.error("未處理的錯誤:", {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    body: req.body,
    query: req.query,
    user: req.user,
  });

  res.status(500).json({
    message: "伺服器錯誤",
    error: process.env.NODE_ENV === "development" ? err.message : undefined,
  });
});

// 優雅關閉
process.on("SIGTERM", async () => {
  await prisma.$disconnect();
  process.exit(0);
});
const HOST = "0.0.0.0"; // 允許外部訪問
const PORT = process.env.PORT || 3000;

app.listen(PORT, HOST, () => {
  logger.info(`伺服器運行在 http://localhost:${PORT}`);
  logger.info(`API 文檔可在 http://localhost:${PORT}/api-docs 查看`);
});

module.exports = app;
