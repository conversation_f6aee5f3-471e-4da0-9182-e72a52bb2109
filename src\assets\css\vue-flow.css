/* TODO: 可以砍? 全局 Vue Flow 樣式 */

/* 淺色模式（預設）*/
.vue-flow__node {
  background-color: white;
  color: #333;
  border-color: #ddd;
}

.vue-flow__edge-path {
  stroke: #b1b1b7;
}

.vue-flow__background {
  background-color: #f8f8fa;
}

.vue-flow__node-default {
  background-color: white;
  border-color: #ddd;
  color: #333;
}

.vue-flow__controls {
  background-color: white;
  border-color: #ddd;
}

.vue-flow__controls-button {
  background-color: white;
  border-color: #ddd;
  color: #333;
}

/* 暗黑模式 */
.dark .vue-flow__node {
  background-color: #1e293b;
  color: #e5e7eb;
  border-color: #475569;
}

.dark .vue-flow__edge-path {
  stroke: #64748b;
}

.dark .vue-flow__background {
  background-color: #0f172a;
}

.dark .vue-flow__minimap {
  background-color: #1e293b;
}

.dark .vue-flow__node-default {
  background-color: #1e293b;
  border-color: #475569;
  color: #e5e7eb;
}

.dark .vue-flow__controls {
  background-color: #1e293b;
  border-color: #475569;
}

.dark .vue-flow__controls-button {
  background-color: #1e293b;
  border-color: #475569;
  color: #e5e7eb;
}

/* 節點預覽容器 */
.node-preview-container .vue-flow {
  background-color: transparent !important;
}
