<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    :width="width || size"
    :height="height || size"
    viewBox="0 0 48 48">
    <defs>
      <linearGradient
        id="G7C1BuhajJQaEWHVlNUzHa_BEMhRoRy403e_gr1"
        x1="6"
        x2="27"
        y1="24"
        y2="24"
        data-name="ÐÐµÐ·ÑÐ¼ÑÐ½Ð½ÑÐ¹ Ð³ÑÐ°Ð´Ð¸ÐµÐ½Ñ 10"
        gradientUnits="userSpaceOnUse">
        <stop
          offset="0"
          stop-color="#21ad64"></stop>
        <stop
          offset="1"
          stop-color="#088242"></stop>
      </linearGradient>
    </defs>
    <path
      fill="#31c447"
      d="m41,10h-16v28h16c.55,0,1-.45,1-1V11c0-.55-.45-1-1-1Z"></path>
    <path
      fill="#fff"
      d="m32,15h7v3h-7v-3Zm0,10h7v3h-7v-3Zm0,5h7v3h-7v-3Zm0-10h7v3h-7v-3Zm-7-5h5v3h-5v-3Zm0,10h5v3h-5v-3Zm0,5h5v3h-5v-3Zm0-10h5v3h-5v-3Z"></path>
    <path
      fill="url(#G7C1BuhajJQaEWHVlNUzHa_BEMhRoRy403e_gr1)"
      d="m27,42l-21-4V10l21-4v36Z"></path>
    <path
      fill="#fff"
      d="m19.13,31l-2.41-4.56c-.09-.17-.19-.48-.28-.94h-.04c-.05.22-.15.54-.32.98l-2.42,4.52h-3.76l4.46-7-4.08-7h3.84l2,4.2c.**********.42,1.18h.04c.08-.27.22-.68.44-1.22l2.23-4.16h3.51l-4.2,6.94,4.32,7.06h-3.74Z"></path>
  </svg>
</template>

<script setup>
defineProps({
  size: {
    type: [Number, String],
    default: 24,
  },
  width: {
    type: [Number, String],
    default: null,
  },
  height: {
    type: [Number, String],
    default: null,
  },
});
</script>
