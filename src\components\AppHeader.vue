<template>
  <el-header
    class="!px-0 !h-12 fixed top-0 left-0 right-0 z-10 bg-light-mode dark:bg-dark-mode border-b border-gray-200 dark:border-gray-700">
    <div class="flex justify-between items-center h-full px-4">
      <!-- Logo 區域 -->
      <div class="flex items-center space-x-0">
        <img
          src="/logo_flowchart.svg"
          class="w-6 h-6 mr-2" />
        <h1 class="text-xl font-semibold text-light-mode dark:text-dark-mode">
          Intelligent Yield Management
        </h1>
      </div>

      <!-- 右側工具列 -->
      <div class="flex items-center space-x-4">
        <!-- <el-switch
          v-model="isDark"
          class="ml-2"
          inline-prompt
          :active-icon="Moon"
          :inactive-icon="Sun"
          @change="toggleTheme" /> -->
        <!-- 暗黑模式切換 -->
        <el-tooltip
          :content="isDark ? '切換亮色模式' : '切換暗色模式'"
          placement="bottom">
          <div
            class="cursor-pointer text-gray-600 dark:text-dark-mode hover:text-blue-500 dark:hover:text-blue-400"
            @click="toggleTheme">
            <Sun
              v-if="isDark"
              :size="18" />
            <Moon
              v-else
              :size="18" />
          </div>
        </el-tooltip>

        <el-dropdown
          placement="bottom-end"
          :hide-timeout="300"
          @visible-change="handleDropdownVisibleChange">
          <div>
            <el-tooltip
              content="我的關注"
              placement="top">
              <div class="inline-flex cursor-pointer">
                <Star
                  :size="18"
                  :class="[
                    'transition-all',
                    favoriteStore.loading
                      ? 'animate-pulse text-gray-400'
                      : 'text-gray-600 dark:text-dark-mode hover:text-blue-500 dark:hover:text-blue-400',
                  ]"
                  @click.stop="router.push('/favorites')" />
              </div>
            </el-tooltip>
          </div>
          <template #dropdown>
            <el-dropdown-menu v-loading="favoriteStore.loading">
              <template v-if="favorites?.length > 0">
                <template
                  v-for="(type, index) in favoriteTypes"
                  :key="type.value">
                  <el-divider
                    v-if="index > 0"
                    class="!my-1" />
                  <el-dropdown-item
                    class="favorite-type cursor-pointer"
                    @click="navigateToType(type.value)">
                    {{ type.label }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="getRecentCategoryFavorites(type.value).length > 0"
                    class="favorite-items">
                    <div class="favorite-items-container">
                      <div
                        v-for="item in getRecentCategoryFavorites(type.value)"
                        :key="`${type.value}-${item.resourceId}`"
                        class="favorite-item"
                        @click="navigateTo(item.path)">
                        <el-tooltip
                          :content="getFavoriteTooltip(item)"
                          placement="top-start"
                          effect="dark"
                          :show-after="300">
                          <span class="favorite-item-name">
                            {{ getFavoriteName(item) }}
                          </span>
                        </el-tooltip>
                        <Delete
                          :size="14"
                          class="favorite-item-delete"
                          @click.stop="removeFavorite(item)" />
                      </div>
                    </div>
                  </el-dropdown-item>
                </template>
                <el-divider class="!my-1" />
                <el-dropdown-item class="favorite-refresh">
                  <div
                    class="w-full flex items-center justify-center text-gray-500 hover:text-blue-500 cursor-pointer"
                    @click="refreshFavorites">
                    <RefreshCcw
                      :size="14"
                      class="mr-1" />
                    刷新資訊
                  </div>
                </el-dropdown-item>
              </template>
              <el-dropdown-item v-else>
                <div class="text-center text-gray-500 py-2">
                  您目前沒有關注的項目
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- Bug 回報圖示
        <el-tooltip
          content="回報問題"
          placement="bottom">
          <Bug
            :size="18"
            class="text-gray-600 hover:text-blue-500 cursor-pointer" />
        </el-tooltip> -->

        <!-- 通知圖標
        <el-badge
          :value="3"
          :max="99"
          class="cursor-pointer">
          <el-tooltip
            content="通知"
            placement="bottom">
            <Bell
              :size="18"
              class="text-gray-600 hover:text-blue-500" />
          </el-tooltip>
        </el-badge> -->

        <!-- 設置圖標
        <el-tooltip
          content="設置"
          placement="bottom">
          <Settings
            :size="18"
            class="cursor-pointer hover:text-blue-500" />
        </el-tooltip> -->

        <!-- 用戶選單 -->
        <el-dropdown
          trigger="hover"
          @command="handleCommand">
          <div class="flex items-center space-x-2 cursor-pointer">
            <div
              class="relative"
              @click.stop="handleAvatarClick">
              <el-avatar
                :size="36"
                :src="userInfo.avatar"
                :alt="userInfo.username"
                class="!bg-blue-100 dark:!bg-blue-900 hover:opacity-80 transition-opacity cursor-pointer">
                <span
                  class="text-sm font-medium text-blue-600 dark:text-blue-300"
                  >{{ userInfo.username?.slice(0, 1) }}</span
                >
              </el-avatar>
              <div
                class="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
              <!-- 懸停提示 -->
              <div
                class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 dark:bg-gray-700 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                點擊更換頭像
              </div>
            </div>
          </div>
          <!-- 用戶下拉選單 -->
          <template #dropdown>
            <el-dropdown-menu>
              <div class="px-4 py-2">
                <div
                  class="text-base font-semibold text-gray-900 dark:text-gray-100">
                  {{ userInfo.username }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ userInfo.email }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <el-tag
                    v-for="role in userInfo.roles"
                    :key="role.id">
                    {{ role.name }}
                  </el-tag>
                </div>
              </div>
              <el-divider class="!my-1" />
              <el-dropdown-item command="profile">
                <User
                  :size="14"
                  class="mr-2" />個人資料
              </el-dropdown-item>
              <!-- <el-dropdown-item command="settings">
                <Settings
                  :size="14"
                  class="mr-2" />偏好設定
              </el-dropdown-item> -->
              <!-- <el-divider class="!my-1" /> -->
              <!-- <el-dropdown-item command="notifications">
                <Bell
                  :size="14"
                  class="mr-2" />通知設定
              </el-dropdown-item> -->
              <!-- <el-dropdown-item command="privacy">
                <Shield
                  :size="14"
                  class="mr-2" />隱私權設定
              </el-dropdown-item> -->
              <el-divider class="!my-1" />
              <el-dropdown-item
                command="logout"
                class="!text-red-500">
                <LogOut
                  :size="14"
                  class="mr-2" />登出
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 頭像上傳對話框 -->
    <el-dialog
      v-model="dialogVisible"
      title="更換頭像"
      width="420px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="flex flex-col items-center">
        <!-- 預覽區域 -->
        <div class="mb-4 relative group">
          <el-upload
            ref="avatarUploadRef"
            class="avatar-preview-uploader"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept="image/jpeg,image/png,image/gif"
            drag>
            <el-avatar
              :size="120"
              :src="previewUrl || userInfo.avatar"
              class="!bg-blue-100 dark:!bg-blue-900 hover:opacity-80 transition-all duration-300">
              <span
                class="text-2xl font-medium text-blue-600 dark:text-blue-300"
                >{{ userInfo.username?.slice(0, 1) }}</span
              >
            </el-avatar>
            <!-- 懸停提示 -->
            <div
              class="absolute inset-0 bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <div class="text-white text-sm">
                <Upload
                  class="mx-auto mb-1"
                  :size="20" />
                <div class="text-xs">點擊或拖拉</div>
              </div>
            </div>
          </el-upload>
        </div>

        <!-- 提示文字 -->
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          支援 JPG、PNG、GIF 格式，檔案大小不超過 2MB
        </p>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            type="primary"
            :loading="uploading"
            :disabled="!selectedFile"
            @click="handleUpload">
            確認上傳
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-header>
</template>

<script setup>
import { useUserStore } from "@/stores/user";
import { updateAvatar } from "@/api/modules/auth";
import { useFavoriteStore } from "@/stores/favorite";
import { useThemeMode } from "@/composables/useThemeMode";
import {
  Moon,
  Sun,
  Star,
  User,
  LogOut,
  Upload,
  Delete,
  RefreshCcw,
} from "lucide-vue-next";
const router = useRouter();
const userStore = useUserStore();
const { isDark, toggleTheme } = useThemeMode();

const userInfo = ref({
  username: "",
  email: "",
  role: "",
  avatar: "",
});

// 使用 favorite store
const favoriteStore = useFavoriteStore();
const { favoriteTypes, getCategoryFavorites } = favoriteStore;
const favorites = computed(() => favoriteStore.favorites);

// 上傳相關狀態
const dialogVisible = ref(false);
const uploadRef = ref(null);
const selectedFile = ref(null);
const previewUrl = ref("");
const uploading = ref(false);

// 處理下拉選單顯示/隱藏
const handleDropdownVisibleChange = async (visible) => {
  if (visible && favorites.value?.length > 0) {
    // 當下拉選單開啟時，獲取所有關注項目的詳細資訊
    await favoriteStore.fetchFavoriteDetails(favorites.value);
  }
};

// 獲取關注項目顯示名稱
const getFavoriteName = (item) => {
  const detail = favoriteStore.getFavoriteDetail(item.type, item.resourceId);
  return detail?.name || item.resourceId;
};

// 獲取關注項目提示文字
const getFavoriteTooltip = (item) => {
  const detail = favoriteStore.getFavoriteDetail(item.type, item.resourceId);
  if (!detail) return "";
  return detail.description || "";
};

// 獲取用戶資訊
const fetchUserInfo = async () => {
  try {
    await userStore.fetchUser();
    userInfo.value = userStore.user;
  } catch (error) {
    console.error("獲取用戶資訊失敗:", error);
    ElMessage.error("獲取用戶資訊失敗，請重新整理頁面");
  }
};

// 移除關注
const removeFavorite = async (item) => {
  await favoriteStore.removeFromFavorite(item.type, item.resourceId);
};

// 導航到關注的路徑
const navigateTo = (path) => {
  router.push(path);
};

// 處理頭像點擊
const handleAvatarClick = (e) => {
  e.stopPropagation();
  dialogVisible.value = true;
};

// 處理檔案選擇
const handleFileChange = (file) => {
  // 檢查檔案大小（2MB）
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error("檔案大小不能超過 2MB");
    return false;
  }

  // 檢查檔案類型
  const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
  if (!allowedTypes.includes(file.raw.type)) {
    ElMessage.error("只支援 JPG、PNG、GIF 格式的圖片");
    return false;
  }

  selectedFile.value = file.raw;
  previewUrl.value = URL.createObjectURL(file.raw);
};

// 處理上傳
const handleUpload = async () => {
  if (!selectedFile.value) return;

  try {
    uploading.value = true;
    const formData = new FormData();
    formData.append("avatar", selectedFile.value);

    const { user } = await updateAvatar(formData);
    // 更新用戶資訊，確保頭像 URL 是完整的，但不包含 api 路徑

    userInfo.value = {
      ...userInfo.value,
      ...user,
    };

    // 清理預覽 URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value);
    }

    ElMessage.success("頭像更新成功");
    handleCancel();
  } catch (error) {
    console.error("上傳頭像失敗:", error);
    ElMessage.error(
      error.response?.data?.message || "上傳頭像失敗，請稍後再試"
    );
  } finally {
    uploading.value = false;
  }
};

// 處理取消
const handleCancel = () => {
  dialogVisible.value = false;
  selectedFile.value = null;
  // 清理預覽 URL
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
    previewUrl.value = "";
  }
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 處理下拉選單命令(!TODO: 未實作)
const handleCommand = async (command) => {
  switch (command) {
    case "profile":
      router.push("/settings/profile");
      break;
    case "settings":
      router.push("/settings");
      break;
    case "notifications":
      router.push("/settings/notifications");
      break;
    case "privacy":
      router.push("/settings/privacy");
      break;
    case "logout":
      try {
        await userStore.handleLogout();
        router.push("/login");
        ElMessage.success("登出成功");
      } catch (error) {
        console.error("登出失敗:", error);
        ElMessage.error("登出失敗");
      }
      break;
  }
};

// 新增 getRecentCategoryFavorites 方法
const getRecentCategoryFavorites = (type) => {
  return getCategoryFavorites(type).slice(0, 5); //取前 5 筆
};

// 修改導航到分類頁面的方法
const navigateToType = (type) => {
  const targetType = favoriteTypes.find((t) => t.value === type);
  if (targetType?.path) {
    router.push(targetType.path);
  }
};

// 刷新關注詳細資訊123
const refreshFavorites = async () => {
  if (favorites.value?.length > 0) {
    await favoriteStore.fetchFavoriteDetails(favorites.value, true);
    ElMessage.success("已更新關注項目資訊");
  }
};

// 監聽用戶 ID 變化來獲取關注
watch(
  () => userInfo.value?.id,
  async (userId) => {
    if (userId) {
      await favoriteStore.fetchFavorites(userId);
      console.log("用戶關注:", favoriteStore.favorites);
    }
  },
  { immediate: true }
);

// 初始化主題
onMounted(() => {
  if (userStore.isAuthenticated) {
    if (userStore.user) {
      // 如果 store 中已有用戶資訊，直接使用
      userInfo.value = userStore.user;

      // 如果 store 中已有關注，直接使用
      if (favoriteStore.favorites.length > 0)
        favorites.value = favoriteStore.favorites;
    } else {
      // 只有在沒有用戶資訊時才呼叫 API
      fetchUserInfo();
    }
  }
});
</script>

<style scoped>
.el-dropdown-menu {
  @apply min-w-[240px];
}

:deep(.el-upload-dragger) {
  @apply w-full border-none p-0;
}

.avatar-uploader :deep(.el-upload) {
  @apply w-full;
}

.avatar-uploader :deep(.el-upload-dragger) {
  @apply w-full border-none p-0;
}

.avatar-uploader :deep(.el-upload-dragger:hover) {
  @apply bg-transparent;
}

.avatar-uploader .upload-area {
  @apply w-full flex flex-col items-center;
}

.avatar-preview-uploader :deep(.el-upload) {
  @apply block;
}

.avatar-preview-uploader :deep(.el-upload-dragger) {
  @apply w-[120px] h-[120px] border-none p-0 bg-transparent;
}

.avatar-preview-uploader :deep(.el-upload-dragger:hover) {
  @apply bg-transparent;
}

/* 懸停效果 */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* 移除 el-dropdown 的外框和優化互動樣式 */
.el-dropdown {
  outline: none !important;
}

.el-dropdown-link {
  outline: none !important;
  cursor: pointer;
}

:deep(.el-dropdown__popper) {
  outline: none !important;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
  outline: none !important;
}

/* 保持鍵盤無障礙性，但使用更柔和的視覺效果 */
.el-dropdown:focus-visible {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
  border-radius: 4px;
}

/* 移除所有可能的外框 */
* {
  outline: none !important;
}

/* 優化懸停效果 */
:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}

:deep(.el-dropdown-menu__item:active) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}

.favorite-type {
  @apply !p-2 !h-auto font-medium text-gray-600 dark:text-gray-300 !w-full block hover:bg-gray-100 dark:hover:bg-gray-700;
}

.favorite-items {
  @apply !p-0 !w-full block hover:bg-transparent !cursor-default;
}

:deep(.favorite-items.el-dropdown-menu__item:hover),
:deep(.favorite-items.el-dropdown-menu__item:focus),
:deep(.favorite-items.el-dropdown-menu__item:active) {
  background-color: transparent !important;
  color: inherit !important;
}

.favorite-items-container {
  @apply w-full flex flex-col gap-1;
}

.favorite-item {
  @apply w-full py-1 cursor-pointer flex items-center justify-between transition-colors duration-200 mx-0;
}

.favorite-item:hover {
  @apply bg-gray-100 dark:bg-gray-700;
}

.favorite-item-name {
  @apply text-sm truncate flex-1 hover:text-blue-500 pl-2;
}

.favorite-item-delete {
  @apply text-gray-500 hover:text-red-500 mr-1 flex-shrink-0;
}

:deep(.el-dropdown-menu) {
  @apply !min-w-[240px] !p-2;
}

:deep(.el-dropdown-menu__item) {
  @apply !w-full;
}

/* 增加載入中的樣式 */
:deep(.el-dropdown-menu.el-loading-parent--relative) {
  @apply min-h-[100px];
}

/* 添加刷新按鈕樣式 */
.favorite-refresh {
  @apply !p-1 !h-auto !w-full block;
}

:deep(.favorite-refresh.el-dropdown-menu__item:hover) {
  background-color: transparent !important;
}
</style>
