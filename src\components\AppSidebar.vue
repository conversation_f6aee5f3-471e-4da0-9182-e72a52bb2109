<template>
  <div class="sidebar-wrapper">
    <el-menu
      class="sidebar-menu bg-light-mode dark:bg-dark-mode text-light-mode dark:text-dark-mode"
      :default-active="route.path"
      :collapse="isCollapse"
      :active-text-color="isDark ? '#5891f7' : '#409EFF'"
      popper-effect="light">
      <div class="menu-scroll-container">
        <el-menu-item
          v-for="item in menuItems"
          :key="item.path"
          :index="item.path"
          @click="handleMenuItemClick(item)">
          <component
            :is="item.icon"
            :size="24" />
          <template #title>
            <span class="menu-title">{{ item.title }}</span>
          </template>
        </el-menu-item>
      </div>
    </el-menu>
    <!-- 版本信息 -->
    <div
      v-show="!isCollapse"
      class="version-info flex justify-center">
      <img
        src="/flexium_logo.png"
        class="h-4"
        :alt="version" />
    </div>

    <!-- 折疊按鈕 -->
    <el-button
      type="text"
      class="collapse-button"
      @click="toggleCollapse">
      <component
        :is="isCollapse ? ChevronRight : ChevronLeft"
        :size="16" />
    </el-button>
  </div>
</template>

<script setup>
import { ChevronLeft, ChevronRight, Settings } from "lucide-vue-next"; //TODO: why can not remove this import??
import { useUserStore } from "@/stores/user";
import { useThemeMode } from "@/composables/useThemeMode";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { isDark } = useThemeMode();
const isCollapse = ref(false);

// 從 localStorage 讀取狀態
onMounted(() => {
  const savedState = localStorage.getItem("sidebarCollapsed");
  if (savedState !== null) {
    isCollapse.value = savedState === "true";
  }
});

// 檢查用戶是否有權限訪問該選單項目
const canAccessMenuItem = (route) => {
  // 如果是管理員，直接返回 true
  if (userStore.isAdmin) {
    return true;
  }

  // 檢查是否需要管理員權限
  if (route.meta?.requiresAdmin) {
    return false;
  }

  // 檢查是否需要特定權限
  if (route.meta?.permissions) {
    return userStore.hasAnyPermission(route.meta.permissions);
  }

  return true;
};

// 從路由配置生成選單項目
const menuItems = computed(() => {
  return router.options.routes
    .filter(
      (route) =>
        !route.redirect &&
        route.name &&
        !route.meta?.guest &&
        !route.meta?.hidden &&
        canAccessMenuItem(route)
    )
    .map((route) => ({
      path: route.path,
      name: route.name,
      icon: route.meta?.icon || Settings,
      title: route.meta?.title || route.name,
      meta: route.meta,
    }));
});

// 處理菜單項點擊
const handleMenuItemClick = (item) => {
  console.log("handleMenuItemClick", item.meta);
  if (item.meta?.openInNewWindow) {
    // 在新視窗中打開
    window.open(item.path, "_blank");
  } else {
    // 正常導航
    router.push(item.path);
  }
};

// 折疊切換
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
  // 保存狀態到 localStorage
  localStorage.setItem("sidebarCollapsed", isCollapse.value.toString());
};

// 暴露 isCollapse 屬性
defineExpose({
  isCollapse,
});
</script>

<style>
/* 全局樣式，不使用 scoped */
.el-popper.is-light {
  @apply !text-xs !font-medium;
  padding: 8px 12px !important;
}

/* 暗黑模式下彈出提示樣式 */
.dark .el-popper.is-light {
  --el-bg-color: var(--color-dark-bg) !important;
  --el-text-color-primary: var(--color-dark-text) !important;
  --el-border-color: #374151 !important;
}

.dark .el-tooltip__trigger {
  --el-tooltip-trigger-color: var(--color-dark-text) !important;
}
</style>

<style scoped>
.sidebar-wrapper {
  @apply relative;
  height: 100vh;
}

.sidebar-menu {
  @apply fixed left-0 top-12 bottom-0 border-r border-slate-200 dark:border-gray-700;
  transition: width 0s ease-in-out;
}

.collapse-button {
  @apply fixed z-50 !w-6 !h-6 !p-0 rounded-full bg-light-mode dark:bg-gray-700 border border-slate-200 dark:border-gray-800 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-blue-500 dark:hover:text-blue-400 flex items-center justify-center dark:text-gray-300;
  /* 192px - 24px = 168px */
  /* 192px 來自.el-menu--vertical:not(.el-menu--collapse) */
  left: calc(12rem - 1.5rem);
  top: 6rem;
  transform: translateX(50%);
  transition: left 0s ease-in-out;
}

/* 當選單收合時，按鈕也要跟著移動 */
:deep(.el-menu.el-menu--collapse) ~ .collapse-button {
  /* 63px - 24px = 39px */
  left: calc(4rem - 1.5rem);
}

/* 移除按鈕 focus 效果 */
.collapse-button:focus {
  @apply !outline-none !text-inherit dark:!text-gray-300;
  box-shadow: none !important;
}

/* 調整選單寬度 */
.el-menu--vertical:not(.el-menu--collapse) {
  width: 12rem !important;
}

.el-menu--collapse {
  @apply !w-16;
}

/* 為選單項目添加過渡效果 */
:deep(.el-menu-item) {
  @apply !border-l-4 !border-transparent;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
}

:deep(.el-menu-item.is-active) {
  @apply !border-l-4 !border-blue-500 !bg-blue-50 dark:!bg-gray-800/80;
}

:deep(.el-menu-item:hover) {
  @apply !bg-gray-50 dark:!bg-slate-500/20;
}

/* 為選單文字添加過渡效果 */
:deep(.menu-title) {
  display: inline-block;
  opacity: 1;
  transition: opacity 0s ease-in-out;
}

:deep(.el-menu--collapse .menu-title) {
  opacity: 0;
  width: 0;
  display: inline-block;
}

/* 為圖標添加過渡效果 */
:deep(.el-menu--collapse .el-menu-item svg) {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0 0;
  transition: margin 0s ease-in-out;
}

.menu-scroll-container {
  height: calc(100vh - 48px);
  @apply overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb transparent;
}

/* 暗黑模式下的捲軸顏色 */
html.dark .menu-scroll-container {
  scrollbar-color: #4b5563 #1a1a1a;
}

/* 自定義捲動條樣式 */
.menu-scroll-container::-webkit-scrollbar {
  @apply w-1;
}

.menu-scroll-container::-webkit-scrollbar-track {
  @apply bg-transparent;
}

html.dark .menu-scroll-container::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

.menu-scroll-container::-webkit-scrollbar-thumb {
  @apply bg-gray-200 dark:bg-gray-600 rounded-full;
}

.menu-scroll-container::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-300 dark:bg-gray-500;
}

.version-info {
  @apply fixed left-0 bottom-0 py-2 px-4 text-xs text-gray-400 border-t border-slate-200 
  border-r bg-light-mode dark:bg-dark-mode dark:border-gray-700 text-center;
  width: 12rem;
  transition: all 0s ease-in-out;
}

.version-text {
  @apply opacity-100 transition-opacity duration-200 inline-block;
}
</style>
