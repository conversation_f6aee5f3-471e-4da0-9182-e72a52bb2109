<template>
  <div>
    <json-viewer
      :value="jsonData"
      :expand-depth="expandDepth"
      expand-icon-style="circle"
      sort
      :expand-on-click="expandOnClick"
      :show-double-quotes="false"
      :show-length="true"
      :copyable="true"
      copy-text="複製"
      copied-text="已複製！"
      :boxed="false"
      class="custom-json-viewer" />
  </div>
</template>

<script setup>
import JsonViewer from "vue-json-viewer";
import "vue-json-viewer/style.css";

const props = defineProps({
  jsonData: {
    type: Object,
    required: true,
  },
  expandDepth: {
    type: Number,
    default: 2,
  },
  expandOnClick: {
    type: Boolean,
    default: true,
  },
});
</script>

<style>
/* JSON Viewer 自定義樣式 */
.custom-json-viewer {
  background-color: #f9fafb !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  border: 1px solid #e5e7eb !important;
}

/* 淺色模式下的文字顏色 */
.custom-json-viewer .jv-container {
  background: none !important;
  color: #1a202c !important;
}

.custom-json-viewer .jv-container .jv-code {
  padding: 0 !important;
  background: none !important;
}

.custom-json-viewer .jv-container .jv-key {
  color: #2563eb !important;
}

.custom-json-viewer .jv-container .jv-item.jv-string {
  color: #059669 !important;
}

.custom-json-viewer .jv-container .jv-item.jv-number {
  color: #d97706 !important;
}

.custom-json-viewer .jv-container .jv-item.jv-boolean {
  color: #7c3aed !important;
}

.custom-json-viewer .jv-container .jv-item.jv-null {
  color: #dc2626 !important;
}

/* 箭頭和圖標 */
.custom-json-viewer .jv-container .jv-icon {
  color: #4b5563 !important;
}

/* 自定義複製按鈕樣式 */
.custom-json-viewer .jv-container .jv-button {
  color: #3b82f6 !important;
  border: 1px solid #3b82f6 !important;
  background-color: transparent !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.custom-json-viewer .jv-container .jv-button:hover {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* 深色模式樣式 */
html.dark .custom-json-viewer {
  background-color: #1e293b !important;
  border: 1px solid #334155 !important;
}

/* 深色模式下的文字顏色 */
html.dark .custom-json-viewer .jv-container {
  color: #e2e8f0 !important;
  background: none !important;
}

html.dark .custom-json-viewer .jv-container .jv-key {
  color: #93c5fd !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-string {
  color: #86efac !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-number {
  color: #fdba74 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-boolean {
  color: #f472b6 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-null {
  color: #94a3b8 !important;
}

/* 箭頭和圖標 - 深色模式 */
html.dark .custom-json-viewer .jv-container .jv-icon {
  color: #94a3b8 !important;
}

/* 折疊箭頭 - 深色模式 */
html.dark .custom-json-viewer .jv-container .jv-more:after {
  background-color: #94a3b8 !important;
}

html.dark .custom-json-viewer .jv-container .jv-ellipsis {
  color: #94a3b8 !important;
}

/* 確保深色模式下的括號和其他符號顯示正確 */
html.dark .custom-json-viewer .jv-container .jv-code,
html.dark .custom-json-viewer .jv-container .jv-array,
html.dark .custom-json-viewer .jv-container .jv-object {
  color: #e2e8f0 !important;
}

/* 確保深色模式下的底層元素文字顏色 */
html.dark .custom-json-viewer * {
  color: #e2e8f0;
}

/* 自定義複製按鈕樣式 - 深色模式 */
html.dark .custom-json-viewer .jv-container .jv-button {
  color: #60a5fa !important;
}

html.dark .custom-json-viewer .jv-container .jv-button:hover {
  background-color: #60a5fa !important;
  color: #0f172a !important;
}
</style>
