<template>
  <span
    class="keycap"
    :class="[`keycap--${theme}`, `keycap--${sizeClass}`]"
    :style="customStyle">
    <slot />
  </span>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  size: { type: [String, Number], default: "small" }, // 'small' | 'medium' | 'large' | px
  theme: { type: String, default: "light" }, // 'light' | 'dark'
  radius: { type: [String, Number], default: 8 }, // px
  fontFamily: {
    type: String,
    default: "'SF Pro', 'Segoe UI', 'Arial', sans-serif",
  },
  fontSize: { type: [String, Number], default: "" }, // px
});

const sizeClass = computed(() => {
  if (["small", "medium", "large"].includes(props.size)) return props.size;
  return "custom";
});

const customStyle = computed(() => {
  const style = {
    borderRadius:
      typeof props.radius === "number" ? `${props.radius}px` : props.radius,
    fontFamily: props.fontFamily,
  };
  if (props.sizeClass === "custom") {
    style.minWidth = style.height =
      typeof props.size === "number" ? `${props.size}px` : props.size;
    style.fontSize = props.fontSize
      ? typeof props.fontSize === "number"
        ? `${props.fontSize}px`
        : props.fontSize
      : "20px";
  } else if (props.fontSize) {
    style.fontSize =
      typeof props.fontSize === "number"
        ? `${props.fontSize}px`
        : props.fontSize;
  }
  return style;
});
</script>

<style scoped>
.keycap {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  margin: 2px;
  border: 1px solid #d1d5db;
  box-shadow: 0 2px 6px 0 rgba(60, 60, 60, 0.1), 0 1.5px 0 0 #e0e0e0 inset;
  font-size: 20px;
  user-select: none;
  transition: box-shadow 0.1s, background 0.2s, color 0.2s;
}

/* 主題色 */
.keycap--light {
  background: #f5f5f7;
  color: #555;
}
.keycap--dark {
  background: #23272e;
  color: #e5e7eb;
  border-color: #444;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.18), 0 1.5px 0 0 #333 inset;
}

/* 尺寸 */
.keycap--small {
  min-width: 24px;
  height: 24px;
  font-size: 14px;
  padding: 0 6px;
}
.keycap--medium {
  min-width: 32px;
  height: 32px;
  font-size: 20px;
  padding: 0 8px;
}
.keycap--large {
  min-width: 44px;
  height: 44px;
  font-size: 28px;
  padding: 0 12px;
}
.keycap:active {
  box-shadow: 0 1px 2px 0 rgba(60, 60, 60, 0.1) inset;
}
</style>
