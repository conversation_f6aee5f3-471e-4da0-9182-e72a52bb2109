<template>
  <div class="loading-text">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 400 100">
      <!-- 背景 -->
      <rect
        width="400"
        height="100"
        fill="#f8f9fa"
        rx="10"
        ry="10" />

      <!-- 載入中文字 -->
      <text
        x="200"
        y="50"
        font-family="Arial, sans-serif"
        font-size="24"
        text-anchor="middle"
        fill="#333">
        載入中
      </text>

      <!-- 動畫點 -->
      <circle
        cx="280"
        cy="50"
        r="5"
        fill="#333">
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0s" />
      </circle>

      <circle
        cx="300"
        cy="50"
        r="5"
        fill="#333">
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.5s" />
      </circle>

      <circle
        cx="320"
        cy="50"
        r="5"
        fill="#333">
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="1s" />
      </circle>
    </svg>
  </div>
</template>
