<!--
網路狀態偵測，每30秒檢查一次API連線狀態，db連線狀態，如果網路斷開，則顯示警告
-->
<template>
  <Transition name="slide-up">
    <div
      v-if="showNetworkAlert"
      class="network-status-alert"
      :class="{
        'is-offline': !isOnline,
        'is-api-error': isApiError && !isDatabaseError,
        'is-db-error': isDatabaseError,
      }">
      <div class="flex items-center">
        <component
          :is="alertIcon"
          class="w-5 h-5 mr-2" />
        <span>{{ alertMessage }}</span>
      </div>
      <el-button
        type="text"
        class="close-btn"
        @click="dismissAlert">
        <XIcon class="w-4 h-4" />
      </el-button>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useNetwork } from "@vueuse/core";
import { WifiOff, ServerOff, Database, XIcon } from "lucide-vue-next";
import { checkSystemHealth } from "@/api/modules/system";

const { isOnline } = useNetwork();
const isApiError = ref(false);
const isDatabaseError = ref(false);
const manuallyDismissed = ref(false);
const apiCheckInterval = ref(null);

// 計算是否顯示網路警告
const showNetworkAlert = computed(() => {
  if (manuallyDismissed.value) return false;
  return !isOnline.value || isApiError.value || isDatabaseError.value;
});

// 警告訊息
const alertMessage = computed(() => {
  if (!isOnline.value) return "網路連線已中斷，請檢查您的網路設定";
  if (isDatabaseError.value) return "資料庫連線失敗，請聯繫系統管理員";
  if (isApiError.value) return "無法連接到伺服器，請檢查您的 VPN 連線";
  return "";
});

// 警告圖標
const alertIcon = computed(() => {
  if (!isOnline.value) return WifiOff;
  if (isDatabaseError.value) return Database;
  if (isApiError.value) return ServerOff;
  return null;
});

// 關閉警告
const dismissAlert = () => {
  manuallyDismissed.value = true;

  // 5分鐘後重置手動關閉狀態，以便在問題持續時再次顯示警告
  setTimeout(() => {
    manuallyDismissed.value = false;
  }, 5 * 60 * 1000);
};

// 檢查 API 連線狀態
const checkApiConnection = async () => {
  if (!isOnline.value) {
    isApiError.value = false;
    isDatabaseError.value = false;
    return;
  }

  try {
    // 使用 API 模塊進行健康檢查
    const response = await checkSystemHealth();

    // 檢查資料庫連線狀態
    if (response.data && response.data.database === "disconnected") {
      isDatabaseError.value = true;
      isApiError.value = false;
    } else {
      isDatabaseError.value = false;
      isApiError.value = false;
    }
  } catch (error) {
    console.error("API 連線檢查失敗:", error);

    // 檢查是否為資料庫連線錯誤
    if (
      error.response &&
      error.response.status === 503 &&
      error.response.data &&
      error.response.data.database === "disconnected"
    ) {
      isDatabaseError.value = true;
      isApiError.value = false;
    } else {
      isDatabaseError.value = false;
      isApiError.value = true;
    }
  }
};

// 監聽網路狀態變化
watch(isOnline, (newValue) => {
  if (newValue) {
    // 網路恢復時，檢查 API 連線
    checkApiConnection();
    manuallyDismissed.value = false;
  } else {
    // 網路斷開時，重置 API 錯誤狀態
    isApiError.value = false;
    isDatabaseError.value = false;
  }
});

// 組件掛載時開始定期檢查 API 連線
onMounted(() => {
  // 立即檢查一次
  checkApiConnection();

  // 設置定期檢查（每 30 秒）
  apiCheckInterval.value = setInterval(checkApiConnection, 30000);
});

// 組件卸載時清除定時器
onUnmounted(() => {
  if (apiCheckInterval.value) {
    clearInterval(apiCheckInterval.value);
  }
});
</script>

<style scoped>
.network-status-alert {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 9999;
  color: white;
  font-weight: 500;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.is-offline {
  background-color: #f56c6c;
}

.is-api-error {
  background-color: #e6943c;
}

.is-db-error {
  background-color: #409eff;
}

.close-btn {
  color: white;
  opacity: 0.8;
}

.close-btn:hover {
  opacity: 1;
}

/* 過渡動畫 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}
</style>
