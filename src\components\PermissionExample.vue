<template>
  <div class="permission-example">
    <h2>權限控制示例</h2>

    <div class="example-section">
      <h3>基本權限控制</h3>

      <!-- 單一權限控制 -->
      <el-button
        type="primary"
        v-permission="'MANAGE_USERS'">
        管理用戶（需要 MANAGE_USERS 權限）
      </el-button>

      <!-- 多個權限控制（任一權限） -->
      <el-button
        type="success"
        v-permission="['MANAGE_USERS', 'VIEW_USERS']">
        用戶操作（需要 MANAGE_USERS 或 VIEW_USERS 權限）
      </el-button>

      <!-- 多個權限控制（所有權限） -->
      <el-button
        type="warning"
        v-permission.all="['MANAGE_USERS', 'MANAGE_ROLES']">
        高級用戶操作（需要 MANAGE_USERS 和 MANAGE_ROLES 權限）
      </el-button>
    </div>

    <div class="example-section">
      <h3>角色控制</h3>

      <!-- 角色控制 -->
      <el-button
        type="danger"
        v-permission.role="'ADMIN'">
        管理員操作（需要 ADMIN 角色）
      </el-button>

      <!-- 管理員指令 -->
      <el-button
        type="info"
        v-admin>
        管理員專用（需要 ADMIN 或 SUPERADMIN 角色）
      </el-button>
    </div>

    <div class="example-section">
      <h3>複合元素示例</h3>

      <el-card v-permission="'VIEW_PROJECTS'">
        <template #header>
          <div class="card-header">
            <span>項目信息</span>
            <el-button
              v-permission="'MANAGE_PROJECTS'"
              type="text"
              >編輯</el-button
            >
          </div>
        </template>
        <div>這是項目的詳細信息，任何有 VIEW_PROJECTS 權限的用戶都能看到</div>
        <div v-permission="'MANAGE_PROJECTS'">
          這是項目的管理選項，只有有 MANAGE_PROJECTS 權限的用戶才能看到
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
// 無需額外導入，指令已全局註冊
</script>

<style scoped>
.permission-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
}

.el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
