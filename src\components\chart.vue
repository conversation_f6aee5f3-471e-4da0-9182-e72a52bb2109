<template>
  <div
    ref="chartRef"
    :style="{ width, height }"
    class="chart-container" />
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import * as echarts from "echarts/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Heatmap<PERSON>hart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  ToolboxComponent,
  VisualMapComponent,
} from "echarts/components";
import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";

// 註冊必要的 echarts 組件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  ToolboxComponent,
  VisualMapComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Heatmap<PERSON>hart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

const props = defineProps({
  options: {
    type: Object,
    default() {
      return {};
    },
  },
  autoResize: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
});

const emit = defineEmits(["click"]);

const chartRef = ref(null);
let chartInstance = null;

// 初始化圖表
const initChart = () => {
  if (chartRef.value) {
    // 如果已存在實例，先銷毀
    if (chartInstance) {
      chartInstance.dispose();
    }

    // 創建新的實例
    chartInstance = echarts.init(chartRef.value);

    // 設置選項
    if (props.options) {
      chartInstance.setOption(props.options);
    }

    // 監聽點擊事件
    chartInstance.on("click", (params) => {
      emit("click", params);
    });

    // 如果需要自適應大小
    if (props.autoResize) {
      window.addEventListener("resize", handleResize);
    }
  }
};

// 處理視窗大小變化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 監聽 options 變化
watch(
  () => props.options,
  (newOptions) => {
    if (chartInstance) {
      chartInstance.setOption(newOptions);
    }
  },
  { deep: true }
);

// 生命周期鉤子
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    if (props.autoResize) {
      window.removeEventListener("resize", handleResize);
    }
  }
});

// 暴露給父元件的方法
defineExpose({
  chartInstance: () => chartInstance,
  resize: handleResize,
  setOption: (options) => {
    if (chartInstance) {
      chartInstance.setOption(options);
    }
  },
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
