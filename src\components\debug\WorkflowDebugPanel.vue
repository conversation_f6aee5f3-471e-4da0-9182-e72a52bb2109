<template>
  <div class="workflow-debug-panel">
    <el-card header="工作流調試面板">
      <div class="debug-controls">
        <el-button 
          type="primary" 
          @click="startDebugging"
          :disabled="isDebugging">
          開始調試
        </el-button>
        <el-button 
          type="danger" 
          @click="stopDebugging"
          :disabled="!isDebugging">
          停止調試
        </el-button>
        <el-button @click="generateReport">生成報告</el-button>
        <el-button @click="clearLogs">清除記錄</el-button>
      </div>

      <div class="debug-status" v-if="isDebugging">
        <el-tag type="success">正在監控工作流事件...</el-tag>
        <span class="event-count">事件數: {{ eventCount }}</span>
      </div>

      <div class="debug-results" v-if="debugReport">
        <h3>調試報告</h3>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="總事件數">
            {{ debugReport.totalEvents }}
          </el-descriptions-item>
          <el-descriptions-item label="節點數量">
            {{ debugReport.nodeCount }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="debugReport.duplicates.length > 0" class="duplicate-events">
          <h4>🚨 重複事件</h4>
          <el-table :data="debugReport.duplicates" size="small">
            <el-table-column prop="event" label="事件" />
            <el-table-column prop="count" label="重複次數" />
          </el-table>
        </div>

        <div v-if="debugReport.parallelExecutions.length > 0" class="parallel-executions">
          <h4>🔀 並行執行</h4>
          <el-table :data="debugReport.parallelExecutions" size="small">
            <el-table-column prop="time" label="時間" />
            <el-table-column prop="nodes" label="並行節點">
              <template #default="{ row }">
                <el-tag v-for="node in row.nodes" :key="node" size="small">
                  {{ node }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="completed-nodes">
          <h4>📊 節點完成順序</h4>
          <el-table :data="debugReport.completedNodes" size="small" max-height="300">
            <el-table-column type="index" label="順序" width="60" />
            <el-table-column prop="nodeId" label="節點ID" />
            <el-table-column prop="timestamp" label="完成時間" />
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { workflowDebugger } from '@/utils/workflowDebugger';

const isDebugging = ref(false);
const eventCount = ref(0);
const debugReport = ref(null);

// 開始調試
const startDebugging = () => {
  workflowDebugger.startLogging();
  isDebugging.value = true;
  eventCount.value = 0;
  debugReport.value = null;
  
  // 定期更新事件計數
  updateEventCount();
};

// 停止調試
const stopDebugging = () => {
  workflowDebugger.stopLogging();
  isDebugging.value = false;
};

// 生成報告
const generateReport = () => {
  debugReport.value = workflowDebugger.generateReport();
};

// 清除記錄
const clearLogs = () => {
  workflowDebugger.clear();
  eventCount.value = 0;
  debugReport.value = null;
};

// 更新事件計數
const updateEventCount = () => {
  if (isDebugging.value) {
    eventCount.value = workflowDebugger.eventLog.length;
    setTimeout(updateEventCount, 1000);
  }
};

onUnmounted(() => {
  if (isDebugging.value) {
    workflowDebugger.stopLogging();
  }
});
</script>

<style scoped>
.workflow-debug-panel {
  margin: 20px;
}

.debug-controls {
  margin-bottom: 20px;
}

.debug-controls .el-button {
  margin-right: 10px;
}

.debug-status {
  margin: 15px 0;
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.event-count {
  font-weight: bold;
  color: #1890ff;
}

.debug-results {
  margin-top: 20px;
}

.debug-results h3 {
  margin-bottom: 15px;
  color: #333;
}

.debug-results h4 {
  margin: 15px 0 10px 0;
  color: #666;
}

.duplicate-events,
.parallel-executions,
.completed-nodes {
  margin-top: 20px;
}

.el-tag {
  margin-right: 5px;
}
</style>
