<!-- 基礎節點組件 -->
<template>
  <div
    ref="nodeWrapperRef"
    class="node-wrapper"
    :class="{
      'border-blue-500 shadow-blue-100 dark:border-blue-400 dark:shadow-blue-900':
        selected,
      'border-purple-500 shadow-purple-300 bg-purple-50 dark:border-purple-400 dark:shadow-purple-900 dark:bg-purple-900':
        nodeState.status === NODE_STATES.RUNNING,
      'cursor-pointer': !disabled && !isReportMode,
      'opacity-50 cursor-not-allowed': disabled && !isReportMode,
      'flow-node--selected': selected,
      'flow-node--running': nodeState.status === NODE_STATES.running,
      'flow-node--completed':
        nodeState.status === NODE_STATES.COMPLETED || isReportMode,
      'flow-node--error': nodeState.status === NODE_STATES.ERROR,
      'flow-node--compact': compactMode,
      'flow-node--report': isReportMode,
      invisible: isFullscreen,
      'fixed-height-node': !compactMode && !isFullscreen,
    }"
    :style="nodeStyle"
    :title="nodeState.status">
    <!-- 非全螢幕模式內容 -->
    <template v-if="!isFullscreen">
      <!-- 顯示 resize 手柄 -->
      <NodeResizer
        v-if="showResizer && !isFullscreen && !compactMode && !isReportMode"
        :isVisible="selected"
        class="!border-blue-400"
        :lineStyle="{ borderWidth: '1px' }"
        :handleStyle="{
          width: '16px',
          height: '16px',
          border: '2px solid white',
          transition: 'all 0.2s ease',
          zIndex: '1',
        }" />
      <!-- 節點標題 -->
      <div
        class="node-header p-3 border-b rounded-t-lg relative overflow-hidden"
        :class="[
          'bg-[#6ca9ff] dark:bg-[#1b6cdd] border-gray-50 dark:border-gray-700',
          { 'cursor-grab': !disabled && !isReportMode },
          {
            'bg-purple-100 dark:bg-purple-900 border-purple-200 dark:border-purple-700':
              nodeState.status === NODE_STATES.RUNNING,
          },
          { 'rounded-b-lg': compactMode },
        ]">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <component
              :is="icon"
              :class="[
                iconClasses[nodeType] || 'text-gray-600 dark:text-dark-mode',
              ]"
              :size="32" />
            <span class="text-lg font-medium text-gray-900 dark:text-gray-100"
              >{{ title }}{{ ` id:${id}` }}</span
            >
            <el-tag
              v-if="statusText && statusText !== '預設'"
              size="small"
              :type="statusType">
              {{ statusText }}
            </el-tag>
          </div>

          <!-- 非報表模式的操作按鈕 -->
          <div
            v-if="!isReportMode"
            class="flex items-center space-x-2">
            <el-tooltip
              v-if="!compactMode"
              content="詳細說明"
              placement="top"
              :effect="isDark ? 'light' : 'dark'">
              <MessageSquareMore
                @click="handleHelpClick"
                class="w-4 h-4 text-black dark:text-white cursor-pointer hover:text-blue-500 dark:hover:text-blue-400 transition-colors" />
            </el-tooltip>
            <el-drawer
              v-model="showHelpDrawer"
              :title="title + ' 詳細說明'"
              direction="rtl"
              :append-to-body="true"
              size="50%"
              :modal-class="isDark ? 'dark-mode-drawer' : ''"
              :show-close="true"
              :with-header="true"
              :destroy-on-close="false">
              <div class="p-6 prose dark:prose-invert max-w-none">
                <div
                  v-if="isHelpLoading"
                  class="flex flex-col items-center justify-center py-10">
                  <el-icon
                    class="is-loading mb-2 text-blue-500"
                    size="32">
                    <Loading />
                  </el-icon>
                  <span class="text-gray-600 dark:text-gray-300"
                    >正在加載詳細說明...</span
                  >
                </div>
                <div
                  v-else-if="nodeHelpContent"
                  v-html="renderMarkdown(nodeHelpContent)"></div>
                <div
                  v-else
                  class="flex flex-col items-center justify-center py-10 text-gray-500 dark:text-gray-400">
                  <Information class="w-8 h-8 mb-2 opacity-50" />
                  <p class="text-center">此節點暫無詳細說明內容。</p>
                </div>
              </div>
            </el-drawer>
            <el-tooltip
              :content="isFullscreen ? '縮小' : '最大化'"
              placement="top"
              :effect="isDark ? 'light' : 'dark'">
              <el-button
                type="text"
                size="small"
                @click="toggleFullscreen"
                class="!text-gray-800 hover:text-gray-900 dark:!text-gray-200 dark:hover:text-gray-100">
                <component
                  :is="isFullscreen ? Shrink : Maximize"
                  class="w-4 h-4" />
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div
          v-if="description && !compactMode"
          class="mt-1 pl-8 text-xs text-gray-900 dark:text-dark-mode">
          {{ description }}
        </div>
      </div>

      <!-- 節點內容區域 -->
      <div
        v-if="!compactMode"
        class="node-content relative p-4 dark:bg-node-dark"
        :class="{ 'report-content': isReportMode }"
        ref="nodeContentRef">
        <!-- 資料處理中的覆蓋層 -->
        <div
          v-if="nodeState.status === NODE_STATES.RUNNING && !isReportMode"
          class="processing-overlay dark:bg-dark-mode/90">
          <div class="processing-content">
            <div class="processing-spinner"></div>
            <div class="processing-text">資料處理中...</div>
          </div>
        </div>

        <!-- 主要內容 -->
        <slot></slot>

        <!-- 非報表模式的額外內容 -->
        <template v-if="!isReportMode">
          <!-- 錯誤訊息區域 -->
          <div
            v-if="nodeState.status === NODE_STATES.ERROR && !compactMode"
            class="error-message-container dark:bg-red-950 dark:border-red-800">
            <div class="error-header">
              <i class="el-icon-warning-outline mr-1"></i>
              <span>執行錯誤</span>
              <el-button
                type="text"
                size="small"
                class="ml-auto dark:text-red-300"
                @click="showErrorDetails = !showErrorDetails">
                {{ showErrorDetails ? "隱藏詳情" : "查看詳情" }}
              </el-button>
            </div>

            <div class="error-summary dark:text-red-300">
              {{
                formatErrorMessage(nodeState.error) || "節點執行過程中發生錯誤"
              }}
            </div>

            <div
              v-if="showErrorDetails"
              class="error-details dark:border-red-800">
              <div
                v-if="nodeState.errorDetails"
                class="mt-2">
                <div
                  v-if="nodeState.errorDetails.code"
                  class="error-detail-item">
                  <span class="error-detail-label dark:text-red-300"
                    >錯誤代碼:</span
                  >
                  <span class="error-detail-value dark:text-red-200">{{
                    nodeState.errorDetails.code
                  }}</span>
                </div>
                <div
                  v-if="nodeState.errorDetails.name"
                  class="error-detail-item">
                  <span class="error-detail-label dark:text-red-300"
                    >錯誤類型:</span
                  >
                  <span class="error-detail-value dark:text-red-200">{{
                    nodeState.errorDetails.name
                  }}</span>
                </div>
                <div
                  v-if="nodeState.retryCount"
                  class="error-detail-item">
                  <span class="error-detail-label dark:text-red-300"
                    >重試次數:</span
                  >
                  <span class="error-detail-value dark:text-red-200">{{
                    nodeState.retryCount
                  }}</span>
                </div>
                <div
                  v-if="nodeState.suggestion"
                  class="error-detail-item">
                  <span class="error-detail-label dark:text-red-300"
                    >建議:</span
                  >
                  <span class="error-detail-value dark:text-red-200">{{
                    nodeState.suggestion
                  }}</span>
                </div>
              </div>

              <!-- 完整錯誤信息 -->
              <div
                v-if="nodeState.error && showFullError"
                class="mt-2">
                <div class="flex justify-between items-center mb-1">
                  <span class="text-xs text-gray-600 dark:text-dark-mode"
                    >完整錯誤信息:</span
                  >
                  <el-button
                    type="text"
                    size="small"
                    class="dark:text-dark-mode"
                    @click="showFullError = false">
                    隱藏
                  </el-button>
                </div>
                <div
                  class="text-xs text-red-600 dark:text-red-300 p-2 bg-red-50 dark:bg-red-950 rounded overflow-auto max-h-32 whitespace-pre-wrap">
                  {{ nodeState.error }}
                </div>
              </div>

              <div
                v-else-if="nodeState.error && nodeState.error.length > 100"
                class="mt-2">
                <el-button
                  type="text"
                  size="small"
                  class="dark:text-dark-mode"
                  @click="showFullError = true">
                  顯示完整錯誤信息
                </el-button>
              </div>

              <div class="error-actions mt-2">
                <el-button
                  type="info"
                  size="small"
                  @click="handleClearError">
                  清除錯誤
                </el-button>
              </div>
            </div>
          </div>

          <!-- 執行時間顯示 -->
          <div
            v-if="nodeContext.executionTime && !compactMode"
            class="execution-time dark:text-gray-400">
            執行時間：{{ formatExecutionTime(nodeContext.executionTime) }}
          </div>

          <!-- 執行按鈕區域 -->
          <div
            v-if="!compactMode"
            class="node-execute-button flex justify-between border-t mt-2 py-3 bg-gray-50 dark:!bg-node-dark dark:border-gray-700 rounded-b-lg">
            <!-- 節點狀態 -->
            <div
              class="node-status"
              v-admin
              v-if="!isFullscreen">
              <div class="flex items-center justify-between text-xs">
                <!-- <el-popover
                  placement="top"
                  width="300"
                  trigger="hover">
                  <template #reference>
                    <info class="w-4 h-4 dark:text-dark-mode !bg-transparent" />
                  </template>
                  <div class="p-2 dark:bg-dark-mode dark:text-dark-mode">
                    <h4 class="text-sm font-bold mb-2">節點狀態信息</h4>
                    <div class="text-xs space-y-1">
                      <div><strong>節點ID:</strong> {{ id }}</div>
                      <div><strong>狀態:</strong> {{ nodeState.status }}</div>
                      <div>
                        <strong>更新時間:</strong>
                        {{ nodeState.updatedAt || "未知" }}
                      </div>
                      <div v-if="nodeState.error">
                        <strong>錯誤:</strong> {{ nodeState.error }}
                      </div>
                    </div>
                  </div>
                </el-popover> -->
              </div>
            </div>
            <div class="flex items-center justify-center space-x-2">
              <el-button
                v-if="nodeType === 'custom-input'"
                type="success"
                size="small"
                @click="handleRunClick"
                :loading-icon="RefreshCw"
                :loading="running"
                :disabled="nodeState.status === NODE_STATES.RUNNING"
                >{{ hasExecutedBefore ? "重新執行" : "執行" }}</el-button
              >

              <!-- 互動式節點的繼續執行按鈕 -->
              <el-button
                v-if="
                  props.executionMode === EXECUTION_MODES.INTERACTIVE &&
                  nodeState.status === NODE_STATES.PENDING
                "
                type="success"
                size="small"
                @click="handleRunClick"
                :loading-icon="RefreshCw"
                :loading="running"
                :disabled="running"
                >繼續執行</el-button
              >
            </div>
          </div>
        </template>
      </div>

      <!-- 使用 NodeHandles 組件 -->
      <NodeHandles
        v-if="!isReportMode"
        :node-id="id"
        :node-type="nodeType"
        :inputs="defaultHandles.inputs"
        :outputs="defaultHandles.outputs"
        :show-labels="showHandleLabels"
        :compact-mode="compactMode"
        :key="`handles-${id}-${
          nodeWrapperRef?.offsetHeight || nodeHeight
        }-${compactMode}`"
        @connect="handleConnect"
        @disconnect="handleDisconnect" />

      <!-- 節點錯誤指示器 - 總是顯示，即使在緊湊模式下 -->
      <div
        v-if="
          !isReportMode &&
          (nodeState.status === NODE_STATES.ERROR ||
            nodeState.status === 'failed')
        "
        class="flow-node__error-indicator">
        <el-tooltip
          :content="errorMessage || '節點執行失敗'"
          placement="top">
          <i class="el-icon-warning flow-node__error-icon"></i>
        </el-tooltip>
      </div>
    </template>
  </div>

  <!-- 使用 Teleport 將全屏內容傳送到主佈局 -->
  <Teleport
    to="#main-content"
    v-if="isFullscreen">
    <div class="fullscreen-node">
      <div class="fullscreen-content">
        <!-- 節點標題 -->
        <div
          class="node-header"
          :class="[
            '!bg-[#6ca9ff] dark:!bg-[#1b6cdd] border-gray-50 dark:border-gray-700',
            { 'cursor-grab': !disabled },
            {
              'bg-purple-100 dark:bg-purple-900 border-purple-200 dark:border-purple-700':
                nodeState.status === NODE_STATES.RUNNING,
            },
            `${nodeType}-header`,
          ]">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <component
                :is="icon"
                :class="[
                  iconClasses[nodeType] || 'text-gray-600 dark:text-dark-mode',
                ]"
                :size="32" />
              <span
                class="text-lg font-medium text-gray-900 dark:text-gray-100"
                >{{ title }}</span
              >
              <el-tag
                size="small"
                :type="statusType">
                {{ statusText }}
              </el-tag>
            </div>
            <div class="flex items-center space-x-2">
              <el-button
                type="text"
                size="small"
                @click="toggleFullscreen"
                class="text-gray-600 hover:text-gray-900 dark:text-dark-mode dark:hover:text-white">
                <component
                  :is="isFullscreen ? Minimize2 : Maximize"
                  class="w-5 h-5" />
              </el-button>
            </div>
          </div>
          <div
            v-if="description"
            class="mt-1 text-xs text-gray-900 dark:text-dark-mode">
            {{ description }}
          </div>
        </div>

        <!-- 節點內容 -->
        <div
          class="node-content relative p-2 dark:bg-node-dark"
          ref="nodeContentRef">
          <!-- 添加資料處理中的覆蓋層 -->
          <div
            v-if="nodeState.status === NODE_STATES.RUNNING"
            class="processing-overlay dark:bg-dark-mode/90">
            <div class="processing-content">
              <div class="processing-spinner"></div>
              <div class="processing-text">資料處理中...</div>
            </div>
          </div>
          <slot></slot>
          <el-divider class="dark:border-gray-700" />
        </div>

        <!-- 在全屏模式下添加底部操作區 -->
        <div
          class="fullscreen-footer"
          v-if="isFullscreen">
          <div
            class="flex align-center justify-center px-8 py-4 bg-gray-50 dark:bg-node-dark border-t dark:border-gray-700">
            <!-- 全屏模式下的執行按鈕 -->
            <el-button
              v-if="nodeType === 'custom-input'"
              v-admin
              type="success"
              size="small"
              @click="handleRunClick"
              :loading-icon="RefreshCw"
              :loading="running"
              :disabled="nodeState.status === NODE_STATES.RUNNING"
              >{{ hasExecutedBefore ? "重新執行" : "執行" }}</el-button
            >

            <el-button
              type="primary"
              size="small"
              @click="toggleFullscreen">
              退出全屏
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, watch, nextTick } from "vue";
import { useFlowStore } from "@/stores/flowStore";
import { storeToRefs } from "pinia";
import NodeHandles from "./NodeHandles.vue";
import { NodeResizer } from "@vue-flow/node-resizer";
import "@vue-flow/node-resizer/dist/style.css";
import { useFlowInstance } from "@/composables/useFlowInstance";
import * as LucideIcons from "lucide-vue-next";
import {
  Box,
  RefreshCw,
  Minimize2,
  Maximize,
  Shrink,
  Info,
  ChevronUp,
  ChevronDown,
  MessageSquareMore,
} from "lucide-vue-next";
import { Loading } from "@element-plus/icons-vue";
import {
  NODE_STATES,
  getNodeStatusType,
  getNodeStatusText,
} from "@/constants/nodeStates";
import { EXECUTION_MODES } from "@/constants/executionModes";
import { wait } from "@/utils/dateUtils";
import { renderMarkdown } from "@/utils/markdown";
import { useThemeMode } from "@/composables/useThemeMode";
// 從 nodeExecution 中解構需要的方法和狀態
import { useNodeExecution } from "@/composables/useNodeExecution";
import { getFlowNodeHelpContent } from "@/api/modules/flow";
import { ElMessage } from "element-plus";

// 定義默認幫助內容
const DEFAULT_HELP_CONTENT = `# 節點幫助內容
此節點的詳細說明暫時無法獲取，請聯繫系統管理員添加。`;

const { isDark } = useThemeMode();

// 添加 debounce 函數
const debounce = (fn, delay) => {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 定義 props
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  // 節點數據，可以包含任何相關資訊，包括 nodeDefinitionId
  data: {
    type: Object,
    default: () => ({}),
    description: "節點資料，可包含 nodeDefinitionId 等屬性",
  },
  // TODO: nowork? 節點類型 目前只用到 custom-input, custom-process , 會影響連接點的顯示()
  nodeType: {
    type: String,
    default: "custom-process",
    validator: (value) =>
      ["custom-input", "custom-process", "custom-output"].includes(value),
    description: "節點類型 custom-input, custom-process , custom-output",
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: "",
    description: "節點說明",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  handles: {
    type: Object,
    default: () => ({
      inputs: [],
      outputs: [],
    }),
  },
  // 節點寬度
  nodeWidth: {
    type: Number,
    default: 360,
  },
  // 節點高度
  nodeHeight: {
    type: Number,
    default: 600,
  },
  // NOTE: 這個屬性是給外部傳入的樣式，目前沒有使用
  style: {
    type: Object,
    default: () => ({}),
  },
  // 是否顯示連接點標籤
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  // 節點頭部背景顏色
  headerBgColor: {
    type: String,
    default: "",
    description: "節點頭部背景顏色",
  },
  // 節點最小寬度
  minWidth: {
    type: Number,
    default: 240,
    description: "節點最小寬度",
  },
  // 節點最小高度
  minHeight: {
    type: Number,
    default: 120,
    description: "節點最小高度",
  },
  // 是否顯示 resize 手柄
  showResizer: {
    type: Boolean,
    default: false,
    description: "是否顯示 resize 手柄",
  },
  errorMessage: {
    type: String,
    default: "",
    description: "節點錯誤訊息",
  },
  // 是否自動調整高度
  autoHeight: {
    type: Boolean,
    default: false,
    description: "是否自動調整高度",
  },
  // 新增：是否使用緊湊模式（只顯示標題）
  defaultCompact: {
    type: Boolean,
    default: false,
    description: "是否預設使用緊湊模式（只顯示標題）",
  },
  // 節點執行邏輯
  // processFunction: {
  //   type: Function,
  //   default: () => {},
  //   description: "節點執行邏輯",
  // },
  nodeRef: {
    type: Object,
    default: () => {},
    description: "節點引用",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  // 執行模式：支援互動式節點
  executionMode: {
    type: String,
    default: EXECUTION_MODES.AUTO,
    validator: (value) => Object.values(EXECUTION_MODES).includes(value),
    description: "節點執行模式：auto、manual、interactive、conditional",
  },
});
const title = computed(() => props.data?.name || props.title);
const description = computed(
  () => props.data?.description || props.description
);

// 詳細說明的本地存儲
const nodeHelpContent = ref("目前沒有詳細說明內容。");
// 控制幫助內容載入狀態
const isHelpLoading = ref(false);

// 添加 compactMode 的狀態
const compactMode = ref(props.defaultCompact);

// 切換緊湊模式的方法
const toggleCompactMode = () => {
  compactMode.value = !compactMode.value;
  // 切換後需要重新計算位置和大小
  nextTick(() => {
    // 發出事件通知父組件
    emit("compactModeChanged", compactMode.value);
    // 觸發重新計算節點位置
    // if (nodeWrapperRef.value) {
    //   const resizeEvent = new Event("resize");
    //   window.dispatchEvent(resizeEvent);
    // }
  });
};

// 創建自己的引用，用於與 useNodeExecution 通信
const selfNodeRef = ref(null);

// 初始化節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: props.nodeType,
  nodeName: title,
  nodeRef: selfNodeRef, // 使用自己的引用
  definitionId: props.nodeDefinitionId, // 添加節點定義ID
});
const { errorMessage, executeNode } = nodeExecution;

const flowStore = useFlowStore();
const { currentInstance } = storeToRefs(flowStore);

// 使用流程實例 composable
const { clearNodeError } = useFlowInstance();

// 將計算屬性改為 ref
const nodeState = ref({ status: NODE_STATES.DEFAULT });

// 用於顯示/隱藏說明抽屜(節點的詳細說明)
const showHelpDrawer = ref(false);

// 獲取節點上下文數據
const nodeContext = computed(() => {
  return flowStore.getNodeContextById(props.id);
});

// 獲取節點日誌
const nodeLogs = computed(() => {
  return flowStore.getNodeLogsById(props.id);
});

// 監聽全局折合/展開事件
const setupCompactModeListener = () => {
  const handleToggleCompactEvent = (event) => {
    // 獲取折合狀態
    const { compactState } = event.detail;

    // 更新節點的折合狀態
    compactMode.value = compactState;

    // 發出事件通知父組件
    emit("compactModeChanged", compactState);

    // 觸發重新計算節點位置
    nextTick(() => {
      if (nodeWrapperRef.value) {
        const resizeEvent = new Event("resize");
        window.dispatchEvent(resizeEvent);
      }
    });
  };

  // 添加事件監聽器
  window.addEventListener("flow:toggleNodesCompact", handleToggleCompactEvent);

  // 返回清理函數
  return () => {
    window.removeEventListener(
      "flow:toggleNodesCompact",
      handleToggleCompactEvent
    );
  };
};

// 添加發送狀態變更事件的方法
const sendStateChangeEvent = (status, result = null, error = null) => {
  // 發送節點狀態變更事件，通知工作流管理器
  const event = new CustomEvent("flow:nodeStateChange", {
    detail: {
      nodeId: props.id,
      status: status,
      result: result || nodeContext.value?.output,
      error: error || nodeState.value.error,
      timestamp: new Date().toISOString(), // 添加時間戳，幫助追蹤事件順序
    },
  });
  window.dispatchEvent(event);
};

// 更新節點狀態
const updateNodeStatus = (newStatus, result = null, error = null) => {
  console.log(`BaseNode ${props.id}: updateNodeStatus 被調用`, {
    newStatus,
    currentStatus: nodeState.value.status,
    result,
    error,
  });

  // 安全的深度比較函數，避免循環引用問題
  const safeDeepEqual = (obj1, obj2) => {
    try {
      // 如果兩者都是 null 或 undefined
      if (obj1 === obj2) return true;
      if (obj1 == null || obj2 == null) return false;

      // 如果類型不同
      if (typeof obj1 !== typeof obj2) return false;

      // 如果是基本類型
      if (typeof obj1 !== "object") return obj1 === obj2;

      // 如果是數組
      if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
      if (Array.isArray(obj1)) {
        if (obj1.length !== obj2.length) return false;
        for (let i = 0; i < obj1.length; i++) {
          if (!safeDeepEqual(obj1[i], obj2[i])) return false;
        }
        return true;
      }

      // 對於對象，只比較可枚舉的屬性，避免響應式對象的內部屬性
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);

      if (keys1.length !== keys2.length) return false;

      for (let key of keys1) {
        if (!keys2.includes(key)) return false;
        if (!safeDeepEqual(obj1[key], obj2[key])) return false;
      }

      return true;
    } catch (error) {
      console.warn("BaseNode", `比較對象時發生錯誤，視為不相等:`, error);
      return false;
    }
  };

  // 避免重複更新相同狀態
  if (
    nodeState.value.status === newStatus &&
    safeDeepEqual(nodeState.value.data, result) &&
    nodeState.value.error === (error ? error.message : null)
  ) {
    console.log("BaseNode", `節點 ${props.id} 狀態未變更，跳過更新`);
    return;
  }

  // 更新本地狀態
  const oldStatus = nodeState.value.status;
  nodeState.value = {
    ...nodeState.value,
    status: newStatus,
    data: result,
    error: error ? error.message || "未知錯誤" : null,
    updatedAt: new Date().toISOString(),
  };

  // 如果有流程實例，更新 flowStore 中的狀態
  if (flowStore.currentInstance?.id) {
    flowStore.updateNodeState(flowStore.currentInstance.id, props.id, {
      status: newStatus,
      data: result,
      error: error ? error.message || "未知錯誤" : null,
      errorDetails: error
        ? { message: error.message, stack: error.stack }
        : null,
      _isDataUpdate: true, // 標記為數據更新
    });
  } else {
    console.warn("BaseNode", `無法更新節點 ${props.id} 狀態：當前實例不存在`);
  }

  // 發送狀態變更事件（統一在這裡發送，避免重複）
  sendStateChangeEvent(newStatus, result, error);
};

// 修改狀態類型映射
const statusType = computed(() => {
  return getNodeStatusType(nodeState.value.status);
});

// 修改狀態文字映射
const statusText = computed(() => {
  return getNodeStatusText(nodeState.value.status);
});

// 判斷節點是否已經執行過
const hasExecutedBefore = computed(() => {
  if (nodeState.value.status === NODE_STATES.COMPLETED) {
    return true;
  }

  // 檢查節點上下文是否有數據
  if (nodeContext.value && Object.keys(nodeContext.value).length > 0) {
    return true;
  }

  // 檢查 sharedData 或 globalVariables 中是否有該節點的數據
  const sharedData = currentInstance.value?.context?.sharedData || {};
  const globalVariables = currentInstance.value?.context?.globalVariables || {};

  // 遍歷 sharedData 中的所有項目，檢查是否有任何項目的 nodeId 與當前節點 ID 匹配
  const hasSharedData = Object.values(sharedData).some((item) => {
    // 檢查項目是否為對象且包含 nodeId 屬性
    return item && typeof item === "object" && item.nodeId === props.id;
  });

  if (hasSharedData) {
    return true;
  }

  // 檢查 globalVariables 中是否有與該節點相關的變數
  const hasGlobalVar = Object.entries(globalVariables).some(([key, value]) => {
    if (key.includes(props.id)) {
      return value !== undefined && value !== null;
    }

    // 檢查變數中是否有 sourceNodeId 欄位與當前節點 ID 匹配
    if (value && typeof value === "object" && value.sourceNodeId === props.id) {
      return true;
    }

    return false;
  });

  return hasGlobalVar;
});

// 節點類型樣式映射 - 這部分不再需要，改由CSS類名處理
const iconClasses = {
  // "custom-input": "text-blue-600",
  // "custom-process": "text-green-600",
  // "custom-output": "text-purple-600",
};

// 根據 data.icon 獲取節點圖標
const icon = computed(() => {
  // 使用 data.icon
  if (props.data?.icon) {
    // 如果是字符串，則嘗試從 lucide-vue-next 中獲取對應的圖標
    if (typeof props.data.icon === "string") {
      // 使用 LucideIcons 物件來獲取圖標
      const iconComponent = LucideIcons[props.data.icon];
      if (iconComponent) {
        return iconComponent;
      } else {
        console.error(`圖標 "${props.data.icon}" 未找到，使用預設圖標`);
        return Box;
      }
    }
    // 如果是一個對象（組件），則直接返回
    else if (typeof props.data.icon === "object") {
      return props.data.icon;
    }
  }

  // 預設使用 Box 圖標
  return Box;
});

const defaultHandles = computed(() => {
  // 如果有傳入 handles，優先使用傳入的設定
  if (props.handles.inputs?.length > 0 || props.handles.outputs?.length > 0) {
    return props.handles;
  }

  const baseHandles = {
    inputs: [
      {
        id: "input",
        position: "left",
        type: "target",
        // label: " ",
      },
    ],
    outputs: [
      {
        id: "output",
        position: "right",
        type: "source",
        // label: " ",
      },
    ],
  };

  //根據節點類型決定顯示哪些連接點;
  switch (props.nodeType) {
    case "input":
    case "custom-input":
      return {
        inputs: [],
        outputs: [
          {
            id: "output",
            position: "right",
            type: "source",
          },
        ],
      };
    case "custom-process":
      return {
        inputs: [
          {
            id: "input",
            position: "left",
            type: "target",
            //label: "輸入",
          },
        ],
        outputs: [
          {
            id: "output",
            position: "right",
            type: "source",
          },
        ],
      };
    default:
      return baseHandles;
  }
});

const running = ref(false);
// 處理節點執行
const handleRun = async (
  processFunction,
  successMessage = "已成功執行",
  errorMessage = "執行失敗",
  duration = 3000
) => {
  //NOTE:預設的執行方法，子類可不寫或重寫來覆蓋
  try {
    setRunningState(true);

    // 使用統一的節點執行邏輯執行節點，啟用批量更新
    const result = await executeNode({}, processFunction, {
      batchUpdate: true,
    });

    // 檢查是否需要互動
    if (result && result.requiresInteraction) {
      setRunningState(false);
      return result;
    }

    ElMessage({
      message: title.value + " " + successMessage,
      type: "success",
      //grouping: true,
      //offset: 500,
      plain: true,
    });
    setRunningState(false);
    updateNodeStatus(NODE_STATES.COMPLETED, result);

    return result;
  } catch (error) {
    console.error("EmployeeDataInputNode", "執行節點時發生錯誤:", error);
    nodeState.value.status = NODE_STATES.ERROR;
    ElNotification({
      title: title,
      message: errorNotify.value, // || errorMessage,
      type: "error",
      position: "bottom-left",
      duration: 0,
      style: {
        backgroundColor: "#db0832",
      },
    });
    setRunningState(false);
    throw error;
  }
};

// 處理執行按鈕點擊
const handleRunClick = async (context = {}) => {
  // 防止重複點擊
  if (running.value) {
    return;
  }

  // 設置載入中狀態
  running.value = true;

  try {
    // 觸發 run 事件，並傳遞上下文數據
    emit("run", context);

    // 由於實際執行是在父組件中處理的，這裡我們不自動重置狀態
    // 而是等待父組件通過 setRunningState 方法通知執行完成
  } catch (error) {
    running.value = false;

    // 更新錯誤狀態
    updateNodeStatus(NODE_STATES.ERROR, null, error);
  }
};

// 設置執行狀態，供父組件調用
const setRunningState = (isRunning) => {
  running.value = isRunning;

  // 同時更新 nodeState.status
  if (isRunning) {
    nodeState.value = {
      ...nodeState.value,
      status: NODE_STATES.RUNNING,
    };
    // 發送狀態變更事件
    updateNodeStatus(NODE_STATES.RUNNING);
  } else if (nodeState.value.status === NODE_STATES.RUNNING) {
    // 只有當當前狀態為 running 時才更新為 default
    // 避免覆蓋其他狀態（如 completed 或 error）
    nodeState.value = {
      ...nodeState.value,
      status: NODE_STATES.DEFAULT,
    };
  }
};

// 定義 emits
const emit = defineEmits([
  "click",
  "run",
  "handle-connect",
  "handle-disconnect",
  "nodeSizeChange", // 添加節點尺寸變化事件
  "compactModeChanged",
  "resized",
]);

// 處理連接事件
const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

// 處理斷開連接事件
const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 節點包裝器引用
const nodeWrapperRef = ref(null);
// 節點內容引用
const nodeContentRef = ref(null);
// ResizeObserver 實例
let resizeObserver = null;

// 計算節點樣式，統一在這裡處理高度相關的樣式
const nodeStyle = computed(() => {
  const style = {
    width: `${props.nodeWidth}px`,
  };

  // 如果不是自動高度，則使用固定高度
  if (!props.autoHeight) {
    style.height = `${props.nodeHeight}px`;
  }

  return style;
});

// 更新節點高度的防抖函數
const updateNodeHeightDebounced = debounce(() => {
  updateNodeHeight();
}, 500);

// 更新節點高度
const updateNodeHeight = async () => {
  if (!props.autoHeight || !nodeWrapperRef.value || !nodeContentRef.value) {
    return;
  }

  await nextTick();

  try {
    // 獲取內容高度
    const contentHeight = nodeContentRef.value.scrollHeight;
    // 獲取節點頭部高度
    const headerHeight =
      nodeWrapperRef.value.querySelector(".node-header")?.offsetHeight || 0;
    // 獲取執行按鈕區域高度
    const buttonHeight =
      nodeWrapperRef.value.querySelector(".node-execute-button")
        ?.offsetHeight || 0;
    // 獲取錯誤訊息容器高度
    const errorHeight =
      nodeWrapperRef.value.querySelector(".error-message-container")
        ?.offsetHeight || 0;

    // 計算總高度，加上一些額外的間距
    const totalHeight =
      contentHeight + headerHeight + buttonHeight + errorHeight + 30;

    // 確保高度不小於最小高度
    const finalHeight = Math.max(totalHeight, props.minHeight);

    // 檢查高度是否有顯著變化，避免微小變化導致的頻繁更新
    const currentHeight =
      parseInt(nodeWrapperRef.value.style.height) || props.minHeight;

    // 只有當高度變化超過閾值時才更新
    if (Math.abs(currentHeight - finalHeight) > 30) {
      // 設置節點高度
      nodeWrapperRef.value.style.height = `${finalHeight}px`;

      // 通知 Vue Flow 節點尺寸已變更
      emit("nodeSizeChange", {
        id: props.id,
        height: finalHeight,
        width: props.nodeWidth,
      });

      // 避免連續多次觸發 nodeSizeChange 事件
      // console.log(`[BaseNode] 節點 ${props.id} 高度更新至 ${finalHeight}px`);
    }
  } catch (error) {
    // console.log(`[BaseNode] 更新節點 ${props.id} 高度時發生錯誤:`, error);
  }
};

// 監聽節點選擇狀態變化
watch(
  () => props.selected,
  async (newVal, oldVal) => {
    // 只在狀態實際改變且是剛被選中時更新一次
    if (newVal === true && oldVal === false) {
      await nextTick();
      // 延遲執行可以避免與其他操作衝突
      setTimeout(() => {
        updateNodeHeight();
      }, 100);
    }
  }
);

// 監聽節點狀態變化
watch(
  () => nodeState.value.status,
  async (newVal, oldVal) => {
    // 只在特定狀態變化時才觸發更新
    const significantChange =
      // 從默認變為運行中
      (oldVal === NODE_STATES.DEFAULT && newVal === NODE_STATES.RUNNING) ||
      // 從運行中變為完成
      (oldVal === NODE_STATES.RUNNING && newVal === NODE_STATES.COMPLETED) ||
      // 從運行中變為等待互動
      (oldVal === NODE_STATES.RUNNING && newVal === NODE_STATES.PENDING) ||
      // 從等待互動變為運行中（繼續執行）
      (oldVal === NODE_STATES.PENDING && newVal === NODE_STATES.RUNNING) ||
      // 從等待互動變為完成
      (oldVal === NODE_STATES.PENDING && newVal === NODE_STATES.COMPLETED) ||
      // 從運行中變為錯誤
      (oldVal === NODE_STATES.RUNNING && newVal === NODE_STATES.ERROR) ||
      // 從錯誤變為默認
      (oldVal === NODE_STATES.ERROR && newVal === NODE_STATES.DEFAULT);

    if (significantChange) {
      await nextTick();
      setTimeout(() => {
        updateNodeHeightDebounced();
      }, 200);
    }
  }
);

// 監聽 flowStore 狀態變化，確保節點狀態與 store 同步
// 這對於重置操作後的狀態同步尤其重要
watch(
  () => flowStore.getNodeStateById(props.id),
  (newStoreState, oldStoreState) => {
    // 避免不必要的更新和無限循環
    if (!newStoreState || !oldStoreState) return;

    // 檢查是否真的有變化
    const hasChanged =
      newStoreState.status !== oldStoreState.status ||
      JSON.stringify(newStoreState.data) !==
        JSON.stringify(oldStoreState.data) ||
      newStoreState.error !== oldStoreState.error;

    if (hasChanged) {
      // 更新本地狀態以同步 flowStore
      nodeState.value = {
        ...nodeState.value,
        ...newStoreState,
        updatedAt: new Date().toISOString(),
      };

      console.log(`BaseNode ${props.id}: 狀態已從 flowStore 同步更新`, {
        from: oldStoreState,
        to: newStoreState,
      });
    }
  },
  { deep: true, immediate: false }
);

// 在 onMounted 中的 ResizeObserver 邏輯
onMounted(() => {
  console.log("onMounted! Node data:", props.data);
  console.log("isReportMode=>", props.isReportMode);

  // 設置自身引用，讓 useNodeExecution 能夠調用本組件的方法
  selfNodeRef.value = {
    updateNodeStatus,
    nodeState,
    nodeContext,
    nodeLogs,
    handleRun,
    handleClearError,
    formatErrorMessage,
    setRunningState,
    sendStateChangeEvent,
  };

  // 從 flowStore 獲取初始狀態
  const initialState = flowStore.getNodeStateById(props.id);
  if (initialState) {
    nodeState.value = initialState;
  }

  nextTick(() => {
    // 初始化時執行一次高度更新
    updateNodeHeight();

    // 使用 MutationObserver 監聽內容變化，比 ResizeObserver 更穩定
    if (window.MutationObserver && nodeContentRef.value) {
      const observer = new MutationObserver(updateNodeHeightDebounced);

      // 只監聽內容區域的變化
      observer.observe(nodeContentRef.value, {
        childList: true,
        subtree: true,
        attributes: true,
        characterData: true,
      });

      // 保存 observer 以便在組件卸載時清理
      nodeWrapperRef.value._mutationObserver = observer;
    }
  });

  // 添加節點執行事件監聽器
  const handleExecuteNodeEvent = (event) => {
    const { nodeId, ...context } = event.detail;

    // 檢查是否是當前節點
    if (nodeId === props.id) {
      // 調用 handleRun 方法，並傳遞上下文數據
      handleRunClick(context);
    }
  };

  // 添加事件監聽器
  window.addEventListener("flow:executeNode", handleExecuteNodeEvent);

  // 保存事件監聽器引用，以便在 onUnmounted 中移除
  nodeWrapperRef.value._handleExecuteNodeEvent = handleExecuteNodeEvent;

  // 設置折合模式事件監聽器
  const cleanupCompactModeListener = setupCompactModeListener();

  // 保存清理函數
  nodeWrapperRef.value._cleanupCompactModeListener = cleanupCompactModeListener;
});

// 清理 observer
onUnmounted(() => {
  if (nodeWrapperRef.value && nodeWrapperRef.value._mutationObserver) {
    nodeWrapperRef.value._mutationObserver.disconnect();
    nodeWrapperRef.value._mutationObserver = null;
  }

  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }

  // 移除節點執行事件監聽器
  if (nodeWrapperRef.value && nodeWrapperRef.value._handleExecuteNodeEvent) {
    window.removeEventListener(
      "flow:executeNode",
      nodeWrapperRef.value._handleExecuteNodeEvent
    );
    nodeWrapperRef.value._handleExecuteNodeEvent = null;
  }

  // 移除折合模式事件監聽器
  if (
    nodeWrapperRef.value &&
    nodeWrapperRef.value._cleanupCompactModeListener
  ) {
    nodeWrapperRef.value._cleanupCompactModeListener();
    nodeWrapperRef.value._cleanupCompactModeListener = null;
  }
});

// 錯誤詳情顯示控制
const showErrorDetails = ref(false);
const showFullError = ref(false);

// 格式化錯誤信息
const formatErrorMessage = (message) => {
  if (!message) return "未知錯誤";

  // 如果錯誤信息包含堆棧跟踪，只顯示第一行
  if (message.includes("\n")) {
    return message.split("\n")[0];
  }

  // 如果錯誤信息太長，截斷它
  if (message.length > 100) {
    return message.substring(0, 100) + "...";
  }

  // 處理特定類型的錯誤信息
  if (message.includes("不支持的節點類型")) {
    return "節點類型不支持，請聯繫系統管理員";
  }

  return message;
};

// 清除錯誤狀態
const handleClearError = async () => {
  try {
    // 更新節點狀態為默認
    if (currentInstance.value?.id) {
      await clearNodeError(props.id);

      // 重置本地狀態
      showErrorDetails.value = false;
      showFullError.value = false;

      // console.log(`已清除節點 ${props.id} 的錯誤狀態`);
    }
  } catch (error) {
    console.error("清除錯誤狀態失敗:", error);
  }
};

// 格式化執行時間
const formatExecutionTime = (time) => {
  if (!time) return "未知";

  // 如果時間小於1秒，顯示毫秒
  if (time < 1) {
    return `${Math.round(time * 1000)}毫秒`;
  }

  // 如果時間大於60秒，顯示分鐘和秒
  if (time > 60) {
    const minutes = Math.floor(time / 60);
    const seconds = Math.round(time % 60);
    return `${minutes}分${seconds}秒`;
  }

  // 否則顯示秒
  return `${time.toFixed(2)}秒`;
};

// 添加全屏狀態控制
const isFullscreen = ref(false);

// 切換全屏狀態
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  // 更新節點尺寸，但需要延遲執行
  if (!isFullscreen.value) {
    nextTick(() => {
      setTimeout(() => {
        updateNodeHeight();
        // 強制觸發一次Vue Flow的重新計算
        emit("nodeSizeChange", {
          id: props.id,
          height: nodeWrapperRef.value
            ? nodeWrapperRef.value.offsetHeight
            : props.nodeHeight,
          width: props.nodeWidth,
        });
      }, 300);
    });
  }
};

// 處理說明按鈕點擊
const handleHelpClick = async () => {
  console.log("執行 handleHelpClick 方法! Node data:", props.data);

  // 顯示加載狀態
  isHelpLoading.value = true;

  // 顯示抽屜，讓用戶知道操作已響應
  showHelpDrawer.value = true;

  try {
    // 發出事件，讓父組件有機會處理
    emit("help-click");

    const definitionId = props?.data?.nodeDefinitionId;
    const helpContent = props?.data?.helpContent;

    // 如果有傳入的 nodeDefinitionId，則使用它
    if (helpContent && helpContent.length > 0) {
      console.log("使用傳入的幫助內容:", helpContent);
      nodeHelpContent.value = helpContent;
    } else if (definitionId) {
      console.log("正在獲取節點幫助內容，定義ID:", definitionId);

      try {
        const response = await getFlowNodeHelpContent(definitionId);
        console.log("獲取節點幫助內容成功:", response.data);

        // 更新幫助內容
        if (response.data?.helpContent) {
          nodeHelpContent.value = response.data.helpContent;
        } else {
          // 如果API返回空內容，使用預設內容
          nodeHelpContent.value = DEFAULT_HELP_CONTENT;
        }
      } catch (error) {
        console.error(
          "獲取節點幫助內容失敗，使用預設內容",
          error.message,
          error
        );
        // 使用預設內容
        nodeHelpContent.value = DEFAULT_HELP_CONTENT;
      }
    } else {
      // 找不到節點定義ID，使用預設內容
      console.log("找不到節點定義ID，使用預設內容");
      nodeHelpContent.value = DEFAULT_HELP_CONTENT;
    }
  } catch (error) {
    console.error("獲取節點幫助內容失敗", error.message, error);
    nodeHelpContent.value = "獲取說明內容時發生錯誤，請稍後再試。";
  } finally {
    // 隱藏加載狀態
    isHelpLoading.value = false;
  }
};

// 打印日志(NOTE:不再使用)
const printLog = (command = "", result = {}) => {
  console.log(title, command, result);
};

const errorNotify = ref("default errro");

const setErrorNotifyMessage = (message) => {
  console.log("setErrorNotifyMessage==========>", message);
  errorNotify.value = message;
};

// 暴露方法和屬性
defineExpose({
  printLog,
  nodeState,
  nodeContext,
  nodeLogs,
  handleRun,
  handleClearError,
  formatErrorMessage,
  setRunningState,
  updateNodeStatus, // 暴露統一的狀態更新方法
  sendStateChangeEvent, // 暴露發送狀態變更事件的方法
  isFullscreen, // 暴露全屏狀態
  toggleFullscreen, // 暴露切換全屏的方法
  formatExecutionTime,
  nodeDefinitionId: props.nodeDefinitionId, // 暴露節點定義ID
  compactMode,
  toggleCompactMode,
  setErrorNotifyMessage,
});
</script>

<style scoped>
.node-wrapper {
  @apply relative rounded-lg border border-gray-200 dark:border-node-dark bg-white dark:bg-node-dark shadow-sm flex flex-col;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: v-bind('minHeight + "px"');
  /* border: 10px solid rgb(251, 251, 251); */
}

/* 深色模式下的節點容器 */
/* html.dark .node-wrapper {
  @apply bg-zinc-900 border-zinc-700;
} */

.node-wrapper:hover {
  @apply shadow-md;
}

.node-wrapper.selected {
  @apply shadow-lg;
}

.node-header {
  padding: 0.75rem;

  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  position: relative;
  overflow: hidden;
}

/* 深色模式下的節點頭部 */

.node-content {
  @apply relative;
  flex: 1;
  min-height: 50px;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: height;
  overflow-y: auto;
}

.fixed-height-node {
  height: v-bind('nodeHeight + "px"') !important; /* 使用 nodeHeight 屬性值 */
  display: flex;
  flex-direction: column;
}

.fixed-height-node .node-content {
  flex: 1;
  overflow-y: auto;
}

.fixed-height-node .node-execute-button {
  margin-top: auto;
}

/* 滾動條美化 */
.node-content::-webkit-scrollbar {
  width: 6px;
}

.node-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
  border-radius: 3px;
}

.node-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600;
  border-radius: 3px;
}

.node-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

.node-status {
  @apply px-3 py-2 bg-transparent;
}

/* 深色模式下的節點狀態區域 */
html.dark .node-status {
  @apply text-gray-300;
}

/* 新增執行按鈕區域樣式 */
.node-execute-button {
  @apply px-3 py-2 bg-gray-50 rounded-b-lg;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
}

/* 深色模式下的執行按鈕區域 */
html.dark .node-execute-button {
  @apply bg-node-dark;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.2);
}

.node-execute-button .el-button {
  min-width: 100px;
  transition: all 0.3s ease;
}

.node-execute-button .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.flow-node {
  @apply relative bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden;
  min-width: 200px;
  max-width: 320px;
  transition: all 0.2s ease;
}

/* 深色模式下的流程節點 */
html.dark .flow-node {
  @apply bg-gray-800 border-gray-700;
}

.flow-node--selected {
  @apply shadow-md border-blue-400;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 深色模式下的選中節點 */
html.dark .flow-node--selected {
  @apply border-blue-500;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.flow-node--running {
  @apply border-purple-500 bg-purple-50;
  animation: purplePulse 1.5s infinite;
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.7) !important;
}

/* 深色模式下的運行中節點 */
html.dark .flow-node--running {
  @apply border-purple-400 bg-purple-950;
  animation: darkPurplePulse 1.5s infinite;
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.9) !important;
}

.flow-node--completed {
  @apply border-green-400;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
}

/* 深色模式下的完成節點 */
html.dark .flow-node--completed {
  @apply border-green-500;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
}

.flow-node--error,
.flow-node--failed {
  @apply border-red-400 border-4;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.3);
}

/* 深色模式下的錯誤節點 */
html.dark .flow-node--error,
html.dark .flow-node--failed {
  @apply border-red-500;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.5);
}

.flow-node--disabled {
  @apply opacity-60 cursor-not-allowed;
}

.flow-node__header {
  @apply flex items-center justify-between p-2 border-b border-gray-100 bg-gray-50;
}

/* 深色模式下的節點頭部 */
html.dark .flow-node__header {
  @apply border-gray-700 bg-gray-800;
}

.flow-node__title {
  @apply flex items-center;
}

.flow-node__icon {
  @apply mr-2 text-gray-500;
}

/* 深色模式下的圖標 */
html.dark .flow-node__icon {
  @apply text-gray-400;
}

.flow-node__label {
  @apply font-medium text-gray-700 truncate;
  max-width: 180px;
}

/* 深色模式下的標籤 */
html.dark .flow-node__label {
  @apply text-gray-300;
}

.flow-node__content {
  @apply p-3;
}

.flow-node__handles {
  @apply absolute top-0 left-0 w-full h-full pointer-events-none;
}

.flow-node__handle {
  @apply absolute w-3 h-3 rounded-full bg-gray-300 border border-gray-400 cursor-pointer pointer-events-auto;
  transition: all 0.2s ease;
}

/* 深色模式下的連接點 */
/* html.dark .flow-node__handle {
  @apply bg-gray-600 border-gray-500;
}

.flow-node__handle:hover {
  @apply bg-green-400 border-red-500;
  z-index: 999;
  transform: scale(1.92);
}

.flow-node__handle--connected {
  @apply bg-green-400 border-green-500;
} */

/* 深色模式下的已連接連接點 */
/* html.dark .flow-node__handle--connected {
  @apply bg-green-500 border-green-600;
}

.flow-node__handle--input {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.flow-node__handle--output {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
} */

.flow-node__error-indicator {
  @apply absolute top-0 right-0 p-1;
}

.flow-node__error-icon {
  @apply text-red-500 text-lg;
}

/* 深色模式下的錯誤指示器 */
html.dark .flow-node__error-icon {
  @apply text-red-400;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 深色模式下的脈衝動畫 */
@keyframes darkPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.5);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(96, 165, 250, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

.error-message-container {
  @apply mx-3 my-2 p-3 bg-red-50 border border-red-200 rounded-md text-sm;
}

.error-header {
  @apply flex items-center text-red-600 font-medium mb-2;
}

/* 深色模式下的錯誤頭部 */
html.dark .error-header {
  @apply text-red-400;
}

.error-summary {
  @apply text-red-700 mb-2;
}

.error-details {
  @apply mt-2 pt-2 border-t border-red-200;
}

.error-detail-item {
  @apply flex justify-between mb-1;
}

.error-detail-label {
  @apply text-red-600 font-medium;
}

.error-detail-value {
  @apply text-red-800;
}

.error-actions {
  @apply flex justify-end space-x-2;
}

.execution-time {
  @apply mx-3 my-2 text-xs text-gray-500;
}

/* 添加錯誤狀態的閃爍邊框效果 */
.flow-node--error,
.flow-node--failed {
  animation: errorPulse 2s infinite;
}

@keyframes errorPulse {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.3);
  }
}

/* 確保連結點能夠正確顯示 */
:deep(.vue-flow__handle) {
  z-index: 50;
  position: absolute;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

:deep(.vue-flow__handle.left) {
  left: 0;
  transform: translateX(-50%);
}

:deep(.vue-flow__handle.right) {
  right: 0;
  transform: translateX(50%);
}

/* 確保 connection lines 平滑過渡 */
:deep(.vue-flow__edge) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.vue-flow__edge-path) {
  transition: stroke-dasharray 0.3s ease, stroke 0.3s ease;
}

/* 深色模式下的連接線樣式 */
html.dark :deep(.vue-flow__edge-path) {
  stroke: #64748b !important;
}

html.dark :deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #3b82f6 !important;
}

html.dark :deep(.vue-flow__edge.animated .vue-flow__edge-path) {
  stroke-dasharray: 5 5 !important;
}

@keyframes purplePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.8);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0.3);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

/* 深色模式的紫色脈衝動畫 */
@keyframes darkPurplePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(192, 132, 252, 0.8);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(192, 132, 252, 0.3);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(192, 132, 252, 0);
  }
}

/* 執行中節點的特殊樣式 */
.node-wrapper.flow-node--running::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 3px solid #a855f7;
  border-radius: 12px;
  animation: borderPulse 2s infinite;
  pointer-events: none;
  z-index: -1;
}

/* 深色模式下的執行中邊框 */
html.dark .node-wrapper.flow-node--running::before {
  border-color: #c084fc;
}

@keyframes borderPulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.03);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 處理中覆蓋層樣式 */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(3px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 0.25rem;
}

/* 深色模式下的處理中覆蓋層 */
html.dark .processing-overlay {
  background-color: rgba(31, 41, 55, 0.9);
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.processing-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(147, 51, 234, 0.2);
  border-radius: 50%;
  border-top-color: #a855f7;
  animation: spin 1s linear infinite;
}

/* 深色模式下的處理中轉圈動畫 */
html.dark .processing-spinner {
  border: 4px solid rgba(192, 132, 252, 0.2);
  border-top-color: #c084fc;
}

.processing-text {
  font-size: 16px;
  font-weight: 600;
  color: #9333ea;
  animation: pulse 1.5s ease infinite;
}

/* 深色模式下的處理中文字 */
html.dark .processing-text {
  color: #c084fc;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 全屏模式樣式 */
.fullscreen-node {
  @apply fixed inset-0 bg-white z-[100];
  width: 100%;
  height: 100%;
}

/* 深色模式下的全屏節點 */
html.dark .fullscreen-node {
  @apply bg-gray-900;
}

.fullscreen-content {
  @apply h-full flex flex-col;
}

.fullscreen-content .node-header {
  @apply sticky top-0 z-10 px-8 py-4 bg-light-mode shadow-md;
}

/* 深色模式下的全屏頭部 */
/* html.dark .fullscreen-content .node-header {
   @apply bg-dark-mode shadow-gray-900;
} */

.fullscreen-content .node-content {
  @apply flex-1 overflow-auto px-8 py-6;
  height: calc(100vh - 64px); /* 調整高度以適應頭部，全屏模式下沒有底部狀態欄 */
}

.fullscreen-content .node-status {
  @apply sticky bottom-0 bg-white shadow-lg px-8 py-4 border-t;
}

/* 深色模式下的全屏狀態區域 */
html.dark .fullscreen-content .node-status {
  @apply bg-gray-800 border-gray-700 shadow-gray-900;
}

.fullscreen-content .fullscreen-footer {
  @apply sticky bottom-0 bg-white shadow-lg border-t;
  z-index: 10;
}

/* 深色模式下的全屏底部 */
html.dark .fullscreen-content .fullscreen-footer {
  @apply bg-node-dark border-gray-700 shadow-gray-900;
}

.fullscreen-content .fullscreen-footer .el-button {
  min-width: 100px;
  transition: all 0.3s ease;
}

.fullscreen-content .fullscreen-footer .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 過渡動畫 */
.fullscreen-node-enter-active,
.fullscreen-node-leave-active {
  transition: all 0.3s ease;
}

.fullscreen-node-enter-from,
.fullscreen-node-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 全屏模式下處理中覆蓋層調整 */
.fullscreen-content .processing-overlay {
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 11;
}

/* 深色模式下的全屏處理中覆蓋層 */
html.dark .fullscreen-content .processing-overlay {
  background-color: rgba(31, 41, 55, 0.95);
}

.fullscreen-content .processing-spinner {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.fullscreen-content .processing-text {
  font-size: 24px;
  margin-top: 16px;
}

/* 深色模式下Element Plus組件的自定義樣式 */
html.dark :deep(.el-divider__text) {
  @apply bg-gray-800 text-gray-300;
}

/* 特定節點圖標的深色模式樣式 */
html.dark .node-header [class*="text-"] {
  @apply text-gray-100;
}

/* 深色模式下的 drawer 樣式 */
:deep(.dark-mode-drawer) {
  @apply bg-gray-900;
}

:deep(.dark-mode-drawer .el-drawer__header) {
  @apply text-gray-100 border-gray-700 mb-0;
}

:deep(.dark-mode-drawer .el-drawer__body) {
  @apply bg-gray-900 text-gray-100;
}

:deep(.el-drawer__header) {
  @apply border-b py-4 px-6 mb-0;
}

/* todo: 重構，整合 */
:deep(.markdown-content) {
  font-size: 1rem;
  line-height: 1.6;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin: 1em 0 0.5em;
  line-height: 1.4;
  font-weight: 600;
}

:deep(.markdown-content h1) {
  font-size: 2em;
}
:deep(.markdown-content h2) {
  font-size: 1.5em;
}
:deep(.markdown-content h3) {
  font-size: 1.3em;
}
:deep(.markdown-content h4) {
  font-size: 1.1em;
}
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  font-size: 1em;
}

:deep(.markdown-content p) {
  margin: 1em 0;
}

:deep(.markdown-content pre) {
  margin: 1em 0;
  padding: 1em;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.markdown-content code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: #f6f8fa;
  border-radius: 3px;
}

:deep(.markdown-content ol),
:deep(.markdown-content ul) {
  margin: 1em 0;
  padding-left: 2em;
}

:deep(.markdown-content li) {
  margin: 0.5em 0;
}

:deep(.markdown-content blockquote) {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #42b983;
  background-color: #f8f8f8;
  color: #666;
}

:deep(.markdown-content img) {
  max-width: 100%;
  margin: 1em 0;
}

:deep(.markdown-content table) {
  width: 100%;
  margin: 1em 0;
  border-collapse: collapse;
}

:deep(.markdown-content th),
:deep(.markdown-content td) {
  padding: 0.5em;
  border: 1px solid #ddd;
}

:deep(.markdown-content th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

/**NOTE: 沒效果 */
.custom-error-notification {
  background-color: red !important;
  color: white !important;
}

:deep(.el-notification__content) {
  color: white !important;
  font-size: 3rem !important;
}

:deep(.my-error-notification .el-notification__title) {
  color: white !important;
  font-size: 30px !important;
}

@keyframes completedPulse {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
  }
}

/* 深色模式下的完成節點脈衝動畫 */
html.dark .flow-node--completed {
  animation: darkCompletedPulse 2s infinite;
}

@keyframes darkCompletedPulse {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
  }
}

/* 緊湊模式下的節點樣式 */
.flow-node--compact {
  min-height: auto !important;
  height: auto !important;
}

.flow-node--compact .node-wrapper {
  min-height: auto !important;
}

/* 報表模式樣式 */
.report-mode {
  border: none;
  box-shadow: none;
  background-color: transparent;
}

.report-mode .node-header {
  background-color: transparent;
  border-bottom: none;
}

.report-mode .node-content {
  padding: 0;
}

/* 深色模式下的報表節點 */
html.dark .report-mode {
  background-color: #1e1e2e;
}

html.dark .report-mode .node-header {
  background-color: #374151;
}

html.dark .report-mode .node-content {
  background-color: #1f2937;
}

/* 報表模式特定樣式 */
.flow-node--report .node-content {
  background-color: white;
}

.dark .flow-node--report .node-content {
  background-color: var(--node-dark-bg);
}

.fixed-height-node {
  height: v-bind('nodeHeight + "px"') !important; /* 使用 nodeHeight 屬性值 */
  min-height: v-bind('nodeHeight + "px"') !important;
  max-height: v-bind('nodeHeight + "px"') !important;
  display: flex;
  flex-direction: column;
}

/* 滾動條美化 */
.node-content::-webkit-scrollbar {
  width: 6px;
}

.node-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
  border-radius: 3px;
}

.node-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600;
  border-radius: 3px;
}

.node-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
