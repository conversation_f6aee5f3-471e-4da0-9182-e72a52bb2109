<template>
  <BaseEdge
    v-if="path && path[0] && typeof path[0] === 'string' && path[0].length > 0"
    :id="id"
    :style="style"
    :path="path[0]"
    :marker-end="markerEnd"
    class="vue-flow__edge-path" />

  <EdgeLabelRenderer>
    <div
      v-if="path && path[1] !== undefined && path[2] !== undefined && 
            typeof path[1] === 'number' && typeof path[2] === 'number' &&
            !isNaN(path[1]) && !isNaN(path[2]) && 
            isFinite(path[1]) && isFinite(path[2])"
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
        zIndex: 1000,
      }"
      class="nodrag nopan edge-buttons-container">
      <el-button
        circle
        size="small"
        type="danger"
        class="edge-button edge-delete-button"
        @click="handleDeleteEdge"
        @mousedown.stop
        @click.stop>
        <!-- <X :size="16" /> -->
        刪除連線
      </el-button>

      <!-- <el-tooltip
        content="編輯標籤"
        placement="top"
        :show-after="300"
        :hide-after="0"
      >
        <el-button
          circle
          size="small"
          type="primary"
          class="edge-button edge-edit-button"
          @click="handleEditLabel"
          @mousedown.stop
          @click.stop
        >
          <Edit :size="16" />
        </el-button>
      </el-tooltip> -->

      <el-button
        circle
        size="small"
        :type="isAnimated ? 'success' : 'info'"
        class="edge-button edge-animate-button"
        @click="handleToggleAnimation"
        @mousedown.stop
        @click.stop>
        <component
          :is="isAnimated ? Play : Pause"
          :size="16" />
        切換動畫
      </el-button>

      <!-- <el-tooltip
        content="調整位置"
        placement="top"
        :show-after="300"
        :hide-after="0"
      >
        <el-button
          circle
          size="small"
          type="warning"
          class="edge-button edge-position-button"
          @click="handleAdjustPosition"
          @mousedown.stop
          @click.stop
        >
          <MoveVertical :size="16" />
        </el-button>
      </el-tooltip> -->
    </div>

    <!-- !TODO: 暫時註解掉 -->
    <el-dialog
      v-model="showLabelDialog"
      title="編輯連線標籤"
      width="30%"
      :close-on-click-modal="false"
      destroy-on-close
      append-to-body
      :style="dialogStyle">
      <el-form
        :model="labelForm"
        label-width="80px">
        <el-form-item label="標籤文字">
          <el-input
            v-model="labelForm.text"
            placeholder="請輸入標籤文字" />
        </el-form-item>
        <el-form-item label="文字顏色">
          <el-color-picker v-model="labelForm.color" />
        </el-form-item>
        <el-form-item label="線條樣式">
          <el-select
            v-model="labelForm.lineStyle"
            placeholder="請選擇線條樣式">
            <el-option
              label="實線"
              value="solid" />
            <el-option
              label="虛線"
              value="dashed" />
            <el-option
              label="點線"
              value="dotted" />
          </el-select>
        </el-form-item>
        <el-form-item label="連線類型">
          <el-select
            v-model="labelForm.type"
            placeholder="請選擇連線類型">
            <el-option
              label="預設"
              value="default" />
            <el-option
              label="簡單"
              value="simplebezier" />
            <el-option
              label="平滑"
              value="step" />
          </el-select>
        </el-form-item>
        <el-form-item label="線條粗細">
          <el-slider
            v-model="labelForm.strokeWidth"
            :min="1"
            :max="5"
            :step="0.5"
            show-stops />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showLabelDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSaveLabel"
            >確定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </EdgeLabelRenderer>
</template>

<script setup>
import {
  BaseEdge,
  EdgeLabelRenderer,
  getBezierPath,
  useVueFlow,
} from "@vue-flow/core";
import { Play, Pause, Edit } from "lucide-vue-next";
import { computed, ref } from "vue";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  sourceX: {
    type: Number,
    required: true,
  },
  sourceY: {
    type: Number,
    required: true,
  },
  targetX: {
    type: Number,
    required: true,
  },
  targetY: {
    type: Number,
    required: true,
  },
  sourcePosition: {
    type: String,
    required: true,
  },
  targetPosition: {
    type: String,
    required: true,
  },
  markerEnd: {
    type: String,
    required: false,
  },
  style: {
    type: Object,
    required: false,
  },
  label: {
    type: String,
    required: false,
    default: "",
  },
  animated: {
    type: Boolean,
    required: false,
    default: false,
  },
  selected: {
    type: Boolean,
    required: false,
    default: false,
  },
  source: {
    type: String,
    required: true,
  },
  target: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: false,
    default: "default", // 'default' | 'straight' | 'step' | 'smoothstep'
  },
});

const vueFlowInstance = useVueFlow();
const { removeEdges } = vueFlowInstance;

// 添加路徑計算輔助函數
const getEdgeCenterPosition = (sourceX, sourceY, targetX, targetY) => {
  // 計算連接線的中點，確保數字精度
  const centerX = Math.round(sourceX + (targetX - sourceX) / 2);
  const centerY = Math.round(sourceY + (targetY - sourceY) / 2);
  return [centerX, centerY];
};

// 修改路徑計算邏輯，增加數值驗證
const path = computed(() => {
  const { sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition } =
    props;

  // 驗證所有座標值是否有效
  const isValidNumber = (value) => {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  };

  if (!isValidNumber(sourceX) || !isValidNumber(sourceY) || 
      !isValidNumber(targetX) || !isValidNumber(targetY)) {
    console.warn('EdgeWithButton: 無效的座標值', { sourceX, sourceY, targetX, targetY });
    // 使用安全的預設值
    const safeSourceX = isValidNumber(sourceX) ? sourceX : 0;
    const safeSourceY = isValidNumber(sourceY) ? sourceY : 0;
    const safeTargetX = isValidNumber(targetX) ? targetX : 100;
    const safeTargetY = isValidNumber(targetY) ? targetY : 100;
    
    const fallbackPath = `M${safeSourceX},${safeSourceY} L${safeTargetX},${safeTargetY}`;
    const fallbackLabelX = safeSourceX + (safeTargetX - safeSourceX) / 2;
    const fallbackLabelY = safeSourceY + (safeTargetY - safeSourceY) / 2;
    return [fallbackPath, fallbackLabelX, fallbackLabelY];
  }

  try {
    // 確保位置參數有效
    const safeSourcePosition = sourcePosition || 'right';
    const safeTargetPosition = targetPosition || 'left';

    // 獲取路徑數據
    const [pathString, labelX, labelY] = getBezierPath({
      sourceX: Number(sourceX),
      sourceY: Number(sourceY),
      sourcePosition: safeSourcePosition,
      targetX: Number(targetX),
      targetY: Number(targetY),
      targetPosition: safeTargetPosition,
    });

    // 驗證返回的路徑和標籤位置
    if (!pathString || !isValidNumber(labelX) || !isValidNumber(labelY)) {
      throw new Error('getBezierPath 返回了無效的數據');
    }

    // 根據不同的類型調整標籤位置
    let adjustedY = labelY;
    if (props.type === "step" || props.type === "smoothstep") {
      // 對於階梯型連接線，標籤位置需要上移一點
      adjustedY -= 20;
    }

    return [pathString, labelX, adjustedY];
  } catch (error) {
    console.error('計算邊線路徑時發生錯誤:', error, { 
      sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition 
    });
    // 返回一個安全的預設路徑
    const fallbackPath = `M${sourceX},${sourceY} L${targetX},${targetY}`;
    const fallbackLabelX = sourceX + (targetX - sourceX) / 2;
    const fallbackLabelY = sourceY + (targetY - sourceY) / 2;
    return [fallbackPath, fallbackLabelX, fallbackLabelY];
  }
});

const isAnimated = ref(props.animated);
const showLabelDialog = ref(false);
const dialogStyle = {
  zIndex: 10001,
  position: "fixed",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
};
const labelForm = ref({
  text: props.label || "",
  color: props.style?.stroke || "#3f3f3f",
  lineStyle: props.style?.strokeDasharray ? "dashed" : "solid",
  strokeWidth: props.style?.strokeWidth || 2,
  type: props.type || "default",
});

const handleDeleteEdge = () => {
  ElMessageBox.confirm("確定要刪除這條連接線嗎？", "提示", {
    confirmButtonText: "確定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      removeEdges([props.id]);
      ElMessage({
        type: "success",
        message: "已刪除連接線",
      });
    })
    .catch(() => {
      // 用戶取消刪除操作
    });
};

const handleEditLabel = () => {
  showLabelDialog.value = true;
};

const handleSaveLabel = () => {
  const edge = vueFlowInstance.edges.value.find((e) => e.id === props.id);
  if (!edge) {
    ElMessage({
      type: "error",
      message: "找不到要更新的連線",
    });
    return;
  }

  const newStyle = {
    ...edge.style,
    stroke: labelForm.value.color,
    strokeWidth: labelForm.value.strokeWidth,
    strokeDasharray:
      labelForm.value.lineStyle === "dashed"
        ? "5 5"
        : labelForm.value.lineStyle === "dotted"
        ? "2 2"
        : undefined,
  };

  const updatedEdge = {
    ...edge,
    label: labelForm.value.text,
    style: newStyle,
    type: labelForm.value.type,
    markerEnd: {
      type: "arrowclosed",
      color: labelForm.value.color,
    },
  };

  vueFlowInstance.setEdges(
    vueFlowInstance.edges.value.map((e) =>
      e.id === props.id ? updatedEdge : e
    )
  );

  showLabelDialog.value = false;
  ElMessage({
    type: "success",
    message: "已更新連線樣式",
  });
};

const handleToggleAnimation = () => {
  const edge = vueFlowInstance.edges.value.find((e) => e.id === props.id);
  if (!edge) {
    ElMessage({
      type: "error",
      message: "找不到要更新的連線",
    });
    return;
  }

  isAnimated.value = !isAnimated.value;
  const updatedEdge = {
    ...edge,
    animated: isAnimated.value,
  };

  vueFlowInstance.setEdges(
    vueFlowInstance.edges.value.map((e) =>
      e.id === props.id ? updatedEdge : e
    )
  );
};

const handleAdjustPosition = () => {
  ElMessageBox.alert(
    "拖動連線的起點或終點來調整位置。你也可以拖動連線中間的部分來調整曲線。",
    "提示",
    {
      confirmButtonText: "知道了",
    }
  );
};
</script>

<script>
export default {
  inheritAttrs: false,
};
</script>

<style scoped>
.edge-buttons-container {
  display: flex;
  gap: 2px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 7px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.edge-button {
  @apply !w-16 !h-5 !p-1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 7px;
  font-size: 12px;
}

.edge-button :deep(svg) {
  color: currentColor;
  stroke-width: 2;
}

.edge-button.edge-delete-button {
  @apply !bg-white !text-red-500 hover:!bg-red-500 hover:!text-white;
}

.edge-button.edge-edit-button {
  @apply !bg-white !text-blue-500 hover:!bg-blue-500 hover:!text-white;
}

.edge-button.edge-animate-button {
  @apply !bg-white !text-green-500 hover:!bg-green-500 hover:!text-white;
}

.edge-button.edge-position-button {
  @apply !bg-white !text-yellow-500 hover:!bg-yellow-500 hover:!text-white;
}

.edge-button:hover {
  transform: scale(1.1);
}

:deep(.vue-flow__edge:hover) ~ .edge-buttons-container,
:deep(.vue-flow__edge.selected) ~ .edge-buttons-container,
.edge-buttons-container:hover {
  opacity: 1;
  transform: translateY(-5px);
}

:deep(.vue-flow__edge.animated) path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

/* 添加不同類型連接線的樣式 */
:deep(.vue-flow__edge-path) {
  transition: stroke-width 0.2s, stroke 0.2s;
}

:deep(.vue-flow__edge.step .vue-flow__edge-path),
:deep(.vue-flow__edge.smoothstep .vue-flow__edge-path) {
  stroke-linecap: round;
}

:deep(.vue-flow__edge.straight .vue-flow__edge-path) {
  stroke-linecap: square;
}

/* 深色模式支援 */
html.dark .edge-buttons-container {
  background: rgba(30, 30, 30, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

html.dark .edge-button.edge-delete-button {
  @apply !bg-gray-800 !text-red-400 hover:!bg-red-500 hover:!text-gray-100;
}

html.dark .edge-button.edge-edit-button {
  @apply !bg-gray-800 !text-blue-400 hover:!bg-blue-500 hover:!text-gray-100;
}

html.dark .edge-button.edge-animate-button {
  @apply !bg-gray-800 !text-green-400 hover:!bg-green-500 hover:!text-gray-100;
}

@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }
}

.nodrag {
  pointer-events: all !important;
}

.nopan {
  pointer-events: all !important;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}
</style>
