<template>
  <div
    class="file-node"
    :class="{ selected: selected, 'is-uploading': isUploading }"
    @click="$emit('click', $event)">
    <!-- 選單按鈕 -->
    <div class="menu-button-wrapper">
      <el-dropdown
        ref="dropdownRef"
        trigger="click"
        @command="handleCommandWrapper">
        <CircleEllipsis class="text-gray-300 hover:text-blue-500 w-2 h-2" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              command="preview"
              v-if="isPreviewableFile">
              <Eye
                :size="14"
                class="mr-2" />預覽{{ id }}
            </el-dropdown-item>
            <el-dropdown-item command="download">
              <Download
                :size="14"
                class="mr-2" />下載
            </el-dropdown-item>
            <el-dropdown-item
              command="remove"
              v-admin>
              <Scissors
                :size="14"
                class="mr-2" />移除顯示
            </el-dropdown-item>
            <el-dropdown-item
              command="delete"
              class="!text-red-500">
              <Trash
                :size="14"
                class="mr-2" />刪除
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="file-content">
      <!-- 圖片預覽 - 只在上傳完成且是圖片類型時顯示 -->
      <div
        v-if="isImageFile && data.uploadProgress === 100"
        class="node-preview">
        <img
          :src="data.fileUrl"
          :alt="decodedFileNameValue"
          class="preview-image cursor-pointer"
          @load="imageLoaded = true"
          @error="handleImageError"
          @click.stop="handlePreviewFile"
          v-show="imageLoaded" />
        <div
          v-if="!imageLoaded"
          class="image-loading">
          <component
            :is="Loading"
            class="loading-icon"
            :size="24" />
          <span>圖片加載中...</span>
        </div>
      </div>
      <!-- 影片預覽 - 只在上傳完成且是影片類型時顯示 -->
      <div
        v-else-if="isVideoFile && data.uploadProgress === 100"
        class="node-preview"
        @click.stop="handlePreviewFile">
        <div class="video-overlay">
          <Play
            :size="24"
            class="play-icon" />
        </div>
        <video
          ref="nodeVideoRef"
          :src="data.fileUrl"
          class="preview-video"
          preload="metadata"
          muted
          disablePictureInPicture
          disableRemotePlayback
          @loadedmetadata="captureVideoThumbnail"></video>
      </div>
      <!-- 上傳中或非圖片/影片類型顯示圖標 -->
      <div
        v-else
        class="icon-wrapper">
        <component
          :is="getFileIcon(data.fileType)"
          :size="24"
          class="file-icon"
          :class="getIconColorClass(data.fileType)" />
        <div
          v-if="data.uploadProgress < 100"
          class="upload-overlay">
          <div class="progress-text">{{ data.uploadProgress }}%</div>
          <div class="progress-bar-container">
            <div
              class="progress-bar"
              :style="{ width: `${data.uploadProgress}%` }"></div>
          </div>
        </div>
      </div>

      <div class="file-info dark:text-white">
        <el-tooltip
          :content="data.fileName"
          placement="top"
          :show-after="500"
          :hide-after="0">
          <div
            class="file-name text-sm"
            :title="data.fileName">
            {{ truncatedFileNameValue }}
          </div>
        </el-tooltip>
        <div class="file-size">{{ formatFileSize(data.fileSize) }}</div>
        <div
          v-if="data.uploadProgress < 100"
          class="upload-progress">
          <el-progress
            :percentage="data.uploadProgress"
            :format="(p) => `${p}%`"
            :stroke-width="4"
            class="mt-1" />
        </div>
        <!-- 檔案操作按鈕 -->
        <div
          v-if="data.uploadProgress === 100"
          class="file-actions mt-2">
          <!-- 預覽按鈕 - 只對可預覽的檔案顯示 -->
          <el-button
            v-if="isPreviewableFile"
            type="primary"
            link
            size="small"
            @click.stop="handlePreviewFile">
            <Eye
              :size="14"
              class="mr-1" />預覽
          </el-button>
          <!-- 下載按鈕 - 所有檔案都顯示 -->
          <el-button
            type="primary"
            link
            size="small"
            @click.stop="handleDownloadFile">
            <Download
              :size="14"
              class="mr-1" />下載
          </el-button>
        </div>
      </div>
    </div>

    <!-- 檔案預覽對話框 -->
    <Teleport to="body">
      <div
        v-if="previewVisible"
        class="custom-dialog-container">
        <div
          class="custom-dialog-overlay"
          @click="handleClosePreview"></div>
        <div
          class="custom-dialog"
          :class="{ 'is-fullscreen': isFullscreen }">
          <div class="custom-dialog-header h-8">
            <File class="text-light-mode dark:text-dark-mode" />
            <h4 class="custom-dialog-title">{{ data.fileName }}</h4>
            <div class="custom-dialog-buttons">
              <!-- 縮放控制，只對圖片和PDF顯示，不對PPT顯示 -->
              <div
                v-if="showZoomControlsValue"
                class="button-group">
                <el-button
                  link
                  @click="handleZoomOut">
                  <ZoomOut :size="16" />
                </el-button>
                <el-button
                  link
                  @click="handleZoomIn">
                  <ZoomIn :size="16" />
                </el-button>
              </div>
              <!-- 全螢幕切換，對所有類型顯示 -->
              <el-button
                link
                @click="toggleFullscreen">
                <component
                  :is="isFullscreen ? Minimize2 : Maximize2"
                  :size="16" />
              </el-button>
              <!-- 關閉按鈕 -->
              <el-button
                link
                @click="handleClosePreview">
                <X :size="16" />
              </el-button>
            </div>
          </div>

          <div
            class="preview-content"
            :class="{ 'is-fullscreen': isFullscreen }">
            <!-- 圖片預覽 -->
            <div
              v-if="isImageFile"
              class="image-preview">
              <img
                ref="imageRef"
                :src="data.fileUrl"
                :alt="data.fileName"
                :style="{
                  transform: `scale(${zoomLevel})`,
                  cursor: isDragging ? 'grabbing' : 'grab',
                }"
                @mousedown.stop="startDrag"
                @mousemove.stop="onDrag"
                @mouseup.stop="stopDrag"
                @mouseleave.stop="stopDrag" />
            </div>

            <!-- 影片預覽 -->
            <div
              v-else-if="isVideoFile"
              class="video-preview">
              <video
                ref="videoRef"
                :src="data.fileUrl"
                class="video-player"
                controls
                autoplay
                muted
                playsinline
                @loadedmetadata="onVideoMetadataLoaded"
                @canplay="handleVideoCanPlay">
                您的瀏覽器不支持影片播放。
              </video>
            </div>

            <!-- PDF 預覽 -->
            <div
              v-else-if="isPdfFile"
              class="pdf-preview">
              <div class="pdf-controls">
                <div class="flex items-center justify-between w-full px-4">
                  <div class="flex items-center space-x-2">
                    <div class="button-group">
                      <el-button
                        link
                        @click="handleDownloadFile">
                        <Download :size="16" />
                      </el-button>
                    </div>
                    <span class="text-sm ml-2"> 使用 PDF.js 查看器預覽 </span>
                  </div>
                </div>
              </div>
              <iframe
                :src="getPdfViewerUrl(data.fileUrl)"
                class="pdf-iframe"
                frameborder="0"
                referrerpolicy="no-referrer"
                allow="fullscreen"></iframe>
            </div>

            <!-- PPT 預覽 -->
            <div
              v-else-if="isPptFile"
              class="ppt-preview">
              <vue-office-pptx
                :key="pptKey"
                :src="data.fileUrl"
                style="height: 100vh" />
            </div>

            <!-- Word預覽 -->
            <div
              v-else-if="isWordFile"
              class="word-preview">
              <vue-office-docx
                :key="wordKey"
                :src="data.fileUrl"
                style="height: 100vh; width: 100%; overflow: visible" />
            </div>

            <!-- Excel預覽 -->
            <div
              v-else-if="isExcelFile"
              class="excel-preview">
              <vue-office-excel
                :key="excelKey"
                :src="data.fileUrl"
                style="height: 100vh" />
            </div>

            <!-- 其他檔案類型預覽 -->
            <div
              v-else
              class="generic-preview">
              <iframe
                v-if="data.fileUrl"
                :src="data.fileUrl"
                class="generic-iframe"
                frameborder="0"></iframe>
              <div
                v-else
                class="preview-error">
                <AlertTriangle
                  :size="48"
                  class="text-yellow-500 mb-2" />
                <p>無法預覽此檔案類型</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { Handle } from "@vue-flow/core";
import { useFileNode } from "@/composables/flow/useFileNode";
import {
  Eye,
  Download,
  Trash,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Minimize2,
  X,
  AlertTriangle,
  Play,
  Loader,
  Scissors,
} from "lucide-vue-next";
import { Teleport, nextTick, ref, computed, watch } from "vue";
import VueOfficePptx from "@vue-office/pptx";
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";

// 定義 props
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
});

// 用於強制重新載入的 key
const pptKey = ref(0);
const wordKey = ref(0);
const excelKey = ref(0);

// 定義 emits
const emit = defineEmits(["click", "delete", "remove"]);

// 使用 composable
const {
  previewVisible,
  zoomLevel,
  isDragging,
  imageRef,
  targetVisible,
  sourceVisible,
  isUploading,
  isFullscreen,
  currentPage,
  totalPages,
  isImage,
  isPdf,
  isPpt,
  isWord,
  isExcel,
  isPreviewable,
  showZoomControls,
  truncatedFileName,
  decodedFileName,
  getFileIcon,
  getIconColorClass,
  formatFileSize,
  handlePreview,
  handleDownload,
  handleDelete,
  handleDeleteWithNodeId,
  handleCommand,
  handleZoomIn,
  handleZoomOut,
  startDrag,
  onDrag,
  stopDrag,
  onConnect,
  isVideo,
  onVideoMetadataLoaded,
  captureVideoThumbnail,
  handleVideoCanPlay,
  getPdfViewerUrl,
} = useFileNode();

// 計算屬性
const isImageFile = computed(() => isImage.value(props.data));
const isPdfFile = computed(() => isPdf.value(props.data));
const isPptFile = computed(() => isPpt.value(props.data));
const isWordFile = computed(() => isWord.value(props.data));
const isExcelFile = computed(() => isExcel.value(props.data));
const isPreviewableFile = computed(() => isPreviewable.value(props.data));
const showZoomControlsValue = computed(() =>
  showZoomControls.value(props.data)
);
const truncatedFileNameValue = computed(() =>
  truncatedFileName.value(props.data)
);
const decodedFileNameValue = computed(() => decodedFileName.value(props.data));
const isVideoFile = computed(() => isVideo.value(props.data));

// 圖片加載狀態
const imageLoaded = ref(false);
const Loading = Loader;

// 處理圖片加載錯誤
const handleImageError = () => {
  console.warn("圖片加載失敗:", props.data.fileUrl);
  imageLoaded.value = false;

  // 嘗試延遲重新加載
  setTimeout(() => {
    console.log("圖片加載失敗:", props.data);
    imageLoaded.value = false;
    // 強制重新加載圖片
    const img = new Image();
    img.onload = () => {
      imageLoaded.value = true;
    };
    img.src = props.data.fileUrl + "?t=" + new Date().getTime();
  }, 1000);
};

// 監視 fileUrl 變化，重置圖片加載狀態
watch(
  () => props.data.fileUrl,
  () => {
    imageLoaded.value = false;
  }
);

// 處理預覽
const handlePreviewFile = () => {
  // 直接設置 previewVisible 為 true，而不是調用 handlePreview
  previewVisible.value = true;

  // 確保對話框在全屏模式下正確顯示
  nextTick(() => {
    // 強制將對話框移至 body 元素下
    const dialogContainers = document.querySelectorAll(
      ".custom-dialog-container"
    );
    dialogContainers.forEach((container) => {
      if (!document.body.contains(container)) {
        document.body.appendChild(container);
      }
      container.style.zIndex = "9999999";
    });

    // 確保對話框在最上層
    document.querySelectorAll(".custom-dialog").forEach((dialog) => {
      dialog.style.zIndex = "9999999";
    });

    // 確保遮罩層在最上層
    document.querySelectorAll(".custom-dialog-overlay").forEach((overlay) => {
      overlay.style.zIndex = "9999990";
    });
  });
};

// 處理下載
const handleDownloadFile = () => {
  handleDownload(props.data);
};

// 處理刪除
const handleDeleteFile = () => {
  handleDelete(props.data, () => {
    emit("delete", props.id);
  });
};

// 處理右鍵選單命令
const handleCommandWrapper = (command) => {
  switch (command) {
    case "preview":
      handlePreviewFile();
      break;
    case "download":
      handleDownloadFile();
      break;
    case "remove":
      // 處理移除命令
      ElMessageBox.confirm(
        "確定要從畫布移除此檔案節點嗎？此操作不會刪除實際檔案。",
        "移除確認",
        {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "info",
        }
      )
        .then(() => {
          console.log("準備移除節點:", props.id);
          // 直接調用 emit 通知父元件刪除節點，不調用 API
          emit("remove", props.id);
          console.log("已發送刪除事件:", props.id);
          ElMessage.success(`檔案節點 ${props.data.fileName} 已從畫布移除`);
        })
        .catch((error) => {
          console.log("移除操作已取消", error);
        });
      break;
    case "delete":
      handleDeleteWithNodeId(props.data, props.id);
      break;
  }
};

// 處理關閉預覽
const handleClosePreview = () => {
  previewVisible.value = false;
};

// 增強版的 toggleFullscreen 方法
const toggleFullscreen = () => {
  // 切換全屏狀態
  isFullscreen.value = !isFullscreen.value;

  // 全屏切換後確保對話框正確顯示
  nextTick(() => {
    // 強制將對話框移至 body 元素下
    const dialogContainers = document.querySelectorAll(
      ".custom-dialog-container"
    );
    dialogContainers.forEach((container) => {
      if (!document.body.contains(container)) {
        document.body.appendChild(container);
      }
      container.style.zIndex = "999999";
    });

    // 強制重新載入vue-office以適應寬度
    pptKey.value++;
    wordKey.value++;
    excelKey.value++;
  });
};

// 右鍵選單參考
const dropdownRef = ref(null);
</script>

<style scoped>
.file-node {
  @apply relative rounded-lg border border-gray-200 dark:border-node-dark bg-white dark:bg-node-dark shadow-sm flex flex-col;
  position: relative;
  width: 160px;
  height: auto;
  min-height: 180px;
  max-height: 200px;
  /* background-color: white; */
  /* border: 1px solid #e2e8f0; */
  /* border-radius: 8px; */
  padding: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
}

.file-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.file-node.is-uploading {
  opacity: 0.7;
}

.file-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.node-preview {
  width: 100%;
  height: 120px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #f8fafc; */
  /* border: 1px solid #e5e7eb; */
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.icon-wrapper {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: #f0f9ff;
  margin-bottom: 4px;
  position: relative;
  border: 1px solid #e0f2fe;
}

html.dark .icon-wrapper {
  background-color: #2b2d30;
  border-color: #4c4b50;
}

.file-icon {
  color: #0ea5e9;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  z-index: 10;
}

html.dark .upload-overlay {
  background-color: rgba(0, 0, 0, 0.8);
}

.progress-text {
  font-size: 14px;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 4px;
}

.progress-bar-container {
  width: 80%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

html.dark .progress-bar-container {
  background-color: #4c4b50;
}

.progress-bar {
  height: 100%;
  background-color: #3b82f6;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.file-info {
  width: 100%;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.file-name {
  font-weight: 500;
  margin-bottom: 2px;
  word-break: break-word;
  /* color: #1e293b; */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding: 0 0 20px 0;
}

.file-size {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 2px;
}

.file-actions .el-button {
  padding: 2px 6px;
  height: auto;
  margin: 0;
  font-size: 12px;
}

/* 移除按鈕的默認邊距 */
:deep(.el-button--small) {
  padding: 2px 6px;
  height: auto;
  margin: 0;
  font-size: 12px;
  line-height: 1.2;
}

/* 檔案預覽對話框樣式 */
.file-preview-dialog {
  z-index: 99999 !important;
}

.file-preview-dialog :deep(.el-dialog__header) {
  padding: 12px 20px;
  margin-right: 0;
  border-bottom: 1px solid #e2e8f0;
  z-index: 99999 !important;
}

.file-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  z-index: 99999 !important;
}

/* 確保對話框在全屏模式下顯示 */
:deep(.el-dialog__wrapper) {
  z-index: 99999 !important;
}

:deep(.el-overlay) {
  z-index: 99990 !important;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(80vh - 50px); /* 減去標題欄高度 */
  overflow: hidden;
}

.preview-content.is-fullscreen {
  height: calc(100vh - 50px);
}

.image-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  overflow: hidden;
}

html.dark .image-preview {
  background-color: #0f0f0f;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.2s ease;
}

.video-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  overflow: hidden;
}

html.dark .video-preview {
  background-color: #0f0f0f;
}

.video-player {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.pdf-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pdf-controls {
  padding: 8px 0;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

html.dark .pdf-controls {
  background-color: #131313;
  border-bottom: 1px solid #0f0f0f;
}

.pdf-iframe {
  flex: 1;
  width: 100%;
  border: none;
}

.generic-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
}

html.dark .generic-preview {
  background-color: #0f0f0f;
}

.generic-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b;
}

.preview-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.video-progress-container {
  width: 200px;
  margin: 0 10px;
}

.video-progress-slider :deep(.el-slider__runway) {
  margin: 8px 0;
}

.video-progress-slider :deep(.el-slider__bar) {
  background-color: #3b82f6;
}

.video-progress-slider :deep(.el-slider__button) {
  border-color: #3b82f6;
  width: 12px;
  height: 12px;
}

.video-time {
  color: #e5e7eb;
  min-width: 90px;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 45%;
  cursor: pointer !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.node-preview:hover .video-overlay {
  cursor: pointer !important;
  background-color: rgba(0, 0, 0, 0.5);
}

.play-icon {
  color: white;
  width: 48px;
  height: 48px;
  cursor: pointer;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
}

/* 自定義對話框樣式 */
.custom-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999999 !important; /* 確保比 CSS 全屏的 z-index 高 */
}

.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(46, 46, 46, 0.9);
  z-index: 9999990 !important; /* 確保比 CSS 全屏的 z-index 高 */
}

.custom-dialog {
  position: relative;
  width: 60%;
  height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999999 !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

html.dark .custom-dialog {
  background-color: #0f0f0f;
}

.custom-dialog.is-fullscreen {
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
  border-radius: 0;
}

/* 自定義對話框標題樣式 */
.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e2e8f0;
}

html.dark .custom-dialog-header {
  background-color: #131313;
  border-bottom: 1px solid #2b2b2b;
}

.custom-dialog-title {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin: 0;
}

html.dark .custom-dialog-title {
  color: #fff;
}

.custom-dialog-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-group {
  display: flex;
  /* border: 1px solid #e2e8f0; */
  border-radius: 4px;
  overflow: hidden;
}
/* 
.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button-group .custom-button {
  border: none;
  border-right: 1px solid #e2e8f0;
  border-radius: 0;
}

.button-group .custom-button:last-child {
  border-right: none;
}

.custom-button:hover {
  background-color: #f8fafc;
}

.custom-button:active {
  background-color: #e2e8f0;
} */

.word-preview {
  overflow: auto;
}
:deep(.docx-wrapper) {
  padding: 0 !important;
  margin: auto !important;
}
:deep(.docx) {
  width: 100% !important;
  max-width: 595.3pt;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  color: #64748b;
  font-size: 12px;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.context-menu-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  cursor: context-menu;
}

/* 確保右鍵選單顯示在最上層 */
:deep(.el-dropdown-menu) {
  z-index: 9999 !important;
}

:deep(.el-popper) {
  z-index: 9999 !important;
}

/* 選單按鈕樣式 */
.menu-button-wrapper {
  position: absolute;
  top: 5px;
  right: 3px;
  z-index: 20;
}

/* .menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: #f1f5f9;
} */

/* 移除右鍵選單觸發區域樣式 */
.context-menu-trigger {
  display: none;
}
</style>
