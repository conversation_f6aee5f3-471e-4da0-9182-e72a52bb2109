<template>
  <div
    class="sticky-note"
    :class="{ 'is-selected': selected, 'youtube-node': data.isYoutubeEmbed }"
    :style="{
      backgroundColor: data.color || '#6841ea',
      opacity: selected ? 1 : 0.95,
      zIndex: selected ? 1 : 0,
      padding: data.isYoutubeEmbed ? '12px' : '20px',

      transformOrigin: 'top left',
      cursor: 'move' /* 添加移動游標 */,
    }">
    <NodeResizer
      v-if="selected"
      :min-width="data.isYoutubeEmbed ? 240 : 180"
      :max-width="1200"
      :min-height="data.isYoutubeEmbed ? 160 : 80"
      :max-height="800"
      :is-visible="selected"
      :node-id="id"
      :update-node-on-resize="false"
      :line-style="{ borderColor: '#3b82f6', borderWidth: '3px', zIndex: 900 }"
      :handle-style="{
        backgroundColor: '#3b82f6',
        width: '16px',
        height: '16px',
        borderRadius: '50%',
        border: '2px solid white',
        zIndex: 1000,
        visibility: data.isYoutubeEmbed ? 'hidden' : 'visible',
      }"
      :keep-aspect-ratio="data.isYoutubeEmbed"
      @resize="onResize" />

    <!-- 編輯模式 -->
    <div
      v-if="isEditing"
      class="sticky-note-content text-editable"
      @mousedown.stop>
      <el-input
        ref="textInput"
        v-model="editingContent"
        type="textarea"
        :rows="5"
        class="sticky-note-textarea"
        :placeholder="'在此輸入便利貼內容...'"
        @blur="saveContent"
        @keydown.esc="isEditing = false"
        @keydown.ctrl.enter="saveContent"
        @mousedown.stop />
    </div>

    <!-- 顯示模式 -->
    <div
      v-else
      class="sticky-note-content"
      @dblclick="startEditing">
      <!-- YouTube嵌入內容 -->
      <div
        v-if="data.isYoutubeEmbed"
        class="youtube-embed"
        :style="{
          width: '100%',
          height: 'auto',
          overflow: 'hidden',
          padding: '0',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transform: 'translateZ(0)',
          willChange: 'transform',
        }"
        @click.stop="handleYoutubeClick"
        v-html="data.content"></div>
      <!-- 普通文本內容 -->
      <div
        v-else
        class="sticky-note-text text-selectable"
        @mousedown.stop="handleTextMouseDown"
        @click.stop
        @dblclick.stop="startEditing">
        {{ data.content || "雙擊編輯內容" }}
      </div>
    </div>

    <!-- 顏色選擇工具欄 -->
    <div
      v-if="selected && !data.isYoutubeEmbed"
      class="sticky-note-toolbar">
      <el-button-group>
        <el-button
          class="color-btn"
          style="background-color: #fef3c7"
          @click="changeColor('#fef3c7')" />
        <el-button
          class="color-btn"
          style="background-color: #dcfce7"
          @click="changeColor('#dcfce7')" />
        <el-button
          class="color-btn"
          style="background-color: #dbeafe"
          @click="changeColor('#dbeafe')" />
        <el-button
          class="color-btn"
          style="background-color: #f3e8ff"
          @click="changeColor('#f3e8ff')" />
        <el-button
          class="color-btn"
          style="background-color: #fee2e2"
          @click="changeColor('#fee2e2')" />
      </el-button-group>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from "vue";
import { useVueFlow } from "@vue-flow/core";
import { NodeResizer } from "@vue-flow/node-resizer";
import "@vue-flow/node-resizer/dist/style.css";
import { useDebounceFn } from "@vueuse/core";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
});

const { updateNode } = useVueFlow();
const isEditing = ref(false);
const editingContent = ref(props.data.content || "");
const textInput = ref(null);

// 在組件掛載時設置初始大小
onMounted(() => {
  // 如果節點數據中沒有寬度和高度，設置默認值
  if (!props.data.width || !props.data.height) {
    const defaultWidth = props.data.isYoutubeEmbed ? 560 : 250;
    const defaultHeight = props.data.isYoutubeEmbed ? "auto" : 280;

    // 更新節點數據
    updateNode(props.id, {
      data: {
        ...props.data,
        width: defaultWidth,
        height: defaultHeight,
      },
      style: {
        width: `${defaultWidth}px`,
        height:
          typeof defaultHeight === "number"
            ? `${defaultHeight}px`
            : defaultHeight,
      },
    });
  }
});

const startEditing = () => {
  // 如果是YouTube嵌入內容，不允許編輯
  if (props.data.isYoutubeEmbed) return;

  isEditing.value = true;
  editingContent.value = props.data.content || "";
  nextTick(() => {
    textInput.value?.focus();
  });
};

const saveContent = () => {
  isEditing.value = false;

  if (editingContent.value !== props.data.content) {
    // 更新節點數據
    updateNode(props.id, {
      data: {
        ...props.data,
        content: editingContent.value,
      },
    });

    // 發送自定義事件通知父組件內容已變更
    const contentChangeEvent = new CustomEvent("nodeDataChange", {
      detail: {
        id: props.id,
        data: {
          ...props.data,
          content: editingContent.value,
        },
      },
      bubbles: true, // 確保事件可以冒泡
    });

    window.dispatchEvent(contentChangeEvent);
  } else {
    console.log(`便利貼 ${props.id} 內容未變更，不觸發更新`);
  }
};

const changeColor = (color) => {
  // 更新節點數據
  updateNode(props.id, {
    data: {
      ...props.data,
      color,
    },
  });

  // 發送自定義事件通知父組件顏色已變更
  const colorChangeEvent = new CustomEvent("nodeDataChange", {
    detail: {
      id: props.id,
      data: {
        ...props.data,
        color,
      },
    },
    bubbles: true, // 確保事件可以冒泡
  });

  window.dispatchEvent(colorChangeEvent);
};

// 創建防抖版本的 dispatchResizeEvent 函數
const dispatchResizeEvent = (id, width, height) => {
  // 觸發自定義事件，通知父組件節點大小已變更
  const resizeEvent = new CustomEvent("nodeResize", {
    detail: {
      id,
      width,
      height,
    },
    bubbles: true, // 確保事件可以冒泡
  });
  window.dispatchEvent(resizeEvent);
};

const onResize = (event) => {
  try {
    // 記錄節點大小變更

    // 確保有寬度和高度
    const width = event.width || (event.params && event.params.width);
    const height = event.height || (event.params && event.params.height);

    if (!width || !height) {
      console.error("無法獲取節點新的寬度和高度", event);
      return;
    }

    // 使用 VueFlow 的 updateNode 方法更新節點樣式
    updateNode(props.id, {
      style: {
        width: `${width}px`,
        height: `${height}px`,
      },
      data: {
        ...props.data,
        width,
        height,
      },
    });
  } catch (error) {
    console.error(`便利貼 ${props.id} 處理大小變更時發生錯誤:`, error);
  }
};

const handleYoutubeClick = (event) => {
  // 如果節點處於選中狀態，就是調整大小模式
  if (props.selected) {
    // 選中狀態下，阻止默認行為，允許用戶直接拖動調整大小
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  // 非選中狀態下，打開 YouTube 視頻
  if (props.data.youtube && props.data.youtube.videoUrl) {
    event.preventDefault();
    event.stopPropagation();
    window.open(props.data.youtube.videoUrl, "_blank");
  }
};

// 添加新的函數處理文字區域的鼠標事件
const handleTextMouseDown = (event) => {
  // 阻止事件冒泡到畫布，避免拖動
  event.stopPropagation();

  // 創建臨時的事件處理器
  const handleWindowMove = (moveEvent) => {
    // 阻止事件冒泡，防止節點被拖動
    moveEvent.stopPropagation();
  };

  const handleWindowUp = () => {
    // 清理事件處理器
    window.removeEventListener("mousemove", handleWindowMove, true);
    window.removeEventListener("mouseup", handleWindowUp);
  };

  // 在捕獲階段添加事件監聽器，這樣我們可以在事件到達 VueFlow 組件之前先處理它
  window.addEventListener("mousemove", handleWindowMove, true);
  window.addEventListener("mouseup", handleWindowUp);
};
</script>

<style>
.vue-flow__node[data-type="sticky"] {
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  min-width: 150px;
  width: auto;
  box-shadow: none;
  overflow: visible !important; /* 確保 NodeResizer 控制點可見 */
  transform: translateZ(0); /* 啟用硬體加速 */
  will-change: transform; /* 提示瀏覽器此元素將會變化 */
}
</style>

<style scoped>
.sticky-note {
  @apply p-3 rounded-lg shadow-md transition-all duration-200;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible !important; /* 確保 NodeResizer 控制點可見，但內容不溢出 */
  display: flex;
  flex-direction: column;
  transform: translateZ(0); /* 啟用硬體加速 */
  will-change: transform; /* 提示瀏覽器此元素將會變化 */
  touch-action: none; /* 防止觸控設備上的默認行為 */
}

.sticky-note.is-selected {
  @apply shadow-lg ring-2 ring-blue-500 ring-opacity-50;
  z-index: 10; /* 確保選中的便利貼在最上層 */
}

.sticky-note-content {
  @apply w-full h-full;
  overflow: hidden; /* 改為 hidden，移除滾動條 */
}

.sticky-note-text {
  @apply text-gray-700 whitespace-pre-wrap break-words;
  padding: 0.5rem 0;
  height: 100%;
  overflow: hidden; /* 改為 hidden，移除滾動條 */
  user-select: text !important; /* 強制確保文本可以被選擇 */
  cursor: text !important; /* 強制在文字上顯示文本選擇游標 */
  pointer-events: auto; /* 確保可以接收鼠標事件 */
}

.sticky-note-textarea {
  @apply bg-transparent border-none shadow-none w-full h-full;
}

.sticky-note-toolbar {
  @apply absolute -bottom-10 left-1/2 transform -translate-x-1/2 bg-white rounded-md shadow-md p-1;
}

.color-btn {
  @apply w-6 h-6 p-0 border border-gray-200;
}

:deep(.el-textarea__inner) {
  @apply bg-transparent border-none shadow-none resize-none;
  height: 100% !important;
}

:deep(.vue-flow__resize-control.line) {
  border: 2px solid #3b82f6 !important;
  opacity: 0.8;
  z-index: 900 !important;
}

:deep(.vue-flow__resize-control.handle) {
  background-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px white !important;
  opacity: 1;
  z-index: 1000 !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  border: 2px solid white !important;
}

/* YouTube嵌入樣式 */
.youtube-embed {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  padding: 0;
  transform: translateZ(0);
  will-change: transform;
  z-index: 100;
}

/* 添加一個覆蓋層，用於捕獲鼠標事件 */
.youtube-embed::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 101;
  cursor: pointer;
  /* 關鍵修改：只在 resize 時攔截事件，其他時候允許事件穿透 */
  pointer-events: none;
}

/* 當元素被選中時（正在調整大小），激活覆蓋層捕獲事件 */
.is-selected .youtube-embed::before {
  pointer-events: none; /* auto; auto 才會攔截事件 */
}

.youtube-embed :deep(iframe) {
  width: 100% !important;
  height: 100% !important;
  aspect-ratio: 16/9;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 100%;
  /* 允許點擊事件 */
  pointer-events: auto;
}

/* 新增 YouTube 節點特定樣式 */
.youtube-node {
  overflow: hidden !important; /* 確保內容不溢出 */
  /* height: auto !important; 高度自動調整 */
  transform: translateZ(0); /* 啟用硬體加速 */
  will-change: transform; /* 提示瀏覽器此元素將會變化 */
  backface-visibility: hidden; /* 優化渲染 */
  -webkit-backface-visibility: hidden; /* Safari 支援 */
  -webkit-transform-style: preserve-3d; /* Safari 支援 */
  transform-style: preserve-3d; /* 優化渲染 */
}

.youtube-node .sticky-note-content {
  padding: 5px 4px 10px 4px;
  margin: 0;
  width: 100%;
  height: 100% !important; /* 高度自動調整 */
  pointer-events: auto; /* 確保可以接收鼠標事件 */
}

/* 可選擇文字的容器 */
.text-selectable {
  /* 可選擇文字 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;

  /* 阻止拖動 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;

  /* 文字游標 */
  cursor: text !important;
}

/* 編輯模式的樣式 */
.text-editable {
  position: relative;
  z-index: 1000;
  pointer-events: auto !important;
}

.text-editable :deep(.el-textarea__inner) {
  @apply bg-transparent border-none shadow-none resize-none;
  height: 100% !important;
  cursor: text !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;

  /* 阻止拖動 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
}

.sticky-note.is-selected.youtube-node {
  @apply shadow-lg ring-2 ring-blue-500 ring-opacity-70;
  z-index: 20; /* 選中時提高 z-index */
  /* 添加特殊的邊框線來代替控制點 */
  outline: 3px dashed rgba(59, 130, 246, 0.7);
  outline-offset: 4px;
  /* 自定義游標 */
  cursor: nwse-resize !important;
}

/* 選中狀態下的 iframe 樣式 */
.is-selected .youtube-embed :deep(iframe) {
  pointer-events: none !important; /* 選中時禁用 iframe 事件，使調整大小更容易 */
  opacity: 0.9 !important; /* 輕微降低透明度作為視覺提示 */
}
</style>
