<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="ai-process"
    :title="title"
    :description="description"
    icon="Bot"
    :selected="selected"
    header-bg-color="#E6F7FF"
    :handles="handles"
    :disabled="disabled"
    :node-width="600"
    :node-height="500"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <!-- 主要內容區域 -->
    <div class="p-4 space-y-4">
      <!-- 參數設置區域 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-700 mb-3">AI 模擬參數設置</h3>
        <div class="space-y-3">
          <el-form
            label-position="top"
            size="small">
            <el-form-item label="輸出文本">
              <el-input
                v-model="aiPrompt"
                type="textarea"
                :rows="3"
                placeholder="請輸入要模擬的 AI 輸出文本"
                size="small" />
            </el-form-item>
            <el-form-item label="輸出速度">
              <el-slider
                v-model="typeSpeed"
                :min="10"
                :max="300"
                :step="10"
                :format-tooltip="(val) => `${val} ms/字元`"
                show-input />
            </el-form-item>
            <el-form-item label="思考延遲">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-input-number
                    v-model="thinkingDelay"
                    :min="0"
                    :max="10000"
                    :step="100"
                    placeholder="思考時間 (ms)"
                    size="small"
                    style="width: 100%" />
                </el-col>
                <el-col :span="12">
                  <el-switch
                    v-model="showThinking"
                    active-text="顯示思考指示器"
                    inactive-text="不顯示" />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item label="重複輸出">
              <el-switch
                v-model="loopOutput"
                active-text="啟用循環"
                inactive-text="單次輸出" />
            </el-form-item>
            <el-form-item
              label="循環間隔"
              v-if="loopOutput">
              <el-input-number
                v-model="loopInterval"
                :min="500"
                :max="10000"
                :step="500"
                placeholder="間隔時間 (ms)"
                size="small"
                style="width: 100%" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- AI 輸出顯示區域 -->
      <div class="bg-blue-50 p-4 rounded-lg ai-output-container">
        <h3 class="text-sm font-medium text-gray-700 mb-3">AI 輸出</h3>
        <div class="ai-output">
          <div
            v-if="isThinking"
            class="thinking-indicator">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
          <div
            v-else
            class="text-md text-gray-800 font-mono whitespace-pre-wrap">
            {{ displayedText }}
          </div>
        </div>
      </div>

      <!-- 執行按鈕 -->
      <div class="flex justify-end space-x-2">
        <el-button
          v-if="isRunning"
          type="danger"
          @click="stopSimulation"
          :disabled="!isRunning">
          停止輸出
        </el-button>
        <el-button
          type="primary"
          @click="handleRun"
          :loading="executing"
          :disabled="!aiPrompt.trim()">
          開始 AI 模擬
        </el-button>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { logger } from "@/utils/logger";
import { ElMessage } from "element-plus";
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "AI 訊息處理(純好玩的)",
  },
  description: {
    type: String,
    default: "模擬 AI 生成文本的打字效果，可自定義輸出速度和思考延遲",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// 節點狀態
const nodeRef = ref(null);
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// AI 模擬參數
const aiPrompt = ref(
  `系統整合前兩個並行節點的輸出，生成全面的健康風險評估：

多因素風險整合：

結合生物醫學風險和行為風險因素
計算加權健康風險總分
生成健康年齡估計（生物學年齡vs.實際年齡）
風險因素優先排序：

識別影響健康最顯著的前3-5個風險因素
分析這些因素的可修改程度
評估改變這些因素可能帶來的健康收益
個人化健康改善建議：

生成具體、可行的生活方式改變建議
為每項建議提供科學依據和預期效果
根據患者的行為改變準備階段調整建議的實施步驟
健康監測計劃：

建議定期檢測的健康指標和頻率
設定關鍵健康指標的目標值
提供自我監測工具和方法建議
輸出：綜合健康風險評估報告和個人化健康改善計劃，包含短期（3個月）、中期（6個月）和長期（1年）目標。`
);
const typeSpeed = ref(50); // 每個字元的輸出延遲（毫秒）
const thinkingDelay = ref(1000); // 思考延遲（毫秒）
const showThinking = ref(true); // 是否顯示思考指示器
const loopOutput = ref(false); // 是否循環輸出
const loopInterval = ref(2000); // 循環間隔（毫秒）

// 模擬狀態
const isRunning = ref(false); // 是否正在運行
const isThinking = ref(false); // 是否正在"思考"
const displayedText = ref(""); // 當前顯示的文本
let typeTimer = null; // 打字計時器
let thinkTimer = null; // 思考計時器
let loopTimer = null; // 循環計時器
let currentCharIndex = 0; // 當前字元索引

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "ai-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  NODE_STATES,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 事件處理
const emit = defineEmits(["handle-connect", "handle-disconnect"]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 開始 AI 模擬
const startSimulation = () => {
  // 重置狀態
  isRunning.value = true;
  isThinking.value = showThinking.value;
  displayedText.value = "";
  currentCharIndex = 0;

  // 清除可能存在的計時器
  clearAllTimers();

  // 如果設置了思考延遲，先顯示思考指示器
  if (showThinking.value && thinkingDelay.value > 0) {
    thinkTimer = setTimeout(() => {
      isThinking.value = false;
      startTyping();
    }, thinkingDelay.value);
  } else {
    // 否則直接開始打字
    isThinking.value = false;
    startTyping();
  }

  // 更新節點狀態
  updateNodeStatus(NODE_STATES.RUNNING);
};

// 開始打字效果
const startTyping = () => {
  typeTimer = setInterval(() => {
    if (currentCharIndex < aiPrompt.value.length) {
      // 逐字添加
      displayedText.value = aiPrompt.value.substring(0, currentCharIndex + 1);
      currentCharIndex++;
    } else {
      // 輸出完成
      clearInterval(typeTimer);

      // 如果啟用了循環輸出，設置下一次循環的計時器
      if (loopOutput.value) {
        loopTimer = setTimeout(() => {
          // 重置並重新開始
          displayedText.value = "";
          currentCharIndex = 0;
          isThinking.value = showThinking.value;

          if (showThinking.value && thinkingDelay.value > 0) {
            thinkTimer = setTimeout(() => {
              isThinking.value = false;
              startTyping();
            }, thinkingDelay.value);
          } else {
            startTyping();
          }
        }, loopInterval.value);
      } else {
        // 輸出數據
        const outputResult = {
          text: aiPrompt.value,
          simulationConfig: {
            typeSpeed: typeSpeed.value,
            thinkingDelay: thinkingDelay.value,
            showThinking: showThinking.value,
            loopOutput: loopOutput.value,
            loopInterval: loopInterval.value,
          },
        };

        nodeContext.value = {
          ...nodeContext.value,
          output: outputResult,
        };

        // 單次輸出完成，更新節點狀態
        isRunning.value = false;
        updateNodeStatus(NODE_STATES.COMPLETED, outputResult);
      }
    }
  }, typeSpeed.value);
};

// 停止模擬
const stopSimulation = () => {
  clearAllTimers();
  isRunning.value = false;
  isThinking.value = false;
  updateNodeStatus(NODE_STATES.COMPLETED);
};

// 清除所有計時器
const clearAllTimers = () => {
  if (typeTimer) clearInterval(typeTimer);
  if (thinkTimer) clearTimeout(thinkTimer);
  if (loopTimer) clearTimeout(loopTimer);

  typeTimer = null;
  thinkTimer = null;
  loopTimer = null;
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `AI模擬節點 handleRun 被調用`);

  if (isRunning.value) {
    stopSimulation();
    return;
  }

  try {
    const processFunction = async () => {
      startSimulation();
      return new Promise((resolve) => {
        const checkCompletion = setInterval(() => {
          if (!isRunning.value) {
            clearInterval(checkCompletion);
            resolve(nodeContext.value.output);
          }
        }, 100);
      });
    };

    const result = await executeNode(context, processFunction);
    return result;
  } catch (error) {
    logger.error(props.title, "AI模擬執行失敗:", error);
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 組件卸載前清除計時器
onBeforeUnmount(() => {
  clearAllTimers();
});

// 檢查是否有之前的分析結果
onMounted(async () => {
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };
    displayedText.value = previousData.detail.text || "";
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.ai-output-container {
  min-height: 150px;
  transition: all 0.3s ease;
}

.ai-output {
  min-height: 100px;
  position: relative;
}

.thinking-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 24px;
  gap: 4px;
}

.dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #1890ff;
  animation: pulse 1.2s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
