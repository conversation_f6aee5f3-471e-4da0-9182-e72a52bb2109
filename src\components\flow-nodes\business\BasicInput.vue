<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 主要內容區域 -->
    <div class="p-4 space-y-4">
      <div :class="panelClasses">
        <h3
          :class="[
            'text-base font-bold mb-3',
            isDark ? 'text-white' : 'text-gray-700',
          ]">
          基本輸入
        </h3>

        <!-- 品目選擇 -->
        <el-form-item
          label="品目"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-input
              v-model="nodeData_value.partNo"
              placeholder="請輸入品目代碼"
              clearable
              class="w-full"
              :disabled="true">
            </el-input>
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              {{ nodeData_value.partNo || "未設定" }}
            </div>
          </template>
        </el-form-item>

        <!-- 起始日期 -->
        <el-form-item
          label="起始日期"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-date-picker
              v-model="nodeData_value.startDate"
              type="date"
              placeholder="選擇起始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disableFutureDate"
              class="w-full"
              :disabled="executing"
              @change="handleStartDateChange" />
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              {{ nodeData_value.startDate || "未設定" }}
            </div>
          </template>
        </el-form-item>

        <!-- 結束日期 -->
        <el-form-item
          label="結束日期"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-date-picker
              v-model="nodeData_value.endDate"
              type="date"
              placeholder="選擇結束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disableEndDate"
              class="w-full"
              :disabled="executing" />
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              {{ nodeData_value.endDate || "未設定" }}
            </div>
          </template>
        </el-form-item>

        <!-- 工單選擇 -->
        <el-form-item label="工單">
          <template v-if="!isReportMode">
            <el-select
              v-model="nodeData_value.lot"
              placeholder="請選擇工單"
              clearable
              filterable
              multiple
              class="w-full"
              :loading="nodeData_value.loadingLots"
              :disabled="executing">
              <el-option
                v-for="lot in nodeData_value.lotOptions"
                :key="lot"
                :label="lot"
                :value="lot" />
            </el-select>
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              <template v-if="nodeData_value.lot && nodeData_value.lot.length">
                {{ nodeData_value.lot.join(", ") }}
              </template>
              <template v-else> 未選擇工單 </template>
            </div>
          </template>
        </el-form-item>

        <!-- 製程選擇 -->
        <el-form-item
          label="製程"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-select
              v-model="nodeData_value.selectedProcesses"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="請選擇製程"
              clearable
              filterable
              class="w-full"
              :loading="nodeData_value.loadingProcesses"
              :disabled="executing"
              @change="handleProcessChange">
              <el-option
                v-for="process in nodeData_value.processOptions"
                :key="process"
                :label="process"
                :value="process" />
            </el-select>
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              <template
                v-if="
                  nodeData_value.selectedProcesses &&
                  nodeData_value.selectedProcesses.length > 0
                ">
                {{ nodeData_value.selectedProcesses.join(", ") }}
              </template>
              <template v-else> 未設定 </template>
            </div>
          </template>
        </el-form-item>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, computed, onMounted, watch } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "基本輸入(品目/起訖日/製程)",
  },
  description: {
    type: String,
    default: "用於輸入品目、起始日期、結束日期、製程四項基礎資料",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-input");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  partNo: "QE2825", // 預設為 PBC 專用品目
  // startDate: new Date(new Date().setDate(new Date().getDate() - 30))
  //   .toISOString()
  //   .split("T")[0], // 預設為30天前
  // endDate: new Date().toISOString().split("T")[0], // 預設為今天
  startDate: "2024-07-01", // TODO DEMO用! 未來要刪除
  endDate: "2024-11-01", // TODO DEMO用! 未來要刪除
  lot: [], // 工單選擇
  lotOptions: [],
  loadingLots: false,
  selectedProcesses: [],
  processOptions: [],
  loadingProcesses: false,
};

// 節點數據結構
const nodeData_value = ref({
  partNo: nodeData_default.partNo,
  startDate: nodeData_default.startDate,
  endDate: nodeData_default.endDate,
  lot: [...nodeData_default.lot],
  lotOptions: [...nodeData_default.lotOptions],
  loadingLots: nodeData_default.loadingLots,
  selectedProcesses: [...nodeData_default.selectedProcesses],
  processOptions: [...nodeData_default.processOptions],
  loadingProcesses: nodeData_default.loadingProcesses,
});

// 提取共用的面板樣式
const panelClasses = computed(() => [
  "p-4 rounded-lg",
  isDark.value ? "bg-dark-secondary" : "bg-blue-50",
]);

// 提取共用的唯讀顯示樣式
const readOnlyDisplayClasses = computed(() => [
  "py-2 px-3 rounded text-sm",
  isDark.value ? "bg-gray-700 text-gray-100" : "bg-gray-50 text-gray-900",
]);

// 日期處理相關函數(禁用未來日期)
const disableFutureDate = (date) => {
  return date > new Date();
};

// 禁用結束日期
const disableEndDate = (date) => {
  if (!nodeData_value.value.startDate) return false;
  return date < nodeData_value.value.startDate || date > new Date();
};

// 處理起始日期變更
const handleStartDateChange = (date) => {
  if (
    date &&
    nodeData_value.value.endDate &&
    date > nodeData_value.value.endDate
  ) {
    ElMessage.warning("結束日期不可早於起始日期");
    nodeData_value.value.endDate = null;
  }
};

// 獲取工單列表
const fetchLotList = async () => {
  if (props.isReportMode) return; // 跳過報表模式

  // 清空工單選項
  nodeData_value.value.lotOptions = [];
  nodeData_value.value.lot = [];

  if (!nodeData_value.value.partNo) {
    return;
  }

  try {
    nodeData_value.value.loadingLots = true;

    const requestVariables = {
      part_no: nodeData_value.value.partNo,
    };
    if (nodeData_value.value.startDate && nodeData_value.value.endDate) {
      requestVariables.start_date = nodeData_value.value.startDate;
      requestVariables.end_date = nodeData_value.value.endDate;
    }

    const response = await request.post(
      "/external/iym/main_method/lot_list_by_partno",
      requestVariables
    );
    nodeData_value.value.lotOptions = response;

    // 檢查選取的工單是否仍然存在於新的選項中
    if (
      nodeData_value.value.lot &&
      !nodeData_value.value.lot.every((lot) =>
        nodeData_value.value.lotOptions.includes(lot)
      )
    ) {
      nodeData_value.value.lot = nodeData_value.value.lot.filter((lot) =>
        nodeData_value.value.lotOptions.includes(lot)
      );
    }
  } catch (error) {
    console.error("獲取工單列表失敗:", error);
    if (error.response?.status === 401) {
      ElMessage.error("請先登入");
    } else if (error.response?.status === 403) {
      ElMessage.error("沒有權限執行此操作");
    } else {
      ElMessage.error(
        "獲取工單列表失敗: " + (error.response?.data?.detail || error.message)
      );
    }
    nodeData_value.value.lotOptions = [];
  } finally {
    nodeData_value.value.loadingLots = false;
  }
};

// 獲取製程列表
const fetchProcessList = async () => {
  printLog("DO fetchProcessList!");

  // 清空製程選項
  nodeData_value.value.processOptions = [];
  nodeData_value.value.selectedProcesses = [];

  if (
    !nodeData_value.value.partNo ||
    !nodeData_value.value.startDate ||
    !nodeData_value.value.endDate
  ) {
    return;
  }

  try {
    nodeData_value.value.loadingProcesses = true;

    const requestData = {
      part_no: nodeData_value.value.partNo,
      start_date: nodeData_value.value.startDate,
      end_date: nodeData_value.value.endDate,
    };

    // 如果有選擇工單，則加入工單參數
    if (nodeData_value.value.lot && nodeData_value.value.lot.length > 0) {
      requestData.workorders = nodeData_value.value.lot;
    }

    const response = await request.post(
      "/external/iym/main_method/process_name_list",
      requestData
    );
    nodeData_value.value.processOptions = response;

    // TODO DEMO用! 未來要刪除
    nodeData_value.value.selectedProcesses = [
      "(R233-01)RTR蝕刻-剝膜",
      "(R223-S01)RTR曝光",
      "(R232-02)RTR局部銅電剝膜",
      "(R221-01)RTR 乾膜前處理",
      "(R233-03)RTR線路顯影",
      "(R212-S01)RTR VCP全局面鍍銅",
      "(R220-S01)RTR乾膜壓合",
    ];

    // 檢查選取的製程是否仍然存在於新的選項中
    if (nodeData_value.value.selectedProcesses.length > 0) {
      // 過濾掉不再存在於新選項中的製程
      nodeData_value.value.selectedProcesses =
        nodeData_value.value.selectedProcesses.filter((process) =>
          nodeData_value.value.processOptions.includes(process)
        );
      printLog(
        `保留 ${nodeData_value.value.selectedProcesses.length} 個已選製程`
      );
    }
  } catch (error) {
    console.error("獲取製程列表失敗:", error);
    if (error.response?.status === 401) {
      ElMessage.error("請先登入");
    } else if (error.response?.status === 403) {
      ElMessage.error("沒有權限執行此操作");
    } else {
      ElMessage.error(
        "獲取製程列表失敗: " + (error.response?.data?.detail || error.message)
      );
    }
  } finally {
    nodeData_value.value.loadingProcesses = false;
  }
};

// 處理製程選擇變更
const handleProcessChange = (value) => {
  printLog("選擇的製程:", value);
};

const processFunction = async (inputData) => {
  printLog("開始執行節點!");

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    "請填寫品目代碼",
    !nodeData_value.value.partNo
  );

  validateRequiredFields(
    props.title,
    "請選擇起始日期",
    !nodeData_value.value.startDate
  );

  validateRequiredFields(
    props.title,
    "請選擇結束日期",
    !nodeData_value.value.endDate
  );

  validateRequiredFields(
    props.title,
    "請選擇至少一個製程",
    nodeData_value.value.selectedProcesses.length === 0
  );

  // 構建結果對象
  const result = {
    partNo: nodeData_value.value.partNo,
    startDate: nodeData_value.value.startDate,
    endDate: nodeData_value.value.endDate,
    lot: nodeData_value.value.lot,
    lotOptions: nodeData_value.value.lotOptions,
    processes: nodeData_value.value.selectedProcesses,
    processOptions: nodeData_value.value.processOptions,
    timestamp: new Date().toISOString(),
  };
  printLog("處理結果:", result);

  // 設置全域變數
  await updateGlobalVariable("resourceType", "PBC"); // 資料來源(input額外必需置入)
  await updateGlobalVariable("partNo", result.partNo);
  await updateGlobalVariable("startDate", result.startDate);
  await updateGlobalVariable("endDate", result.endDate);
  await updateGlobalVariable("lot", result.lot);
  await updateGlobalVariable("processes", result.processes);

  return result;
};

// 監聽節點數據變化，自動觸發相關操作
watch(
  () => [
    nodeData_value.value.partNo,
    nodeData_value.value.startDate,
    nodeData_value.value.endDate,
  ],
  async (
    [newPartNo, newStartDate, newEndDate],
    [oldPartNo, oldStartDate, oldEndDate]
  ) => {
    await fetchLotList();
    await fetchProcessList();
  },
  { immediate: false }
);

// 監聽工單變化，重新獲取製程列表
watch(
  () => nodeData_value.value.lot,
  async (newLot, oldLot) => {
    await fetchProcessList();
  },
  { deep: true, immediate: false }
);

// 初始化處理
onMounted(async () => {
  printLog("DO onMounted!");

  // 嘗試從共享數據中獲取之前的輸入數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData:", previousData);
  if (previousData?.detail) {
    printLog("載入之前的數據:", previousData);
    nodeData_value.value.partNo =
      previousData.detail.partNo || nodeData_default.partNo;
    nodeData_value.value.startDate =
      previousData.detail.startDate || nodeData_default.startDate;
    nodeData_value.value.endDate =
      previousData.detail.endDate || nodeData_default.endDate;
    nodeData_value.value.lot = previousData.detail.lot || [
      ...nodeData_default.lot,
    ];
    nodeData_value.value.lotOptions = previousData.detail.lotOptions || [
      ...nodeData_default.lotOptions,
    ];
    nodeData_value.value.selectedProcesses = previousData.detail.processes || [
      ...nodeData_default.selectedProcesses,
    ];
    nodeData_value.value.processOptions = previousData.detail
      .processOptions || [...nodeData_default.processOptions];
  }

  // 獲取工單和製程列表
  await fetchLotList();
  await fetchProcessList();
});
// ==============END 節點自定義區==============

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped>
.iym-input {
  width: 100%;
}

.iym-input__info {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.dark .iym-input__info {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

:deep(.el-date-editor .el-range-input) {
  width: 80px;
}

:deep(.el-select),
:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 1rem;
}
</style>
