<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :help-content="helpContent"
    :icon="BarChart2"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">卡方圖分析</h3>
        <el-form
          label-position="top"
          size="small">
          <el-form-item label="不良原因">
            <el-select
              v-model="formData.defectCode"
              placeholder="請選擇不良原因"
              class="w-full">
              <el-option
                v-for="code in formData.defectCodeOptions"
                :key="code"
                :label="code"
                :value="code" />
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="重要性排名數量">
            <el-input-number
              v-model="formData.displayCount"
              :min="3"
              :max="10"
              :step="1"
              class="w-full" />
          </el-form-item> -->

          <!-- <el-form-item label="重要性排名數量">
            <el-slider
              v-model="formData.quantity"
              :min="0"
              :max="100"
              :step="5"
              show-input />
          </el-form-item> -->
        </el-form>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="chartReady && nodeContext && nodeContext.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 卡方分析圖表 -->
          <div
            v-for="(processData, processIndex) in groupedChartData"
            :key="processIndex"
            class="mb-6">
            <div
              v-for="(chart, chartIndex) in processData.charts"
              :key="chartIndex"
              class="mb-4">
              <Chart
                width="auto"
                height="280px"
                :options="getChartOption(chart, processData.processName)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { BarChart2 } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";
import Chart from "@/components/Chart.vue";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "卡方圖分析(不良原因)",
  },
  description: {
    type: String,
    default: "根據選擇的不良原因，進行卡方分析，找出影響不良的關鍵因素",
  },

  helpContent: {
    type: String,
    default: `卡方圖分析（Chi-Square Test）是一種統計方法，用於檢驗觀察數據與期望數據之間的差異。它主要應用於以下情況：

### 主要用途

1. **獨立性檢驗**：檢查兩個分類變數之間是否存在關聯。例如，檢查性別和購買偏好是否相關。

2. **適合度檢驗**：檢查觀察到的頻率分布是否符合某個理論分布。例如，檢查骰子是否公平。

### 計算方法

卡方檢驗基於以下公式：

\[
\chi^2 = \sum \frac{(O_i - E_i)^2}{E_i}
\]

- \(O_i\) 是觀察值。
- \(E_i\) 是期望值。

### 使用步驟

1. **定義假設**：
   - 零假設 (\(H_0\))：變數之間沒有關聯或觀察值符合期望分布。
   - 替代假設 (\(H_1\))：變數之間有關聯或觀察值不符合期望分布。

2. **計算期望值**：根據假設計算每個類別的期望頻率。

3. **計算卡方統計量**：使用上述公式計算。

4. **決策**：根據卡方統計量和臨界值（通常從卡方分布表中查找），判斷是否拒絕零假設。

### 應用

卡方檢驗在市場研究、社會科學、醫學研究等領域廣泛應用，幫助分析分類數據的關聯性和適合度。

這種方法可以提供對數據關聯性的直觀理解，並幫助做出統計上的推斷。`,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點狀態
const nodeRef = ref(null);
const chartReady = ref(false);

// 初始化 nodeContext，提供默認值避免 undefined 錯誤
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  defectCodeOptions: [],
  defectCode: "",
});

// 計算是否可以分析
const canAnalyze = computed(() => {
  return formData.value.defectCode !== "";
});

// 將API返回的數據轉換為圖表數據
const groupedChartData = computed(() => {
  logger.debug(props.title, "DO groupedChartData:", nodeContext.value);
  if (
    !nodeContext.value?.output?.result ||
    !Array.isArray(nodeContext.value.output?.result)
  ) {
    return [];
  }

  // 按製程分組
  const groupedByProcess = {};

  nodeContext.value.output.result.forEach((item) => {
    const processName = item.process_name;
    const step = item.Step;

    if (!groupedByProcess[processName]) {
      groupedByProcess[processName] = {
        processName,
        charts: {},
      };
    }

    if (!groupedByProcess[processName].charts[step]) {
      groupedByProcess[processName].charts[step] = {
        step,
        data: [],
      };
    }

    groupedByProcess[processName].charts[step].data.push(item);
  });

  // 轉換為數組格式
  const result = Object.values(groupedByProcess).map((process) => {
    return {
      processName: process.processName,
      charts: Object.values(process.charts),
    };
  });

  return result;
});

// 獲取圖表選項
const getChartOption = (chartData, processName) => {
  if (!chartData || !chartData.data || !chartData.data.length) {
    return {};
  }

  // 排序數據，根據P-Value由高到低
  const sortedData = [...chartData.data].sort(
    (a, b) => b["P-Value"] - a["P-Value"]
  );

  // 提取x軸數據（變數名稱）
  const xAxis = sortedData.map((d) => d.Variable);

  // 計算顯示數值 (1 - P-Value)
  const seriesData = sortedData.map((d) => {
    return Math.round((1 - d["P-Value"]) * 1000) / 1000;
  });

  // 計算y軸標籤最大長度，用於設置左邊距
  const xAxisLength =
    Math.max(
      ...xAxis.map((str) => {
        let length = 0;
        for (let i = 0; i < str.length; i++) {
          // 判斷是否為中文字元
          if (/[\u4e00-\u9fa5]/.test(str[i])) {
            length += 2; // 中文字元算 2
          } else {
            length += 1; // 非中文字元算 1
          }
        }
        return length;
      })
    ) * 7;

  return {
    title: {
      text: processName,
    },
    tooltip: {
      show: true,
      formatter: function (params) {
        const dataIndex = params.dataIndex;
        const item = sortedData[dataIndex];
        return `變數: ${item.Variable}<br/>
                卡方值: ${item.Chi2.toFixed(4)}<br/>
                P值: ${item["P-Value"].toFixed(4)}<br/>
                重要性: ${params.value} (1-P值)<br/>
                自由度: ${item.DOF}`;
      },
    },
    xAxis: {
      type: "value",
      min: 0,
      max: 1,
      axisLabel: {
        formatter: "{value}",
      },
    },
    yAxis: {
      type: "category",
      data: xAxis,
      axisLabel: {
        formatter: function (value) {
          // 如果標籤太長，截斷顯示
          if (value.length > 15) {
            return value.substring(0, 12) + "...";
          }
          return value;
        },
      },
    },
    toolbox: {
      feature: {
        saveAsImage: {},
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        data: seriesData,
        type: "bar",
        markLine: {
          data: [
            {
              name: "界線值",
              xAxis: 0.95, // 設置指標線的位置
            },
          ],
          lineStyle: {
            color: "red", // 指標線顏色
            width: 2, // 指標線寬度
            type: "dashed", // 設置為虛線
          },
          symbol: ["none", "none"], // 去掉箭頭
        },
      },
    ],
    grid: {
      left: xAxisLength,
      right: 30,
      bottom: 30,
      top: 50,
    },
  };
};

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "statistic-process",
  nodeName: props.title,
  nodeRef: nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError, // 不需要重命名
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `卡方分析節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 由先前節點獲取參數 - defectCodeOptions
  formData.value.defectCodeOptions =
    globalVariables?.defectCodeOptions ||
    context.sourceNodeOutput?.result.map((r) => r["不良大項代碼"]) ||
    [];
  if (formData.value.defectCodeOptions.length < 1) {
    throw new Error("取得不良原因選項失敗，請檢查全域變數或前一個節點的輸出");
  }
  await updateGlobalVariable(
    "defectCodeOptions",
    formData.value.defectCodeOptions
  );

  // 由先前節點獲取參數 - defectCode
  formData.value.defectCode =
    context.sourceNodeOutput?.defectCode || globalVariables?.defectCode || "";
  // TODO DEMO預設S03(當前只有限定條件能查獲資料)! 未來要刪除，等候「餐與者」設置好，由user選取
  if (formData.value.defectCode === "") {
    logger.warn(props.title, "未選擇不良原因，使用預設值 S03");
    formData.value.defectCode = "S03";
  }
  if (formData.value.defectCode === "") {
    throw new Error("請選擇不良原因");
  }
  logger.debug(props.title, "選擇的不良原因:", formData.value.defectCode);
  await updateGlobalVariable("defectCode", formData.value.defectCode);

  // 由先前節點獲取參數 - other
  const partNo =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  const startDate =
    context.sourceNodeOutput?.startDate || globalVariables?.startDate || "";
  const endDate =
    context.sourceNodeOutput?.endDate || globalVariables?.endDate || "";
  const processes =
    context.sourceNodeOutput?.processes || globalVariables?.processes || [];
  const defectCode = formData.value.defectCode;

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: partNo,
    開始日期: startDate,
    結束日期: endDate,
    製程: processes.length > 0,
    不良原因: defectCode,
  };

  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingParams.length > 0) {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行輸入節點並選擇不良原因`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  try {
    // 準備處理函數
    const processFunction = async (inputData) => {
      // 使用所有製程進行分析
      const processName = processes.join(",");

      const result = await request.post(
        "/external/iym/test_statistics/chi_square",
        {
          part_no: partNo,
          start_date: startDate,
          end_date: endDate,
          defect_code: defectCode,
          process_name: processName,
        }
      );

      if (!Array.isArray(result) || result.length === 0) {
        throw new Error("卡方分析未返回有效數據");
      }

      // 構建完整的結果對象
      const completeResult = {
        result: result,
        defectCode: formData.value.defectCode,
        defectCodeOptions: formData.value.defectCodeOptions,
        partNo,
        startDate,
        endDate,
        processName,
        timestamp: new Date().toISOString(),
      };

      // 更新圖表數據
      nodeContext.value = {
        ...nodeContext.value,
        output: completeResult,
      };

      return completeResult;
    };

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);

    // 確保圖表更新
    chartReady.value = false;
    await nextTick();
    chartReady.value = true;

    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    formData.value.defectCode = previousData.detail.defectCode || "";
    formData.value.defectCodeOptions =
      previousData.detail.defectCodeOptions || [];

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };

    // 設置圖表準備好顯示
    chartReady.value = true;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 600px;
  overflow-y: auto;
}
</style>
