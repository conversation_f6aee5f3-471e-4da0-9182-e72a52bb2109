<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 參數選擇 -->
    <div class="p-2 rounded-lg bg-blue-50 dark:bg-dark-secondary">
      <el-form label-position="left">
        <el-form-item
          class="!mb-0"
          label="迭代次數"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-select
              v-model="nodeData_value.iterations"
              placeholder="請選擇迭代次數"
              clearable>
              <el-option
                v-for="item in [20, 40, 60, 80, 100]"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
          </template>
          <template v-else>
            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              {{ nodeData_value.iterations }}
            </div>
          </template>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分析結果區域 -->
    <div v-if="nodeData_value.chartData">
      <el-divider content-position="left">分析結果</el-divider>
      <div class="result-container">
        <!-- 卡方分析圖表 -->
        <div class="mb-4">
          <Chart
            width="auto"
            height="280px"
            :options="chartData" />
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted, computed } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "卡方圖分析",
  },
  description: {
    type: String,
    default:
      "根據品目、工單及卡方離散變數，進行卡方分析，找出影響不良的關鍵因素",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("test-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
  iterations: 100,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
  iterations: 100,
});

const chartData = computed(() => {
  if (
    !nodeData_value.value?.chartData ||
    !Array.isArray(nodeData_value.value.chartData)
  ) {
    return {};
  }

  // 排序數據，根據P-Value由高到低
  const sortedData = [...nodeData_value.value.chartData].sort(
    (a, b) => parseFloat(b.p) - parseFloat(a.p)
  );

  // 提取x軸數據（變數名稱）
  const xAxis = sortedData.map((d) => d.feature);

  // 計算顯示數值 (1 - P-Value)
  const seriesData = sortedData.map((d) => {
    return Math.round((1 - parseFloat(d.p)) * 1000) / 1000;
  });

  // 計算y軸標籤最大長度，用於設置左邊距
  const xAxisLength =
    Math.max(
      ...xAxis.map((str) => {
        let length = 0;
        for (let i = 0; i < str.length; i++) {
          // 判斷是否為中文字元
          if (/[\u4e00-\u9fa5]/.test(str[i])) {
            length += 2; // 中文字元算 2
          } else {
            length += 1; // 非中文字元算 1
          }
        }
        return length;
      })
    ) * 7;

  return {
    tooltip: {
      position: "top",
      formatter: function (params) {
        const dataIndex = params.dataIndex;
        const item = sortedData[dataIndex];
        return `變數: ${item.feature}<br/>
                卡方值: ${parseFloat(item.significant_count || 0).toFixed(
                  4
                )}<br/>
                P值: ${parseFloat(item.p || 0).toFixed(4)}<br/>
                重要性: ${params.value} (1-P值)<br/>
                自由度: ${parseInt(item.dof || 0)}`;
      },
    },
    grid: {
      left: xAxisLength,
      right: 30,
      bottom: 30,
      top: 50,
    },
    xAxis: {
      type: "value",
      min: 0,
      max: 1,
      axisLabel: {
        rotate: props.isReportMode || nodeRef.value.isFullscreen ? 0 : 45,
        formatter: function (value) {
          return value.toFixed(2);
        },
      },
      color: isDark.value ? "#fff" : "#909399",
    },
    yAxis: {
      type: "category",
      data: xAxis,
      axisLabel: {
        formatter: function (value) {
          // 如果標籤太長，截斷顯示
          if (value.length > 15) {
            return value.substring(0, 12) + "...";
          }
          return value;
        },
        color: isDark.value ? "#fff" : "#909399",
      },
    },
    toolbox: {
      feature: {
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        data: seriesData,
        type: "bar",
        itemStyle: {
          color: "#3498db",
        },
        markLine: {
          data: [
            {
              name: "界線值",
              xAxis: 0.95, // 設置指標線的位置
            },
          ],
          lineStyle: {
            color: "red", // 指標線顏色
            width: 2, // 指標線寬度
            type: "dashed", // 設置為虛線
          },
          symbol: ["none", "none"], // 去掉箭頭
        },
      },
    ],
  };
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const features =
    mergedPreviousOutputs.discreteVariables ||
    globalVariables?.discreteVariables ||
    [];
  // printLog("品目:", part_no);
  // printLog("工單:", work_order);
  // printLog("變數:", variables);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止取得示範選項清單`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「參數」! 終止取得示範選項清單`,
    !features || features.length < 1
  );

  // 調用API
  const result = await request.post(
    "/external/iym/test_statistics/chi_square",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      features: features,
      iterations: nodeData_value.value.iterations,
    }
  );

  if (!Array.isArray(result) || result.length === 0) {
    throw new Error("卡方分析未返回有效數據");
  }
  await updateGlobalVariable("testParam_chi_square", result);

  nodeData_value.value.chartData = result;

  const completeResult = {
    chartData: result,
    iterations: nodeData_value.value.iterations,
    timestamp: new Date().toISOString(),
  };

  return completeResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || [];
    nodeData_value.value.iterations = previousData.detail.iterations || 100;
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
