<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :title="title"
    :description="description"
    :data="props.data"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    :isReportMode="isReportMode"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2 dark:text-white">
          卡方圖分析
        </h3>
        <el-form
          label-position="top"
          size="small">
        </el-form>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="chartReady && nodeContext && nodeContext.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 卡方分析圖表 -->
          <div
            v-for="(processData, processIndex) in groupedChartData"
            :key="processIndex"
            class="mb-6">
            <div
              v-for="(chart, chartIndex) in processData.charts"
              :key="chartIndex"
              class="mb-4">
              <Chart
                width="auto"
                height="280px"
                :options="getChartOption(chart, processData.processName)" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { BarChart2 } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";
import Chart from "@/components/chart.vue";
import { ref, computed, onMounted } from "vue";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "卡方圖分析(SPI)",
  },
  description: {
    type: String,
    default:
      "根據品目、工單及卡方離散變數，進行卡方分析，找出影響不良的關鍵因素",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點狀態
const nodeRef = ref(null);
const chartReady = ref(false);

// 初始化 nodeContext，提供默認值避免 undefined 錯誤
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  defectCodeOptions: [],
  defectCode: "",
});

// 計算是否可以分析
const canAnalyze = computed(() => {
  return formData.value.defectCode !== "";
});

// 將API返回的數據轉換為圖表數據
const groupedChartData = computed(() => {
  logger.debug(props.title, "DO groupedChartData:", nodeContext.value);
  if (
    !nodeContext.value?.output?.result ||
    !Array.isArray(nodeContext.value.output?.result)
  ) {
    return [];
  }

  // 返回簡化的資料結構，所有資料放在同一個圖表中
  return [
    {
      processName: "SPI卡方分析",
      charts: [
        {
          step: "變數重要性分析",
          data: nodeContext.value.output.result.map((item) => ({
            Variable: item.feature_col,
            Chi2: parseFloat(item.significant_count) || 0,
            "P-Value": parseFloat(item.p) || 0,
            DOF: parseInt(item.dof) || 0,
            SignificantRate: parseFloat(item.Significant_rate) || 0,
          })),
        },
      ],
    },
  ];
});

// 獲取圖表選項
const getChartOption = (chartData, processName) => {
  if (!chartData || !chartData.data || !chartData.data.length) {
    return {};
  }

  // 排序數據，根據P-Value由高到低
  const sortedData = [...chartData.data].sort(
    (a, b) => b["P-Value"] - a["P-Value"]
  );

  // 提取x軸數據（變數名稱）
  const xAxis = sortedData.map((d) => d.Variable);

  // 計算顯示數值 (1 - P-Value)
  const seriesData = sortedData.map((d) => {
    return Math.round((1 - d["P-Value"]) * 1000) / 1000;
  });

  // 計算y軸標籤最大長度，用於設置左邊距
  const xAxisLength =
    Math.max(
      ...xAxis.map((str) => {
        let length = 0;
        for (let i = 0; i < str.length; i++) {
          // 判斷是否為中文字元
          if (/[\u4e00-\u9fa5]/.test(str[i])) {
            length += 2; // 中文字元算 2
          } else {
            length += 1; // 非中文字元算 1
          }
        }
        return length;
      })
    ) * 7;

  return {
    title: {
      text: processName,
    },
    tooltip: {
      show: true,
      formatter: function (params) {
        const dataIndex = params.dataIndex;
        const item = sortedData[dataIndex];
        return `變數: ${item.Variable}<br/>
                卡方值: ${item.Chi2.toFixed(4)}<br/>
                P值: ${item["P-Value"].toFixed(4)}<br/>
                重要性: ${params.value} (1-P值)<br/>
                自由度: ${item.DOF}`;
      },
    },
    xAxis: {
      type: "value",
      min: 0,
      max: 1,
      axisLabel: {
        formatter: "{value}",
      },
    },
    yAxis: {
      type: "category",
      data: xAxis,
      axisLabel: {
        formatter: function (value) {
          // 如果標籤太長，截斷顯示
          if (value.length > 15) {
            return value.substring(0, 12) + "...";
          }
          return value;
        },
      },
    },
    toolbox: {
      feature: {
        saveAsImage: {},
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        data: seriesData,
        type: "bar",
        markLine: {
          data: [
            {
              name: "界線值",
              xAxis: 0.95, // 設置指標線的位置
            },
          ],
          lineStyle: {
            color: "red", // 指標線顏色
            width: 2, // 指標線寬度
            type: "dashed", // 設置為虛線
          },
          symbol: ["none", "none"], // 去掉箭頭
        },
      },
    ],
    grid: {
      left: xAxisLength,
      right: 30,
      bottom: 30,
      top: 50,
    },
  };
};

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "statistic-process",
  nodeName: props.title,
  nodeRef: nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError, // 不需要重命名
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 使用重置邏輯 composable 監聽節點狀態變化
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  resetHandler: () => {
    // 清除分析結果
    nodeContext.value = {
      output: null,
      input: null,
      status: "idle",
    };
    chartReady.value = false;
  },
});

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `卡方分析節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 由先前節點或全域變數取得參數
  const part_no =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  const work_order =
    context.sourceNodeOutput?.lot || globalVariables?.lot || [];
  const variables =
    context.sourceNodeOutput?.chiSquareVariables ||
    globalVariables?.chiSquareVariables ||
    [];

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: part_no,
    工單: work_order.length > 0,
    變數: variables.length > 0,
  };

  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingParams.length > 0) {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行前置節點並選擇變數`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  try {
    // 準備處理函數
    const processFunction = async (inputData) => {
      const result = await request.post(
        "/external/iym/test_statistics/chi_square_spi",
        {
          part_no: part_no,
          work_order: work_order.join(","),
          columns: variables,
        }
      );
      if (!Array.isArray(result) || result.length === 0) {
        throw new Error("卡方分析未返回有效數據");
      }
      await updateGlobalVariable("chisquare_spi", result);

      // 構建完整的結果對象
      const completeResult = {
        result: result,
        timestamp: new Date().toISOString(),
      };

      // 更新圖表數據
      nodeContext.value = {
        ...nodeContext.value,
        output: completeResult,
      };

      return completeResult;
    };

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);

    // 確保圖表更新
    chartReady.value = false;
    await nextTick();
    chartReady.value = true;

    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = props.isReportMode
    ? props.data?.context?.sharedData[props.id] || {}
    : getSharedData(props.id);

  if (previousData && previousData.detail) {
    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };

    // 設置圖表準備好顯示
    chartReady.value = true;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 600px;
  overflow-y: auto;
}
</style>
