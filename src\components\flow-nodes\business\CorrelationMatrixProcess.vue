<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :node-width="500"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 分析結果區域 -->
    <div v-if="nodeData_value.chartData">
      <el-divider content-position="left">分析結果</el-divider>
      <div class="result-container">
        <!-- 熱力圖 -->
        <div class="mb-4">
          <Chart
            width="auto"
            height="400px"
            :options="chartOption" />
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted, computed } from "vue";
import { Position } from "@vue-flow/core";

const { isDark } = useThemeMode();

const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "相關性矩陣",
  },
  description: {
    type: String,
    default: "計算各變數間的相關性",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
});

// 獲取圖表選項方法（供 Chart 組件使用）
const chartOption = computed(() => {
  if (!nodeData_value.value.chartData) return {};
  console.log("nodeData_value.value.chartData", nodeData_value.value.chartData);

  const features = Object.keys(nodeData_value.value.chartData);
  const heatmapData = [];

  features.forEach((rowVar, i) => {
    features.forEach((colVar, j) => {
      const value =
        nodeData_value.value.chartData[rowVar]?.[colVar] ??
        nodeData_value.value.chartData[colVar]?.[rowVar] ??
        null;
      heatmapData.push([j, i, value]);
    });
  });

  return {
    tooltip: {
      position: "top",
      formatter: function (params) {
        return `${features[params.value[0]]}*${
          features[params.value[1]]
        }<br/>值: ${params.value[2].toFixed(2)}`;
      },
    },
    grid: {
      top: "15%",
      left: "30%",
      bottom: "25%",
    },
    xAxis: {
      type: "category",
      data: features,
      axisLabel: {
        rotate: 45,
        formatter: function (value) {
          if (props.isReportMode || nodeRef.value.isFullscreen) {
            return value;
          }
          return value.length > 10 ? value.substring(0, 10) + "..." : value;
        },
        color: isDark.value ? "#fff" : "#909399",
      },
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: features,
      axisLabel: {
        color: isDark.value ? "#fff" : "#909399",
        formatter: function (value) {
          if (props.isReportMode || nodeRef.value.isFullscreen) {
            return value;
          }
          return value.length > 10 ? value.substring(0, 10) + "..." : value;
        },
      },
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: "horizontal",
      left: "40%",
      top: "0%",
      textStyle: {
        color: isDark.value ? "#fff" : "#909399",
      },
      formatter: function (value) {
        return value.toFixed(2);
      },
      inRange: {
        color: [
          "#313695",
          "#4575b4",
          "#74add1",
          "#abd9e9",
          "#e0f3f8",
          "#ffffbf",
          "#fee090",
          "#fdae61",
          "#f46d43",
          "#d73027",
          "#a50026",
        ],
      },
    },
    series: [
      {
        name: "相關性矩陣",
        type: "heatmap",
        data: heatmapData,
        label: {
          show: false,
          formatter: function (value) {
            return value.toFixed(2);
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType ||
    globalVariables?.resourceType ||
    "SPI";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const features =
    mergedPreviousOutputs.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止取得示範選項清單`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「特徵」! 終止取得示範選項清單`,
    !features || features.length < 1
  );

  nodeData_value.value.chartData = await request.post(
    "/external/iym/basic_statistics/correlation_matrix",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      features: features,
    }
  );

  if (!Object.keys(nodeData_value.value.chartData).length > 0) {
    throw new Error("相關性矩陣分析未返回有效數據");
  }

  await updateGlobalVariable(
    "correlation_matrix_analysis",
    nodeData_value.value.chartData
  );

  // 構建結果對象
  const result = {
    chartData: nodeData_value.value.chartData,
    timestamp: new Date().toISOString(),
  };

  return result;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || null;
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
