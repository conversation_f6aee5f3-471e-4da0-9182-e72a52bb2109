<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 分析結果區域 -->
    <div v-if="nodeData_value.chartData">
      <el-divider content-position="left">分析結果</el-divider>
      <!-- 決策樹圖 -->
      <div class="mb-4">
        <Chart
          width="auto"
          height="400px"
          :options="chartOption" />
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ElMessage } from "element-plus";
import { ref, onMounted, computed } from "vue";

const { isDark } = useThemeMode();

const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "決策樹交互作用分析",
  },
  description: {
    type: String,
    default: "以決策樹分析參數之間的互動關係",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
  iterations: 100,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
  iterations: 100,
});

// 工具函數：將路徑列表轉換為樹狀結構
const convertToTreeStructure = (pathList) => {
  // 根節點
  const root = {
    name: "Root",
    children: [],
  };

  pathList.forEach((path) => {
    // 解析規則路徑
    const rules = path.RulePath.split(" & ");
    let currentNode = root;

    rules.forEach((rule) => {
      // 在當前層級尋找或創建節點
      let found = currentNode.children.find((child) => child.name === rule);
      if (!found) {
        found = {
          name: rule,
          children: [],
        };
        currentNode.children.push(found);
      }
      currentNode = found;
    });

    // 添加預測值和重要性資訊到葉節點
    currentNode.value = `預測: ${path.Prediction}\n重要性: ${(
      path.Importance * 100
    ).toFixed(1)}%`;
  });

  return root;
};

// 建立圖表配置
const chartOption = computed(() => {
  const data = nodeData_value.value?.chartData;
  if (!data) {
    printLog(props.title, "沒有chartData資料");
    return {};
  }

  // 將路徑數據轉換為樹狀結構
  const treeStructure = convertToTreeStructure(data);

  return {
    tooltip: {
      trigger: "item",
      triggerOn: "mousemove",
      formatter: (params) => {
        const data = params.data;
        return data.value
          ? `${data.name}<br/>${data.value.replace("\n", "<br/>")}`
          : data.name;
      },
    },
    toolbox: {
      feature: {
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        type: "tree",
        data: [treeStructure],
        top: "12%",
        left: "5%",
        bottom: "38%",
        right: "15%",
        symbolSize: 7,
        initialTreeDepth: 2,
        orient: "vertical", // 垂直方向
        label: {
          position: "left",
          rotate: -45,
          verticalAlign: "middle",
          align: "right",
          fontSize: 12,
          distance: 5,
          formatter: (params) => {
            return params.data.value
              ? `${params.data.name}\n${params.data.value}`
              : params.data.name;
          },
        },
        leaves: {
          label: {
            position: "right",
            rotate: -45,
            verticalAlign: "middle",
            align: "left",
          },
        },
        emphasis: {
          focus: "descendant",
        },
        expandAndCollapse: true,
        animationDuration: 550,
        animationDurationUpdate: 750,
      },
    ],
  };
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const continuousVariables =
    mergedPreviousOutputs.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];
  const target = mergedPreviousOutputs.target || globalVariables?.target || "";
  // printLog("品目:", part_no);
  // printLog("工單:", work_order);
  // printLog("特徵:", features);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止取得示範選項清單`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「變數」! 終止取得示範選項清單`,
    !continuousVariables || continuousVariables.length < 1
  );

  // 調用API
  //todo: 需要回來修改target不一致問題
  const result = await request.post(
    "/external/iym/machine_learning/decision_tree_interaction_for_parameter",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      variables: continuousVariables,
      target: "ooc_rate",
    }
  );

  if (!Array.isArray(result) || result.length === 0) {
    throw new Error("決策樹交互作用分析未返回有效數據");
  }
  await updateGlobalVariable("decision_tree_interaction_for_parameter", result);

  nodeData_value.value.chartData = result;

  const completeResult = {
    chartData: result,
    timestamp: new Date().toISOString(),
  };

  return completeResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
