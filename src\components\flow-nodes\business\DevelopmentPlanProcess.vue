<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="FileText"
    :node-width="nodeWidth"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">於執行取得:</h3>

        <!-- 全域變數顯示區域 -->
        <div class="mb-4">
          <h4 class="text-xs font-medium text-gray-600 mb-1">全域變數</h4>
          <pre class="text-xs bg-gray-50 p-2 rounded">{{
            displayGlobalVars
          }}</pre>
        </div>

        <!-- 上一節點輸出顯示區域 -->
        <div class="mb-4">
          <h4 class="text-xs font-medium text-gray-600 mb-1">上一節點輸出</h4>
          <pre class="text-xs bg-gray-50 p-2 rounded">{{
            displayPreviousOutput
          }}</pre>
        </div>
      </div>

      <!-- 發展計劃區域 -->
      <div v-if="developmentPlan">
        <el-divider content-position="left">個人發展計劃</el-divider>

        <!-- 員工基本信息 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">員工信息</h4>
          <div class="bg-white p-4 rounded border">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-sm text-gray-600">姓名</div>
                <div class="text-sm font-medium text-gray-900">
                  {{ developmentPlan.employeeInfo.name }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">部門</div>
                <div class="text-sm font-medium text-gray-900">
                  {{ developmentPlan.employeeInfo.department }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">職位</div>
                <div class="text-sm font-medium text-gray-900">
                  {{ developmentPlan.employeeInfo.position }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-600">入職日期</div>
                <div class="text-sm font-medium text-gray-900">
                  {{ developmentPlan.employeeInfo.joinDate }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 發展目標 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">發展目標</h4>
          <div class="bg-white p-4 rounded border">
            <div class="space-y-3">
              <div
                v-for="(goal, index) in developmentPlan.goals"
                :key="index"
                class="flex items-start">
                <div
                  class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium mr-3 mt-1">
                  {{ index + 1 }}
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ goal.title }}
                  </div>
                  <div class="text-sm text-gray-600 mt-1">
                    {{ goal.description }}
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    預計完成時間: {{ goal.targetDate }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 具體行動計劃 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">具體行動計劃</h4>
          <div class="bg-white p-4 rounded border">
            <div class="space-y-4">
              <div
                v-for="(action, index) in developmentPlan.actionPlans"
                :key="index"
                class="border-l-4 border-blue-500 pl-4">
                <div class="text-sm font-medium text-gray-900">
                  {{ action.title }}
                </div>
                <div class="text-sm text-gray-600 mt-1">
                  {{ action.description }}
                </div>
                <div class="mt-2">
                  <div class="text-xs text-gray-500">資源需求:</div>
                  <div class="flex flex-wrap gap-2 mt-1">
                    <el-tag
                      v-for="(resource, rIndex) in action.resources"
                      :key="rIndex"
                      size="small"
                      type="info">
                      {{ resource }}
                    </el-tag>
                  </div>
                </div>
                <div class="mt-2">
                  <div class="text-xs text-gray-500">時間安排:</div>
                  <div class="text-sm text-gray-600 mt-1">
                    {{ action.timeline }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 評估指標 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">評估指標</h4>
          <div class="bg-white p-4 rounded border">
            <div class="space-y-3">
              <div
                v-for="(metric, index) in developmentPlan.metrics"
                :key="index"
                class="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ metric.name }}
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ metric.description }}
                  </div>
                </div>
                <el-tag
                  size="small"
                  :type="metric.type">
                  {{ metric.target }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 注意事項 -->
        <div v-if="developmentPlan.notes">
          <h4 class="text-sm font-medium text-gray-700 mb-2">注意事項</h4>
          <div class="bg-white p-4 rounded border">
            <ul class="list-disc list-inside space-y-2">
              <li
                v-for="(note, index) in developmentPlan.notes"
                :key="index"
                class="text-sm text-gray-700">
                {{ note }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { FileText } from "lucide-vue-next";
import { ElMessage } from "element-plus";
import { useFlowStore } from "@/stores/flowStore";
import { useThemeMode } from "@/composables/useThemeMode";

// 取得主題
const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log("DevelopmentPlanProcessNode", title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "個人發展計劃生成",
  },
  description: {
    type: String,
    default: "整合績效和技能分析結果，生成個人發展計劃",
  },
  nodeWidth: {
    type: Number,
    default: 500,
  },
  nodeHeight: {
    type: Number,
    default: 700,
  },
});

// 節點狀態
const nodeRef = ref(null);
const developmentPlan = ref(null);
const displayGlobalVars = ref("尚未執行");
const displayPreviousOutput = ref("尚未執行");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef: nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const { handleClearError, getSharedData, updateGlobalVariables } =
  nodeExecution;

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

// 獲取 flow store

// 生成發展計劃
const generateDevelopmentPlan = (employeeData) => {
  if (!employeeData || !employeeData.employeeDetail) {
    throw new Error("未獲取到員工數據");
  }

  // 生成發展目標
  const goals = [
    {
      title: "提升核心技能",
      description: "在未來6個月內提升核心技能水平，達到職位要求",
      targetDate: "2024-12-31",
    },
    {
      title: "掌握進階技能",
      description: "在未來12個月內掌握必要的進階技能",
      targetDate: "2025-06-30",
    },
    {
      title: "提升績效表現",
      description: "在下一季度提升績效評分至優秀水平",
      targetDate: "2024-09-30",
    },
  ];

  // 生成具體行動計劃
  const actionPlans = [
    {
      title: "核心技能培訓",
      description: "參加公司內部培訓課程和線上學習平台",
      resources: ["內部培訓課程", "線上學習平台", "導師指導"],
      timeline: "2024-07-01 至 2024-12-31",
    },
    {
      title: "進階技能學習",
      description: "參與專案實踐和技術研討會",
      resources: ["專案實踐機會", "技術研討會", "專業書籍"],
      timeline: "2024-10-01 至 2025-06-30",
    },
    {
      title: "績效提升計劃",
      description: "制定具體的工作改進計劃，定期與主管溝通",
      resources: ["主管指導", "績效反饋", "工作改進工具"],
      timeline: "2024-07-01 至 2024-09-30",
    },
  ];

  // 生成評估指標
  const metrics = [
    {
      name: "核心技能達標率",
      description: "核心技能達到職位要求的比例",
      target: "80%",
      type: "success",
    },
    {
      name: "進階技能掌握度",
      description: "進階技能的掌握程度",
      target: "60%",
      type: "warning",
    },
    {
      name: "績效評分",
      description: "季度績效評分",
      target: "4.5/5",
      type: "success",
    },
  ];

  // 生成注意事項
  const notes = [
    "定期與主管進行發展計劃進度檢討",
    "保持學習記錄和成果證明",
    "及時調整計劃以應對變化",
    "積極參與團隊分享和知識傳遞",
  ];

  return {
    employeeInfo: employeeData.employeeDetail,
    goals,
    actionPlans,
    metrics,
    notes,
    generatedTime: new Date().toISOString(),
  };
};

const processFunction = async (context = {}) => {
  const employeeDetail = context.globalVariables?.employeeDetail;
  const performanceAnalysis = context.globalVariables?.performanceAnalysis;
  const skillAnalysis = context.globalVariables?.skillAnalysis;
  printLog("context", context);
  printLog("performanceAnalysis", performanceAnalysis);
  printLog("skillAnalysis", skillAnalysis);

  if (!employeeDetail) {
    console.error("未獲取到員工數據");
  }

  if (!performanceAnalysis || !skillAnalysis) {
    console.error("未獲取到必要的分析數據");
  }

  // 模擬處理過程
  await new Promise((resolve) => setTimeout(resolve, 1500));

  // 生成發展計劃
  const result = generateDevelopmentPlan(
    { employeeDetail },
    performanceAnalysis,
    skillAnalysis
  );

  // 更新發展計劃（添加 _isDataUpdate 標記）
  developmentPlan.value = result;

  // 設置全域變數（添加 _isDataUpdate 標記）
  await updateGlobalVariables(
    {
      developmentPlan: result,
    },
    {
      _isDataUpdate: true,
    }
  );

  return result;
};

// 執行節點
const handleRun = async (context = {}) => {
  // 更新顯示用的數據
  displayGlobalVars.value = JSON.stringify(
    context.globalVariables || {},
    null,
    2
  );
  displayPreviousOutput.value = JSON.stringify(
    context.sourceNodeOutput || {},
    null,
    2
  );

  // 使用箭頭函數傳遞 context
  nodeRef.value.handleRun(() => processFunction(context));
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    developmentPlan.value = previousData.detail;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 400px;
  overflow-y: auto;
}

.result-container pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

pre {
  max-height: 200px;
  overflow-y: auto;
}
</style>
