<template>
  <!-- TODO: 測試 nodeRef 是否能夠傳遞到 BaseNode -->
  <BaseNode
    :id="id"
    ref="nodeRef"
    :title="title"
    nodeType="custom-input"
    :description="description"
    icon="User"
    :node-width="600"
    @click="handleNodeClick"
    @run="handleRun">
    <!-- 主要內容區域 -->
    <div class="p-4 space-y-4">
      <!-- 員工選擇 -->
      <el-form-item label="員工選擇">
        <el-select
          v-model="selectedEmployee"
          placeholder="請選擇員工"
          clearable
          filterable
          class="w-full"
          :loading="loading"
          @change="handleEmployeeChange">
          <el-option
            v-for="item in employeeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <!-- 已選擇的員工資訊 -->
      <div
        v-if="selectedEmployee && employeeDetail"
        class="bg-blue-500 p-3 rounded text-sm dark:bg-dark-secondary">
        <div class="flex items-center justify-between mb-2">
          <span>員工資訊</span>
          <el-tag
            size="small"
            type="info"
            >{{ selectedEmployee }}</el-tag
          >
        </div>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span>部門</span>
            <span>{{ employeeDetail.department || "研發部" }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span>職位</span>
            <span>{{ employeeDetail.position || "資深工程師" }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span>入職日期</span>
            <span>{{ employeeDetail.joinDate || "2023-01-01" }}</span>
          </div>
        </div>
      </div>

      <!-- HACK 移除，不過先少量調整看看 錯誤信息展示 -->
      <div
        v-if="errorMessage"
        class="bg-red-50 p-3 rounded text-sm border border-red-200">
        <div class="flex items-center justify-between text-red-500 mb-2">
          <div class="flex items-center">
            <i class="el-icon-warning mr-1"></i>
            <span>執行錯誤</span>
          </div>
          <el-button
            type="text"
            size="small"
            @click="toggleErrorDetails">
            {{ showErrorDetails ? "隱藏詳情" : "查看詳情" }}
          </el-button>
        </div>
        <p class="text-red-600">
          {{ nodeRef.formatErrorMessage(errorMessage) }}
        </p>

        <!-- 錯誤詳情 -->
        <div
          v-if="showErrorDetails && errorDetails"
          class="mt-2 pt-2 border-t border-red-200">
          <div
            v-if="errorDetails.suggestion"
            class="text-orange-600 mb-2">
            {{ errorDetails.suggestion }}
          </div>

          <div
            v-if="errorDetails.timestamp"
            class="flex justify-between text-xs mb-1">
            <span class="text-gray-600">發生時間:</span>
            <span>{{ formatTime(errorDetails.timestamp) }}</span>
          </div>

          <!-- 顯示完整錯誤信息 -->
          <div
            v-if="errorDetails.message"
            class="mt-2">
            <div class="text-xs text-gray-600 mb-1">完整錯誤信息:</div>
            <div
              class="text-xs text-red-600 p-2 bg-red-50 rounded overflow-auto max-h-24">
              {{ errorDetails.message }}
            </div>
          </div>
        </div>

        <div class="mt-2 flex justify-end space-x-2">
          <el-button
            type="danger"
            size="small"
            plain
            @click="handleClearError">
            清除錯誤
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleRun">
            重試執行
          </el-button>
        </div>
      </div>

      <!-- 此節點客制專用而已 執行按鈕 -->
      <!-- <div class="flex justify-end space-x-2">
        <el-button
          v-if="
            nodeRef && nodeRef.nodeState && nodeRef.nodeState.status === 'error'
          "
          type="warning"
          size="small"
          :disabled="!selectedEmployee || executing"
          @click="handleRun">
          重試執行
        </el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="!selectedEmployee || executing"
          :loading="executing"
          @click="handleRun">
          {{
            nodeRef &&
            nodeRef.nodeState &&
            nodeRef.nodeState.status === "completed"
              ? "重新執行分析"
              : "執行數據導入"
          }}
        </el-button>
      </div> -->
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { User } from "@element-plus/icons-vue";
import { wait } from "@/utils/dateUtils";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useThemeMode } from "@/composables/useThemeMode";

// 取得主題
const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  // console.log(props.title, title, result);
};

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "員工數據導入",
  },
  description: {
    type: String,
    default: "用於導入員工績效評估數據、工作時間記錄、培訓參與記錄等",
  },
  // HACK: 程式碼可以拿掉
  // selected: {
  //   type: Boolean,
  //   default: false,
  // },
  icon: {
    type: Object,
    default: () => User,
  },
  // HACK: 程式碼可以拿掉
  // minHeight: {
  //   type: Number,
  //   default: 500,
  // },
  // HACK: 程式碼可以拿掉
  // autoHeight: {
  //   type: Boolean,
  //   default: false,
  // },
});

// 節點引用
const nodeRef = ref(null);

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "EmployeeDataInputNode", // 需要! 用於useNodeExecution 的識別
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariables,
} = nodeExecution;

// 節點內容
const selectedEmployee = ref(null);
const employeeDetail = ref(null);
const loading = ref(false);
const showErrorDetails = ref(false);

// 模擬的員工選項
const employeeOptions = [
  { value: "EMP001", label: "張三" },
  { value: "EMP002", label: "李四" },
  { value: "EMP003", label: "王五" },
  { value: "EMP004", label: "趙六" },
  { value: "EMP005", label: "陳七" },
];

// 事件處理 HACK: 移到 BaseNode 即可
// const emit = defineEmits(["update:data", "click", "connect", "disconnect"]);

// const handleNodeClick = (event) => {
//   emit("click", { id: props.id, event });
// };

// const handleConnect = (data) => {
//   emit("connect", { id: props.id, ...data });
// };

// const handleDisconnect = (data) => {
//   emit("disconnect", { id: props.id, ...data });
// };

// 處理員工選擇變更
const handleEmployeeChange = async (value) => {
  if (!value) {
    employeeDetail.value = null;
    return;
  }

  try {
    loading.value = true;

    // 模擬API調用獲取員工詳情
    await wait(0.5);
    employeeDetail.value = {
      id: value,
      department: "研發部",
      position: "資深工程師",
      joinDate: "2023-01-01",
      performanceRecords: [
        { quarter: "2023-Q1", score: 85 },
        { quarter: "2023-Q2", score: 88 },
        { quarter: "2023-Q3", score: 90 },
        { quarter: "2023-Q4", score: 92 },
      ],
      trainingRecords: [
        { course: "Vue.js 進階", date: "2023-03-15", hours: 8 },
        { course: "TypeScript 實戰", date: "2023-06-20", hours: 16 },
      ],
      workingHours: [
        { month: "2023-01", hours: 176 },
        { month: "2023-02", hours: 168 },
        { month: "2023-03", hours: 184 },
      ],
    };
  } catch (error) {
    ElMessage.error("獲取員工詳情失敗");
    logger.error("EmployeeDataInputNode", "獲取員工詳情失敗:", error);
  } finally {
    loading.value = false;
  }
};

// 切換錯誤詳情顯示
const toggleErrorDetails = () => {
  showErrorDetails.value = !showErrorDetails.value;
};

// 格式化錯誤信息
//HACK: 刪除，改用 baseNode 的 formatErrorMessage
// const formatErrorMessage = (message) => {
//   if (!message) return "未知錯誤";
//   if (message.includes("\n")) {
//     return message.split("\n")[0];
//   }
//   if (message.length > 100) {
//     return message.substring(0, 100) + "...";
//   }
//   return message;
// };

const processFunction = async (input) => {
  //NOTE: 模擬API調用時間
  await wait(1);

  //故意拋出錯誤
  // throw new Error("故意拋出錯誤");

  // console.log("EmployeeDataInputNode", "員工數據處理完成");

  // 構建結果對象
  const result = {
    employeeId: selectedEmployee.value,

    //取得選中的員工名
    employeeName: employeeOptions.find(
      (option) => option.value === selectedEmployee.value
    )?.label,
    timestamp: new Date().toISOString(),
    nodeType: "EmployeeDataInputNode",
    completed: true,
    output: {
      employeeId: selectedEmployee.value,
      employeeDetail: employeeDetail.value,
    },
  };

  //NOTE: 更新全域變數 - 使用批量更新，全域變數應該是存 key-value 的數據，其中 value 應該簡單的字串或數據，不要是複雜的數據
  await updateGlobalVariables({
    employeeData: result,
    employeeId: selectedEmployee.value,
    employeeDetail: employeeDetail.value,
    performanceRecords: employeeDetail.value.performanceRecords,
    trainingRecords: employeeDetail.value.trainingRecords,
    workingHours: employeeDetail.value.workingHours,
  });

  //NOTE: 更新共享數據, 主要是存節點的輸出結果
  const sharedData = {
    id: selectedEmployee.value, //必填
    detail: employeeDetail.value, //必填
    timestamp: new Date().toISOString(), //必填
    completed: true, //必填
    output: result.output,
  };
  await updateSharedData(props.id, sharedData);

  // 更新節點狀態
  // await updateNodeStatus("completed", result);

  return result;
};

//HACK: 取消 context 參數，並改為使用 nodeRef 的 handleRun 方法
const handleRun = async () => {
  printLog(`開始執行數據導入，員工: ${selectedEmployee.value}`);

  //HACK:alert("handleRun");
  nodeRef.value.handleRun(processFunction);
};

// 取得工作流全域變數
const globalVariables = computed(() => {
  return useFlowStore().currentInstance?.context?.globalVariables;
});

onMounted(async () => {
  // 檢查是否有之前選擇的員工 HACK: 要取得 context 的話，應該要使用 getShareData

  const previousSelection = getSharedData(props.id);
  if (previousSelection) {
    // console.log("EmployeeDataInputNode", "之前的選擇:", previousSelection);
    selectedEmployee.value = previousSelection.id;
    employeeDetail.value = previousSelection.detail;
    selectedEmployee.value = previousSelection.detail.employeeId;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.employee-selector {
  @apply w-full;
}

.employee-selector__info {
  @apply mt-2 p-2 bg-gray-50 rounded-md text-sm;
}
</style>
