<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 分析結果區域 -->
    <div v-if="chartOptions && chartOptions.length > 0">
      <el-divider content-position="left">分析結果</el-divider>
      <!-- KDE圖表 - 依照特徵順序顯示 -->
      <div
        v-for="chartOption in chartOptions"
        :key="chartOption.title.text"
        class="mb-8">
        <Chart
          width="auto"
          height="300px"
          :options="chartOption" />
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ElMessage } from "element-plus";
import { ref, onMounted, computed } from "vue";
import { color } from "echarts";

const { isDark } = useThemeMode();

const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "KDE密度圖數據分析",
  },
  description: {
    type: String,
    default: "觀察連續型隨機變數分布的趨勢",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("test-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
});

// 建立圖表配置
const chartOptions = computed(() => {
  const data = nodeData_value.value?.chartData;
  if (!data) {
    printLog(props.title, "沒有chartData資料");
    return [];
  }

  const chartOptionArray = [];
  for (const item of data) {
    const feature = item.feature;
    const normalData = item.normal;
    const defectData = item.defect;

    // 計算x軸範圍
    const normalX = normalData.x;
    const defectX = defectData.x;
    const allX = [...normalX, ...defectX];
    const minX = Math.min(...allX);
    const maxX = Math.max(...allX);

    chartOptionArray.push({
      title: {
        text: `${feature} 的密度分布`,
        left: "center",
        textStyle: {
          color: isDark.value ? "#fff" : "#909399",
        },
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          let tip = `x值: ${params[0].axisValue}<br/>`;
          params.forEach((param) => {
            tip += `${param.seriesName}: ${param.value[1].toFixed(2)}<br/>`;
          });
          return tip;
        },
      },
      legend: {
        data: ["正常", "不良"],
        bottom: 5,
        left: "center",
        textStyle: {
          color: isDark.value ? "#fff" : "#909399",
        },
      },
      grid: {
        left: "10%",
        right: "5%",
        bottom: "25%",
      },
      xAxis: {
        type: "value",
        name: "特徵值",
        nameLocation: "middle",
        nameGap: 30,
        nameTextStyle: {
          color: isDark.value ? "#fff" : "#909399",
        },
        min: minX,
        max: maxX,
        axisLabel: {
          color: isDark.value ? "#fff" : "#909399",
          formatter: (value) => value.toFixed(1),
        },
      },
      yAxis: {
        type: "value",
        name: "密度",
        nameRotate: 90,
        nameLocation: "middle",
        nameGap: 45,
        axisLabel: {
          color: isDark.value ? "#fff" : "#909399",
          formatter: (value) => value.toFixed(2),
        },
      },
      toolbox: {
        feature: {
          dataView: { readOnly: true },
        },
      },
      series: [
        {
          name: "正常",
          type: "line",
          showSymbol: false,
          smooth: true,
          data: normalData.x.map((x, index) => [x, normalData.y[index]]),
          itemStyle: {
            color: "#67C23A",
          },
        },
        {
          name: "不良",
          type: "line",
          showSymbol: false,
          smooth: true,
          data: defectData.x.map((x, index) => [x, defectData.y[index]]),
          itemStyle: {
            color: "#F56C6C",
          },
        },
      ],
    });
  }
  return chartOptionArray;
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const target = mergedPreviousOutputs.target || globalVariables?.target || "";
  const features = globalVariables?.reliability_index[0].features || [];
  //todo: 之後要回來改寫
  const normal_group = globalVariables?.normal_group || "in_control";
  const defect_group = globalVariables?.defect_group || "ooc";

  // printLog("品目:", part_no);
  // printLog("工單:", work_order);
  // printLog("特徵:", features);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止取得示範選項清單`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「特徵」! 終止取得示範選項清單`,
    !features || features.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「目標變數」! 終止取得示範選項清單`,
    !target || target.length < 1
  );

  // 調用API
  const result = await request.post(
    "/external/iym/test_statistics/kde_analysis_for_features",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      features: features,
      target: target,
      normal_group: normal_group,
      defect_group: defect_group,
    }
  );

  if (!Array.isArray(result) || result.length === 0) {
    throw new Error("KDE分析未返回有效數據");
  }
  await updateGlobalVariable("kde_analysis_for_features", result);

  nodeData_value.value.chartData = result;

  const completeResult = {
    chartData: result,
    timestamp: new Date().toISOString(),
  };

  return completeResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
