<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 分析結果區域 -->
    <div v-if="nodeData_value.chartData && nodeData_value.chartData.length > 0">
      <el-divider content-position="left">分析結果</el-divider>
      <!-- 決策樹圖 -->
      <div class="mb-4">
        <Chart
          width="auto"
          height="400px"
          :options="chartOption" />
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ElMessage } from "element-plus";
import { ref, onMounted, computed } from "vue";

const { isDark } = useThemeMode();

const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "MARS interaction for parameter",
  },
  description: {
    type: String,
    default: "呈現自變數之間的交互作用對應變數的影響。",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
});

//#region 節點專用函數 START
function extractMarsVariables(expr) {
  // 用正則抽出 h(...) 裡面的內容
  const hingeParts = [];
  const regex = /h\((.*?)\)/g;
  let match;
  while ((match = regex.exec(expr)) !== null) {
    hingeParts.push(match[1]);
  }

  const variables = new Set();

  hingeParts.forEach((part) => {
    // 用 + - * / 拆解字串
    const tokens = part.split(/[\-\+\*\/]/);
    tokens.forEach((token) => {
      token = token.trim();
      // 排除純數字 (包含浮點數與科學記號)
      if (token && !/^[\d\.eE]+$/.test(token)) {
        variables.add(token);
      }
    });
  });

  return Array.from(variables);
}
//#endregion

// 建立圖表配置
const chartOption = computed(() => {
  const data = nodeData_value.value?.chartData;
  if (!data) {
    printLog(props.title, "沒有chartData資料");
    return {};
  }

  // 排序數據，根據重要性由高到低
  const sortedData = [...data].sort((a, b) => a.importance - b.importance);

  // 提取特徵名稱和係數值
  const features = sortedData.map((item) => extractMarsVariables(item.feature));
  const detailFeatures = sortedData.map((item) => item.feature);
  const importances = sortedData.map((item) => item.importance.toFixed(2));
  const coefficients = sortedData.map((item) => item.coefficient.toFixed(2));

  // 處理標籤配置
  const labelRight = {
    position: "right",
    color: "#000",
  };

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        // 確保 params 是陣列且有數據
        if (!params || !Array.isArray(params) || params.length === 0) {
          return "無數據";
        }

        const dataIndex = params[0].dataIndex;
        const feature = detailFeatures[dataIndex];
        const importance = importances[dataIndex];
        const coefficient = coefficients[dataIndex];

        return `${feature}<br/>係數: ${coefficient}<br/>重要性: ${importance}`;
      },
    },
    grid: {
      top: "15%",
      left: "30%",
      bottom: "25%",
    },
    xAxis: {
      type: "value",
      axisLabel: {
        rotate: props.isReportMode || nodeRef.value.isFullscreen ? 0 : 45,
        color: isDark.value ? "#fff" : "#909399",
      },
      position: "bottom",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
    },
    yAxis: {
      type: "category",
      axisLabel: {
        color: isDark.value ? "#fff" : "#909399",
        formatter: function (value) {
          if (props.isReportMode || nodeRef.value.isFullscreen) {
            return value;
          }
          return value.length > 10 ? value.substring(0, 10) + "..." : value;
        },
      },
      data: features,
    },
    toolbox: {
      feature: {
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        name: "係數",
        type: "bar",
        stack: "Total",
        itemStyle: {
          color: "#3498db",
        },
        data: coefficients.map((coefficient) => {
          return coefficient < 0
            ? { value: coefficient, label: labelRight }
            : coefficient;
        }),
      },
    ],
  };
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const continuousVariables =
    mergedPreviousOutputs.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];
  const target = mergedPreviousOutputs.target || globalVariables?.target || "";
  // printLog("品目:", part_no);
  // printLog("工單:", work_order);
  // printLog("特徵:", features);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止取得示範選項清單`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「變數」! 終止取得示範選項清單`,
    !continuousVariables || continuousVariables.length < 1
  );

  // 調用API
  const result = await request.post(
    "/external/iym/machine_learning/mars_interaction_for_parameter",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      features: continuousVariables,
      target: target,
    }
  );

  if (!Array.isArray(result) || result.length === 0) {
    throw new Error("MARS交互作用分析未返回有效數據");
  }
  await updateGlobalVariable("mars_interaction_for_parameter", result);

  nodeData_value.value.chartData = result;

  const completeResult = {
    chartData: result,
    timestamp: new Date().toISOString(),
  };

  return completeResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
