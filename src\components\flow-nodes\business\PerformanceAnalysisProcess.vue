<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="BarChart2"
    :node-width="nodeWidth"
    @run="handleRun">
    <div class="p-4">
      <!-- 資料來源顯示 -->
      <div class="mb-4 p-3 rounded">
        <h3 class="text-sm font-medium mb-2">資料來源狀態</h3>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm">員工基本資料</span>
            <el-tag
              size="small"
              :type="hasEmployeeData ? 'success' : 'danger'">
              {{ hasEmployeeData ? "已載入" : "未載入" }}
            </el-tag>
            {{ globalVariables?.employeeData?.employeeName }}
          </div>

          <div
            v-if="employeeInfo"
            class="text-xs mt-1">
            當前分析員工：{{ employeeInfo }}
          </div>
        </div>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="analysisResult">
        <el-divider content-position="left">績效分析結果</el-divider>

        <!-- 績效趨勢圖表 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium mb-2">績效趨勢</h4>
          <div class="p-4 rounded border">
            <div class="flex items-center justify-between mb-2">
              <div class="text-sm">季度績效分數</div>
              <el-tag
                size="small"
                :type="getTrendType(analysisResult.trend)">
                {{ analysisResult.trend }}
              </el-tag>
            </div>
            <div class="h-40 flex items-end space-x-2">
              <div
                v-for="(record, index) in analysisResult.performanceRecords"
                :key="index"
                class="flex-1 flex flex-col items-center">
                <div
                  class="w-full bg-blue-100 rounded-t"
                  :style="{ height: `${(record.score / 100) * 100}%` }"></div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ record.quarter }}
                </div>
                <div class="text-xs font-medium">{{ record.score }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 績效分析摘要 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium mb-2">分析摘要</h4>
          <div class="p-4 rounded border space-y-2">
            <div class="flex justify-between">
              <span class="">平均績效分數</span>
              <span class="font-medium">{{ analysisResult.averageScore }}</span>
            </div>
            <div class="flex justify-between">
              <span class="">最高績效分數</span>
              <span class="font-medium">{{ analysisResult.maxScore }}</span>
            </div>
            <div class="flex justify-between">
              <span class="">最低績效分數</span>
              <span class="font-medium">{{ analysisResult.minScore }}</span>
            </div>
            <div class="flex justify-between">
              <span class="">績效穩定性</span>
              <el-tag
                size="small"
                :type="getStabilityType(analysisResult.stability)">
                {{ analysisResult.stability }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 建議區域 -->
        <div v-if="analysisResult.recommendations">
          <h4 class="text-sm font-medium mb-2">發展建議</h4>
          {{ analysisResult }}
          <div class="p-4 rounded border">
            <ul class="list-disc list-inside space-y-1">
              <li
                v-for="(
                  recommendation, index
                ) in analysisResult.recommendations"
                :key="index"
                class="text-sm">
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { BarChart2 } from "lucide-vue-next";
import { useFlowStore } from "@/stores/flowStore";
import { useThemeMode } from "@/composables/useThemeMode";

// 取得主題
const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  //console.log("PerformanceAnalysisProcessNode", title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "績效趨勢分析",
  },
  description: {
    type: String,
    default: "分析員工過去6個季度的績效變化趨勢",
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
});

// 節點狀態
const nodeRef = ref(null);
const analysisResult = ref(null);

// 新增狀態
const hasEmployeeData = ref(false);
const hasPerformanceRecords = ref(false);
const employeeInfo = ref(null);

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef: nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariables,
} = nodeExecution;

// 獲取趨勢類型
const getTrendType = (trend) => {
  const trendMap = {
    上升: "success",
    持平: "info",
    下降: "warning",
  };
  return trendMap[trend] || "info";
};

// 獲取穩定性類型
const getStabilityType = (stability) => {
  const stabilityMap = {
    穩定: "success",
    一般: "info",
    不穩定: "warning",
  };
  return stabilityMap[stability] || "info";
};

// 分析績效數據
const analyzePerformance = (records) => {
  if (!records || records.length === 0) {
    throw new Error("沒有績效記錄數據");
  }

  // 計算基本統計數據
  const scores = records.map((r) => r.score);
  const averageScore = Math.round(
    scores.reduce((a, b) => a + b, 0) / scores.length
  );
  const maxScore = Math.max(...scores);
  const minScore = Math.min(...scores);

  // 分析趨勢
  const firstScore = scores[0];
  const lastScore = scores[scores.length - 1];
  const trend =
    lastScore > firstScore ? "上升" : lastScore < firstScore ? "下降" : "持平";

  // 分析穩定性
  const scoreRange = maxScore - minScore;
  const stability =
    scoreRange <= 5 ? "穩定" : scoreRange <= 10 ? "一般" : "不穩定";

  console.log("stability", stability, trend);
  // 生成建議
  const recommendations = [];
  if (trend === "上升") {
    recommendations.push("建議繼續保持良好的績效趨勢");
  }
  if (trend === "下降") {
    recommendations.push("建議加強績效管理，找出績效下降的原因");
  }
  if (stability === "不穩定") {
    recommendations.push("建議加強績效穩定性，保持穩定的工作表現");
  }
  if (lastScore < averageScore) {
    recommendations.push("最近一季績效低於平均水平，需要特別關注");
  }
  console.log("recommendations", recommendations);

  return {
    performanceRecords: records,
    averageScore,
    maxScore,
    minScore,
    trend,
    stability,
    recommendations,
    analysisTime: new Date().toISOString(),
  };
};

// 檢查必要資料是否存在
const checkRequiredData = (context) => {
  const employeeDetail = context.globalVariables?.employeeDetail;

  // 檢查員工基本資料
  hasEmployeeData.value = !!employeeDetail;

  // 檢查績效記錄
  hasPerformanceRecords.value = !!(
    employeeDetail?.performanceRecords?.length > 0
  );

  // 更新員工資訊顯示
  if (employeeDetail) {
    employeeInfo.value = `${context.globalVariables?.employeeId} - ${employeeDetail.department} ${employeeDetail.position}`;
  } else {
    employeeInfo.value = null;
  }

  // 記錄檢查結果
  printLog("資料檢查結果:", {
    hasEmployeeData: hasEmployeeData.value,
    hasPerformanceRecords: hasPerformanceRecords.value,
    employeeInfo: employeeInfo.value,
  });

  // 返回檢查結果
  return {
    isValid: hasEmployeeData.value && hasPerformanceRecords.value,
    missingData: [
      !hasEmployeeData.value && "員工基本資料",
      !hasPerformanceRecords.value && "績效記錄",
    ].filter(Boolean),
  };
};

// 處理函數
const processFunction = async (context = {}) => {
  // 檢查必要資料
  const { isValid, missingData } = checkRequiredData(context);
  if (!isValid) {
    throw new Error(`缺少必要資料：${missingData.join("、")}`);
  }

  // 從全域變數獲取員工數據
  const employeeDetail = context.globalVariables.employeeDetail;

  printLog("開始處理績效數據");

  // 模擬處理過程
  // await wait(1000);

  // 分析績效數據
  const result = analyzePerformance(employeeDetail.performanceRecords);

  printLog("績效數據分析完成", result);

  // 更新分析結果
  analysisResult.value = result;
  printLog("績效數據分析完成", result);
  // 設置全域變數

  await updateGlobalVariables({
    performanceAnalysis: result,
  });

  // 更新共享數據
  await updateSharedData(props.id, {
    detail: result,
    timestamp: new Date().toISOString(),
    completed: true,
    output: result,
  });

  return result;
};

// 修改 handleRun 函數
const handleRun = async (context = {}) => {
  // 使用箭頭函數傳遞 context
  nodeRef.value.handleRun(() => processFunction(context));
};

const globalVariables = computed(() => {
  return useFlowStore().currentInstance?.context?.globalVariables;
});

// 檢查是否有之前的分析結果
onMounted(async () => {
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    analysisResult.value = previousData.detail;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 400px;
  overflow-y: auto;
}

.result-container pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

pre {
  max-height: 200px;
  overflow-y: auto;
}

.data-source-status {
  @apply bg-blue-50 p-3 rounded mb-4;
}

.data-source-item {
  @apply flex items-center justify-between mb-2;
}

.data-source-label {
  @apply text-sm text-gray-600;
}

.data-source-value {
  @apply text-sm font-medium;
}
</style>
