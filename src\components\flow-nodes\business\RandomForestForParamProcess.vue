<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 參數選擇 -->
    <div class="p-2 rounded-lg bg-blue-50 dark:bg-dark-secondary">
      <el-form label-position="left">
        <el-form-item
          class="!mb-0"
          label="迭代次數"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-select
              v-model="nodeData_value.iterations"
              placeholder="請選擇迭代次數"
              clearable>
              <el-option
                v-for="item in [20, 40, 60, 80, 100]"
                :key="item"
                :label="item"
                :value="item" />
            </el-select>
          </template>
          <template v-else>
            <div class="p-2 bg-gray-100 dark:bg-gray-700 rounded text-sm">
              {{ nodeData_value.iterations }}
            </div>
          </template>
        </el-form-item>
      </el-form>
    </div>
    <!-- 分析結果區域 -->
    <div v-if="nodeData_value.chartData">
      <el-divider content-position="left">分析結果</el-divider>
      <!-- 隨機森林圖表-->
      <div class="mb-4">
        <Chart
          width="auto"
          height="400px"
          :options="chartOption" />
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted, computed } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "Random Forest參數分析",
  },
  description: {
    type: String,
    default: "使用Random Forest分析參數重要性",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("ml-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// 節點預設數據
const nodeData_default = {
  chartData: null,
  iterations: 100,
};

// 節點數據結構
const nodeData_value = ref({
  chartData: null,
  iterations: 100,
});

// 建立圖表配置
const chartOption = computed(() => {
  const data = nodeData_value.value?.chartData;
  if (!data) {
    printLog(props.title, "沒有chartData資料");
    return {};
  }

  // 從API回應中提取數據(反轉順序)
  const featureImportance = [...(data || [])].reverse();
  // 提取特徵名稱和重要性值
  const features = featureImportance.map((item) => item.Feature);
  const values = featureImportance.map((item) => item.Importance);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        const data = params[0];
        return `${data.name}: ${(data.value * 100).toFixed(2)}%`;
      },
    },
    grid: {
      left: "30%",
      right: "10%",
      bottom: "25%",
    },
    xAxis: {
      type: "value",
      axisLabel: {
        rotate: props.isReportMode || nodeRef.value.isFullscreen ? 0 : 45,
        color: isDark.value ? "#fff" : "#909399",
        formatter: function (value) {
          return (value * 100).toFixed(0) + "%";
        },
      },
    },
    yAxis: {
      type: "category",
      data: features,
      axisLabel: {
        color: isDark.value ? "#fff" : "#909399",
        formatter: function (value) {
          if (props.isReportMode || nodeRef.value.isFullscreen) {
            return value;
          }
          return value.length > 10 ? value.substring(0, 10) + "..." : value;
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { readOnly: true },
      },
    },
    series: [
      {
        name: "重要性",
        type: "bar",
        data: values,
        itemStyle: {
          color: "#3498db",
        },
        label: {
          show: true,
          position: "right",
          formatter: function (params) {
            return (params.value * 100).toFixed(2) + "%";
          },
        },
      },
    ],
  };
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  const variables =
    mergedPreviousOutputs.continuousVariables ||
    globalVariables?.continuousVariables ||
    [];
  const target = mergedPreviousOutputs.target || globalVariables?.target || "";

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」!`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」!`,
    !work_order || work_order.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「變數」!`,
    !variables || variables.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「迭代次數」!`,
    !nodeData_value.value.iterations || nodeData_value.value.iterations < 1
  );

  // 調用API
  const result = await request.post(
    "/external/iym/machine_learning/random_forest_categorical_for_parameter",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
      variables: variables,
      target: target,
      iterations: nodeData_value.value.iterations,
    }
  );

  if (!Array.isArray(result) || result.length === 0) {
    throw new Error("Random Forest分析未返回有效數據");
  }
  await updateGlobalVariable("feature_importance_randomForest", result);

  nodeData_value.value.chartData = result;

  // 構建完整的結果對象
  const finalResult = {
    chartData: result,
    iterations: nodeData_value.value.iterations,
    timestamp: new Date().toISOString(),
  };

  return finalResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData = previousData.detail.chartData || [];
    nodeData_value.value.iterations = previousData.detail.iterations || 100;
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
