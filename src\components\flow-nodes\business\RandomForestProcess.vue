<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :help-content="helpContent"
    :header-bg-color="isDark ? '#6d0015' : '#5ec480'"
    :icon="AlignStartVertical"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 參數設定區域 -->
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">Random Forest</h3>
        <el-form
          label-position="top"
          size="small">
          <el-form-item label="不良原因">
            <el-select
              v-model="formData.defectCode"
              placeholder="請選擇不良原因"
              class="w-full">
              <el-option
                v-for="code in formData.defectCodeOptions"
                :key="code"
                :label="code"
                :value="code" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 分析結果區域 -->
      <div v-if="chartReady && nodeContext?.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 隨機森林圖 -->
          <div class="mb-4">
            <Chart
              width="auto"
              height="400px"
              :options="chartOption" />
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from "vue";
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { AlignStartVertical } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { request } from "@/api/request";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";
import Chart from "@/components/chart.vue";

// 要自定義節點的header背景顏色的話就用下面程式碼，沒寫就會取 baseNode 的 header-bg-color
import { useThemeStore } from "@/stores/theme";

const themeStore = useThemeStore();
const isDark = computed(() => themeStore.isDark);

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "Random Forest",
  },
  description: {
    type: String,
    default: "隨機森林分析",
  },
  helpContent: {
    type: String,
    default: `## 隨機森林分析

### 邏輯概念：
隨機森林是一種集成學習方法，透過建立多棵隨機抽樣的決策樹，並以多數決方式進行預測。每棵樹都在不同的資料與特徵子集上訓練，能有效降低過擬合，提升模型穩定性。

### 適用情境：
- 分析多個製程參數對不良率的影響
- 特徵重要性排序
- 資料有非線性關係或雜訊時

### 可用來做什麼：
- 預測不良率
- 找出關鍵製程參數
- 輔助製程優化與異常檢測`,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點狀態
const nodeRef = ref(null);
const chartReady = ref(false);

// 初始化 nodeContext
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  defectCodeOptions: [],
  defectCode: "",
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "ml-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 建立圖表配置
const chartOption = computed(() => {
  const data = nodeContext.value?.output;
  if (!data?.feature_importance) {
    console.warn("No feature_importance data available");
    return {};
  }

  // 從API回應中提取數據(反轉順序)
  const featureImportance = [...(data.feature_importance || [])].reverse();
  // 提取特徵名稱和重要性值
  const features = featureImportance.map((item) => item.Feature);
  const values = featureImportance.map((item) => item.Importance);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        const data = params[0];
        return `${data.name}: ${(data.value * 100).toFixed(2)}%`;
      },
    },
    grid: {
      left: "100px",
      right: "5%",
      bottom: "10%",
    },
    xAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value) {
          return (value * 100).toFixed(0) + "%";
        },
      },
    },
    yAxis: {
      type: "category",
      data: features,
      axisLabel: {
        rotate: 50,
        formatter: function (value) {
          return value.length > 15 ? value.substring(0, 12) + "..." : value;
        },
      },
    },
    series: [
      {
        name: "重要性",
        type: "bar",
        data: values,
        itemStyle: {
          color: "#3498db",
        },
        label: {
          show: true,
          position: "right",
          formatter: function (params) {
            return (params.value * 100).toFixed(2) + "%";
          },
        },
      },
    ],
  };
});

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `Random Forest節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 由先前節點獲取參數 - defectCodeOptions
  formData.value.defectCodeOptions =
    globalVariables?.defectCodeOptions ||
    context.sourceNodeOutput?.result.map((r) => r["不良大項代碼"]) ||
    [];
  if (formData.value.defectCodeOptions.length < 1) {
    throw new Error("取得不良原因選項失敗，請檢查全域變數或前一個節點的輸出");
  }
  await updateGlobalVariable(
    "defectCodeOptions",
    formData.value.defectCodeOptions
  );

  // 由先前節點獲取參數 - defectCode
  formData.value.defectCode =
    context.sourceNodeOutput?.defectCode || globalVariables?.defectCode || "";
  // TODO DEMO預設S03(當前只有限定條件能查獲資料)! 未來要刪除，等候「餐與者」設置好，由user選取
  if (formData.value.defectCode === "") {
    logger.warn(props.title, "未選擇不良原因，使用預設值 S03");
    formData.value.defectCode = "S03";
  }
  if (formData.value.defectCode === "") {
    throw new Error("請選擇不良原因");
  }
  logger.debug(props.title, "選擇的不良原因:", formData.value.defectCode);
  await updateGlobalVariable("defectCode", formData.value.defectCode);

  // 由先前節點獲取參數 - other
  const partNo =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  const startDate =
    context.sourceNodeOutput?.startDate || globalVariables?.startDate || "";
  const endDate =
    context.sourceNodeOutput?.endDate || globalVariables?.endDate || "";
  const processes =
    context.sourceNodeOutput?.processes || globalVariables?.processes || [];
  const defectCode = formData.value.defectCode;

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: partNo,
    開始日期: startDate,
    結束日期: endDate,
    製程: processes.length > 0,
    不良原因: defectCode,
  };

  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingParams.length > 0) {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行輸入節點並選擇不良原因`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  try {
    // 準備處理函數
    const processFunction = async () => {
      // 使用所有製程進行分析
      const processName = processes.join(",");

      const result = await request.post(
        "/external/iym/machine_learning/random_forest",
        {
          part_no: partNo,
          start_date: startDate,
          end_date: endDate,
          process_name: processName,
          defect_code: defectCode,
        }
      );

      if (!result?.feature_importance) {
        throw new Error("Random Forest分析未返回有效數據");
      }

      // 構建完整的結果對象
      const completeResult = {
        result,
        defectCode,
        defectCodeOptions: formData.value.defectCodeOptions,
        partNo,
        startDate,
        endDate,
        processName,
        feature_importance: result.feature_importance,
        timestamp: new Date().toISOString(),
      };

      // 更新nodeContext
      nodeContext.value = {
        ...nodeContext.value,
        output: completeResult,
      };

      return completeResult;
    };

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);

    // 確保圖表更新
    chartReady.value = false;
    await nextTick();
    chartReady.value = true;

    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const sharedData = getSharedData(props.id);
  if (sharedData && sharedData.detail) {
    logger.info(props.title, "之前的分析結果:", sharedData);

    formData.value.defectCode = sharedData.detail.defectCode || "";
    formData.value.defectCodeOptions =
      sharedData.detail.defectCodeOptions || [];

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: sharedData.detail.result,
    };

    // 設置圖表準備好顯示
    chartReady.value = true;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 800px;
  overflow-y: auto;
}
</style>
