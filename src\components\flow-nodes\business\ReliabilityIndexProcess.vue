<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 分析結果區域 -->
    <div
      v-if="
        nodeData_value.pairResults && nodeData_value.pairResults.length > 0
      ">
      <el-divider content-position="left">分析結果</el-divider>
      <h4 class="text-lg font-semibold mb-2">最佳可靠性指數配對</h4>
      <div class="p-4 bg-blue-50 dark:bg-dark-secondary rounded-lg">
        <div class="flex justify-between items-center">
          <span class="text-gray-700 dark:text-white">{{
            nodeData_value.pairResults[0].title
          }}</span>
          <span class="text-xl font-bold text-blue-600 dark:text-blue-200">
            {{ nodeData_value.pairResults[0].reliability_index.toFixed(2) }}
          </span>
        </div>
      </div>

      <el-divider content-position="left">各演算法配對之信心指數</el-divider>
      <div
        v-for="(pairResult, index) in nodeData_value.pairResults"
        :key="index"
        class="mb-6 p-4 border rounded-lg">
        <div class="flex justify-between items-center mb-2">
          <strong class="text-lg">{{ pairResult.title }}</strong>
          <span class="font-semibold">
            {{ pairResult.reliability_index.toFixed(2) }}
          </span>
        </div>
        <div class="mt-2">
          <h5 class="font-medium mb-1">共同特徵:</h5>
          <ul class="list-disc list-inside pl-4">
            <li
              v-for="feature in pairResult.features"
              :key="feature"
              class="text-sm text-gray-600 dark:text-gray-200">
              {{ feature }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ElMessage } from "element-plus";
import { ref, onMounted } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "可靠性指數評估",
  },
  description: {
    type: String,
    default: "對多個演算法進行可靠性指數評估",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  extractPreviousNodeDataAsync,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  pairResults: [],
};

// 節點數據結構
const nodeData_value = ref({
  pairResults: [],
});

const getFeatureData = (mergedPreviousOutputs, globalVariables) => {
  return {
    feature_importance_logisticRegression:
      mergedPreviousOutputs.feature_importance_logisticRegression ||
      globalVariables?.feature_importance_logisticRegression ||
      "",
    feature_importance_xgboost:
      mergedPreviousOutputs.feature_importance_xgboost ||
      globalVariables?.feature_importance_xgboost ||
      "",
    feature_importance_randomForest:
      mergedPreviousOutputs.feature_importance_randomForest ||
      globalVariables?.feature_importance_randomForest ||
      "",
  };
};
const getValidate = (
  feature_importance_logisticRegression,
  feature_importance_xgboost,
  feature_importance_randomForest
) => {
  // 檢查必要參數 - 至少需要兩個參數
  const validParams = [
    feature_importance_logisticRegression &&
      feature_importance_logisticRegression.length > 0,
    feature_importance_xgboost && feature_importance_xgboost.length > 0,
    feature_importance_randomForest &&
      feature_importance_randomForest.length > 0,
  ].filter(Boolean);
  return validParams.length < 2;
};
const validateFunc = (mergedPreviousOutputs, globalVariables) => {
  const {
    feature_importance_logisticRegression,
    feature_importance_xgboost,
    feature_importance_randomForest,
  } = getFeatureData(mergedPreviousOutputs, globalVariables);
  return getValidate(
    feature_importance_logisticRegression,
    feature_importance_xgboost,
    feature_importance_randomForest
  );
};
const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  let {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  // const feature_importance_logisticRegression =
  //   mergedPreviousOutputs.feature_importance_logisticRegression ||
  //   globalVariables?.feature_importance_logisticRegression ||
  //   "";
  // const feature_importance_xgboost =
  //   mergedPreviousOutputs.feature_importance_xgboost ||
  //   globalVariables?.feature_importance_xgboost ||
  //   "";
  // const feature_importance_randomForest =
  //   mergedPreviousOutputs.feature_importance_randomForest ||
  //   globalVariables?.feature_importance_randomForest ||
  //   "";
  let {
    feature_importance_logisticRegression,
    feature_importance_xgboost,
    feature_importance_randomForest,
  } = getFeatureData(mergedPreviousOutputs, globalVariables);
  // printLog("LogisticRegression特徵值:", feature_importance_logisticRegression);
  // printLog("XGBoost特徵值:", feature_importance_xgboost);
  // printLog("RandomForest特徵值:", feature_importance_randomForest);

  if (
    // 檢查必要參數
    getValidate(
      feature_importance_logisticRegression,
      feature_importance_xgboost,
      feature_importance_randomForest
    )
  ) {
    // 使用異步版本確保數據一致性
    ({
      mergedPreviousOutputs,
      previousNodesDetails,
      globalVariables,
      previousNodeIds,
    } = await extractPreviousNodeDataAsync(validateFunc));
    printLog("前置節點IDs(Async):", previousNodeIds);
    printLog("合併後的前置節點輸出(Async):", mergedPreviousOutputs);
    printLog("前置節點詳細信息(Async):", previousNodesDetails);
    printLog("全局變量(Async):", globalVariables);
    // 再次取出資料
    ({
      feature_importance_logisticRegression,
      feature_importance_xgboost,
      feature_importance_randomForest,
    } = getFeatureData(mergedPreviousOutputs, globalVariables));
    // 再次檢查必要參數
    validateRequiredFields(
      props.title,
      `特徵值「RandomForest、XGBoost、LogisticRegression」至少需要兩個!`,
      getValidate(
        feature_importance_logisticRegression,
        feature_importance_xgboost,
        feature_importance_randomForest
      )
    );
  }

  // 根據可用參數動態執行API調用
  const apiCalls = [];
  const pairResults = [];

  // 檢查哪些參數對可以執行
  if (
    feature_importance_logisticRegression &&
    feature_importance_logisticRegression.length > 0 &&
    feature_importance_xgboost &&
    feature_importance_xgboost.length > 0
  ) {
    apiCalls.push({
      title: "Logistic vs XGB",
      call: request.post("/external/iym/machine_learning/reliability_index", {
        feature_importance_1: feature_importance_logisticRegression,
        feature_importance_2: feature_importance_xgboost,
      }),
    });
  }

  if (
    feature_importance_logisticRegression &&
    feature_importance_logisticRegression.length > 0 &&
    feature_importance_randomForest &&
    feature_importance_randomForest.length > 0
  ) {
    apiCalls.push({
      title: "Logistic vs RF",
      call: request.post("/external/iym/machine_learning/reliability_index", {
        feature_importance_1: feature_importance_logisticRegression,
        feature_importance_2: feature_importance_randomForest,
      }),
    });
  }

  if (
    feature_importance_xgboost &&
    feature_importance_xgboost.length > 0 &&
    feature_importance_randomForest &&
    feature_importance_randomForest.length > 0
  ) {
    apiCalls.push({
      title: "XGB vs RF",
      call: request.post("/external/iym/machine_learning/reliability_index", {
        feature_importance_1: feature_importance_xgboost,
        feature_importance_2: feature_importance_randomForest,
      }),
    });
  }

  // 執行所有可用的API調用
  const results = await Promise.all(apiCalls.map((item) => item.call));

  // 構建結果陣列
  for (let i = 0; i < results.length; i++) {
    pairResults.push({
      title: apiCalls[i].title,
      reliability_index: results[i].reliability_index,
      features: results[i].common_features || [],
    });
  }

  // 按可靠性指數排序
  pairResults.sort((a, b) => b.reliability_index - a.reliability_index);

  await updateGlobalVariable("reliability_index", pairResults);

  // 更新組件的響應式數據
  nodeData_value.value.pairResults = pairResults;

  // 構建結果對象
  const result = {
    pairResults: pairResults,
    timestamp: new Date().toISOString(),
  };

  return result;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  printLog("previousData", previousData);
  if (previousData && previousData.detail) {
    nodeData_value.value.pairResults = previousData.detail.pairResults || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
