<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="330"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <div class="p-4">
      <div class="mb-4">
        <el-form
          label-position="top"
          size="small">
          <el-form-item>
            <template v-if="!isReportMode">
              <el-select
                v-model="nodeData_value.continuousVariables"
                placeholder="請選擇連續變數"
                class="w-full"
                multiple>
                <el-option
                  v-for="variable in nodeData_value.continuousVariableOptions"
                  :key="variable"
                  :label="variable"
                  :value="variable" />
              </el-select>
            </template>
            <template v-else>
              <div
                class="py-2 px-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                <template
                  v-if="
                    nodeData_value.continuousVariables &&
                    nodeData_value.continuousVariables.length
                  ">
                  {{ nodeData_value.continuousVariables.join(", ") }}
                </template>
                <template v-else> 未選擇連續變數 </template>
              </div>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日誌, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "選取連續變數",
  },
  description: {
    type: String,
    default: "將根據選擇的連續變數進行接續的分析",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// 節點預設數據
const nodeData_default = {
  continuousVariableOptions: [],
  continuousVariables: [],
};

// 節點數據結構
const nodeData_value = ref({
  continuousVariableOptions: [...nodeData_default.continuousVariableOptions],
  continuousVariables: [...nodeData_default.continuousVariables],
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  // printLog("品目:", part_no);
  // printLog("工單:", work_order);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止查取連續變數清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止查取連續變數清單`,
    !work_order || work_order.length < 1
  );

  nodeData_value.value.continuousVariableOptions = await request.post(
    "/external/iym/main_method/variables_list",
    {
      resource_type: resource_type,
      part_no: part_no,
      work_order: work_order.join(","),
    }
  );
  printLog(
    "continuousVariableOptions:",
    nodeData_value.value.continuousVariableOptions
  );
  if (
    !Array.isArray(nodeData_value.value.continuousVariableOptions) ||
    nodeData_value.value.continuousVariableOptions.length === 0
  ) {
    throw new Error("取得連續變數選項未返回有效數據");
  }
  await updateGlobalVariable(
    "continuousVariableOptions",
    nodeData_value.value.continuousVariableOptions
  );

  // TODO DEMO預設! 未來要刪除，等候「參與者」設置好，由user選取
  nodeData_value.value.continuousVariables = [
    "TEST_NUMBER",
    "solder_life",
    "PRINT_SPEED",
    "PRINT_PRESSURE",
    "SNAP_OFF_DISTANCE",
    "SNAP_OFF_SPEED",
    "TEMPRATURE",
    "HUMIDITY",
    "TD",
    "MD",
    "tension_mean",
    "tension_range",
    "SQUEEGEE_SUM_COUNT",
  ].filter((variable) =>
    nodeData_value.value.continuousVariableOptions.includes(variable)
  );
  await updateGlobalVariable(
    "continuousVariables",
    nodeData_value.value.continuousVariables
  );

  // 構建結果對象
  const result = {
    continuousVariableOptions: nodeData_value.value.continuousVariableOptions,
    continuousVariables: nodeData_value.value.continuousVariables,
    part_no,
    work_order,
    timestamp: new Date().toISOString(),
  };

  return result;
};

// 檢查是否有之前的輸入數據並初始化
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.continuousVariables =
      previousData.detail.continuousVariables || "";
    nodeData_value.value.continuousVariableOptions =
      previousData.detail.continuousVariableOptions || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
