<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="SquareDashedMousePointer"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">選取不良原因</h3>
        <el-form
          label-position="top"
          size="small">
          <el-form-item label="不良原因">
            <el-select
              v-model="formData.defectCode"
              placeholder="請選擇不良原因"
              class="w-full">
              <el-option
                v-for="code in formData.defectCodeOptions"
                :key="code"
                :label="code"
                :value="code" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { SquareDashedMousePointer } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "選取不良原因",
  },
  description: {
    type: String,
    default: "將根據選擇的不良原因，預備進行接續的分析",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 350,
  },
  nodeHeight: {
    type: Number,
    default: 200,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: false,
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點引用
const nodeRef = ref(null);

// 表單數據
const formData = ref({
  defectCodeOptions: [],
  defectCode: "",
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "selection-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  getSharedData,
  updateSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 事件處理
const emit = defineEmits(["handle-connect", "handle-disconnect"]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 數據處理函數
const processData = async (inputData) => {
  const result = {
    detail: { ...formData.value },
    timestamp: new Date().toISOString(),
    nodeId: props.id,
    nodeName: props.title,
  };

  await updateSharedData(props.id, result);
  return result;
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `選取不良原因節點 handleRun 被調用`);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  try {
    // 由先前節點獲取參數 - defectCodeOptions
    formData.value.defectCodeOptions =
      context.sourceNodeOutput?.result.map((r) => r["不良大項代碼"]) || [];
    if (formData.value.defectCodeOptions.length < 1) {
      throw new Error("取得不良原因選項失敗，請檢查前一個節點的輸出");
    }
    await updateGlobalVariable(
      "defectCodeOptions",
      formData.value.defectCodeOptions
    );

    // TODO DEMO預設S03(當前只有限定條件能查獲資料)! 未來要刪除，等候「餐與者」設置好，由user選取
    if (formData.value.defectCode === "") formData.value.defectCode = "S03";
    if (formData.value.defectCode === "") {
      throw new Error("請選擇不良原因");
    }
    await updateGlobalVariable("defectCode", formData.value.defectCode);

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processData);

    logger.info(props.title, "節點執行完成");

    return result;
  } catch (error) {
    logger.error(props.title, "執行失敗", error);
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的輸入數據並初始化
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的輸入數據
  const sharedData = await getSharedData(props.id);
  // console.log("sharedData", sharedData);
  if (sharedData?.detail) {
    formData.value.defectCode = sharedData.detail.defectCode || "";
    formData.value.defectCodeOptions =
      sharedData.detail.defectCodeOptions || [];
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>
