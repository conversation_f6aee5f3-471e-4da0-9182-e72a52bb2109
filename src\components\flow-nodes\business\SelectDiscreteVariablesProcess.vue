<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="350"
    :execution-mode="executionMode"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <div class="p-4">
      <div class="mb-4">
        <el-form
          label-position="top"
          size="small">
          <el-form-item>
            <template v-if="!isReportMode">
              <el-select
                v-model="nodeData_value.discreteVariables"
                placeholder="請選擇離散變數"
                class="w-full"
                multiple
                @change="handleSelectionChange">
                <el-option
                  v-for="variable in nodeData_value.discreteVariableOptions"
                  :key="variable"
                  :label="variable"
                  :value="variable" />
              </el-select>

              <!-- 驗證訊息 -->
              <div
                v-if="validationMessage"
                class="mt-2">
                <el-alert
                  :title="validationMessage"
                  type="warning"
                  show-icon />
              </div>
            </template>
            <template v-else>
              <div
                class="py-2 px-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                <template
                  v-if="
                    nodeData_value.discreteVariables &&
                    nodeData_value.discreteVariables.length
                  ">
                  {{ nodeData_value.discreteVariables.join(", ") }}
                </template>
                <template v-else> 未選擇離散變數 </template>
              </div>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { EXECUTION_MODES } from "@/constants/executionModes";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "選取離散變數",
  },
  description: {
    type: String,
    default: "將根據選擇的離散變數進行接續的分析",
  },
  executionMode: {
    type: String,
    default: EXECUTION_MODES.INTERACTIVE,
    validator: (value) => Object.values(EXECUTION_MODES).includes(value),
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  discreteVariableOptions: [],
  discreteVariables: [],
};

// 節點數據結構
const nodeData_value = ref({
  discreteVariableOptions: [...nodeData_default.discreteVariableOptions],
  discreteVariables: [...nodeData_default.discreteVariables],
});

// 互動相關狀態
const validationMessage = ref("");

// 標準節點處理函數 - 支援互動檢查邏輯
const processFunction = async (inputData) => {
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);

  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 獲取必要參數
  const resource_type =
    mergedPreviousOutputs.resourceType || globalVariables?.resourceType || "";
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止查取離散變數清單`,
    !part_no || part_no.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「工單」! 終止查取離散變數清單`,
    !work_order || work_order.length < 1
  );

  // 獲取離散變數選項（如果還沒有）
  if (nodeData_value.value.discreteVariableOptions.length === 0) {
    printLog("獲取離散變數選項...");
    const discreteVariableOptions = await request.post(
      "/external/iym/main_method/variables_list",
      {
        resource_type: resource_type,
        part_no: part_no,
        work_order: work_order.join(","),
      }
    );

    printLog("discreteVariableOptions:", discreteVariableOptions);

    if (
      !Array.isArray(discreteVariableOptions) ||
      discreteVariableOptions.length === 0
    ) {
      throw new Error("取得離散變數選項未返回有效數據");
    }

    nodeData_value.value.discreteVariableOptions = discreteVariableOptions;

    await updateGlobalVariable(
      "discreteVariableOptions",
      discreteVariableOptions
    );
  }

  // 檢查互動需求是否滿足
  const interactionCheck = checkInteractionNeeded();
  if (interactionCheck.required) {
    printLog("需要用戶互動:", interactionCheck.reason);
    validationMessage.value = interactionCheck.reason;

    // 中斷執行，等待用戶互動
    return {
      requiresInteraction: true,
    };
  }

  // 互動需求已滿足，繼續執行節點功能
  printLog("互動需求已滿足，執行節點功能");
  validationMessage.value = "";

  // 保存用戶選擇到全局變數
  await updateGlobalVariable(
    "discreteVariables",
    nodeData_value.value.discreteVariables
  );

  // 構建最終結果
  const result = {
    discreteVariableOptions: nodeData_value.value.discreteVariableOptions,
    discreteVariables: nodeData_value.value.discreteVariables,
    part_no,
    work_order,
    resource_type,
    timestamp: new Date().toISOString(),
  };

  printLog("節點執行完成，結果:", result);
  return result;
};

// 檢查用戶互動是否完成
const checkInteractionNeeded = () => {
  if (
    nodeData_value.value.discreteVariableOptions.length > 0 &&
    nodeData_value.value.discreteVariables.length === 0
  ) {
    return {
      required: true,
      reason: "請至少選擇一個離散變數",
    };
  }

  return {
    required: false,
    reason: null,
  };
};

// 互動處理方法
const handleSelectionChange = () => {
  validationMessage.value = "";
  printLog("用戶選擇變更:", nodeData_value.value.discreteVariables);
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.discreteVariables =
      previousData.detail.discreteVariables || [];
    nodeData_value.value.discreteVariableOptions =
      previousData.detail.discreteVariableOptions || [];
  }
});
// ==============END 節點自定義區==============

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
