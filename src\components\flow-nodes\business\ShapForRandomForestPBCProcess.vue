<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <div class="p-4">
      <div class="mb-4">
        <!-- 缺陷代碼選擇 -->
        <el-form
          label-position="top"
          size="small">
          <el-form-item label="缺陷代碼">
            <template v-if="!isReportMode">
              <el-select
                v-model="nodeData_value.defectCode"
                placeholder="請選擇缺陷代碼"
                class="w-full">
                <el-option
                  v-for="option in nodeData_value.defectCodeOptions"
                  :key="option"
                  :label="option"
                  :value="option" />
              </el-select>
            </template>
            <template v-else>
              <div
                class="py-2 px-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                <template v-if="nodeData_value.defectCode">
                  {{ nodeData_value.defectCode }}
                </template>
                <template v-else> 未選擇缺陷代碼 </template>
              </div>
            </template>
          </el-form-item>
        </el-form>

        <!-- 分析結果區域 -->
        <div v-if="nodeData_value.chartData">
          <el-divider content-position="left">分析結果</el-divider>
          <div>
            <div
              v-for="(processData, processIndex) in groupedChartData"
              :key="processIndex"
              class="mb-6">
              <div
                v-for="(chart, chartIndex) in processData.charts"
                :key="chartIndex"
                class="mb-4">
                <Chart
                  width="auto"
                  height="280px"
                  :options="getChartOption(chart, processData.processName)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, computed, onMounted } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "SHAP表 - Random Forest(PBC)",
  },
  description: {
    type: String,
    default: "各製程對不良率的貢獻度",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  chartData: null,
  process: [], // y 軸製程名稱
  shapValues: [], // x 軸 SHAP 值
  defectCodeOptions: [],
  defectCode: "",
};

// 節點數據結構
const nodeData_value = ref({
  chartData: nodeData_default.chartData,
  process: [...nodeData_default.process],
  shapValues: [...nodeData_default.shapValues],
  defectCodeOptions: [...nodeData_default.defectCodeOptions],
  defectCode: nodeData_default.defectCode,
});

// 圖表數據處理
const groupedChartData = computed(() => {
  if (!nodeData_value.value.chartData) {
    return [];
  }

  // 將數據組織成所需的格式
  return [
    {
      processName: "SHAP Values Distribution",
      charts: [nodeData_value.value.chartData],
    },
  ];
});

// 獲取圖表選項
const getChartOption = (chartData, processName) => {
  return {
    title: {
      text: processName,
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 暗色模式使用白色，亮色模式使用深灰色
      },
    },
    legend: {
      data: ["SHAP值"],
      left: "right",
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 同步調整圖例顏色
      },
    },
    tooltip: {
      position: "top",
      formatter: function (params) {
        return (
          "SHAP值: " +
          params.value[2] +
          "<br/>" +
          nodeData_value.value.process[params.value[0]] +
          " / " +
          nodeData_value.value.shapValues[params.value[1]]
        );
      },
    },
    grid: {
      left: 2,
      bottom: 10,
      right: 60, // 增加右側空間以容納熱力條
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: nodeData_value.value.shapValues,
      boundaryGap: false,
      splitLine: {
        show: true,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "category",
      data: nodeData_value.value.process,
      axisLine: {
        show: false,
      },
    },
    visualMap: {
      min: 0,
      max: 15, // 根據您的數據可能需要調整
      calculable: true,
      orient: "vertical",
      right: 0,
      top: "center",
      inRange: {
        color: ["#3b5af9", "#f23c55"], // 從藍色到紅色的漸變
        symbolSize: [10, 30],
      },
      text: ["High", "Low"], // 添加高低值標籤
      textStyle: {
        color: isDark ? "#ffffff" : "#333333", // 暗色模式使用白色，亮色模式使用深灰色
      },
    },
    series: [
      {
        name: "SHAP值",
        type: "scatter",
        symbolSize: function (val) {
          return val[2] * 2;
        },
        data: chartData,
        itemStyle: {
          color: function (params) {
            // 根據值大小決定顏色
            const value = params.value[2];
            const max = 15; // 和visualMap設置的max一致
            const ratio = Math.min(value / max, 1);
            // 從藍色(#3b5af9)過渡到紅色(#f23c55)
            return {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: "#3b5af9" }, // 藍色
                { offset: ratio, color: "#f23c55" }, // 紅色
              ],
            };
          },
        },
        animationDelay: function (idx) {
          return idx * 5;
        },
      },
    ],
  };
};

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const partNo = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const startDate =
    mergedPreviousOutputs.startDate || globalVariables?.startDate || "";
  const endDate =
    mergedPreviousOutputs.endDate || globalVariables?.endDate || "";
  const processes =
    mergedPreviousOutputs.processes || globalVariables?.processes || "";

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止SHAP表取得`,
    !partNo || partNo.length < 1
  );
  validateRequiredFields(
    props.title,
    `缺少必要參數「製程」! 終止SHAP表取得`,
    !processes || processes.length < 1
  );

  // TODO DEMO預設! 未來要刪除，等候「餐與者」設置好，由user選取
  nodeData_value.value.defectCodeOptions = ["S03", "S04", "S05"];
  nodeData_value.value.defectCode = nodeData_value.value.defectCodeOptions[0];

  const response = await request.post("/external/iym/iym_rf_shap_summary", {
    start_date: startDate,
    end_date: endDate,
    part_no: partNo,
    process_name: processes.join(","),
    defect_code: nodeData_value.value.defectCode,
  });
  printLog("API response:", response);
  const apiData = response?.rf_shap_summary;
  if (!apiData) {
    ElMessage.error("API返回數據格式錯誤");
    return;
  }
  const { feature_names, shap_values, feature_values } = apiData;

  // 將SHAP值分為5個區間
  const getAllShapValues = () => {
    const allValues = shap_values.flat();
    const min = Math.min(...allValues);
    const max = Math.max(...allValues);
    const step = (max - min) / 5;

    return Array.from({ length: 5 }, (_, i) => {
      const rangeStart = min + i * step;
      const rangeEnd = min + (i + 1) * step;
      return `${rangeStart.toFixed(3)} ~ ${rangeEnd.toFixed(3)}`;
    });
  };

  // 構建圖表數據
  const tempChatData = [];
  shap_values.forEach((shapRow, rowIndex) => {
    shapRow.forEach((shapValue, colIndex) => {
      // 確定SHAP值所屬的區間
      const allValues = shap_values.flat();
      const min = Math.min(...allValues);
      const max = Math.max(...allValues);
      const step = (max - min) / 5;
      const rangeIndex = Math.min(Math.floor((shapValue - min) / step), 4);

      // 使用feature_values作為點的大小，並放大以便可見
      const pointSize = Math.abs(feature_values[rowIndex][colIndex]) * 100;

      tempChatData.push([
        colIndex, // X軸：feature索引
        rangeIndex, // Y軸：SHAP值範圍索引
        pointSize, // 點的大小
      ]);
    });
  });

  nodeData_value.value.chartData = tempChatData;
  nodeData_value.value.process = feature_names;
  nodeData_value.value.shapValues = getAllShapValues();

  await updateGlobalVariable(
    "shap_rf_chartData",
    nodeData_value.value.chartData
  );
  await updateGlobalVariable("shap_rf_process", nodeData_value.value.process);
  await updateGlobalVariable(
    "shap_rf_shapValues",
    nodeData_value.value.shapValues
  );

  // 構建結果對象
  const result = {
    chartData: nodeData_value.value.chartData,
    process: nodeData_value.value.process,
    shapValues: nodeData_value.value.shapValues,
    defectCodeOptions: nodeData_value.value.defectCodeOptions,
    defectCode: nodeData_value.value.defectCode,
    timestamp: new Date().toISOString(),
  };
  console.log("處理結果:", result);

  return result;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.chartData =
      previousData.detail.chartData || nodeData_default.chartData;
    nodeData_value.value.process = previousData.detail.process || [
      ...nodeData_default.process,
    ];
    nodeData_value.value.shapValues = previousData.detail.shapValues || [
      ...nodeData_default.shapValues,
    ];
    nodeData_value.value.defectCodeOptions = previousData.detail
      .defectCodeOptions || [...nodeData_default.defectCodeOptions];
    nodeData_value.value.defectCode =
      previousData.detail.defectCode || nodeData_default.defectCode;
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped></style>
