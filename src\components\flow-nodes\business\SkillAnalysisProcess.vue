<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="Brain"
    :node-width="nodeWidth"
    @run="handleRun">
    <div class="p-4 text-light-mode dark:text-dark-mode">
      <!-- 資料來源顯示 -->
      <div class="mb-4 bg-blue-50 dark:bg-dark-secondary p-3 rounded">
        <h3 class="dark:bg-dark-secondary text-sm font-medium mb-2">
          資料來源狀態
        </h3>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm">員工基本資料</span>
            <el-tag
              size="small"
              :type="hasEmployeeData ? 'success' : 'danger'">
              {{ hasEmployeeData ? "已載入" : "未載入" }}
            </el-tag>
            {{ globalVariables?.employeeData?.employeeName }}
          </div>

          <div
            v-if="employeeInfo"
            class="text-xs mt-1">
            當前分析員工：{{ employeeInfo }}
          </div>
        </div>
      </div>

      <!-- 參數設定區域 -->

      <!-- 分析結果區域 -->
      <div
        v-if="analysisResult"
        class="text-light-mode dark:text-dark-mode">
        <el-divider content-position="left">技能分析結果</el-divider>

        <!-- 技能缺口分析 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium mb-2">技能缺口分析</h4>
          <div class="p-4 rounded border space-y-4">
            <!-- 核心技能缺口 -->
            <div>
              <h5 class="text-sm font-medium mb-2">核心技能缺口</h5>
              <div class="space-y-2">
                <div
                  v-for="(gap, index) in analysisResult.coreSkillGaps"
                  :key="'core-' + index"
                  class="flex items-center justify-between p-2 rounded">
                  <span class="text-sm">{{ gap.skill }}</span>
                  <el-tag
                    size="small"
                    type="danger"
                    >缺口: {{ gap.gap }}%</el-tag
                  >
                </div>
              </div>
            </div>

            <!-- 進階技能缺口 -->
            <div>
              <h5 class="text-sm font-medium mb-2">進階技能缺口</h5>
              <div class="space-y-2">
                <div
                  v-for="(gap, index) in analysisResult.advancedSkillGaps"
                  :key="'advanced-' + index"
                  class="flex items-center justify-between p-2 rounded">
                  <span class="text-sm">{{ gap.skill }}</span>
                  <el-tag
                    size="small"
                    type="warning"
                    >缺口: {{ gap.gap }}%</el-tag
                  >
                </div>
              </div>
            </div>

            <!-- 技能優勢 -->
            <div>
              <h5 class="text-sm font-medium mb-2">技能優勢</h5>
              <div class="space-y-2">
                <div
                  v-for="(strength, index) in analysisResult.skillStrengths"
                  :key="'strength-' + index"
                  class="flex items-center justify-between p-2 bg-green-50 rounded">
                  <span class="text-sm">{{ strength.skill }}</span>
                  <el-tag
                    size="small"
                    type="success"
                    >超出: {{ strength.excess }}%</el-tag
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 培訓建議 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium mb-2">培訓建議</h4>
          <div class="p-4 rounded border">
            <ul class="list-disc list-inside space-y-2">
              <li
                v-for="(
                  suggestion, index
                ) in analysisResult.trainingSuggestions"
                :key="index"
                class="text-sm">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>

        <!-- 技能發展路徑 -->
        <div v-if="analysisResult.developmentPath">
          <h4 class="text-sm font-medium mb-2">技能發展路徑</h4>
          <div class="p-4 rounded border">
            <div class="space-y-3">
              <div
                v-for="(path, index) in analysisResult.developmentPath"
                :key="index"
                class="flex items-center">
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center font-medium">
                  {{ index + 1 }}
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium">
                    {{ path.skill }}
                  </div>
                  <div class="text-xs">
                    {{ path.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 錯誤信息展示 -->
      <!-- <div
        v-if="errorMessage"
        class="bg-red-50 p-3 rounded text-sm border border-red-200 mt-4">
        <div class="flex items-center justify-between text-red-500 mb-2">
          <div class="flex items-center">
            <i class="el-icon-warning mr-1"></i>
            <span>執行錯誤</span>
          </div>
          <el-button
            type="text"
            size="small"
            @click="toggleErrorDetails">
            {{ showErrorDetails ? "隱藏詳情" : "查看詳情" }}
          </el-button>
        </div>
        <p class="text-red-600">{{ formatErrorMessage(errorMessage) }}</p> -->

      <!-- 錯誤詳情 -->
      <!-- <div
          v-if="showErrorDetails && errorDetails"
          class="mt-2 pt-2 border-t border-red-200">
          <div
            v-if="errorDetails.suggestion"
            class="text-orange-600 mb-2">
            {{ errorDetails.suggestion }}
          </div>

          <div
            v-if="errorDetails.timestamp"
            class="flex justify-between text-xs mb-1">
            <span class="text-gray-600">發生時間:</span>
            <span>{{ formatTime(errorDetails.timestamp) }}</span>
          </div> -->

      <!-- 顯示完整錯誤信息 -->
      <!-- <div
            v-if="errorDetails.message"
            class="mt-2">
            <div class="text-xs text-gray-600 mb-1">完整錯誤信息:</div>
            <div
              class="text-xs text-red-600 p-2 bg-red-50 rounded overflow-auto max-h-24">
              {{ errorDetails.message }}
            </div>
          </div> 
        </div>

        <div class="mt-2 flex justify-end space-x-2">
          <el-button
            type="danger"
            size="small"
            plain
            @click="handleClearError">
            清除錯誤
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleRun">
            重試執行
          </el-button>
        </div>
        
      </div>
      -->
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { Brain } from "lucide-vue-next";
import { ElMessage } from "element-plus";
import { wait } from "@/utils/dateUtils";
import { useFlowStore } from "@/stores/flowStore"; //HACK: MUST HAVE
import { useThemeMode } from "@/composables/useThemeMode";

// 取得主題
const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  // console.log("SkillAnalysisProcessNode", title, result);
};

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "技能缺口分析",
  },
  description: {
    type: String,
    default: "分析員工技能與職位要求的差距，提供培訓建議",
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
});

// 節點狀態
const nodeRef = ref(null);
const analysisResult = ref(null);

// 新增狀態

const hasEmployeeData = ref(false);
const hasSkillData = ref(false);
const employeeInfo = ref(null);

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  //nodeType: "skill-analysis", // HACK: 這個可以移除
  nodeName: props.title,
  nodeRef: nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariables, //HACK: 注意是複數
} = nodeExecution;

// 分析技能數據
const analyzeSkills = (employeeDetail) => {
  if (!employeeDetail) {
    throw new Error("未獲取到員工數據");
  }

  printLog("開始分析技能數據", employeeDetail);

  // 模擬職位要求
  const positionRequirements = {
    coreSkills: [
      { skill: "Vue.js", required: 80 },
      { skill: "TypeScript", required: 70 },
      { skill: "Node.js", required: 75 },
    ],
    advancedSkills: [
      { skill: "微服務架構", required: 60 },
      { skill: "雲端部署", required: 65 },
      { skill: "DevOps", required: 55 },
    ],
  };

  // 從員工數據中獲取技能水平
  // 如果沒有明確的技能評分，則從培訓記錄中推斷
  const currentSkills = employeeDetail.skills || {};

  // 如果有培訓記錄，用它來補充或更新技能水平
  if (employeeDetail.trainingRecords) {
    employeeDetail.trainingRecords.forEach((record) => {
      // 根據培訓課程更新相關技能
      switch (record.course) {
        case "Vue.js 進階":
          currentSkills["Vue.js"] = Math.max(currentSkills["Vue.js"] || 0, 75);
          break;
        case "TypeScript 實戰":
          currentSkills["TypeScript"] = Math.max(
            currentSkills["TypeScript"] || 0,
            70
          );
          break;
        // 可以添加更多課程的映射
      }
    });
  }

  // 設置默認技能水平（如果沒有數據）
  const defaultSkills = {
    "Vue.js": 60,
    TypeScript: 55,
    "Node.js": 65,
    微服務架構: 40,
    雲端部署: 45,
    DevOps: 35,
  };

  // 合併默認技能和當前技能
  const finalSkills = { ...defaultSkills, ...currentSkills };

  // 分析核心技能缺口
  const coreSkillGaps = positionRequirements.coreSkills
    .map((req) => {
      const currentLevel = finalSkills[req.skill] || 0;
      return {
        skill: req.skill,
        current: currentLevel,
        required: req.required,
        gap: Math.max(0, req.required - currentLevel),
      };
    })
    .filter((gap) => gap.gap > 0);

  // 分析進階技能缺口
  const advancedSkillGaps = positionRequirements.advancedSkills
    .map((req) => {
      const currentLevel = finalSkills[req.skill] || 0;
      return {
        skill: req.skill,
        current: currentLevel,
        required: req.required,
        gap: Math.max(0, req.required - currentLevel),
      };
    })
    .filter((gap) => gap.gap > 0);

  // 分析技能優勢
  const skillStrengths = [
    ...positionRequirements.coreSkills,
    ...positionRequirements.advancedSkills,
  ]
    .map((req) => {
      const currentLevel = finalSkills[req.skill] || 0;
      return {
        skill: req.skill,
        current: currentLevel,
        required: req.required,
        excess: Math.max(0, currentLevel - req.required),
      };
    })
    .filter((strength) => strength.excess > 0);

  // 生成培訓建議
  const trainingSuggestions = [];
  if (coreSkillGaps.length > 0) {
    trainingSuggestions.push(
      `建議優先提升核心技能：${coreSkillGaps
        .map((gap) => gap.skill)
        .join("、")}`
    );
  }
  if (advancedSkillGaps.length > 0) {
    trainingSuggestions.push(
      `建議逐步提升進階技能：${advancedSkillGaps
        .map((gap) => gap.skill)
        .join("、")}`
    );
  }

  // 生成技能發展路徑
  const developmentPath = [
    ...coreSkillGaps.map((gap) => ({
      skill: `${gap.skill} 進階課程`,
      description: `完成 ${gap.skill} 進階課程，從 ${gap.current}% 提升至 ${gap.required}% 水平`,
    })),
    ...advancedSkillGaps.map((gap) => ({
      skill: `${gap.skill} 基礎課程`,
      description: `完成 ${gap.skill} 基礎課程，從 ${gap.current}% 提升至 ${gap.required}% 水平`,
    })),
  ].slice(0, 3); // 只取前三個最重要的發展路徑

  printLog("技能分析完成");

  return {
    coreSkillGaps,
    advancedSkillGaps,
    skillStrengths,
    trainingSuggestions,
    developmentPath,
    analysisTime: new Date().toISOString(),
  };
};

// HACK: 重點方法
const processFunction = async () => {
  printLog("開始處理技能數據");
  const context = useFlowStore().currentInstance?.context || {}; // HACK: 從 userStore 中獲取 context
  // 模擬處理過程x
  wait(1000);
  const { isValid, missingData } = checkRequiredData(context);

  if (!isValid) {
    throw new Error(`缺少必要資料：${missingData.join("、")}`);
  }

  // 從全域變數獲取員工數據
  const employeeDetail = context.globalVariables.employeeDetail;
  printLog("獲取到的員工數據", employeeDetail);

  // 分析技能數據
  const result = analyzeSkills(employeeDetail);

  printLog("技能數據分析完成", result);

  // 更新分析結果
  analysisResult.value = result;

  // 設置全域變數
  await updateGlobalVariables({
    skillAnalysis: result,
  });

  // 更新共享數據
  await updateSharedData(props.id, {
    detail: result,
    timestamp: new Date().toISOString(),
    completed: true,
    output: result,
  });

  return result;
};

// 執行節點
const handleRun = async (context = {}) => {
  printLog(`技能分析節點 handleRun 被調用`);
  printLog("上下文數據", context);

  // HACK: 交給父節點執行，error 會被父節點捕獲
  nodeRef.value.handleRun(() => processFunction(context));
};

const globalVariables = computed(() => {
  return useFlowStore().currentInstance?.context?.globalVariables;
});
// 檢查是否有之前的分析結果
onMounted(async () => {
  console.log("globalVariables", globalVariables.value);
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    analysisResult.value = previousData.detail;
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});

// 檢查必要資料是否存在
const checkRequiredData = (context) => {
  printLog("檢查資料時的上下文:", context);

  const employeeDetail = context.globalVariables?.employeeDetail;
  const employeeData = context.globalVariables?.employeeData;

  // 檢查員工基本資料
  hasEmployeeData.value = !!employeeDetail;

  // 檢查技能評估資料
  hasSkillData.value = !!(
    employeeDetail?.skills || employeeDetail?.trainingRecords
  );

  // 更新員工資訊顯示
  if (employeeDetail) {
    employeeInfo.value = `${context.globalVariables?.employeeId} - ${employeeDetail.department} ${employeeDetail.position}`;
  } else {
    employeeInfo.value = null;
  }

  // 記錄檢查結果
  printLog("資料檢查結果:", {
    hasEmployeeData: hasEmployeeData.value,
    hasSkillData: hasSkillData.value,
    employeeInfo: employeeInfo.value,
  });

  // 返回檢查結果
  return {
    isValid: hasEmployeeData.value && hasSkillData.value,
    missingData: [
      !hasEmployeeData.value && "員工基本資料",
      !hasSkillData.value && "技能評估資料",
    ].filter(Boolean),
  };
};
</script>

<style scoped>
.result-container {
  max-height: 400px;
  overflow-y: auto;
}

.result-container pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

pre {
  max-height: 200px;
  overflow-y: auto;
}

.data-source-status {
  @apply bg-blue-50 p-3 rounded mb-4;
}

.data-source-item {
  @apply flex items-center justify-between mb-2;
}

.data-source-label {
  @apply text-sm text-gray-600;
}

.data-source-value {
  @apply text-sm font-medium;
}
</style>
