<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :title="title"
    nodeType="custom-summary"
    :description="description"
    icon="ClipboardList"
    :header-bg-color="isDark ? '#2c3e50' : '#10B981'"
    :selected="selected"
    :min-height="500"
    :auto-height="autoHeight"
    @click="handleNodeClick"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4 space-y-4">
      <!-- 總結內容區域 -->
      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-4">
        <!-- 績效趨勢總結 -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            績效趨勢分析
          </h3>
          <div class="text-sm text-gray-600 dark:text-gray-300">
            {{ performanceSummary }}
          </div>
        </div>

        <!-- 技能差距總結 -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            技能差距分析
          </h3>
          <div class="text-sm text-gray-600 dark:text-gray-300">
            {{ skillGapSummary }}
          </div>
        </div>

        <!-- 發展建議 -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
            發展建議
          </h3>
          <div class="text-sm text-gray-600 dark:text-gray-300">
            <ul class="list-disc list-inside space-y-1">
              <li
                v-for="(suggestion, index) in developmentSuggestions"
                :key="index">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 執行按鈕 -->
      <div class="flex justify-end space-x-2">
        <el-button
          type="primary"
          size="small"
          :disabled="executing"
          :loading="executing"
          @click="handleRun">
          生成總結報告
        </el-button>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { ClipboardList } from "lucide-vue-next";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useThemeMode } from "@/composables/useThemeMode";
import { logger } from "@/utils/logger";

// 取得主題
const { isDark } = useThemeMode();

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "總結報告",
  },
  description: {
    type: String,
    default: "匯總績效趨勢分析和技能差距分析的結果，生成綜合報告",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: Object,
    default: () => ClipboardList,
  },
  minHeight: {
    type: Number,
    default: 500,
  },
  autoHeight: {
    type: Boolean,
    default: true,
  },
});

// 節點引用
const nodeRef = ref(null);

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "SummaryNode",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  getGlobalVariable,
} = nodeExecution;

// 總結內容
const performanceSummary = ref("");
const skillGapSummary = ref("");
const developmentSuggestions = ref([]);

// 事件處理
const emit = defineEmits(["update:data", "click", "connect", "disconnect"]);

const handleNodeClick = (event) => {
  emit("click", { id: props.id, event });
};

const handleConnect = (data) => {
  emit("connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("disconnect", { id: props.id, ...data });
};

// 生成總結報告
const generateSummary = async () => {
  try {
    // 獲取績效分析結果
    const performanceData = await getGlobalVariable("performanceAnalysis");
    if (performanceData) {
      performanceSummary.value = `過去六季度的績效呈現${
        performanceData.trend === "up" ? "上升" : "下降"
      }趨勢。平均績效分數為 ${performanceData.averageScore}，最高分為 ${
        performanceData.highestScore
      }。`;
    }

    // 獲取技能差距分析結果
    const skillGapData = await getGlobalVariable("skillGapAnalysis");
    if (skillGapData) {
      skillGapSummary.value = `主要技能差距：${skillGapData.gaps.join(
        "、"
      )}。優勢技能：${skillGapData.strengths.join("、")}。`;

      // 生成發展建議
      developmentSuggestions.value = [
        ...skillGapData.suggestions,
        "建議參加相關培訓課程以提升核心技能",
        "可以通過實際項目實踐來強化技能應用",
        "建議與資深同事進行經驗交流和學習",
      ];
    }

    return {
      performanceSummary: performanceSummary.value,
      skillGapSummary: skillGapSummary.value,
      developmentSuggestions: developmentSuggestions.value,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error("SummaryNode", "生成總結報告時發生錯誤:", error);
    throw error;
  }
};

// 實作 handleRun 方法
const handleRun = async (context = {}) => {
  try {
    // 更新節點狀態為執行中
    updateNodeStatus("running");

    // 生成總結報告
    const result = await generateSummary();

    // 更新共享數據
    await updateSharedData(props.id, result);

    // 設置全域變數
    await updateGlobalVariable("summaryReport", result);

    ElMessage.success("總結報告生成成功");
    return result;
  } catch (error) {
    logger.error("SummaryNode", "執行節點時發生錯誤:", error);
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.summary-section {
  @apply border-b border-gray-200 dark:border-gray-700 pb-4 mb-4 last:border-0 last:mb-0 last:pb-0;
}
</style>
