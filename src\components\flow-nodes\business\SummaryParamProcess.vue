<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <div
      v-if="nodeData_value.testParams.length > 0"
      v-for="testParam in nodeData_value.testParams"
      :key="testParam.key"
      class="bg-blue-50 dark:bg-gray-700 mb-4 p-2 rounded-lg">
      <h3
        class="flex flex-col gap-1 justify-center items-center text-lg font-bold text-gray-700 dark:text-gray-200 mb-2">
        <div class="text-center">{{ testParam.key }}</div>
        <div class="text-center text-xs">僅列出投票數>50%的參數</div>
      </h3>
      <template
        v-if="Array.isArray(testParam.value) && testParam.value.length > 0"
        class="space-y-2">
        <div
          v-for="param in testParam.value"
          :key="param.feature">
          <div
            class="flex justify-between items-center px-3 py-2 bg-white dark:bg-gray-900 rounded-sm">
            <span class="text-black dark:text-white font-semibold">
              {{ param.feature }}
            </span>
            <span class="text-blue-600 dark:text-blue-400 font-semibold">
              {{ param.significant_rate }}
            </span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="text-gray-500 dark:text-gray-400 text-center py-2">
          無符合條件的結果
        </div>
      </template>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted } from "vue";

const { isDark } = useThemeMode();

const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "關鍵製程因子",
  },
  description: {
    type: String,
    default: "歸納流程所偵測出之製程關鍵因子",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("result-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  testParams: [],
};

// 節點數據結構
const nodeData_value = ref({
  testParams: [...nodeData_default.testParams],
});

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 globalVariables 獲取所有testParam_開頭的項目
  const testParams = Object.entries(globalVariables)
    .filter(([key, value]) => key.startsWith("testParam_"))
    .map(([key, value]) => ({
      key: key.split("_").slice(1).join(" "),
      value: value,
    }));
  printLog("testParams:", testParams);
  // 檢查必要參數
  validateRequiredFields(
    props.title,
    "無任何參數測試結果!",
    !testParams || testParams.length < 1
  );
  if (!Array.isArray(testParams) || testParams.length === 0) {
    throw new Error("無任何參數測試結果!");
  }

  const filteredTestParams = testParams.map((item) => {
    // 確保 value 是陣列
    if (!Array.isArray(item.value)) {
      return {
        ...item,
        value: [],
      };
    }

    // 過濾並排序參數
    const filteredValue = item.value
      .filter((param) => {
        // 確保 significant_rate 存在且為數字
        const rate = parseFloat(param.significant_rate);
        return !isNaN(rate) && rate > 0.5;
      })
      .sort((a, b) => {
        const rateA = parseFloat(a.significant_rate);
        const rateB = parseFloat(b.significant_rate);
        return rateB - rateA;
      })
      .map((param) => ({
        feature: param.feature,
        significant_rate: parseFloat(param.significant_rate).toFixed(2),
      }));

    return {
      ...item,
      value: filteredValue,
    };
  });

  nodeData_value.value.testParams = filteredTestParams;

  // 構建結果對象
  const completeResult = {
    testParams: filteredTestParams,
    timestamp: new Date().toISOString(),
  };

  return completeResult;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.testParams = previousData.detail.testParams || [];
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
