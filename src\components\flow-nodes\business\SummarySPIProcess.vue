<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="KeySquare"
    :selected="selected"
    :disabled="disabled"
    :node-width="600"
    :node-height="500"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <!-- 主要內容區域 -->
    <div class="p-4 space-y-4">
      <!-- 輸出顯示區域 - 卡方 -->
      <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
          From Chi-square (投票數 > 50%)
        </h3>
        <div class="space-y-2">
          <div
            v-for="(item, index) in formData.keyProcessParams_chiSquare"
            :key="index"
            class="flex justify-between items-center px-3 py-2 bg-light-mode dark:bg-dark-mode rounded">
            <span class="text-light-mode dark:text-dark-mode">{{
              item.name
            }}</span>
            <span class="text-blue-600 dark:text-blue-400 font-semibold">{{
              item.rate
            }}</span>
          </div>
          <div
            v-if="formData.keyProcessParams_chiSquare.length === 0"
            class="text-gray-500 dark:text-gray-400 text-center py-2">
            無符合條件的結果
          </div>
        </div>
      </div>

      <!-- 輸出顯示區域 - Kruskal-Wallis Test -->
      <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">
          From Kruskal-Wallis Test (投票數 > 50%)
        </h3>
        <div class="space-y-2">
          <div
            v-for="(item, index) in formData.keyProcessParams_kruskalWallis"
            :key="index"
            class="flex justify-between items-center px-3 py-2 bg-light-mode dark:bg-dark-mode rounded">
            <span class="text-light-mode dark:text-dark-mode">{{
              item.name
            }}</span>
            <span class="text-blue-600 dark:text-blue-400 font-semibold">{{
              item.rate
            }}</span>
          </div>
          <div
            v-if="formData.keyProcessParams_kruskalWallis.length === 0"
            class="text-gray-500 dark:text-gray-400 text-center py-2">
            無符合條件的結果
          </div>
        </div>
      </div>

      <!-- 執行按鈕
      <div class="flex justify-end space-x-2">
        <el-button
          v-if="isRunning"
          type="danger"
          @click="stopSimulation"
          :disabled="!isRunning">
          停止輸出
        </el-button>
        <el-button
          type="primary"
          @click="handleRun"
          :loading="executing"
          :disabled="!aiPrompt.trim()">
          開始 AI 模擬
        </el-button>
      </div> -->
    </div>
  </BaseNode>
</template>

<script setup>
import { ref, onMounted } from "vue";
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { KeySquare } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { useFlowStore } from "@/stores/flowStore";
import { ElMessage } from "element-plus";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "Key Process Parameter",
  },
  description: {
    type: String,
    default: "影響不良的製程關鍵因子",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// 連接點配置
const handles = {
  inputs: [{ id: "input", type: "target", position: "left" }],
  outputs: [{ id: "output", type: "source", position: "right" }],
};

const nodeRef = ref(null);

// 初始化 nodeContext，提供默認值避免 undefined 錯誤
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 表單數據
const formData = ref({
  keyProcessParams_chiSquare: [],
  keyProcessParams_kruskalWallis: [],
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "ml-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `Summary SPI 節點 handleRun 被調用`);
  // logger.debug(props.title, "上下文數據:", context);

  // 從flowStore中取得全域變數(全域上下文數據)
  const { globalVariables } = useFlowStore().currentInstance?.context || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 由先前節點獲取參數
  const rawChiSquareData = globalVariables?.chisquare_spi || [];
  const rawKruskalData = globalVariables?.kruskal_wallis_test || [];

  // 準備處理函數
  const processFunction = async (inputData) => {
    // 過濾並處理卡方檢定結果
    const processedChiSquareResults = rawChiSquareData
      .filter((item) => item.Significant_rate > 0.5)
      .sort((a, b) => b.Significant_rate - a.Significant_rate)
      .map((item) => ({
        name: item.feature_col || item.Variable,
        rate: item.Significant_rate.toFixed(2),
      }));

    // 過濾並處理 Kruskal-Wallis 檢定結果
    const processedKruskalResults = rawKruskalData
      .filter((item) => item.Significant_Rate > 0.95)
      .sort((a, b) => b.Significant_Rate - a.Significant_Rate)
      .map((item) => ({
        name: item.Feature,
        rate: item.Significant_Rate.toFixed(2),
      }));

    // 更新 formData
    formData.value.keyProcessParams_chiSquare = processedChiSquareResults;
    formData.value.keyProcessParams_kruskalWallis = processedKruskalResults;

    // 構建結果對象
    const completeResult = {
      keyProcessParams_kruskalWallis: processedKruskalResults,
      keyProcessParams_chiSquare: processedChiSquareResults,
      timestamp: new Date().toISOString(),
    };

    // 更新nodeContext
    nodeContext.value = {
      ...nodeContext.value,
      output: completeResult,
    };

    return completeResult;
  };

  try {
    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);
    return result;
  } catch (error) {
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    logger.debug(props.title, "之前的分析結果:", previousData);

    // 恢復先前的參數
    formData.value.keyProcessParams_chiSquare =
      previousData.detail.keyProcessParams_chiSquare;
    formData.value.keyProcessParams_kruskalWallis =
      previousData.detail.keyProcessParams_kruskalWallis;

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 800px;
  overflow-y: auto;
}

/* 深色模式樣式調整 */
html.dark .bg-blue-50 {
  background-color: #1e3a8a; /* dark:bg-blue-900 的適當替代 */
}

html.dark .text-gray-700 {
  color: #e2e8f0; /* dark:text-dark-mode 的適當替代 */
}

html.dark .bg-white {
  background-color: #1e293b; /* dark:bg-dark-mode 的適當替代 */
}

html.dark .text-gray-800 {
  color: #e2e8f0; /* dark:text-dark-mode 的適當替代 */
}

html.dark .text-blue-600 {
  color: #60a5fa; /* dark:text-blue-400 的適當替代 */
}

html.dark .text-gray-500 {
  color: #94a3b8; /* dark:text-gray-400 的適當替代 */
}
</style>
