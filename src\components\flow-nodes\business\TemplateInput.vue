<template>
  <!-- Node可調整項目
      min-height: 最小高度
      node-width: 節點寬度
      node-height
    -->
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :min-height="600"
    :node-width="600"
    :node-height="600"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <!-- 主要內容區域 -->
    <div class="p-4 space-y-4">
      <div :class="panelClasses">
        <h3
          :class="[
            'text-base font-bold mb-3',
            isDark ? 'text-white' : 'text-gray-700',
          ]">
          樣板輸入
        </h3>

        <!-- 品目選擇 -->
        <el-form-item
          label="品目"
          :required="!isReportMode">
          <template v-if="!isReportMode">
            <el-select
              v-model="nodeData_value.partNo"
              placeholder="請選擇品目"
              clearable
              class="w-full"
              :disabled="executing"
              @change="handlePartNoChange">
              <el-option
                v-for="option in nodeData_value.partNoOptions"
                :key="option"
                :label="option"
                :value="option" />
            </el-select>
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              {{ nodeData_value.partNo || "未設定" }}
            </div>
          </template>
        </el-form-item>

        <!-- 日期區間 -->
        <el-form-item label="日期區間">
          <template v-if="!isReportMode">
            <el-date-picker
              v-model="nodeData_value.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="開始日期"
              end-placeholder="結束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disableFutureDate"
              class="w-full"
              :disabled="executing"
              @change="handleDateRangeChange" />
          </template>
          <template v-else>
            <div :class="readOnlyDisplayClasses">
              <template
                v-if="nodeData_value.startDate && nodeData_value.endDate">
                {{ nodeData_value.startDate }} 至 {{ nodeData_value.endDate }}
              </template>
              <template v-else> 未設定日期範圍 </template>
            </div>
          </template>
        </el-form-item>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "@/components/flow-nodes/base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, computed, onMounted, watch } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "輸入節點樣板",
  },
  description: {
    type: String,
    default: "整理好的節點樣板，供複製使用",
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-input");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  partNo: "ZE18113-A", // 預設第一個選項
  startDate: null,
  endDate: null,
  partNoOptions: ["ZE18113-A", "ZE18113-B", "ZE18115-A", "ZE18115-B"],
  dateRange: null, // 添加 dateRange 欄位
};

// 節點數據結構
const nodeData_value = ref({
  partNo: nodeData_default.partNo,
  startDate: nodeData_default.startDate,
  endDate: nodeData_default.endDate,
  partNoOptions: [...nodeData_default.partNoOptions],
  dateRange: nodeData_default.dateRange,
});

// 提取共用的面板樣式
const panelClasses = computed(() => [
  "p-4 rounded-lg",
  isDark.value ? "bg-dark-secondary" : "bg-blue-50",
]);

// 提取共用的唯讀顯示樣式
const readOnlyDisplayClasses = computed(() => [
  "py-2 px-3 rounded text-sm",
  isDark.value ? "bg-gray-700 text-gray-100" : "bg-gray-50 text-gray-900",
]);

// 日期處理相關函數(禁用未來日期)
const disableFutureDate = (date) => {
  return date > new Date();
};

// 處理品目變更
const handlePartNoChange = () => {
  printLog("品目已變更", { partNo: nodeData_value.value.partNo });
};

// 處理日期變更
const handleDateRangeChange = (dates) => {
  if (!dates) {
    nodeData_value.value.startDate = null;
    nodeData_value.value.endDate = null;
    return;
  }
  const [start, end] = dates;
  if (start && end && start > end) {
    ElMessage.warning("結束日期不可早於起始日期");
    nodeData_value.value.dateRange = null;
    nodeData_value.value.startDate = null;
    nodeData_value.value.endDate = null;
    return;
  }
  nodeData_value.value.startDate = start;
  nodeData_value.value.endDate = end;
};

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 檢查必要參數
  validateRequiredFields(
    props.title,
    "請填寫品目代碼",
    !nodeData_value.value.partNo
  );

  // 構建結果對象
  const result = {
    partNo: nodeData_value.value.partNo,
    startDate: nodeData_value.value.dateRange
      ? nodeData_value.value.dateRange[0]
      : null,
    endDate: nodeData_value.value.dateRange
      ? nodeData_value.value.dateRange[1]
      : null,
    timestamp: new Date().toISOString(),
  };
  printLog("處理結果:", result);

  // 設置全域變數
  await updateGlobalVariable("resourceType", "TEMPLATE"); // 資料來源(input額外必需置入) PBC/SPI
  await updateGlobalVariable("partNo", result.partNo);
  if (nodeData_value.value.dateRange) {
    await updateGlobalVariable("startDate", result.startDate);
    await updateGlobalVariable("endDate", result.endDate);
  }

  return result;
};

// 監聽節點數據變化，自動觸發相關操作
watch(
  () => [
    nodeData_value.value.partNo,
    nodeData_value.value.startDate,
    nodeData_value.value.endDate,
  ],
  async (
    [newPartNo, newStartDate, newEndDate],
    [oldPartNo, oldStartDate, oldEndDate]
  ) => {
    printLog("DO Node data changed!", {
      newPartNo,
      newStartDate,
      newEndDate,
      oldPartNo,
      oldStartDate,
      oldEndDate,
    });
  },
  { immediate: false }
);

// 初始化處理
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的輸入數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  console.log("previousData:", previousData);
  if (previousData?.detail) {
    printLog("載入之前的數據:", previousData);
    nodeData_value.value.partNo =
      previousData.detail.partNo || nodeData_default.partNo;
    if (previousData.detail.startDate && previousData.detail.endDate) {
      nodeData_value.value.startDate = previousData.detail.startDate;
      nodeData_value.value.endDate = previousData.detail.endDate;
    } else {
      nodeData_value.value.startDate = nodeData_default.startDate;
      nodeData_value.value.endDate = nodeData_default.endDate;
    }
  }
});
// ==============END 節點自定義區==============

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>

<style scoped>
.iym-input {
  width: 100%;
}

.iym-input__info {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.dark .iym-input__info {
  background-color: var(--el-bg-color-overlay);
  color: var(--el-text-color-primary);
}

:deep(.el-date-editor .el-range-input) {
  width: 80px;
}

:deep(.el-select),
:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 1rem;
}
</style>
