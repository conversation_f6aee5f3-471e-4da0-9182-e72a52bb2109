<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    :nodeType="nodeType"
    :title="title"
    :description="description"
    :data="data"
    :node-height="600"
    :execution-mode="executionMode"
    :isReportMode="isReportMode"
    @run="nodeRef.handleRun(processFunction)">
    <div class="p-4">
      <div class="mb-4">
        <el-form
          label-position="top"
          size="small">
          <el-form-item>
            <template v-if="!isReportMode">
              <el-select
                v-model="nodeData_value.selectedOptions"
                placeholder="請選擇示範選項"
                class="w-full"
                multiple>
                <el-option
                  v-for="option in nodeData_value.availableOptions"
                  :key="option"
                  :label="option"
                  :value="option" />
              </el-select>
            </template>
            <template v-else>
              <div
                class="py-2 px-3 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                <template
                  v-if="
                    nodeData_value.selectedOptions &&
                    nodeData_value.selectedOptions.length
                  ">
                  {{ nodeData_value.selectedOptions.join(", ") }}
                </template>
                <template v-else> 未選擇示範選項 </template>
              </div>
            </template>
          </el-form-item>
        </el-form>

        <!-- 分析結果區域 -->
        <div v-if="nodeData_value.chartData">
          <el-divider content-position="left">分析結果</el-divider>
          <div class="result-container">
            <!-- 熱力圖 -->
            <div class="mb-4">
              <Chart
                width="auto"
                height="400px"
                :options="
                  getChartOption(nodeData_value.chartData, '示範分析')
                " />
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import Chart from "@/components/chart.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useNodeReset } from "@/composables/useNodeReset";
import { EXECUTION_MODES } from "@/constants/executionModes";
import { ElMessage } from "element-plus";
import { request } from "@/api/request";
import { useThemeMode } from "@/composables/useThemeMode";
import { ref, onMounted } from "vue";

const { isDark } = useThemeMode();

// NOTE:打印日志, 不使用時就能註解掉
const printLog = (title = "", result = {}) => {
  console.log(props.title, title, result);
};

// 節點自定義屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "處理節點樣板",
  },
  description: {
    type: String,
    default: "整理好的節點樣板，供複製使用",
  },
  executionMode: {
    type: String,
    default: EXECUTION_MODES.AUTO,
    validator: (value) => Object.values(EXECUTION_MODES).includes(value),
  },
  isReportMode: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

//#region 節點固定屬性
// 節點引用
const nodeRef = ref(null);
const nodeType = ref("custom-process");

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: nodeType.value,
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  executeNode,
  errorMessage,
  errorDetails,
  NODE_STATES,
  updateNodeStatus,
  getPreviousData,
  updateGlobalVariable,
  extractPreviousNodeData,
  validateRequiredFields,
} = nodeExecution;
//#endregion

// ==============節點自定義區 START==============

// 節點預設數據
const nodeData_default = {
  availableOptions: [],
  selectedOptions: [],
  chartData: null,
};

// 節點數據結構
const nodeData_value = ref({
  availableOptions: [...nodeData_default.availableOptions],
  selectedOptions: [...nodeData_default.selectedOptions],
  chartData: null,
});

// 生成範例圖表數據
const generateDemoChartData = () => {
  const hours = [
    "12a",
    "1a",
    "2a",
    "3a",
    "4a",
    "5a",
    "6a",
    "7a",
    "8a",
    "9a",
    "10a",
    "11a",
    "12p",
    "1p",
    "2p",
    "3p",
    "4p",
    "5p",
    "6p",
    "7p",
    "8p",
    "9p",
    "10p",
    "11p",
  ];

  const days = ["週六", "週五", "週四", "週三", "週二", "週一", "週日"];

  // 生成隨機示範數據（2D熱力圖格式）
  const data = [];
  for (let i = 0; i < days.length; i++) {
    for (let j = 0; j < hours.length; j++) {
      const value = Math.floor(Math.random() * 20);
      data.push([j, i, value]);
    }
  }

  return { hours, days, data };
};

// 獲取圖表選項方法（供 Chart 組件使用）
const getChartOption = (chartData, processName) => {
  if (!chartData) return {};

  return {
    title: {
      text: `${processName} - 熱力圖`,
      left: "center",
    },
    tooltip: {
      position: "top",
      formatter: function (params) {
        const { hours, days } = chartData;
        return `${days[params.value[1]]} ${hours[params.value[0]]}<br/>值: ${
          params.value[2]
        }`;
      },
    },
    grid: {
      height: "50%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: chartData.hours,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: chartData.days,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 0,
      max: 20,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "15%",
      inRange: {
        color: [
          "#313695",
          "#4575b4",
          "#74add1",
          "#abd9e9",
          "#e0f3f8",
          "#ffffbf",
          "#fee090",
          "#fdae61",
          "#f46d43",
          "#d73027",
          "#a50026",
        ],
      },
    },
    series: [
      {
        name: "示範數據",
        type: "heatmap",
        data: chartData.data,
        label: {
          show: false,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
};

// 模擬取得選項的方法（包含等候效果）
const fetchDemoOptions = async () => {
  printLog("開始取得示範選項...");

  // 模擬API等候時間（1-3秒）
  const delay = Math.floor(Math.random() * 2000) + 1000;
  await new Promise((resolve) => setTimeout(resolve, delay));

  // 模擬回傳的示範選項
  const demoOptions = [
    "選項A - 示範項目1",
    "選項B - 示範項目2",
    "選項C - 示範項目3",
    "選項D - 示範項目4",
    "選項E - 示範項目5",
  ];

  return demoOptions;
};

const processFunction = async (inputData) => {
  printLog("開始執行節點!");
  // 使用便利方法提取前置節點數據
  const {
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);
  printLog("前置節點IDs:", previousNodeIds);
  printLog("合併後的前置節點輸出:", mergedPreviousOutputs);
  printLog("前置節點詳細信息:", previousNodesDetails);
  printLog("全局變量:", globalVariables);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  // printLog("品目:", part_no);
  // printLog("工單:", work_order);

  // 檢查必要參數
  validateRequiredFields(
    props.title,
    `缺少必要參數「品目」! 終止取得示範選項清單`,
    !part_no || part_no.length < 1
  );

  // 如果有互動模式，檢查互動狀態，判斷是否進入Pending狀態
  // if (checkInteraction()) { // 判斷是否已完成互動 checkInteraction方法需自行撰寫
  //   return {
  //     requiresInteraction: true
  //   };
  // }

  // === 原本的API調用，保留給複製使用 ===
  // nodeData_value.value.chiSquareVariableOptions = await request.post(
  //   "/external/iym/main_method/chi_square_discrete_variables_list_spi",
  //   {
  //     part_no: part_no,
  //     work_order: work_order.join(","),
  //   }
  // );

  // 使用模擬的選項取得方法
  nodeData_value.value.availableOptions = await fetchDemoOptions();

  printLog("availableOptions:", nodeData_value.value.availableOptions);
  if (
    !Array.isArray(nodeData_value.value.availableOptions) ||
    nodeData_value.value.availableOptions.length === 0
  ) {
    throw new Error("取得示範選項未返回有效數據");
  }
  await updateGlobalVariable(
    "availableOptions",
    nodeData_value.value.availableOptions
  );

  // 示範預設選擇前兩個選項
  nodeData_value.value.selectedOptions =
    nodeData_value.value.availableOptions.slice(0, 2);
  await updateGlobalVariable(
    "selectedOptions",
    nodeData_value.value.selectedOptions
  );

  // 生成圖表數據
  nodeData_value.value.chartData = generateDemoChartData();
  printLog("chartData generated:", nodeData_value.value.chartData);

  // 構建結果對象
  const result = {
    availableOptions: nodeData_value.value.availableOptions,
    selectedOptions: nodeData_value.value.selectedOptions,
    chartData: nodeData_value.value.chartData,
    part_no,
    work_order,
    timestamp: new Date().toISOString(),
  };
  console.log("處理結果:", result);

  return result;
};

// 初始化處理
onMounted(async () => {
  // console.log("onMounted", props);
  // 嘗試從共享數據中獲取之前的數據
  const previousData = getPreviousData(props.data, props.isReportMode);
  if (previousData && previousData.detail) {
    nodeData_value.value.selectedOptions =
      previousData.detail.selectedOptions || [];
    nodeData_value.value.availableOptions =
      previousData.detail.availableOptions || [];
    nodeData_value.value.chartData = previousData.detail.chartData || null;
  }
});

//#region 節點固定方法
// 使用重置邏輯
useNodeReset({
  nodeId: props.id,
  nodeName: props.title,
  nodeRef,
  nodeData_default,
  nodeData_value,
});

defineExpose({}); // 暴露方法給父元件
//#endregion
</script>
