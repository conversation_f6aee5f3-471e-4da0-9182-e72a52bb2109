<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="custom-process"
    :title="title"
    :description="description"
    :icon="BarChart2"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :show-handle-labels="showHandleLabels"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div class="p-4">
      <!-- 分析結果區域 -->
      <div v-if="nodeContext && nodeContext.output">
        <el-divider content-position="left">分析結果</el-divider>
        <div class="result-container">
          <!-- 圖表區域 - 垂直佈局：圖表在上 -->
          <div class="mb-4">
            <Chart
              width="auto"
              height="350px"
              :options="chartOption" />
          </div>

          <!-- 清單區域 - 垂直佈局：清單在下 -->
          <div class="mb-4">
            <el-card header="TOP5 不良原因">
              <template #header>
                <div class="card-header">
                  <span>TOP5 不良原因 : 不良數</span>
                </div>
              </template>
              <div class="top5-list">
                <div
                  v-for="item in sortedYieldData.slice(0, 5)"
                  :key="item['不良大項代碼']"
                  class="top5-item">
                  {{ item["不良大項代碼"] }} : {{ item.defect_pcs }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import BaseNode from "../base/BaseNode.vue";
import { useNodeExecution } from "@/composables/useNodeExecution";
import { useFlowStore } from "@/stores/flowStore";
import { BarChart2 } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import { formatTimestamp } from "@/utils/dateUtils";
import { request } from "@/api/request";
import { ElMessage } from "element-plus";
import Chart from "@/components/Chart.vue";

// 節點基本屬性
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "入庫良率分析",
  },
  description: {
    type: String,
    default: "分析入庫良率",
  },
  selected: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  nodeWidth: {
    type: Number,
    default: 450,
  },
  nodeHeight: {
    type: Number,
    default: 650,
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
  },
  showResizer: {
    type: Boolean,
    default: true,
  },
});

// 連接點配置
const handles = {
  inputs: [
    {
      id: "input",
      type: "target",
      position: "left",
    },
  ],
  outputs: [
    {
      id: "output",
      type: "source",
      position: "right",
    },
  ],
};

// 節點狀態
const nodeRef = ref(null);
const nodeContext = ref({
  output: null,
  input: null,
  status: "idle",
});

// 排序後的良率資料
const sortedYieldData = computed(() => {
  if (!nodeContext.value?.output?.result) return [];
  return [...nodeContext.value.output.result].sort(
    (a, b) => b.defect_pcs - a.defect_pcs
  );
});

// 建立圖表選項
const chartOption = computed(() => {
  if (!sortedYieldData.value.length) return {};

  const xAxis = sortedYieldData.value.map((d) => d["不良大項代碼"]);
  const seriesData_bar = sortedYieldData.value.map((d) => Number(d.defect_pcs));

  const total = seriesData_bar.reduce((acc, curr) => acc + curr, 0);
  const seriesData_line = sortedYieldData.value
    .map((d) => d.defect_pcs)
    .reduce((acc, curr, index) => {
      acc.push((acc[index - 1] || 0) + curr);
      return acc;
    }, [])
    .map((d) => (d / total) * 100);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    legend: {
      data: ["不良PCS數量", "累計百分比"],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: xAxis,
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "不良PCS數量",
        min: 0,
        max: seriesData_bar[0] ? Math.ceil(seriesData_bar[0] * 1.1) : 100,
        axisLabel: {
          formatter: "{value}",
        },
      },
      {
        type: "value",
        name: "累計百分比",
        min: 0,
        max: 100,
        interval: 10,
        axisLabel: {
          formatter: "{value} %",
        },
      },
    ],
    series: [
      {
        name: "不良PCS數量",
        type: "bar",
        data: seriesData_bar,
      },
      {
        name: "累計百分比",
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value.toFixed(2) + " %";
          },
        },
        data: seriesData_line,
      },
    ],
  };
});

// 使用統一的節點執行邏輯
const nodeExecution = useNodeExecution({
  nodeId: props.id,
  nodeType: "statistic-process",
  nodeName: props.title,
  nodeRef,
});

// 從 nodeExecution 中解構需要的方法和狀態
const {
  executing,
  errorMessage,
  errorDetails,
  executeNode,
  updateNodeStatus,
  handleClearError,
  updateSharedData,
  getSharedData,
  updateGlobalVariable,
} = nodeExecution;

// 事件處理
const emit = defineEmits([
  "update:data",
  "handle-connect",
  "handle-disconnect",
]);

const handleConnect = (data) => {
  emit("handle-connect", { id: props.id, ...data });
};

const handleDisconnect = (data) => {
  emit("handle-disconnect", { id: props.id, ...data });
};

// 執行節點
const handleRun = async (context = {}) => {
  logger.info(props.title, `良率分析節點 handleRun 被調用`);
  logger.debug(props.title, "上下文數據:", context);

  // 從 flowStore 中獲取全域上下文數據
  const flowStore = useFlowStore();
  const flowContext = flowStore.currentInstance?.context || {};
  const globalVariables = flowContext.globalVariables || {};
  logger.debug(props.title, "globalVariables:", globalVariables);

  // 由先前節點獲取參數
  const partNo =
    context.sourceNodeOutput?.partNo || globalVariables?.partNo || "";
  const startDate =
    context.sourceNodeOutput?.startDate || globalVariables?.startDate || "";
  const endDate =
    context.sourceNodeOutput?.endDate || globalVariables?.endDate || "";

  // 檢查必要參數是否存在
  const requiredParams = {
    品目: partNo,
    開始日期: startDate,
    結束日期: endDate,
  };

  const missingParams = Object.entries(requiredParams)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingParams.length > 0) {
    const errorMsg = `缺少必要參數(${missingParams.join(
      "、"
    )})，請先執行輸入節點`;
    logger.error(props.title, errorMsg);
    ElMessage.error(errorMsg);
    return;
  }

  try {
    // 準備處理函數
    const processFunction = async (inputData) => {
      // 調用良率分析 API 獲取真實數據
      const result = await request.post("/external/iym/main_method/yield", {
        part_no: partNo,
        start_date: startDate,
        end_date: endDate,
      });

      // 構建完整的結果對象
      const processedResult = {
        ...result,
        partNo,
        startDate,
        endDate,
        timestamp: new Date().toISOString(),
        nodeId: props.id,
        nodeName: props.title,
      };

      // 更新本地狀態
      nodeContext.value = {
        ...nodeContext.value,
        output: processedResult,
      };

      return processedResult;
    };

    // 使用統一的節點執行邏輯執行節點
    const result = await executeNode(context, processFunction);

    ElMessage.success("良率分析執行成功");
    return result;
  } catch (error) {
    logger.error(props.title, "良率分析失敗", error);
    ElMessage.error(`執行失敗: ${error.message || "未知錯誤"}`);
    throw error;
  }
};

// 檢查是否有之前的分析結果
onMounted(async () => {
  // 嘗試從共享數據中獲取之前的分析結果
  const previousData = getSharedData(props.id);
  if (previousData && previousData.detail) {
    logger.debug(props.title, "之前的分析結果:", previousData);

    // 恢復之前的分析結果
    nodeContext.value = {
      ...nodeContext.value,
      output: previousData.detail,
    };
  }
});

// 暴露方法給父元件
defineExpose({
  handleRun,
  handleClearError,
});
</script>

<style scoped>
.result-container {
  max-height: 600px;
  overflow-y: auto;
}

.top5-list {
  padding: 0;
}

.top5-item {
  font-size: 14px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.top5-item:last-child {
  border-bottom: none;
}
</style>
