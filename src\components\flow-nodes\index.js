s; //TODO: Is this file necessary?

import ComplaintSelectorNode from "./business/ComplaintInput.vue";
import TopDefectsNode from "./xx/TopDefectsProcess.vue";
import StatisticProcessNode from "./xx/ChiSquareProcess.vue";
import HttpRequestNode from "./base/HttpRequestNode.vue";
import CorrelationAnalysisNode from "./business/CorrelationAnalysisNode.vue";
import DecisionTreeAnalysisNode from "./xx/DecisionTreeProcess.vue";
import DescriptiveStatisticsNode from "./xx/DescriptiveStatisticsProcess.vue";

// 註冊所有節點
export const registerNodes = (vueFlow) => {
  // 業務節點
  vueFlow.addNode("complaint-selector", ComplaintSelectorNode);
  vueFlow.addNode("custom-process", TopDefectsNode);
  vueFlow.addNode("statistic-process", StatisticProcessNode);
  vueFlow.addNode("correlation-analysis", CorrelationAnalysisNode);
  vueFlow.addNode("decision-tree-analysis", DecisionTreeAnalysisNode);
  vueFlow.addNode("descriptive-statistics", DescriptiveStatisticsNode);
};

// 導出所有節點組件
export const nodes = {
  ComplaintSelectorNode,
  TopDefectsNode,
  StatisticProcessNode,
  HttpRequestNode,
  CorrelationAnalysisNode,
  DecisionTreeAnalysisNode,
  DescriptiveStatisticsNode,
};

export default {
  registerNodes,
  nodes,
};
