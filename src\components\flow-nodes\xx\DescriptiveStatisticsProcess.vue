<template>
  <BaseNode
    :id="id"
    ref="nodeRef"
    node-type="descriptive-statistics"
    :title="title"
    :description="description"
    :icon="BarChart"
    :selected="selected"
    :disabled="disabled"
    :node-width="nodeWidth"
    :node-height="nodeHeight"
    :auto-height="false"
    :show-handle-labels="showHandleLabels"
    header-bg-color="#a1d99b"
    :show-resizer="false"
    :handles="handles"
    @handle-connect="handleConnect"
    @handle-disconnect="handleDisconnect"
    @run="handleRun">
    <div
      class="p-4 overflow-y-auto"
      style="max-height: calc(100% - 40px)">
      <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">
          描述性統計分析設定
        </h3>
        <el-form
          label-position="top"
          size="small">
          <el-form-item label="數據來源">
            <el-select
              v-model="formData.dataSource"
              placeholder="請選擇數據來源"
              class="w-full">
              <el-option
                label="上游節點數據"
                value="upstream" />
              <el-option
                label="手動輸入數據"
                value="manual" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="formData.dataSource === 'manual'"
            label="數據輸入 (以逗號分隔)">
            <el-input
              v-model="formData.manualData"
              type="textarea"
              :rows="3"
              placeholder="請輸入數值，以逗號分隔 (例如: 1.2, 2.3, 3.4, 4.5)"
              class="w-full" />
          </el-form-item>

          <el-form-item
            v-if="formData.dataSource === 'upstream'"
            label="數據欄位">
            <el-select
              v-model="formData.dataColumn"
              placeholder="請選擇數據欄位"
              class="w-full">
              <el-option
                v-for="column in availableColumns"
                :key="column"
                :label="column"
                :value="column" />
            </el-select>
          </el-form-item>

          <el-form-item label="視覺化選項">
            <el-checkbox-group v-model="formData.visualOptions">
              <el-checkbox label="histogram">直方圖</el-checkbox>
              <el-checkbox label="boxplot">箱型圖</el-checkbox>
              <el-checkbox label="qq_plot">Q-Q圖</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <div
        v-if="analyzing"
        class="flex justify-center items-center py-4">
        <el-progress
          type="circle"
          :percentage="analyzeProgress"
          :status="analyzeProgress === 100 ? 'success' : 'processing'" />
        <div class="ml-4 text-gray-600">正在進行描述性統計分析...</div>
      </div>

      <div
        v-if="nodeContext.error"
        class="bg-red-50 p-3 rounded mb-4">
        <div class="text-red-700 font-medium">分析過程中發生錯誤</div>
        <div class="text-red-600 text-sm mt-1">{{ nodeContext.error }}</div>
        <el-button
          type="danger"
          size="small"
          class="mt-2"
          @click="handleClearError">
          清除錯誤
        </el-button>
      </div>

      <div
        v-if="showResults && !nodeContext.error"
        class="overflow-y-auto">
        <el-divider content-position="left">分析結果</el-divider>

        <div class="bg-gray-50 p-4 rounded border mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-3">基本統計量</h4>
          <div class="grid grid-cols-2 gap-4">
            <div class="stat-item">
              <div class="stat-label">樣本數量</div>
              <div class="stat-value">
                {{ nodeContext.output.result.sample_size }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">平均數</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.mean) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">中位數</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.median) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">眾數</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.mode.value) }}
                <span class="text-xs text-gray-500"
                  >(出現 {{ nodeContext.output.result.mode.count }} 次)</span
                >
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">標準差</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.std) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">變異數</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.variance) }}
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 p-4 rounded border mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-3">範圍與分位數</h4>
          <div class="grid grid-cols-2 gap-4">
            <div class="stat-item">
              <div class="stat-label">最小值</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.range.min) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最大值</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.range.max) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">全距</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.range.range) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">第一四分位數 (Q1)</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.quartiles.q1) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">第三四分位數 (Q3)</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.quartiles.q3) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">四分位距 (IQR)</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.quartiles.iqr) }}
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 p-4 rounded border mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-3">分布形狀</h4>
          <div class="grid grid-cols-2 gap-4">
            <div class="stat-item">
              <div class="stat-label">偏度 (Skewness)</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.shape.skewness) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">峰度 (Kurtosis)</div>
              <div class="stat-value">
                {{ formatNumber(nodeContext.output.result.shape.kurtosis) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 視覺化圖表 -->
        <div class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <h4 class="text-sm font-medium text-gray-700">視覺化圖表</h4>
          </div>

          <div class="grid grid-cols-1 gap-4">
            <div class="bg-white p-3 rounded border">
              <h5 class="text-xs font-medium text-gray-600 mb-2">
                數據分布直方圖
              </h5>
              <div
                :class="{
                  'h-64': !nodeRef?.isFullscreen,
                  'h-96': nodeRef?.isFullscreen,
                }">
                <div
                  ref="histogramChartRef"
                  class="w-full h-full"></div>
              </div>
            </div>

            <div class="bg-white p-3 rounded border">
              <h5 class="text-xs font-medium text-gray-600 mb-2">數據箱型圖</h5>
              <div
                :class="{
                  'h-40': !nodeRef?.isFullscreen,
                  'h-64': nodeRef?.isFullscreen,
                }">
                <div
                  ref="boxplotChartRef"
                  class="w-full h-full"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 解釋文字 -->
        <div class="bg-blue-50 p-3 rounded text-sm">
          <p class="text-blue-800">
            <strong>結論：</strong>
            {{ nodeContext.output.result.conclusion }}
          </p>
        </div>
      </div>
    </div>
  </BaseNode>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import BaseNode from "../base/BaseNode.vue";
import { useFlowStore } from "@/stores/flowStore";
import { useFlowInstance } from "@/composables/useFlowInstance";
import { BarChart } from "lucide-vue-next";
import { logger } from "@/utils/logger";
import request from "@/api/request";
import * as echarts from "echarts";
import { formatNumber } from "@/utils/numberUtil";

// 定義 props
const props = defineProps({
  id: {
    type: String,
    required: true,
    description: "節點的唯一 ID",
  },
  title: {
    type: String,
    default: "描述性統計分析",
    description: "節點的標題",
  },
  description: {
    type: String,
    default: "計算數據的基本統計量和分布特徵",
    description: "節點的描述",
  },
  selected: {
    type: Boolean,
    default: false,
    description: "是否選中節點",
  },
  disabled: {
    type: Boolean,
    default: false,
    description: "是否禁用節點",
  },
  nodeWidth: {
    type: Number,
    default: 480,
    description: "節點寬度",
  },
  nodeHeight: {
    type: Number,
    default: 600,
    description: "節點高度",
  },
  showHandleLabels: {
    type: Boolean,
    default: false,
    description: "是否顯示連接點標籤",
  },
});

// 引入 Flow Store
const flowStore = useFlowStore();
const { getNodeInputs, clearNodeError } = flowStore;
const { executeNode, updateSharedData } = useFlowInstance();

// 節點引用
const nodeRef = ref(null);

// 表單數據
const formData = ref({
  dataSource: "manual", // "upstream",
  manualData: "10.2, 15.3, 20.1, 25.4, 30.2, 18.7, 22.3, 19.8, 24.1, 27.5",
  dataColumn: "",
  visualOptions: ["histogram", "boxplot"],
});

// 分析狀態
const analyzing = ref(false);
const analyzeProgress = ref(0);
const showResults = ref(false);

// 節點上下文
const nodeContext = ref({
  inputs: {},
  output: null,
  error: null,
});

// 可用的數據欄位
const availableColumns = ref([]);

// 計算節點的連接點
const handles = computed(() => {
  return [
    {
      id: "input",
      type: "target",
      position: "left",
      description: "接收上游節點的數據",
    },
    {
      id: "output",
      type: "source",
      position: "right",
      description: "輸出描述性統計分析結果",
    },
  ];
});

// 統一的狀態更新方法
const updateNodeStatus = (newStatus, result = null, error = null) => {
  logger.debug(
    "DescriptiveStatisticsProcess",
    `更新節點 ${props.id} 狀態為 ${newStatus}`
  );

  // 如果有節點引用，使用 BaseNode 中的方法更新狀態
  if (nodeRef.value) {
    logger.debug("DescriptiveStatisticsProcess", `使用 nodeRef 更新狀態`);
    nodeRef.value.updateNodeStatus(newStatus, result, error);
  } else {
    // 如果節點引用不可用，直接更新 flowStore
    logger.debug(
      "DescriptiveStatisticsProcess",
      `nodeRef 不可用，直接更新 flowStore`
    );
    flowStore.updateNodeState(flowStore.currentInstance?.id, props.id, {
      status: newStatus,
      data: result,
      error: error ? error.message || "未知錯誤" : null,
      _isDataUpdate: true, // 標記為數據更新
    });
  }
};

// 處理連接
const handleConnect = async (connection) => {
  logger.info("DescriptiveStatisticsProcess", "連接建立", connection);

  // 獲取上游節點的輸出數據
  if (connection.targetHandle === "input") {
    try {
      const sourceNodeId = connection.source;
      // 使用 flowStore.getNodeById 而不是 getNodeById
      const sourceNode = flowStore.getNodeById(sourceNodeId);

      if (sourceNode) {
        // 獲取上游節點的輸出數據結構
        const inputs = await getNodeInputs(props.id);
        nodeContext.value.inputs = inputs;

        // 提取可用的數據欄位
        if (inputs && inputs.data) {
          if (Array.isArray(inputs.data)) {
            availableColumns.value = ["數據"];
          } else if (typeof inputs.data === "object") {
            availableColumns.value = Object.keys(inputs.data);
          }
        }

        // 如果只有一個欄位，自動選擇
        if (availableColumns.value.length === 1) {
          formData.value.dataColumn = availableColumns.value[0];
        }
      }
    } catch (error) {
      logger.error("DescriptiveStatisticsProcess", "獲取輸入數據失敗", error);
    }
  }
};

// 處理斷開連接
const handleDisconnect = (connection) => {
  logger.info("DescriptiveStatisticsProcess", "連接斷開", connection);

  // 如果是輸入連接斷開，清除相關數據
  if (connection.targetHandle === "input") {
    nodeContext.value.inputs = {};
    availableColumns.value = [];
    formData.value.dataColumn = "";
  }
};

// 圖表引用
const histogramChartRef = ref(null);
const boxplotChartRef = ref(null);
let histogramChart = null;
let boxplotChart = null;

// 監聽全屏狀態變化
watch(
  () => nodeRef.value?.isFullscreen,
  async (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 延遲一下，確保 DOM 已經更新
      setTimeout(() => {
        // 重新初始化圖表
        if (showResults.value && nodeContext.value.output) {
          updateCharts();

          // 在全屏模式下，圖表容器大小變化後需要調整圖表大小
          if (histogramChart) {
            histogramChart.resize();
          }
          if (boxplotChart) {
            boxplotChart.resize();
          }
        }
      }, 300);
    }
  },
  { immediate: false }
);

// 更新圖表
const updateCharts = () => {
  console.log("updateCharts 被調用");

  if (!nodeContext.value.output || !nodeContext.value.output.result) {
    console.error("updateCharts: 沒有有效的輸出數據");
    return;
  }

  // 獲取原始數據
  let rawData = [];

  try {
    if (formData.value.dataSource === "manual") {
      rawData = formData.value.manualData
        .split(",")
        .map((item) => parseFloat(item.trim()))
        .filter((item) => !isNaN(item));
    } else {
      const inputs = nodeContext.value.inputs;
      if (inputs && inputs.data) {
        if (
          formData.value.dataColumn === "數據" &&
          Array.isArray(inputs.data)
        ) {
          rawData = inputs.data;
        } else if (
          typeof inputs.data === "object" &&
          inputs.data[formData.value.dataColumn]
        ) {
          rawData = inputs.data[formData.value.dataColumn];
        }
      }
    }

    // 確保有數據
    if (rawData.length === 0) {
      console.error("updateCharts: 無法獲取有效的原始數據");
      return;
    }

    console.log("updateCharts: 獲取到原始數據", rawData);

    // 使用較長的延遲確保 DOM 已完全渲染
    setTimeout(() => {
      // 再次使用 nextTick 確保 DOM 更新完成
      nextTick(() => {
        console.log("updateCharts: 開始生成圖表");
        try {
          // 清除現有圖表
          if (histogramChart) {
            histogramChart.dispose();
            histogramChart = null;
          }
          if (boxplotChart) {
            boxplotChart.dispose();
            boxplotChart = null;
          }

          generateHistogram(rawData);
          generateBoxplot(rawData);
        } catch (error) {
          console.error("updateCharts: 生成圖表時發生錯誤", error);
        }
      });
    }, 300);
  } catch (error) {
    console.error("updateCharts: 處理數據時發生錯誤", error);
  }
};

// 生成直方圖
const generateHistogram = (data) => {
  console.log("generateHistogram 被調用", data);

  // 檢查數據是否有效
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.error("generateHistogram: 無效的數據", data);
    return;
  }

  // 檢查 DOM 元素是否存在
  if (!histogramChartRef.value) {
    console.error("generateHistogram: histogramChartRef 不存在");
    return;
  }

  // 檢查 DOM 元素尺寸
  const containerWidth = histogramChartRef.value.clientWidth;
  const containerHeight = histogramChartRef.value.clientHeight;

  if (containerWidth === 0 || containerHeight === 0) {
    console.error("generateHistogram: 容器尺寸為零", {
      width: containerWidth,
      height: containerHeight,
    });
    // 延遲重試
    setTimeout(() => {
      console.log("generateHistogram: 延遲重試");
      generateHistogram(data);
    }, 200);
    return;
  }

  try {
    // 清除舊圖表
    if (histogramChart) {
      histogramChart.dispose();
      histogramChart = null;
    }

    // 計算數據範圍和分組
    const min = Math.min(...data);
    const max = Math.max(...data);
    const range = max - min;
    const binCount = Math.min(Math.ceil(Math.sqrt(data.length)), 10); // 使用平方根法則確定分組數
    const binWidth = range / binCount;

    console.log("直方圖數據統計:", { min, max, range, binCount, binWidth });

    // 創建分組
    const bins = Array(binCount).fill(0);
    data.forEach((value) => {
      const binIndex = Math.min(
        Math.floor((value - min) / binWidth),
        binCount - 1
      );
      bins[binIndex]++;
    });

    // 創建標籤
    const labels = bins.map((_, i) => {
      const start = min + i * binWidth;
      const end = min + (i + 1) * binWidth;
      return `${start.toFixed(2)}-${end.toFixed(2)}`;
    });

    console.log("直方圖分組結果:", { bins, labels });

    // 初始化 ECharts 實例
    histogramChart = echarts.init(histogramChartRef.value);
    console.log("ECharts 實例已創建", histogramChart);

    // 設置圖表配置
    const option = {
      title: {
        text: "數據分布直方圖",
        left: "center",
      },
      tooltip: {
        trigger: "item",
        formatter: "{b}: {c}",
      },
      xAxis: {
        type: "category",
        data: labels,
        name: "數值範圍",
        nameLocation: "middle",
        nameGap: 30,
      },
      yAxis: {
        type: "value",
        name: "頻率",
        nameLocation: "middle",
        nameGap: 30,
      },
      series: [
        {
          data: bins,
          type: "bar",
          itemStyle: {
            color: "rgba(75, 192, 192, 0.6)",
          },
        },
      ],
    };

    // 使用配置項設置圖表
    histogramChart.setOption(option);
    console.log("直方圖生成成功");

    // 響應窗口大小變化
    window.addEventListener("resize", () => {
      histogramChart && histogramChart.resize();
    });
  } catch (error) {
    console.error("生成直方圖時發生錯誤:", error);
  }
};

// 生成箱型圖
const generateBoxplot = (data) => {
  console.log("generateBoxplot 被調用", data);

  // 檢查數據是否有效
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.error("generateBoxplot: 無效的數據", data);
    return;
  }

  // 檢查 DOM 元素是否存在
  if (!boxplotChartRef.value) {
    console.error("generateBoxplot: boxplotChartRef 不存在");
    return;
  }

  // 檢查 DOM 元素尺寸
  const containerWidth = boxplotChartRef.value.clientWidth;
  const containerHeight = boxplotChartRef.value.clientHeight;

  if (containerWidth === 0 || containerHeight === 0) {
    console.error("generateBoxplot: 容器尺寸為零", {
      width: containerWidth,
      height: containerHeight,
    });
    // 延遲重試
    setTimeout(() => {
      console.log("generateBoxplot: 延遲重試");
      generateBoxplot(data);
    }, 200);
    return;
  }

  try {
    // 清除舊圖表
    if (boxplotChart) {
      boxplotChart.dispose();
      boxplotChart = null;
    }

    // 排序數據
    const sortedData = [...data].sort((a, b) => a - b);

    // 計算統計量
    const min = sortedData[0];
    const max = sortedData[sortedData.length - 1];
    const q1Index = Math.floor(sortedData.length * 0.25);
    const q2Index = Math.floor(sortedData.length * 0.5);
    const q3Index = Math.floor(sortedData.length * 0.75);
    const q1 = sortedData[q1Index];
    const median = sortedData[q2Index];
    const q3 = sortedData[q3Index];

    console.log("箱型圖數據統計:", { min, max, q1, median, q3 });

    // 初始化 ECharts 實例
    boxplotChart = echarts.init(boxplotChartRef.value);
    console.log("ECharts 實例已創建", boxplotChart);

    // 設置圖表配置
    const option = {
      title: {
        text: "數據箱型圖",
        left: "center",
      },
      tooltip: {
        trigger: "item",
        formatter: function (params) {
          if (params.componentSubType === "boxplot") {
            return `最小值: ${min}<br/>
                    第一四分位數: ${q1}<br/>
                    中位數: ${median}<br/>
                    第三四分位數: ${q3}<br/>
                    最大值: ${max}`;
          }
          return params.name + ": " + params.value;
        },
      },
      grid: {
        left: "10%",
        right: "10%",
        bottom: "15%",
      },
      xAxis: {
        type: "category",
        data: ["箱型圖"],
        boundaryGap: true,
        nameGap: 30,
        splitArea: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: "數值",
        nameLocation: "middle",
        nameGap: 30,
        splitArea: {
          show: true,
        },
      },
      series: [
        {
          name: "箱型圖",
          type: "boxplot",
          datasetIndex: 0,
          data: [[min, q1, median, q3, max]],
          itemStyle: {
            color: "rgba(75, 192, 192, 0.6)",
            borderColor: "rgba(75, 192, 192, 1)",
          },
        },
      ],
    };

    // 使用配置項設置圖表
    boxplotChart.setOption(option);
    console.log("箱型圖生成成功");

    // 響應窗口大小變化
    window.addEventListener("resize", () => {
      boxplotChart && boxplotChart.resize();
    });
  } catch (error) {
    console.error("生成箱型圖時發生錯誤:", error);
  }
};

/**
 * 生成模擬的描述性統計結果
 * @param {Array<number>} data 數據數組
 * @returns {Object} 模擬的描述性統計結果
 */
const generateMockDescriptiveStats = (data) => {
  // 計算基本統計量
  const sum = data.reduce((acc, val) => acc + val, 0);
  const mean = sum / data.length;

  // 計算標準差
  const squaredDiffs = data.map((value) => Math.pow(value - mean, 2));
  const avgSquaredDiff =
    squaredDiffs.reduce((acc, val) => acc + val, 0) / data.length;
  const std = Math.sqrt(avgSquaredDiff);

  // 排序數據用於計算中位數和分位數
  const sortedData = [...data].sort((a, b) => a - b);
  const median = sortedData[Math.floor(sortedData.length / 2)];

  // 計算眾數
  const counts = {};
  let maxCount = 0;
  let modeValue = null;

  for (const value of data) {
    counts[value] = (counts[value] || 0) + 1;
    if (counts[value] > maxCount) {
      maxCount = counts[value];
      modeValue = value;
    }
  }

  // 計算分位數
  const q1Index = Math.floor(sortedData.length * 0.25);
  const q3Index = Math.floor(sortedData.length * 0.75);
  const q1 = sortedData[q1Index];
  const q3 = sortedData[q3Index];
  const iqr = q3 - q1;

  // 計算範圍
  const min = sortedData[0];
  const max = sortedData[sortedData.length - 1];
  const range = max - min;

  // 計算偏度和峰度（簡化版）
  let skewness = 0;
  let kurtosis = 0;

  for (const value of data) {
    const z = (value - mean) / std;
    skewness += Math.pow(z, 3);
    kurtosis += Math.pow(z, 4);
  }

  skewness = skewness / data.length;
  kurtosis = kurtosis / data.length - 3; // 減去正態分布的峰度

  // 生成結論
  let conclusion = `該數據集包含 ${data.length} 個樣本，平均值為 ${mean.toFixed(
    4
  )}，標準差為 ${std.toFixed(4)}。`;

  if (skewness > 0.5) {
    conclusion += " 數據呈現右偏分布（正偏態），大部分值集中在左側。";
  } else if (skewness < -0.5) {
    conclusion += " 數據呈現左偏分布（負偏態），大部分值集中在右側。";
  } else {
    conclusion += " 數據分布接近對稱。";
  }

  if (kurtosis > 0.5) {
    conclusion += " 數據的峰度較高，表示有較多的極端值。";
  } else if (kurtosis < -0.5) {
    conclusion += " 數據的峰度較低，表示極端值較少。";
  } else {
    conclusion += " 數據的峰度接近正態分布。";
  }

  // 構建模擬結果
  return {
    success: true,
    result: {
      sample_size: data.length,
      mean: mean,
      median: median,
      mode: {
        value: modeValue,
        count: maxCount,
      },
      std: std,
      variance: std * std,
      range: {
        min: min,
        max: max,
        range: range,
      },
      quartiles: {
        q1: q1,
        q3: q3,
        iqr: iqr,
      },
      shape: {
        skewness: skewness,
        kurtosis: kurtosis,
      },
      conclusion: conclusion,
    },
  };
};

// 執行分析
const handleRun = async () => {
  if (analyzing.value) return;

  analyzing.value = true;
  analyzeProgress.value = 0;
  showResults.value = false;

  // 設置節點為運行狀態
  nodeRef.value?.setRunningState(true);

  try {
    // 準備輸入數據
    let inputData = [];

    if (formData.value.dataSource === "manual") {
      // 處理手動輸入的數據
      if (!formData.value.manualData.trim()) {
        throw new Error("請輸入數據");
      }

      inputData = formData.value.manualData
        .split(",")
        .map((item) => parseFloat(item.trim()))
        .filter((item) => !isNaN(item));

      if (inputData.length === 0) {
        throw new Error("無效的數據格式");
      }
    } else {
      // 處理上游節點的數據
      const inputs = nodeContext.value.inputs;

      if (!inputs || !inputs.data) {
        throw new Error("未找到輸入數據，請連接上游數據節點");
      }

      if (!formData.value.dataColumn) {
        throw new Error("請選擇數據欄位");
      }

      // 提取選定欄位的數據
      if (formData.value.dataColumn === "數據" && Array.isArray(inputs.data)) {
        inputData = inputs.data;
      } else if (
        typeof inputs.data === "object" &&
        inputs.data[formData.value.dataColumn]
      ) {
        inputData = inputs.data[formData.value.dataColumn];
      } else {
        throw new Error(`無法找到欄位 "${formData.value.dataColumn}" 的數據`);
      }
    }

    let result;
    let progressInterval;
    // 移除 isPreviewMode 相關的邏輯，直接使用正常模式的 API 調用
    try {
      // 更新分析狀態
      analyzing.value = true;
      analyzeProgress.value = 0;

      // 模擬進度更新
      progressInterval = setInterval(() => {
        if (analyzeProgress.value < 90) {
          analyzeProgress.value += 10;
        }
      }, 300);

      // 調用 API 進行分析
      const response = await request.post("/external/analysis/descriptive", {
        method: "descriptive",
        data: {
          data: inputData,
        },
      });

      // 移除大型 base64 圖片數據，避免內存問題
      if (response.result) {
        if (response.result.distribution_plot) {
          delete response.result.distribution_plot;
        }
        if (response.result.box_plot) {
          delete response.result.box_plot;
        }
      }

      // 清除進度模擬
      // clearInterval(progressInterval);
      // analyzeProgress.value = 100;

      console.log("result", response.result);

      // 生成圖表
      if (response.result) {
        // 使用原始輸入數據生成圖表，而不是嘗試使用 response.result
        console.log("API 調用後生成圖表", inputData);
        // 延遲生成圖表，確保 DOM 已經渲染
        setTimeout(() => {
          try {
            generateHistogram(inputData);
            generateBoxplot(inputData);
          } catch (error) {
            console.error("API 調用後生成圖表失敗", error);
          }
        }, 300);
      }

      result = response;
    } catch (error) {
      logger.error("DescriptiveStatisticsProcess", "分析失敗", error);
      throw error;
    } finally {
      clearInterval(progressInterval);
      analyzeProgress.value = 100;
    }

    // 更新結果
    if (result && result.result) {
      // 更新節點上下文
      nodeContext.value = {
        ...nodeContext.value,
        output: result,
        error: null,
      };

      // 顯示結果
      showResults.value = true;

      // 獲取原始數據用於生成圖表
      let chartData = [];
      if (formData.value.dataSource === "manual") {
        chartData = formData.value.manualData
          .split(",")
          .map((item) => parseFloat(item.trim()))
          .filter((item) => !isNaN(item));
      } else {
        const inputs = nodeContext.value.inputs;
        if (inputs && inputs.data) {
          if (
            formData.value.dataColumn === "數據" &&
            Array.isArray(inputs.data)
          ) {
            chartData = inputs.data;
          } else if (
            typeof inputs.data === "object" &&
            inputs.data[formData.value.dataColumn]
          ) {
            chartData = inputs.data[formData.value.dataColumn];
          }
        }
      }

      // 使用原始數據生成圖表
      if (chartData.length > 0) {
        console.log("使用原始數據生成圖表", chartData);
        nextTick(() => {
          generateHistogram(chartData);
          generateBoxplot(chartData);
        });
      }
    }

    // 更新分析狀態
    analyzing.value = false;

    // 將分析結果保存到共享數據中，但不包含大型 base64 圖片數據
    const sharedResult = { ...result };

    // 移除大型 base64 圖片數據，避免請求過大
    if (sharedResult.result) {
      if (sharedResult.result.distribution_plot) {
        delete sharedResult.result.distribution_plot;
      }
      if (sharedResult.result.box_plot) {
        delete sharedResult.result.box_plot;
      }
    }

    // 添加元數據
    sharedResult.timestamp = new Date().toISOString();
    sharedResult.nodeId = props.id;
    sharedResult.nodeName = props.title;

    // 保存不含大型圖片數據的結果到共享數據
    if (typeof updateSharedData === "function") {
      await updateSharedData(props.id, sharedResult);
    }

    ElMessage.success("描述性統計分析完成");

    // 構建完整的結果對象 (不含圖片數據)
    const completeResult = {
      ...sharedResult,
    };

    // 更新節點狀態為完成
    updateNodeStatus("completed", completeResult);

    // 觸發節點狀態變更事件
    const event = new CustomEvent("node:stateChange", {
      detail: {
        nodeId: props.id,
        status: "completed",
        result: completeResult,
        timestamp: new Date().toISOString(),
      },
    });
    window.dispatchEvent(event);

    logger.info(
      "DescriptiveStatisticsProcess",
      "節點執行完成，已觸發狀態變更事件"
    );

    return completeResult;
  } catch (error) {
    logger.error("DescriptiveStatisticsProcess", "描述性統計分析失敗", error);
    ElMessage.error(`分析失敗: ${error.message || "未知錯誤"}`);

    // 更新節點狀態為錯誤
    updateNodeStatus("error", null, error);

    // 更新本地錯誤狀態
    nodeContext.value.error = error.message || "未知錯誤";

    throw error;
  } finally {
    analyzing.value = false;
    // 重置 loading 狀態
    nodeRef.value?.setRunningState(false);
  }
};

// 清除錯誤
const handleClearError = async () => {
  updateNodeStatus("default");
  nodeContext.value.error = null;
};

// 組件掛載時初始化
onMounted(async () => {
  try {
    // 檢查節點狀態
    if (flowStore.currentInstance && props.id) {
      // 正確使用 computed getter 獲取節點狀態

      const nodeState = flowStore.getNodeStateById(props.id);

      if (nodeState) {
        // 如果節點已經執行過，恢復結果
        if (nodeState.status === "completed" && nodeState.data) {
          nodeContext.value.output = nodeState.data;
          showResults.value = true;
          updateNodeStatus("completed", nodeState.data);

          // 生成圖表
          updateCharts();
        } else if (nodeState.status === "error" && nodeState.error) {
          nodeContext.value.error = nodeState.error;
          updateNodeStatus("error", null, { message: nodeState.error });
        }
      }
    }

    // 獲取輸入數據
    try {
      // 可能沒有連接的輸入，所以我們需要處理這種情況
      if (typeof getNodeInputs === "function") {
        const inputs = await getNodeInputs(props.id);

        if (inputs) {
          nodeContext.value.inputs = inputs;

          // 提取可用的數據欄位
          if (inputs.data) {
            if (Array.isArray(inputs.data)) {
              availableColumns.value = ["數據"];
            } else if (typeof inputs.data === "object") {
              availableColumns.value = Object.keys(inputs.data);
            }

            // 如果只有一個欄位，自動選擇
            if (availableColumns.value.length === 1) {
              formData.value.dataColumn = availableColumns.value[0];
            }
          }
        }
      } else {
        // 使用默認的手動輸入數據
        formData.value.dataSource = "manual";
        // 確保有一些示例數據
        if (!formData.value.manualData) {
          formData.value.manualData =
            "10.2, 15.3, 20.1, 25.4, 30.2, 18.7, 22.3, 19.8, 24.1, 27.5";
        }
      }

      // 確保圖表容器已經渲染完成後再初始化圖表
      setTimeout(() => {
        // 如果有結果數據，嘗試生成圖表
        if (
          showResults.value &&
          nodeContext.value.output &&
          nodeContext.value.output.result
        ) {
          console.log("onMounted: 嘗試初始化圖表");
          try {
            updateCharts();
          } catch (error) {
            console.error("onMounted: 初始化圖表失敗", error);
          }
        }
      }, 500);
    } catch (error) {
      logger.error("DescriptiveStatisticsProcess", "獲取輸入數據失敗", error);
      // 在錯誤情況下，也使用手動輸入模式
      formData.value.dataSource = "manual";
    }
  } catch (error) {
    logger.error("DescriptiveStatisticsProcess", "初始化失敗", error);
  }

  // 創建 resize 事件處理函數
  const handleResize = () => {
    if (histogramChart) histogramChart.resize();
    if (boxplotChart) boxplotChart.resize();
  };

  // 添加 resize 事件監聽器
  window.addEventListener("resize", handleResize);

  // 在組件卸載時清除圖表實例和事件監聽器
  onUnmounted(() => {
    // 移除事件監聽器
    window.removeEventListener("resize", handleResize);

    // 清除圖表實例
    if (histogramChart) {
      histogramChart.dispose();
      histogramChart = null;
    }
    if (boxplotChart) {
      boxplotChart.dispose();
      boxplotChart = null;
    }
  });
});
</script>

<style scoped>
.stat-item {
  @apply bg-white p-2 rounded border;
}

.stat-label {
  @apply text-xs text-gray-500 mb-1;
}

.stat-value {
  @apply text-sm font-medium;
}
</style>
