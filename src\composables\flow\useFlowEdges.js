export function useFlowEdges() {
  // 設置默認的連接線選項
  const defaultEdgeOptions = {
    type: "button", // 這個會影響到節點的連接線類型
    animated: true,
    label: "",
    markerEnd: {
      type: "arrowclosed",
      color: "#3f3f3f",
    },
    updatable: true,
    deletable: true,
    style: {
      strokeWidth: 2,
      stroke: "#3f3f3f",
    },
  };

  // 註冊自定義邊線類型
  const edgeTypes = {
    button: "EdgeWithButton", // 這裡只是引用名稱，實際元件需要在使用時導入
    custom: "CustomEdge",
  };

  // 連接事件
  const onConnect = (params, elements) => {
    // 驗證連接參數
    if (!params || !params.source || !params.target) {
      console.error("useFlowEdges: 無效的連接參數", params);
      return;
    }

    // 生成更安全的邊線 ID
    const sourceHandle = params.sourceHandle || "default";
    const targetHandle = params.targetHandle || "default";
    const timestamp = Date.now();

    // 確保所有必要的屬性都存在且有效
    const newEdge = {
      id: `edge_${params.source}_${sourceHandle}_${params.target}_${targetHandle}_${timestamp}`,
      source: params.source,
      target: params.target,
      sourceHandle: sourceHandle,
      targetHandle: targetHandle,
      type: "button",
      animated: true,
      style: {
        strokeWidth: 2,
        stroke: "#3f3f3f",
      },
      markerEnd: defaultEdgeOptions.markerEnd,
    };

    try {
      elements.value = [...elements.value, newEdge];
      // console.log('useFlowEdges: 成功創建新連接線', newEdge);
    } catch (error) {
      console.error("useFlowEdges: 添加連接線時發生錯誤", error);
    }
  };

  // 邊線點擊事件
  const onEdgeClick = (event) => {
    // 可以在這裡處理邊線點擊事件
  };

  // 邊線更新事件
  const onEdgeUpdate = (oldEdge, newConnection, elements) => {
    const newEdge = {
      ...oldEdge,
      ...newConnection,
    };
    elements.value = elements.value.map((el) =>
      el.id === oldEdge.id ? newEdge : el
    );
  };

  // 邊線更新開始事件
  const onEdgeUpdateStart = (event) => {
    // 可以在這裡處理邊線更新開始事件
  };

  // 邊線更新結束事件
  const onEdgeUpdateEnd = (event) => {
    // 可以在這裡處理邊線更新結束事件
  };

  // 邊線變化事件
  const onEdgesChange = (changes, setHasUnsavedChanges) => {
    setHasUnsavedChanges && setHasUnsavedChanges(true);
  };

  return {
    defaultEdgeOptions,
    edgeTypes,
    onConnect,
    onEdgeClick,
    onEdgeUpdate,
    onEdgeUpdateStart,
    onEdgeUpdateEnd,
    onEdgesChange,
  };
}
