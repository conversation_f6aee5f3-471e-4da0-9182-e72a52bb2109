import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorker from "pdfjs-dist/build/pdf.worker.min.mjs?url";
import { useFullscreen } from "@vueuse/core";
// 導入自定義圖標組件
import FileWord from "@/assets/icons/FileWord.vue";
import FileExcel from "@/assets/icons/FileExcel.vue";
import FilePowerpoint from "@/assets/icons/FilePowerpoint.vue";
import FilePDF from "@/assets/icons/FilePDF.vue";
// 導入 Element Plus 圖標組件
import {
  Document,
  Picture,
  Files as FilesIcon,
  Memo,
  VideoPlay,
} from "@element-plus/icons-vue";

// 設置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

// 獲取 PDF.js viewer 的 URL
const getPdfViewerUrl = (pdfUrl) => {
  return `/pdfjs/web/viewer.html?file=${encodeURIComponent(pdfUrl)}`;
};

export function useFilePreview() {
  // 預覽狀態
  const previewVisible = ref(false);
  const zoomLevel = ref(1);
  const isDragging = ref(false);
  const dragStartX = ref(0);
  const dragStartY = ref(0);
  const imageRef = ref(null);

  // PDF 相關狀態
  const currentPage = ref(1);
  const totalPages = ref(1);
  const pdfObject = ref(null);

  // 影片相關狀態
  const videoRef = ref(null);
  const isVideoPlaying = ref(false);
  const isVideoMuted = ref(false);
  const videoCurrentTime = ref(0);
  const videoDuration = ref(0);

  // 全螢幕控制
  const { isFullscreen, toggle: toggleFullscreen } = useFullscreen();

  // 重設鍵值，用於強制重新渲染某些組件
  const pptKey = ref(0);
  const wordKey = ref(0);
  const excelKey = ref(0);

  // 檢查文件類型的函數
  const isImage = (fileName) => {
    if (!fileName) return false;
    const imgExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
    return imgExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
  };

  const isVideo = (fileName) => {
    if (!fileName) return false;
    const videoExtensions = [
      ".mp4",
      ".avi",
      ".mov",
      ".wmv",
      ".flv",
      ".mkv",
      ".webm",
    ];
    return videoExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
  };

  const isPdf = (fileName) => {
    if (!fileName) return false;
    return fileName.toLowerCase().endsWith(".pdf");
  };

  const isWord = (fileName) => {
    if (!fileName) return false;
    return (
      fileName.toLowerCase().endsWith(".doc") ||
      fileName.toLowerCase().endsWith(".docx")
    );
  };

  const isExcel = (fileName) => {
    if (!fileName) return false;
    return (
      fileName.toLowerCase().endsWith(".xls") ||
      fileName.toLowerCase().endsWith(".xlsx")
    );
  };

  const isPpt = (fileName) => {
    if (!fileName) return false;
    return (
      fileName.toLowerCase().endsWith(".ppt") ||
      fileName.toLowerCase().endsWith(".pptx")
    );
  };

  const isTxt = (fileName) => {
    if (!fileName) return false;
    return fileName.toLowerCase().endsWith(".txt");
  };
  const isPreviewable = (fileName) => {
    return (
      isImage(fileName) ||
      isVideo(fileName) ||
      isPdf(fileName) ||
      isWord(fileName) ||
      isExcel(fileName) ||
      isPpt(fileName) ||
      isTxt(fileName)
    );
  };

  // 檔案操作處理
  const handlePreview = (file) => {
    if (!file.url) {
      ElMessage.warning("檔案未上傳或無法訪問");
      return;
    }

    previewVisible.value = true;
    zoomLevel.value = 1;

    // 重置 PDF 頁面
    if (isPdf(file.name)) {
      currentPage.value = 1;
    }

    // 重置鍵值以確保組件重新渲染
    if (isPpt(file.name)) pptKey.value++;
    if (isWord(file.name)) wordKey.value++;
    if (isExcel(file.name)) excelKey.value++;
  };

  const handleDownload = (file) => {
    if (!file.url) {
      ElMessage.error("檔案連結無效，無法下載");
      return;
    }

    const link = document.createElement("a");
    link.href = file.url;
    link.download = file.name || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 縮放控制
  const handleZoomIn = () => {
    zoomLevel.value = Math.min(zoomLevel.value + 0.1, 3);
  };

  const handleZoomOut = () => {
    zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5);
  };

  // 拖拽控制
  const startDrag = (event) => {
    isDragging.value = true;
    dragStartX.value = event.clientX;
    dragStartY.value = event.clientY;
  };

  const onDrag = (event) => {
    if (!isDragging.value) return;

    const dx = event.clientX - dragStartX.value;
    const dy = event.clientY - dragStartY.value;

    if (imageRef.value) {
      const img = imageRef.value;
      img.style.transform = `translate(${dx}px, ${dy}px) scale(${zoomLevel.value})`;
    }
  };

  const stopDrag = () => {
    isDragging.value = false;
  };

  // PDF 相關函數
  const handlePdfLoad = async (event) => {
    try {
      // 獲取 PDF 檔案的 URL
      const pdfUrl = event.target.src.split("#")[0];

      // 使用 fetch 先獲取 PDF 檔案的 ArrayBuffer
      const response = await fetch(pdfUrl);
      if (!response.ok) {
        throw new Error(`無法獲取 PDF: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();

      // 使用 PDF.js 載入 PDF 檔案
      const loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@4.10.38/cmaps/",
        cMapPacked: true,
      });

      // 獲取 PDF 文檔
      const pdf = await loadingTask.promise;

      // 設置總頁數
      totalPages.value = pdf.numPages;

      // 確保當前頁碼不超過總頁數
      if (currentPage.value > totalPages.value) {
        currentPage.value = 1;
      }
    } catch (error) {
      console.error("載入 PDF 失敗:", error);
      totalPages.value = 1; // 設置默認值
      ElMessage.warning(`PDF 載入失敗: ${error.message}`);
    }
  }; // 格式化檔案大小
  const formatFileSize = (bytes) => {
    if (!bytes) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  // 獲取文件圖標
  const getFileIcon = (fileType, fileName) => {
    if (!fileType && !fileName) return "Files";

    // 如果提供了便利貼類型
    if (fileType === "sticky") return Memo;

    // 根據檔名判斷
    if (fileName) {
      if (isPdf(fileName)) return FilePDF;
      if (isWord(fileName)) return FileWord;
      if (isExcel(fileName)) return FileExcel;
      if (isPpt(fileName)) return FilePowerpoint;
      if (isImage(fileName)) return Picture;
      if (isVideo(fileName)) return VideoPlay;
      if (isTxt(fileName)) return Document;
    }

    // 根據文件類型判斷
    if (fileType) {
      const type = fileType.toLowerCase();

      switch (type) {
        case "pdf":
        case "application/pdf":
          return FilePDF;
        case "doc":
        case "docx":
        case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          return FileWord;
        case "xls":
        case "xlsx":
        case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
          return FileExcel;
        case "ppt":
        case "pptx":
        case "application/vnd.ms-powerpoint":
        case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
          return FilePowerpoint;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "webp":
        case "svg":
          return Picture;
        case "mp4":
        case "avi":
        case "mov":
        case "wmv":
          return VideoPlay;
        case "txt":
          return Document;
      }
    }

    return FilesIcon; // 默認圖標
  };

  // 獲取圖標顏色類
  const getIconColorClass = (fileType, fileName) => {
    // 如果是便利貼
    if (fileType === "sticky") return "text-yellow-500";

    // 根據檔名判斷
    if (fileName) {
      if (isPdf(fileName)) return "text-red-500";
      if (isWord(fileName)) return "text-blue-600";
      if (isExcel(fileName)) return "text-green-600";
      if (isPpt(fileName)) return "text-orange-500";
      if (isImage(fileName)) return "text-purple-500";
      if (isVideo(fileName)) return "text-blue-500";
      if (isTxt(fileName)) return "text-gray-500";
    }

    return "text-blue-500"; // 默認顏色
  };

  // 獲取文件類型的 CSS 類名
  const getFileTypeClass = (fileType, fileName) => {
    if (fileType === "sticky") return "file-sticky";
    if (isPdf(fileName)) return "file-pdf";
    if (isWord(fileName)) return "file-word";
    if (isExcel(fileName)) return "file-excel";
    if (isPpt(fileName)) return "file-powerpoint";
    if (isImage(fileName)) return "file-image";
    if (isVideo(fileName)) return "file-video";
    if (isTxt(fileName)) return "file-text";
    return "file-unknown";
  };
  return {
    // 狀態
    previewVisible,
    zoomLevel,
    isDragging,
    imageRef,
    videoRef,
    isFullscreen,
    currentPage,
    totalPages,
    pptKey,
    wordKey,
    excelKey,

    // 功能函數
    isImage,
    isVideo,
    isPdf,
    isWord,
    isExcel,
    isPpt,
    isTxt,
    isPreviewable,
    getPdfViewerUrl,
    handlePreview,
    handleDownload,
    handleZoomIn,
    handleZoomOut,
    startDrag,
    onDrag,
    stopDrag,
    handlePdfLoad,
    formatFileSize,
    toggleFullscreen,

    // 新增的文件圖標/類型相關函數
    getFileIcon,
    getIconColorClass,
    getFileTypeClass,
  };
}
