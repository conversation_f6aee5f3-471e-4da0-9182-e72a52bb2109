import { ref, computed, watch } from "vue";
import { useDebounceFn } from "@vueuse/core";

// 緩存鍵前綴
const CACHE_PREFIX = "flow_cache_";
const DB_NAME = "flowCacheDB";
const DB_VERSION = 1;
const STORE_NAME = "nodes";

/**
 * 深度清理對象，移除循環引用和不可序列化的屬性
 * @param {any} obj - 需要清理的對象
 * @param {WeakSet} visited - 已訪問的對象集合
 * @returns {any} - 清理後的對象
 */
const deepCleanObject = (obj, visited = new WeakSet()) => {
  // 基本類型直接返回
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  // 檢查循環引用
  if (visited.has(obj)) {
    return "[Circular Reference]";
  }

  // 處理 Vue 響應式對象
  if (obj && typeof obj === "object") {
    // 檢查是否為 Vue 響應式引用
    if (obj._isRef || obj._isComputed || obj.__v_isRef) {
      return deepCleanObject(obj.value, visited);
    }

    // 檢查是否為 ComputedRefImpl
    if (obj.constructor && obj.constructor.name === "ComputedRefImpl") {
      return deepCleanObject(obj.value, visited);
    }

    // 檢查是否為 Vue 組件實例或其他不可序列化的對象
    if (obj.$ || obj._isVue || obj.__vueParentComponent) {
      return "[Vue Component]";
    }

    // 檢查是否為函數
    if (typeof obj === "function") {
      return "[Function]";
    }

    // 檢查是否為 DOM 元素
    if (obj.nodeType) {
      return "[DOM Element]";
    }

    // 檢查是否有 dep 屬性（可能是響應式依賴）
    if (obj.dep && typeof obj.dep === "object") {
      return "[Reactive Dependency]";
    }
  }

  visited.add(obj);

  try {
    // 處理數組
    if (Array.isArray(obj)) {
      const cleanArray = obj.map((item) => deepCleanObject(item, visited));
      visited.delete(obj);
      return cleanArray;
    }

    // 處理普通對象
    const cleanObj = {};
    for (const [key, value] of Object.entries(obj)) {
      // 跳過以 _ 或 $ 開頭的屬性（通常是內部屬性）
      if (key.startsWith("_") || key.startsWith("$")) {
        continue;
      }

      // 跳過已知的 Vue 內部屬性
      if (["dep", "computed", "__v_isRef", "__v_isReactive"].includes(key)) {
        continue;
      }

      try {
        cleanObj[key] = deepCleanObject(value, visited);
      } catch (error) {
        // 如果某個屬性無法清理，跳過它
        console.warn(`跳過無法序列化的屬性: ${key}`, error.message);
        cleanObj[key] = "[Unserializable]";
      }
    }

    visited.delete(obj);
    return cleanObj;
  } catch (error) {
    visited.delete(obj);
    return "[Error cleaning object]";
  }
};

/**
 * 安全的 JSON 序列化
 * @param {any} obj - 需要序列化的對象
 * @returns {any} - 可以安全序列化的對象
 */
const safeSerialize = (obj) => {
  try {
    const cleaned = deepCleanObject(obj);
    // 嘗試序列化以驗證
    JSON.stringify(cleaned);
    return cleaned;
  } catch (error) {
    console.warn("序列化失敗，返回安全的錯誤對象:", error.message);
    return {
      __serialization_error: true,
      error: "序列化失敗",
      message: error.message,
      timestamp: new Date().toISOString(),
      // 如果是基本類型，嘗試直接返回
      originalType: typeof obj,
      isArray: Array.isArray(obj),
    };
  }
};

// 全局緩存實例映射
const cacheInstances = new Map();

/**
 * 工作流緩存管理器
 * 負責管理流程節點的狀態和結果緩存
 * 使用單例模式確保每個流程實例只有一個緩存管理器
 */
export function useFlowCache(flowInstanceId) {
  // 檢查是否已經有該實例的緩存管理器
  if (cacheInstances.has(flowInstanceId)) {
    return cacheInstances.get(flowInstanceId);
  }
  // 緩存鍵
  const cacheKey = computed(() => `${CACHE_PREFIX}${flowInstanceId}`);

  // 未同步的變更計數
  const pendingChanges = ref(0);

  // 最後同步時間
  const lastSyncTime = ref(null);

  // 是否正在同步
  const isSyncing = ref(false);

  // IndexedDB 實例
  let db = null;

  /**
   * 初始化 IndexedDB
   * @returns {Promise<IDBDatabase>}
   */
  const initDB = () => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        console.error("useFlowCache", "打開 IndexedDB 失敗", request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        db = request.result;
        resolve(db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          db.createObjectStore(STORE_NAME, { keyPath: "id" });
        }
      };
    });
  };

  /**
   * 從 IndexedDB 讀取緩存
   * @returns {Promise<Object>} 緩存數據
   */
  const readCache = async () => {
    try {
      if (!db) {
        await initDB();
      }

      return new Promise((resolve, reject) => {
        const transaction = db.transaction(STORE_NAME, "readonly");
        const store = transaction.objectStore(STORE_NAME);
        const request = store.get(cacheKey.value);

        request.onerror = () => {
          console.error("useFlowCache", "讀取緩存失敗", request.error);
          reject(request.error);
        };

        request.onsuccess = () => {
          const data = request.result || {
            nodes: {},
            context: null,
            lastUpdate: null,
            version: 1,
          };
          resolve(data);
        };
      });
    } catch (error) {
      console.error("useFlowCache", "讀取緩存失敗", error);
      return {
        nodes: {},
        context: null,
        lastUpdate: null,
        version: 1,
      };
    }
  };

  /**
   * 保存緩存到 IndexedDB
   * @param {Object} data 要保存的數據
   */
  const saveCache = async (data) => {
    try {
      if (!db) {
        await initDB();
      }

      // 先讀取現有的緩存
      const existingCache = await readCache();

      // 確保數據可以被序列化
      const serializableData = {
        id: cacheKey.value,
        nodes: { ...existingCache.nodes }, // 保留現有的節點數據
        context: data.context
          ? JSON.parse(JSON.stringify(data.context))
          : existingCache.context,
        lastUpdate: new Date().toISOString(),
        version: 1,
      };

      // 合併新的節點數據
      Object.entries(data.nodes || {}).forEach(([nodeId, nodeData]) => {
        // 保留現有的節點數據，只更新提供的新數據
        serializableData.nodes[nodeId] = {
          ...serializableData.nodes[nodeId], // 保留現有數據
          status: nodeData.status,
          result: nodeData.result
            ? JSON.parse(JSON.stringify(nodeData.result))
            : serializableData.nodes[nodeId]?.result || null,
          error: nodeData.error
            ? {
                message: nodeData.error.message,
                stack: nodeData.error.stack,
              }
            : serializableData.nodes[nodeId]?.error || null,
          lastUpdate: nodeData.lastUpdate || new Date().toISOString(),
        };
      });
      // console.log("useFlowCache", "序列化後的數據:", serializableData);

      // 保存緩存到 IndexedDB
      return new Promise((resolve, reject) => {
        const transaction = db.transaction(STORE_NAME, "readwrite");
        const store = transaction.objectStore(STORE_NAME);
        const request = store.put(serializableData);

        request.onerror = () => {
          console.error("useFlowCache", "保存緩存失敗", request.error);
          reject(request.error);
        };

        request.onsuccess = () => {
          // console.log("useFlowCache", "緩存保存成功", {
          //   節點數量: Object.keys(serializableData.nodes).length,
          //   最後更新: serializableData.lastUpdate,
          // });
          resolve();
        };
      });
    } catch (error) {
      console.error("useFlowCache", "保存緩存失敗", error);
      throw error;
    }
  };

  /**
   * 更新節點緩存 //TODO: node 也有覆蓋的問題
   * @param {string} nodeId 節點ID
   * @param {Object} data 節點數據
   */
  const updateNodeCache = async (nodeId, data) => {
    try {
      // 驗證節點 ID
      if (!nodeId || typeof nodeId !== "string") {
        console.error("useFlowCache", "無效的節點 ID", nodeId);
        return;
      }

      const cache = await readCache();

      // 特殊處理 context 更新 //NOTE: 重點 - 修正為合併而不是覆蓋
      if (nodeId === "context") {
        // 合併現有的 context 和新的 context，避免覆蓋其他字段
        const existingContext = cache.context || {};
        const newContext = safeSerialize(data.result) || {};

        // 深度合併 context，特別處理 globalVariables
        cache.context = {
          ...existingContext,
          ...newContext,
          globalVariables: {
            ...(existingContext.globalVariables || {}),
            ...(newContext.globalVariables || {}),
          },
          sharedData: {
            ...(existingContext.sharedData || {}),
            ...(newContext.sharedData || {}),
          },
        };

        await saveCache(cache);
        pendingChanges.value++;
        return;
      }

      // 確保數據可以被序列化
      const serializableData = {
        status: data.status || "pending",
        result: data.result ? safeSerialize(data.result) : null,
        error: data.error
          ? {
              message: data.error.message,
              stack: data.error.stack,
            }
          : null,
        lastUpdate: new Date().toISOString(),
        ...safeSerialize(data),
      };

      // 確保 nodes 物件存在
      if (!cache.nodes) {
        cache.nodes = {};
      }

      // 更新節點資料
      cache.nodes[nodeId] = serializableData;

      // 更新最後修改時間
      cache.lastUpdate = new Date().toISOString();

      await saveCache(cache);
      pendingChanges.value++;

      // console.log("節點緩存更新成功:", nodeId, serializableData);
    } catch (error) {
      console.error("useFlowCache", "更新節點緩存失敗", error);
      throw error;
    }
  };

  /**
   * 獲取節點緩存
   * @param {string} nodeId 節點ID
   * @returns {Promise<Object>} 節點緩存數據
   */
  const getNodeCache = async (nodeId) => {
    const cache = await readCache();

    // 特殊處理 context
    if (nodeId === "context") {
      return cache.context
        ? {
            status: "completed",
            result: cache.context,
            lastUpdate: cache.lastUpdate,
          }
        : null;
    }

    return cache.nodes[nodeId] || null;
  };

  /**
   * 清除節點緩存
   * @param {string} nodeId 節點ID
   */
  const clearNodeCache = async (nodeId) => {
    const cache = await readCache();
    delete cache.nodes[nodeId];
    await saveCache(cache);
  };

  /**
   * 清除所有緩存
   */
  const clearAllCache = async () => {
    try {
      if (!db) {
        await initDB();
      }

      return new Promise((resolve, reject) => {
        const transaction = db.transaction(STORE_NAME, "readwrite");
        const store = transaction.objectStore(STORE_NAME);
        const request = store.delete(cacheKey.value);

        request.onerror = () => {
          console.error("useFlowCache", "清除緩存失敗", request.error);
          reject(request.error);
        };

        request.onsuccess = () => {
          pendingChanges.value = 0;
          lastSyncTime.value = null;
          resolve();
        };
      });
    } catch (error) {
      console.error("useFlowCache", "清除緩存失敗", error);
    }
  };

  /**
   * 同步緩存到伺服器
   * @param {Function} syncFunction 同步函數
   */
  const syncToServer = async (syncFunction) => {
    if (isSyncing.value || pendingChanges.value === 0) return;

    try {
      isSyncing.value = true;
      const cache = await readCache();
      await syncFunction(cache.nodes);
      lastSyncTime.value = new Date().toISOString();
      pendingChanges.value = 0;
    } catch (error) {
      console.error("useFlowCache", "同步到伺服器失敗", error);
    } finally {
      isSyncing.value = false;
    }
  };

  // 創建防抖版本的同步函數
  const debouncedSync = useDebounceFn(syncToServer, 30000); // 30秒防抖

  // 在頁面關閉前同步
  window.addEventListener("beforeunload", async () => {
    if (pendingChanges.value > 0) {
      await syncToServer();
    }
  });

  const cacheInstance = {
    readCache,
    updateNodeCache,
    getNodeCache,
    clearNodeCache,
    clearAllCache,
    syncToServer: debouncedSync,
    pendingChanges,
    lastSyncTime,
    isSyncing,
  };

  // 保存到全局實例映射
  cacheInstances.set(flowInstanceId, cacheInstance);

  return cacheInstance;
}

/**
 * 清理特定流程實例的緩存管理器
 */
export function cleanupFlowCache(flowInstanceId) {
  if (cacheInstances.has(flowInstanceId)) {
    cacheInstances.delete(flowInstanceId);
  }
}
