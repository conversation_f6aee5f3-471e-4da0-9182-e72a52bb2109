import { ref } from "vue";
import { ElMessage } from "element-plus";
import { createFlowInstance } from "@/api/modules/flow";
import { useFlowStore } from "@/stores/flowStore";
import { logger } from "@/utils/logger";
import { NODE_STATES } from "@/constants/nodeStates";
import { useFlowCache } from "@/composables/useFlowCache";

/**
 * 流程實例操作 Composable
 *
 * 這個 composable 專注於流程實例的上下文管理和節點間數據交互功能，包括：
 * - 流程上下文的初始化和管理
 * - 流程統計資訊的更新
 * - 節點間數據傳遞的管理
 */

// 流程執行階段定義
export const FLOW_PHASES = {
  INITIALIZATION: "initialization",
  PROCESSING: "processing",
  COMPLETED: "completed",
  ERROR: "error",
  READY: "ready",
};

// 執行狀態類型
export const EXECUTION_ACTIONS = {
  STARTED: "started",
  COMPLETED: "completed",
  ERROR: "error",
  ERROR_CLEARED: "error_cleared",
};

export function useFlowInstance() {
  const flowStore = useFlowStore();
  const creating = ref(false);
  const error = ref(null);

  /**
   * 初始化流程上下文
   * @returns {Object} 初始化的上下文對象
   */
  const initializeContext = () => {
    return {
      // 全域變數，可供所有節點訪問
      globalVariables: {
        createdAt: new Date().toISOString(),
        environment: process.env.NODE_ENV || "development",
      },
      // 節點間共享的數據
      sharedData: {},
      // 流程執行統計資訊
      statistics: {
        totalNodes: 0,
        completedNodes: 0,
        errorNodes: 0,
        executionProgress: 0,
        lastUpdated: new Date().toISOString(),
      },
      // 執行歷史記錄
      executionHistory: [],
      // 流程執行階段
      executionPhase: FLOW_PHASES.INITIALIZATION,
    };
  };

  /**
   * 確保存在流程實例，如果不存在則創建臨時實例
   * @param {Object} nodeProps - 節點屬性
   * @returns {Promise<Object>} 流程實例
   */
  const ensureFlowInstance = async (nodeProps) => {
    // 如果已有當前實例，直接返回
    if (flowStore.currentInstance) {
      return flowStore.currentInstance;
    }

    creating.value = true;
    error.value = null;

    try {
      // console.log("useFlowInstance", "創建臨時測試實例...");

      // 創建臨時實例數據
      const tempInstance = {
        projectId: flowStore.projectId || "1", // 優先使用 store 中的 projectId
        templateId: flowStore.templateId || "1", // 優先使用 store 中的 templateId
        name: "臨時測試實例",
        status: "draft",
        nodes: [
          {
            id: nodeProps.id,
            type: nodeProps.type || "GenericNode",
            data: nodeProps.data || {},
          },
        ],
        edges: [],
        // 初始化流程上下文
        context: initializeContext(),
      };

      // 調用 API 創建實例
      const response = await createFlowInstance(tempInstance);
      const instance = response.data;
      // console.log("useFlowInstance", "成功創建臨時測試實例:", instance);

      // 設置當前實例
      flowStore.setCurrentInstance(instance);

      creating.value = false;
      return instance;
    } catch (error) {
      logger.error("useFlowInstance", "創建臨時測試實例失敗:", error);
      ElMessage.error("創建臨時測試實例失敗: " + (error.message || "未知錯誤"));

      creating.value = false;
      error.value = error;

      throw error;
    }
  };

  /**
   * 執行節點並更新狀態
   * @param {String} nodeId - 節點ID
   * @param {Object} input - 輸入數據
   * @param {Function} processFunction - 處理數據的函數
   * @returns {Promise<Object>} 執行結果
   */
  const executeNode = async (nodeId, input = {}, processFunction) => {
    try {
      // 確保有流程實例
      const instance = await ensureFlowInstance({ id: nodeId });

      // 更新流程上下文中的執行歷史
      await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.STARTED);

      // 更新執行階段
      await updateFlowContextField("executionPhase", FLOW_PHASES.PROCESSING);

      // 執行節點處理邏輯
      // console.log("useFlowInstance", `開始執行節點 ${nodeId} 的處理函數`);
      const result = await processFunction(input);
      // console.log("useFlowInstance", `節點 ${nodeId} 處理函數執行完成`);

      // 更新節點數據
      await flowStore.updateNodeData(instance.id, nodeId, {
        input,
        output: result,
        timestamp: new Date().toISOString(),
      });

      // 更新流程上下文中的執行歷史
      await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.COMPLETED, {
        result: { success: true },
      });

      // 更新統計資訊
      await updateFlowStatistics();

      return result;
    } catch (error) {
      logger.error("useFlowInstance", `執行節點 ${nodeId} 時發生錯誤:`, error);

      // 如果有流程實例，更新節點錯誤狀態
      if (flowStore.currentInstance?.id) {
        // 更新流程上下文中的執行歷史
        await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.ERROR, {
          error: error.message,
        });

        // 更新執行階段
        await updateFlowContextField("executionPhase", FLOW_PHASES.ERROR);

        // 更新統計資訊
        await updateFlowStatistics();
      }

      throw error;
    }
  };

  /**
   * 清除節點錯誤狀態
   * @param {String} nodeId - 節點ID
   * @returns {Promise<void>}
   */
  const clearNodeError = async (nodeId) => {
    try {
      // 確保有流程實例
      const instance = await ensureFlowInstance({ id: nodeId });

      // console.log("useFlowInstance", `清除節點 ${nodeId} 的錯誤狀態`);

      // 更新節點狀態
      await flowStore.updateNodeState(instance.id, nodeId, {
        status: NODE_STATES.DEFAULT,
        error: null,
        errorDetails: null,
      });

      // 更新流程上下文中的執行歷史
      await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.ERROR_CLEARED);

      // 如果所有節點都沒有錯誤，更新執行階段
      const hasErrors = Object.values(
        flowStore.currentInstance.value?.nodeStates || {}
      ).some(
        (state) =>
          state.status === NODE_STATES.ERROR || state.status === "failed"
      );

      if (!hasErrors) {
        await updateFlowContextField("executionPhase", FLOW_PHASES.READY);
      }

      // 更新統計資訊
      await updateFlowStatistics();

      // console.log("useFlowInstance", `節點 ${nodeId} 的錯誤狀態已清除`);
    } catch (error) {
      logger.error("useFlowInstance", `清除節點錯誤狀態失敗:`, error);
      throw error;
    }
  };

  /**
   * 更新流程上下文字段
   * @param {String} field - 字段名稱
   * @param {*} valueOrUpdater - 新值或更新函數
   * @returns {Promise<Object>} 更新後的上下文
   */
  const updateFlowContextField = async (field, valueOrUpdater) => {
    try {
      const instance = flowStore.currentInstance;
      if (!instance) {
        logger.warn("useFlowInstance", "無法更新流程上下文：當前實例不存在");
        return null;
      }

      // 確保上下文存在
      if (!instance.context) {
        instance.context = initializeContext();
      }

      // 計算新值
      let newValue;
      if (typeof valueOrUpdater === "function") {
        newValue = valueOrUpdater(instance.context[field]);
      } else {
        newValue = valueOrUpdater;
      }

      // 更新上下文
      const updatedContext = {
        ...instance.context,
        [field]: newValue,
      };

      // 使用緩存系統 //NOTE: useFlowCache 會用到(updateNodeCache)
      const flowCache = useFlowCache(instance.id);
      await flowCache.updateNodeCache("context", {
        status: "completed",
        result: updatedContext,
        lastUpdate: new Date().toISOString(),
      });

      // 更新本地狀態 //NOTE: 重點
      instance.context = updatedContext;

      return updatedContext;
    } catch (error) {
      logger.error(
        "useFlowInstance",
        `更新流程上下文字段 ${field} 失敗:`,
        error
      );
      throw error;
    }
  };

  /**
   * 獲取流程上下文中的特定字段
   * @param {String} field - 上下文字段名
   * @param {any} defaultValue - 默認值
   * @returns {any} 字段值
   */
  const getFlowContextField = (field, defaultValue = null) => {
    if (!flowStore.currentInstance?.context) return defaultValue;
    return flowStore.currentInstance.context[field] ?? defaultValue;
  };

  /**
   * 更新流程共享數據
   * @param {String} key - 數據鍵名
   * @param {any} value - 數據值
   */
  const updateSharedData = async (key, value) => {
    await updateContext("sharedData", (sharedData = {}) => {
      return {
        ...sharedData,
        [key]: value,
      };
    });
  };

  /**
   * 獲取流程共享數據
   * @param {String} key - 數據鍵名
   * @param {any} defaultValue - 默認值
   * @returns {any} 數據值
   */
  const getSharedData = (key, defaultValue = null) => {
    const sharedData = getFlowContextField("sharedData", {});
    return sharedData[key] ?? defaultValue;
  };

  /**
   * 更新流程全域變數- TODO: 移除
   * @param {String} key - 變數名
   * @param {any} value - 變數值
   */
  const updateGlobalVariable = async (key, value) => {
    //先取得全域變數
    const globalVariables = getFlowContextField("globalVariables", {});
    //更新全域變數
    await updateContext("globalVariables", (variables = {}) => {
      console.log("DDDDD updateGlobalVariable!", {
        ...globalVariables,
        [key]: value,
      });
      return {
        ...globalVariables,
        [key]: value,
      };
    });
  };

  /**
   * 批量更新流程全域變數
   * @param {Object} variableMap - 變數映射對象
   * @param {Object} options - 選項
   */
  const updateGlobalVariables = async (variableMap, options = {}) => {
    if (typeof variableMap === "string" && arguments.length >= 2) {
      // 兼容舊的 API 格式: updateGlobalVariables(key, value) //TODO: 移除
      const key = variableMap;
      const value = arguments[1];
      return await updateGlobalVariable(key, value);
    }

    await updateContext("globalVariables", (variables = {}) => {
      return {
        ...variables,
        ...variableMap,
      };
    });
  };

  /**
   * 獲取流程全域變數
   * @param {String} key - 變數名
   * @param {any} defaultValue - 默認值
   * @returns {any} 變數值
   */
  const getGlobalVariable = (key, defaultValue = null) => {
    const variables = getFlowContextField("globalVariables", {});
    return variables[key] ?? defaultValue;
  };

  /**
   * 更新流程統計資訊
   */
  const updateFlowStatistics = async () => {
    if (!flowStore.currentInstance?.id) return;

    // 計算統計資訊
    const stats = calculateExecutionStatistics();

    // 更新統計資訊
    await updateFlowContextField("statistics", {
      ...stats,
      lastUpdated: new Date().toISOString(),
    });
  };

  /**
   * 獲取節點間的數據傳遞
   * @param {String} sourceNodeId - 源節點ID
   * @param {String} targetNodeId - 目標節點ID
   * @returns {Object|null} 節點間傳遞的數據
   */
  const getNodeDataTransfer = (sourceNodeId, targetNodeId) => {
    const dataTransfers = getFlowContextField("dataTransfers", {});
    const key = `${sourceNodeId}->${targetNodeId}`;
    return dataTransfers[key] || null;
  };

  /**
   * 設置節點間的數據傳遞
   * @param {String} sourceNodeId - 源節點ID
   * @param {String} targetNodeId - 目標節點ID
   * @param {Object} data - 要傳遞的數據
   */
  const setNodeDataTransfer = async (sourceNodeId, targetNodeId, data) => {
    const key = `${sourceNodeId}->${targetNodeId}`;
    await updateContext("dataTransfers", (dataTransfers = {}) => {
      return {
        ...dataTransfers,
        [key]: {
          data,
          timestamp: new Date().toISOString(),
        },
      };
    });
  };

  /**
   * 獲取流程執行階段
   * @returns {String} 執行階段
   */
  const getExecutionPhase = () => {
    return getFlowContextField("executionPhase", FLOW_PHASES.INITIALIZATION);
  };

  /**
   * 設置流程執行階段
   * @param {String} phase - 執行階段
   */
  const setExecutionPhase = async (phase) => {
    await updateContext("executionPhase", phase);
  };

  /**
   * 獲取流程執行歷史
   * @returns {Array} 執行歷史
   */
  const getExecutionHistory = () => {
    return getFlowContextField("executionHistory", []);
  };

  /**
   * 清除流程上下文
   */
  const clearFlowContext = async () => {
    if (!flowStore.currentInstance?.id) return;

    // 重新初始化上下文
    await flowStore.updateInstance(flowStore.currentInstance.id, {
      context: initializeContext(),
    });

    // console.log("useFlowInstance", "流程上下文已清除並重新初始化");
  };

  /**
   * 檢查流程狀態
   */
  const validateFlowState = () => {
    if (!flowStore.currentInstance?.id) {
      throw new Error("No active flow instance");
    }
    return true;
  };

  /**
   * 執行進度追蹤
   */
  const trackExecutionProgress = async (nodeId, action, data = {}) => {
    await updateFlowContextField("executionHistory", (history = []) => {
      return [
        ...history,
        {
          nodeId,
          action,
          timestamp: new Date().toISOString(),
          ...data,
        },
      ];
    });
  };

  /**
   * 計算執行統計
   */
  const calculateExecutionStatistics = () => {
    const nodeStates = flowStore.currentInstance.nodeStates || {};
    const stats = {
      totalNodes: Object.keys(nodeStates).length,
      completedNodes: 0,
      errorNodes: 0,
      executionProgress: 0,
    };

    Object.values(nodeStates).forEach((state) => {
      if (state.status === NODE_STATES.COMPLETED) stats.completedNodes++;
      if (state.status === NODE_STATES.ERROR) stats.errorNodes++;
    });

    stats.executionProgress =
      stats.totalNodes > 0
        ? Math.round((stats.completedNodes / stats.totalNodes) * 100)
        : 0;

    return stats;
  };

  /**
   * 上下文更新
   */
  const updateContext = async (field, updater) => {
    validateFlowState();
    await updateFlowContextField(field, updater);
    await updateFlowStatistics();
  };

  return {
    ensureFlowInstance,
    executeNode,
    clearNodeError,
    updateFlowContextField,
    getFlowContextField,
    updateSharedData,
    getSharedData,
    updateGlobalVariable,
    updateGlobalVariables,
    getGlobalVariable,
    updateFlowStatistics,
    getNodeDataTransfer,
    setNodeDataTransfer,
    getExecutionPhase,
    setExecutionPhase,
    getExecutionHistory,
    clearFlowContext,
    creating,
    error,
    flowStore,
    FLOW_PHASES,
    EXECUTION_ACTIONS,
    validateFlowState,
    trackExecutionProgress,
    calculateExecutionStatistics,
    updateContext,
  };
}
