import { ref, shallowRef, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 工作流程組件 (主要使用於節點定義管理，使其能動態載入所有節點的組件)
export function useFlowNodeComponents() {
  const flowNodeComponents = ref({});
  const showPreview = ref(false);
  const currentComponent = shallowRef(null);
  const previewNodeData = ref(null);
  const nodes = ref([]);

  // 從路徑中獲取組件名稱
  const getComponentName = (path) => {
    return path.split("/").pop().replace(".vue", "");
  };

  // 動態載入組件
  const loadComponent2 = async (componentPath, componentName) => {
    try {
      // 確保組件名稱包含 .vue 副檔名
      const fullComponentName = componentName.endsWith(".vue")
        ? componentName
        : `${componentName}.vue`;
      // console.log("fullComponentName", componentPath, fullComponentName);
      // 構建完整的組件路徑
      //const fullPath = `/src/components/flow-nodes/${componentPath}/${fullComponentName}`;
      const fullPath = componentPath.startsWith("/components")
        ? `/src/${componentPath.slice(1)}/${fullComponentName}`
        : `/src/components/flow-nodes/${componentPath}/${fullComponentName}`;

      // 動態載入組件
      const module = await import(/* @vite-ignore */ fullPath);
      return module.default;
    } catch (error) {
      // console.error("載入組件失敗：", error);
      throw new Error(`載入組件失敗：${error.message}`);
    }
  };

  // 預先導入所有組件
  const modules = import.meta.glob("/src/components/flow-nodes/**/*.vue");

  const loadComponent = async (componentPath, componentName) => {
    try {
      // 確保組件名稱包含 .vue 副檔名
      const fullComponentName = componentName.endsWith(".vue")
        ? componentName
        : `${componentName}.vue`;

      // 構建完整的組件路徑
      const fullPath = componentPath.startsWith("/components")
        ? `/src/${componentPath.slice(1)}/${fullComponentName}`
        : `/src/components/flow-nodes/${componentPath}/${fullComponentName}`;

      // 檢查是否有對應的模組
      if (modules[fullPath]) {
        const module = await modules[fullPath]();
        return module.default;
      } else {
        throw new Error(`找不到組件: ${fullPath}`);
      }
    } catch (error) {
      // console.error("載入組件失敗：", error);
      throw new Error(`載入組件失敗：${error.message}`);
    }
  };

  // 獲取組件的 props 定義
  const getComponentProps = async (componentPath, componentName) => {
    try {
      // 載入組件
      const component = await loadComponent(componentPath, componentName);

      // 檢查組件是否存在
      if (!component) {
        throw new Error("組件不存在");
      }

      // 獲取組件的 props 定義
      const props = component.props || {};

      // 將 props 轉換為更易於使用的格式
      const propsInfo = {};

      // 處理不同格式的 props 定義
      if (Array.isArray(props)) {
        // 如果 props 是數組形式 ['prop1', 'prop2']
        props.forEach((propName) => {
          propsInfo[propName] = {
            type: null,
            required: false,
            default: undefined,
            description: "",
          };
        });
      } else {
        // 如果 props 是對象形式 { prop1: { type: String, ... } }
        Object.entries(props).forEach(([propName, propConfig]) => {
          // 處理簡單類型定義，如 prop1: String
          if (typeof propConfig === "function") {
            propsInfo[propName] = {
              type: propConfig.name,
              required: false,
              default: undefined,
              description: "",
            };
          }
          // 處理對象類型定義，如 prop1: { type: String, required: true, ... }
          else if (typeof propConfig === "object") {
            propsInfo[propName] = {
              type: propConfig.type?.name || "Any",
              required: !!propConfig.required,
              default: propConfig.default,
              description: propConfig.description || "",
            };
          }
        });
      }

      return propsInfo;
    } catch (error) {
      // console.error("獲取組件 props 失敗：", error);
      throw new Error(`獲取組件 props 失敗：${error.message}`);
    }
  };

  // 預覽組件
  const previewComponent = async ({
    componentPath,
    componentName,
    nodeData,
  }) => {
    try {
      if (!componentPath || !componentName) {
        throw new Error("組件路徑或名稱未提供");
      }

      // console.log("componentPath", componentPath, nodeData);

      // 載入組件
      currentComponent.value = await loadComponent(
        componentPath,
        componentName
      );

      // 設置預覽節點數據
      const newNode = {
        id: "preview-node",
        type: "custom",
        position: { x: 0, y: 0 },
        draggable: false,
        selectable: false,
        data: {
          ...nodeData,
          title: nodeData.name,
          description: nodeData.description,
          type: nodeData.nodeType,
        },
      };

      // 更新節點數據
      nodes.value = [newNode];
      previewNodeData.value = newNode;

      showPreview.value = true;
    } catch (error) {
      ElMessageBox.alert(
        `<div class="text-red-500 font-bold text-lg mb-2">❌ 載入組件失敗</div>
         <div class="text-gray-700">請檢查：</div>
         <ul class="list-disc pl-4 mt-2 space-y-1 text-gray-600">
           <li>組件路徑是否正確</li>
           <li>檔案名稱是否有錯字</li>
           <li>對應的 Vue 檔案是否存在</li>
         </ul>`,
        "錯誤提示",
        {
          confirmButtonText: "我知道了",
          dangerouslyUseHTMLString: true,
          customClass: {
            container: "error-alert-container",
          },
          width: "360px",
        }
      );
      throw error;
    }
  };

  // 關閉預覽
  const closePreview = () => {
    showPreview.value = false;
    currentComponent.value = null;
    previewNodeData.value = null;
    nodes.value = [];
  };

  // 載入所有組件
  const loadFlowNodeComponents = async () => {
    try {
      // console.log("useFlowNodeComponents", "開始載入流程節點組件...");
      const modules = import.meta.glob("@/components/flow-nodes/**/*.vue", {
        eager: true,
      });

      const componentCount = Object.keys(modules).length;
      // console.log("useFlowNodeComponents", `找到 ${componentCount} 個組件`);

      if (componentCount === 0) {
        // console.warn("警告: 未找到任何流程節點組件!");
      }

      flowNodeComponents.value = modules;
      // console.log("useFlowNodeComponents", "流程節點組件載入完成");
      return modules;
    } catch (error) {
      // console.log("useFlowNodeComponents", "載入流程節點組件失敗:", error);
      // 嘗試使用不同的方式載入
      try {
        // console.log("useFlowNodeComponents", "嘗試使用替代方式載入組件...");
        // 明確列出需要的組件，這是一個應急方案
        const basicModules = {
          // 基礎組件應該始終可用
        };
        flowNodeComponents.value = {
          ...flowNodeComponents.value,
          ...basicModules,
        };
        // console.log("useFlowNodeComponents", "使用替代方式載入了基礎組件");
        return flowNodeComponents.value;
      } catch (fallbackError) {
        // console.log(
        //   "useFlowNodeComponents",
        //   "替代載入方式也失敗:",
        //   fallbackError
        // );
        throw new Error(`載入流程組件失敗: ${error.message}`);
      }
    }
  };

  // 在組件掛載時載入
  onMounted(() => {
    loadFlowNodeComponents();
  });

  return {
    flowNodeComponents,
    showPreview,
    currentComponent,
    previewNodeData,
    nodes,
    getComponentName,
    loadComponent,
    previewComponent,
    closePreview,
    loadFlowNodeComponents,
    getComponentProps,
  };
}
