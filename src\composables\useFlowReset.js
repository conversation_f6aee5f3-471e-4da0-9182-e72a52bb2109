import { ElMessage, ElMessageBox } from "element-plus";
import { updateFlowInstance } from "@/api/modules/flow";
import { useFlowCache } from "@/composables/useFlowCache";
import { executeAllNodeResets } from "@/composables/useNodeReset";

/**
 * 用於重置流程實例節點的 Composable
 * 提供了重置流程實例所有節點數據的功能
 */
export function useFlowReset() {
  /**
   * 重置流程實例的所有節點數據
   * 包含確認對話框和錯誤處理邏輯
   *
   * @param {Object} options - 重置選項
   * @param {string} options.flowInstanceId - 流程實例ID
   * @param {Array} options.elements - 元素數組 (節點和連接線)
   * @param {Object} options.flowInstanceRef - 流程實例引用對象 (用於更新本地狀態)
   * @param {boolean} options.reloadAfterReset - 重置後是否重新載入頁面，默認為 false
   * @param {boolean} options.skipConfirmation - 是否跳過確認對話框，默認為 false
   * @returns {Promise<boolean>} - 返回是否成功重置
   */
  const handleResetAllNodes = async ({
    flowInstanceId,
    elements,
    flowInstanceRef,
    reloadAfterReset = false,
    skipConfirmation = false,
  }) => {
    // console.log("DO useFlowReset: 開始重置所有節點數據!", flowInstanceId);
    try {
      // 只有在不跳過確認時才顯示確認對話框
      if (!skipConfirmation) {
        await ElMessageBox.confirm(
          "確定要重置所有節點的數據嗎？此操作不可恢復。",
          "重置確認",
          {
            confirmButtonText: "確定重置",
            cancelButtonText: "取消",
            type: "warning",
          }
        );
      }

      // 準備要清空的數據
      const updateData = {
        context: {}, // 清空上下文
        nodes: elements
          .filter((el) => !el.source)
          .map((node) => ({
            ...node,
            data: {
              ...node.data,
              result: null, // 清空結果
              error: null, // 清空錯誤
              status: "IDLE", // 重置狀態
              executionTime: null,
              lastExecuted: null,
            },
          })),
        edges: elements.filter((el) => el.source),
        nodeStates: {}, // 清空節點狀態
        nodeData: {}, // 清空節點數據
        logs: [], // 清空日誌
      };

      // 調用 API 更新流程實例
      await updateFlowInstance(flowInstanceId, updateData);

      // 直接調用所有已註冊的節點重置函數
      executeAllNodeResets();

      // 清理緩存(indexdb)
      await useFlowCache(flowInstanceId).clearAllCache();

      // 更新本地狀態 (如果提供了流程實例引用)
      if (flowInstanceRef) {
        flowInstanceRef.context = {};
        flowInstanceRef.nodeStates = {};
        flowInstanceRef.logs = [];
        flowInstanceRef.nodeData = {};
      }

      // 確保 flowStore 狀態也被重置，觸發響應式更新
      const { useFlowStore } = await import("@/stores/flowStore");
      const flowStore = useFlowStore();

      if (flowStore.currentInstance?.id === flowInstanceId) {
        // 重置所有節點狀態為默認值
        const resetNodeStates = {};
        elements
          .filter((el) => !el.source)
          .forEach((node) => {
            resetNodeStates[node.id] = {
              status: "IDLE",
              data: null,
              error: null,
              updatedAt: new Date().toISOString(),
            };
          });

        // 更新 flowStore 的當前實例
        flowStore.currentInstance.nodeStates = resetNodeStates;
        flowStore.currentInstance.context = {};
        flowStore.currentInstance.nodeData = {};
        flowStore.currentInstance.logs = [];

        console.log("useFlowReset: 已重置 flowStore 狀態，觸發節點同步更新");
      }

      // 返回更新後的元素
      const updatedElements = elements.map((el) => {
        if (!el.source) {
          return {
            ...el,
            data: {
              ...el.data,
              result: null,
              error: null,
              status: "IDLE",
              executionTime: null,
              lastExecuted: null,
            },
            class: (el.class || "").replace(/\bflow-node--\w+\b/g, ""),
          };
        }
        return el;
      });

      ElMessage.success("已重置所有節點數據");

      // 重新整理頁面 (可選)
      if (reloadAfterReset) {
        window.location.reload();
      }

      return { success: true, updatedElements };
    } catch (error) {
      if (error === "cancel") return { success: false, reason: "user_cancel" };
      console.error("重置節點數據失敗", error);
      ElMessage.error("重置節點數據失敗" + error);
      return { success: false, reason: "error", error };
    }
  };

  return {
    handleResetAllNodes,
  };
}
