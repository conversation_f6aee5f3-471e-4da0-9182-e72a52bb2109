/**
 * 節點執行邏輯 Composable
 *
 * 這個 composable 專注於單個節點執行的生命週期管理，包括：
 * - 節點執行狀態的維護
 * - 節點錯誤處理和恢復
 * - 節點執行結果的處理
 */
import { ref, computed } from "vue";
import { useFlowInstance } from "@/composables/useFlowInstance";
import { useFlowStore } from "@/stores/flowStore";
import { globalEventBus, NodeEventType } from "@/utils/eventBus";
import { NODE_STATES } from "@/constants/nodeStates";
import { useFlowReset } from "@/composables/useFlowReset";

/**
 * 節點執行邏輯 Composable
 * @param {Object} options - 選項
 * @param {string} options.nodeId - 節點ID
 * @param {string} options.nodeType - 節點類型
 * @param {string} options.nodeName - 節點名稱
 * @param {Object} options.nodeRef - 節點引用
 * @param {string} options.nodeDefinitionId - 節點定義ID
 * @param {Array} options.requiredVariables - 需要等待的變數列表
 * @param {number} options.waitTime - 等待時間（毫秒）
 * @param {number} options.maxRetries - 最大重試次數
 * @returns {Object} - 節點執行邏輯相關方法和狀態
 */
export function useNodeExecution({
  nodeId,
  /**
   * 必要性：
   * nodeType 不是絕對必要的
   * 主要用於事件追蹤和調試目的
   * 在事件系統中用作來源標識，幫助識別事件的發出者
   * 唯一性：
   * 不需要唯一識別
   * 可以是描述性的類型名稱，如 "development-plan"、"file-process" 等
   * 主要用於分類和識別節點的類型，而不是唯一標識節點
   */
  nodeType = "default", //TODO: need confirm
  nodeName,
  nodeRef,
  nodeDefinitionId = "", // 節點定義ID，用於關聯資料庫中的節點定義
  requiredVariables = [], // 檢查必要的變數
  waitTime = 1000,
  maxRetries = 3,
}) {
  // 使用流程實例 composable
  const {
    executeNode: executeFlowNode,
    clearNodeError,
    updateSharedData,
    getSharedData,
    updateGlobalVariable,
    updateGlobalVariables,
    getGlobalVariable,
  } = useFlowInstance();

  // 獲取流程 store
  const flowStore = useFlowStore();

  // 獲取重置功能
  const { handleResetAllNodes } = useFlowReset();

  // 移除對 useWorkflowManager 的依賴，避免創建多個實例

  // 節點狀態
  const executing = ref(false);
  const errorMessage = ref("");
  const errorDetails = ref(null);
  const outputData = ref(null);
  const nodeStatus = ref(NODE_STATES.DEFAULT);
  const resultData = ref(null);

  // computed properties 用於節點狀態管理
  const isIdle = computed(() => !executing.value);
  const isCompleted = computed(
    () => nodeStatus.value === NODE_STATES.COMPLETED
  );
  const hasError = computed(() => nodeStatus.value === NODE_STATES.ERROR);

  /**
   * 安全地清理對象，移除循環引用和不可序列化的屬性
   * @param {*} obj - 要清理的對象
   * @param {Set} seen - 已訪問的對象集合（防止循環引用）
   * @returns {*} 清理後的對象
   */
  const safeCleanObject = (obj, seen = new Set()) => {
    try {
      if (obj === null || obj === undefined) return obj;

      // 基本類型直接返回
      if (typeof obj !== "object") return obj;

      // 檢查循環引用
      if (seen.has(obj)) return "[Circular Reference]";
      seen.add(obj);

      // 處理日期對象
      if (obj instanceof Date) return obj.toISOString();

      // 處理 Vue 響應式對象，提取原始值
      if (obj._value !== undefined) {
        // computed ref 或 ref 對象
        return safeCleanObject(obj._value, seen);
      }

      // 處理數組
      if (Array.isArray(obj)) {
        return obj.map((item) => safeCleanObject(item, new Set(seen)));
      }

      // 處理普通對象
      const cleaned = {};
      for (const [key, value] of Object.entries(obj)) {
        // 跳過以 _ 開頭的私有屬性和一些 Vue 內部屬性
        if (
          key.startsWith("_") ||
          key.startsWith("$") ||
          key === "__v_isRef" ||
          key === "__v_isReadonly"
        ) {
          continue;
        }

        try {
          cleaned[key] = safeCleanObject(value, new Set(seen));
        } catch (err) {
          console.warn(
            `useNodeExecution: 清理屬性 ${key} 時發生錯誤，跳過該屬性`,
            err
          );
          cleaned[key] = "[Serialization Error]";
        }
      }

      seen.delete(obj);
      return cleaned;
    } catch (error) {
      console.warn("useNodeExecution: 清理對象時發生錯誤", error);
      return "[Cleanup Error]";
    }
  };

  /**
   * 更新節點狀態
   * @param {String} status - 節點狀態
   * @param {Object} result - 節點執行結果
   * @param {Error} error - 錯誤對象
   * @returns {Promise<void>}
   */
  const updateNodeStatus = async (status, result = null, error = null) => {
    try {
      // console.log("useNodeExecution", `更新節點 ${nodeId} 狀態為: ${status}`, {
      //   result,
      //   error,
      //   hasNodeRef: !!nodeRef?.value,
      //   nodeRefUpdateMethod: !!nodeRef?.value?.updateNodeStatus
      // });

      // 清理 result 對象，避免循環引用
      const cleanResult = result ? safeCleanObject(result) : null;

      // 如果節點引用存在，更新其內部狀態
      if (nodeRef?.value?.updateNodeStatus) {
        nodeRef.value.updateNodeStatus(status, cleanResult, error);
      } else {
        console.warn(
          "useNodeExecution",
          `節點 ${nodeId} 的 nodeRef 或 updateNodeStatus 方法不存在`,
          {
            nodeRef: nodeRef?.value,
            updateNodeStatus: nodeRef?.value?.updateNodeStatus,
          }
        );
      }

      // 發送事件通知 - 使用自定義事件
      const detail = {
        nodeId,
        status,
        result: cleanResult, // 使用清理後的結果
        error,
        nodeDefinitionId,
        timestamp: new Date().toISOString(),
      };

      const event = new CustomEvent("flow:nodeStateChange", { detail });
      window.dispatchEvent(event);

      // 使用事件總線（全局）
      globalEventBus.emit(NodeEventType.STATE_CHANGE, detail, {
        source: nodeType,
      });

      // 更新本地狀態
      nodeStatus.value = status;
      if (cleanResult) resultData.value = cleanResult;
      if (error) {
        errorMessage.value = error.message || "執行時發生未知錯誤";
        errorDetails.value = error;
      }
    } catch (err) {
      console.error(
        "useNodeExecution",
        `更新節點 ${nodeId} 狀態時發生錯誤:`,
        err
      );
    }
  };

  /**
   * 準備節點輸入數據
   * @param {Object} context - 執行上下文
   * @returns {Object} 準備好的輸入數據
   */
  const prepareNodeInput = (context) => {
    return {
      ...context,
      timestamp: new Date().toISOString(),
      nodeType,
      nodeName,
      nodeDefinitionId,
    };
  };

  /**
   * 處理節點執行結果
   * @param {Object} result - 執行結果
   * @returns {Object} 處理後的結果
   */
  const handleNodeResult = (result) => {
    // 檢查是否需要互動
    if (result && result.requiresInteraction) {
      // 互動式節點中斷，設定為 PENDING 狀態
      const pendingResult = {
        ...result,
        timestamp: new Date().toISOString(),
        nodeId,
        nodeName,
        nodeDefinitionId,
      };

      // 更新本地狀態
      outputData.value = pendingResult;

      // 更新節點狀態為 PENDING
      updateNodeStatus(NODE_STATES.PENDING, pendingResult);

      return pendingResult;
    }

    // 正常完成的結果處理
    const completeResult = {
      ...result,
      timestamp: new Date().toISOString(),
      nodeId,
      nodeName,
      nodeDefinitionId,
    };

    // 更新本地狀態
    outputData.value = result;

    // 更新節點狀態為 COMPLETED
    updateNodeStatus(NODE_STATES.COMPLETED, completeResult);

    return completeResult;
  };

  /**
   * 處理節點執行錯誤
   * @param {Error} error - 錯誤對象
   */
  const handleNodeError = (error) => {
    console.error("useNodeExecution", `執行節點 ${nodeId} 時發生錯誤:`, error);

    // 更新錯誤狀態
    errorMessage.value = error.message || "執行節點時發生未知錯誤";
    errorDetails.value = {
      message: error.message,
      stack: error.stack,
    };

    // 更新節點狀態
    updateNodeStatus(NODE_STATES.ERROR, null, error);

    // 觸發節點錯誤事件
    globalEventBus.emit(
      NodeEventType.ERROR,
      {
        nodeId,
        nodeDefinitionId,
        error: {
          message: error.message,
          stack: error.stack,
        },
        timestamp: new Date().toISOString(),
      },
      { source: nodeType }
    );

    throw error;
  };

  // 驗證函數 - 檢查輸入是否有效
  const validateInput = (input) => {
    if (!input) return false;
    // TODO:可以加入更多驗證邏輯
    return true;
  };

  // 清除錯誤的輔助函數
  const clearErrors = () => {
    errorMessage.value = "";
    errorDetails.value = null;
  };

  // 保存結果的輔助函數 //TODO: WHY shareddata
  const saveNodeResult = async (result) => {
    await updateSharedData(nodeId, {
      detail: result,
      timestamp: new Date().toISOString(),
      nodeId,
      nodeName,
      nodeDefinitionId,
    });
  };

  // 錯誤處理包裝器
  const executeWithErrorHandling = async (operation) => {
    try {
      const result = await operation();

      // handleNodeResult 已經處理了 requiresInteraction 的情況
      // 這裡只需要返回處理後的結果
      return result;
    } catch (error) {
      return handleNodeError(error);
    } finally {
      // 只有在非 PENDING 狀態時才結束執行狀態
      if (nodeStatus.value !== NODE_STATES.PENDING) {
        executing.value = false;
        nodeRef?.value?.setRunningState?.(false);
      }
    }
  };

  // 執行邏輯 - 實際處理節點執行
  const processNode = async (inputData, processFunction) => {
    // 如果節點不是處於 PENDING 狀態，則開始新的執行
    if (nodeStatus.value !== NODE_STATES.PENDING) {
      executing.value = true;
      updateNodeStatus(NODE_STATES.RUNNING);
      clearErrors();
    } else {
      // 從 PENDING 狀態繼續執行
      // console.log("useNodeExecution", `節點 ${nodeId} 從 PENDING 狀態繼續執行`);
      updateNodeStatus(NODE_STATES.RUNNING);
    }

    // console.log("useNodeExecution", `準備執行節點 ${nodeId}`);
    // console.log("useNodeExecution", "輸入數據:", inputData);

    // NOTE: 這邊的 executeFlowNode 是 流程實例的useFlowInstance 的 executeNode
    return await executeFlowNode(nodeId, inputData, processFunction);
  };

  // 新增：檢查必要變數是否存在
  const checkRequiredVariables = (context) => {
    if (!requiredVariables.length) return true;

    const missingVariables = requiredVariables.filter(
      (variable) => !context.globalVariables?.[variable]
    );

    if (missingVariables.length > 0) {
      throw new Error(`缺少必要變數: ${missingVariables.join(", ")}`);
    }

    return true;
  };

  // 新增：等待函數
  const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  /**
   * 執行節點
   * @param {Object} context - 執行上下文
   * @param {Function} processFunction - 處理函數
   * @returns {Promise<Object>} - 執行結果
   */
  const executeNode = async (context = {}, processFunction) => {
    console.log("DO useNodeExecution.executeNode!", nodeType);
    console.log("DO useNodeExecution.executeNode!", getSharedData(nodeId));

    if (nodeType.includes("input")) {
      // 如果是輸入節點，檢查是否已經執行過
      const existingData = getSharedData(nodeId);
      if (existingData && existingData.detail) {
        // 自動重置數據並繼續執行
        console.log("檢測到輸入節點已執行過，正在自動重置數據...");

        try {
          // 直接調用重置功能，跳過用戶確認
          const result = await handleResetAllNodes({
            flowInstanceId: flowStore.currentInstance?.id,
            elements: flowStore.currentInstance
              ? [
                  ...(flowStore.currentInstance.nodes || []),
                  ...(flowStore.currentInstance.edges || []),
                ]
              : [],
            flowInstanceRef: flowStore.currentInstance,
            skipConfirmation: true, // 跳過確認對話框
            reloadAfterReset: false,
          });

          // 檢查重置是否成功
          if (!result.success) {
            console.error("自動重置失敗:", result.reason, result.error);
            updateNodeStatus("error", null, "自動重置數據失敗");
            return;
          }

          // console.log("數據重置完成，繼續執行節點", flowStore);

          // 重置完成後繼續正常執行流程，不返回
        } catch (error) {
          console.error("自動重置數據失敗:", error);
          updateNodeStatus("error", null, "自動重置數據失敗: " + error.message);
          return;
        }
      }
    }

    //判斷 requiredVariables 是否為空
    console.log("executeNode requiredVariables", requiredVariables);
    if (requiredVariables.length <= 0) {
      if (!validateInput(context)) {
        console.error(
          "useNodeExecution",
          `無效的輸入數據，無法執行節點 ${nodeId}`
        );
        return;
      }
    }

    let retryCount = 0;
    let lastError = null;
    // console.log(
    //   "executeNode retryCount:",
    //   retryCount,
    //   "maxRetries:",
    //   maxRetries
    // );
    while (retryCount < maxRetries) {
      try {
        // 等待指定時間
        if (waitTime > 0) {
          await wait(waitTime);
        }

        // 如果不是並行匯流節點，則需要檢查輸入數據是否有效 NOTE: 單一檢查，仍要重試
        if (requiredVariables.length <= 0 && !validateInput(context)) {
          console.error(
            "useNodeExecution",
            `無效的輸入數據，無法執行節點 ${nodeId}`
          );
          return;
        } else {
          // 檢查必要變數
          checkRequiredVariables(context);
        }

        // 更新節點狀態為執行中
        //updateNodeStatus("running");

        // 執行處理函數
        //const result = await processFunction(context);

        // 清除錯誤信息
        errorMessage.value = null;
        errorDetails.value = null;

        let result = null;
        const executionResult = await executeWithErrorHandling(async () => {
          const inputData = prepareNodeInput(context);
          result = await processNode(inputData, processFunction);
          await saveNodeResult(result);
          return handleNodeResult(result);
        });

        // 檢查執行結果
        if (executionResult && executionResult.requiresInteraction) {
          // 互動式節點中斷，PENDING 狀態已由 handleNodeResult 設置
          console.log(
            "useNodeExecution",
            `節點 ${nodeId} 等待用戶互動，狀態已設為 PENDING`
          );
          return executionResult;
        }

        // 更新節點狀態為成功
        updateNodeStatus(NODE_STATES.COMPLETED, executionResult);

        return executionResult;
      } catch (error) {
        lastError = error;
        retryCount++;

        // 如果是缺少必要變數的錯誤，等待後重試
        if (error.message.includes("缺少必要變數") && retryCount < maxRetries) {
          console.warn(
            `${nodeName} 重試 ${retryCount}/${maxRetries}: ${error.message}`
          );
          await wait(waitTime * retryCount); // 每次重試等待時間遞增
          continue;
        }

        // 其他錯誤直接拋出
        throw error;
      }
    }

    // 如果所有重試都失敗，拋出最後的錯誤
    throw lastError;
  };

  /**
   * 清除節點錯誤
   */
  const handleClearError = async () => {
    await clearNodeError(nodeId);
    clearErrors();
    // 從任何狀態重置為 DEFAULT，包括 PENDING 狀態
    nodeStatus.value = NODE_STATES.DEFAULT;
    executing.value = false;
  };

  /**
   * 從共享數據中恢復節點狀態
   */
  const restoreFromSharedData = () => {
    const previousData = getSharedData(nodeId);
    if (previousData && previousData.detail) {
      // console.log("useNodeExecution", `從共享數據中恢復節點 ${nodeId} 的狀態`);
      // console.log("useNodeExecution", "恢復的數據:", previousData);

      outputData.value = previousData.detail;
      return previousData.detail;
    }
    return null;
  };

  /**
   * 驗證必要參數
   * @param {string} title - 驗證標題
   * @param {string} errorMsg - 錯誤訊息
   * @param {boolean} validate - 驗證條件
   */
  const validateRequiredFields = (title, errorMsg, validate) => {
    if (validate) {
      console.error(title, errorMsg);
      ElMessage.warning(errorMsg);
      throw new Error(errorMsg);
    }
  };

  /**
   * 從共享數據或報表模式中獲取之前的數據
   * @param {Object} data - 報表模式下的數據
   * @param {boolean} isReportMode - 是否為報表模式
   * @param {string} nodeId - 節點ID
   * @returns {Object} - 之前的數據
   */
  const getPreviousData = (data, isReportMode) => {
    return isReportMode
      ? data?.context?.sharedData?.[nodeId] || {}
      : getSharedData(nodeId);
  };

  /**
   * 提取前置節點數據的便利方法
   * 直接從工作流狀態中獲取並合併前置節點數據
   * @param {Object} inputData - 節點執行的輸入數據（暫未使用，保留以備將來擴展）
   * @returns {Object} - 包含前置節點信息的對象
   */
  const extractPreviousNodeData = () => {
    // 簡化實現，直接從 flowStore 獲取前置節點數據
    const flowContext = flowStore.currentInstance?.context || {};
    const sharedData = flowContext.sharedData || {};

    // 獲取當前節點的前置節點（這裡簡化處理）
    const previousOutputs = {};
    Object.keys(sharedData).forEach((prevNodeId) => {
      if (prevNodeId !== nodeId && sharedData[prevNodeId]?.output) {
        previousOutputs[prevNodeId] = sharedData[prevNodeId].output;
      }
    });

    return {
      previousOutputs,
      globalVariables: flowContext.globalVariables || {},
    };
  };

  /**
   * 檢查節點是否可以執行
   * @returns {boolean} - 是否可以執行
   */
  const canExecute = computed(() => {
    // PENDING 狀態的節點可以重新執行（繼續執行）
    return !executing.value || nodeStatus.value === NODE_STATES.PENDING;
  });

  return {
    // 狀態
    executing,
    errorMessage,
    errorDetails,
    outputData,
    canExecute,
    isIdle,
    isCompleted,
    hasError,
    nodeStatus,
    NODE_STATES,

    // 方法
    executeNode,
    updateNodeStatus,
    handleClearError,
    restoreFromSharedData,
    handleNodeError,
    prepareNodeInput,
    handleNodeResult,
    validateInput,
    clearErrors,

    // 工具方法
    updateSharedData,
    getSharedData,
    updateGlobalVariable,
    updateGlobalVariables,
    getGlobalVariable,
    getPreviousData,
    extractPreviousNodeData,

    // 新增方法
    checkRequiredVariables,

    // 驗證相關方法
    validateRequiredFields,
  };
}
