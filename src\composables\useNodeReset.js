/**
 * 節點重置邏輯 Composable
 *
 * 提供統一的節點重置機制，每個節點可以定義自己的初始狀態恢復邏輯
 * 該 composable 負責監聽節點狀態變化，當狀態從其他狀態變為 idle 時自動觸發重置
 */
import { watch } from "vue";
import { globalEventBus, NodeEventType } from "@/utils/eventBus";

// 全局節點重置函數註冊表
const nodeResetRegistry = new Map();

/**
 * 執行所有已註冊的節點重置函數
 * 由 useFlowReset 在流程重置時直接調用
 */
export function executeAllNodeResets() {
  console.log("DO useNodeReset.executeAllNodeResets");
  for (const [nodeId, nodeInfo] of nodeResetRegistry) {
    try {
      nodeInfo.resetFunction();
    } catch (error) {
      console.error(`節點 ${nodeId} 重置失敗:`, error);
    }
  }
}

/**
 * 創建節點重置函數並註冊到全局註冊表
 * @param {Object} options - 選項
 * @param {string} options.nodeId - 節點ID
 * @param {string} options.nodeName - 節點名稱
 * @param {Function} options.resetHandler - 重置處理函數，當需要重置時會調用
 * @param {Object} options.nodeData_default - 節點的預設數據
 * @param {Object} options.nodeData_value - 節點的當前數據
 * @returns {Function} - 清除註冊的函數
 */
export function useNodeReset({
  nodeId,
  nodeName,
  nodeRef,
  resetHandler,
  nodeData_default,
  nodeData_value,
  config = {},
}) {
  console.log("DO useNodeReset: 開始節點數據!", nodeId, nodeName);
  const { immediate = false, debug = false } = config;

  // 重置數據的通用函數
  const resetNodeData = () => {
    if (nodeData_default && nodeData_value) {
      // 深度複製 default 數據到 value
      const defaultData = nodeData_default.value || nodeData_default;
      const resetData = JSON.parse(JSON.stringify(defaultData));

      if (nodeData_value.value !== undefined) {
        // nodeData_value 是 ref
        nodeData_value.value = resetData;
      } else {
        // nodeData_value 是普通對象，清空後重新賦值
        Object.keys(nodeData_value).forEach(
          (key) => delete nodeData_value[key]
        );
        Object.assign(nodeData_value, resetData);
      }
    }
  };

  // 創建重置監聽器
  const stopWatcher = watch(
    () => nodeRef.value?.nodeState?.status,
    (newStatus, oldStatus) => {
      // 當節點狀態從其他狀態變為 IDLE 時（重置操作），調用重置處理函數
      if (newStatus === "IDLE" && oldStatus && oldStatus !== "IDLE") {
        if (debug || true) {
          console.log(`${nodeName} ${nodeId}: 檢測到重置操作，清除內部數據`);
        }
        // 優先使用新的 default/value 結構
        if (nodeData_default && nodeData_value) {
          // 使用新的 default/value 結構進行重置
          resetNodeData();
        } else if (resetHandler && typeof resetHandler === "function") {
          // 向後相容：僅使用 resetHandler 的方式
          if (debug) {
            console.log("使用 resetHandler 處理重置");
          }
          resetHandler();
        } else {
          if (debug) {
            console.log("未提供重置數據或處理函數，跳過重置");
          }
        }

        // 重要：通知工作流管理器重置該節點的完成追蹤狀態
        const resetEvent = new CustomEvent("flow:nodeReset", {
          detail: {
            nodeId,
            nodeName,
            timestamp: new Date().toISOString(),
          },
        });
        window.dispatchEvent(resetEvent);

        // 同時使用事件總線發送節點重置事件
        globalEventBus.emit("node:reset", {
          nodeId,
          nodeName,
          timestamp: new Date().toISOString(),
        });

        console.log("=== 重置確認完成 ===", nodeData_value);
      }
    },
    { immediate }
  );
  return stopWatcher;
}
