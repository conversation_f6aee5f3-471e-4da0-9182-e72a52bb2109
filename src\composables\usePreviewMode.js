import { ref } from "vue";
import request from "@/api/request";

/**
 * 預覽模式相關的 composable 函數
 * 用於處理節點預覽模式下的臨時專案管理和相關邏輯
 */
export function usePreviewMode() {
  // 是否處於預覽模式
  const isPreviewMode =
    typeof window !== "undefined" &&
    (typeof window.executeNode !== "function" ||
      window.location.pathname.includes("/preview"));

  // 臨時專案 ID
  const tempProjectId = ref(null);

  // 是否正在加載臨時專案
  const isLoadingTempProject = ref(false);

  /**
   * 獲取或創建臨時專案
   * @returns {Promise<string|null>} 臨時專案 ID 或 null
   */
  const getTempProject = async () => {
    if (!isPreviewMode) return null;

    isLoadingTempProject.value = true;

    try {
      // 嘗試從 localStorage 獲取已保存的臨時專案 ID
      let projectId = localStorage.getItem("temp_preview_project_id");

      // 如果有保存的專案 ID，檢查它是否存在
      if (projectId) {
        try {
          const checkResponse = await request.get(`/projects/${projectId}`);
          if (checkResponse.data && checkResponse.data.id) {
            console.info("PreviewMode", `使用已保存的臨時專案: ${projectId}`);
            tempProjectId.value = projectId;
            return projectId;
          } else {
            // 專案不存在，清除 localStorage
            localStorage.removeItem("temp_preview_project_id");
            projectId = null;
          }
        } catch (error) {
          // 專案不存在或無法訪問，清除 localStorage
          console.warn("PreviewMode", `臨時專案不存在或無法訪問: ${projectId}`);
          localStorage.removeItem("temp_preview_project_id");
          projectId = null;
        }
      }

      // 如果沒有有效的專案 ID，嘗試獲取一個 status 為 temporary 的專案
      if (!projectId) {
        try {
          const response = await request.get("/projects", {
            params: { status: "temporary", limit: 1 },
          });

          console.log("PreviewMode", response.data);

          console.info(
            "PreviewMode",
            `綜綜0000000000獲取臨時專案: ${response.data[0].id}`
          );

          if (response.data && response.data.length > 0) {
            projectId = response.data[0].id;
            // 保存到 localStorage 以便下次使用
            localStorage.setItem("temp_preview_project_id", projectId);
            console.info("PreviewMode", `使用現有臨時專案: ${projectId}`);
          } else {
            // 如果沒有找到臨時專案，創建一個新的
            const createResponse = await request.post("/projects", {
              name: `預覽測試專案_${new Date().toISOString()}`,
              description: "用於節點預覽測試的臨時專案",
              status: "temporary",
            });

            if (createResponse.data && createResponse.data.id) {
              projectId = createResponse.data.id;
              // 保存到 localStorage 以便下次使用
              localStorage.setItem("temp_preview_project_id", projectId);
              console.info("PreviewMode", `創建新臨時專案: ${projectId}`);
            }
          }
        } catch (error) {
          console.error("PreviewMode", "獲取或創建臨時專案失敗", error);
          projectId = null;
        }
      }

      tempProjectId.value = projectId;
      return projectId;
    } finally {
      isLoadingTempProject.value = false;
    }
  };

  /**
   * 調用 API
   * @param {string} url API 端點
   * @param {Object} data 請求數據
   * @param {Object} options 額外選項
   * @returns {Promise<Object>} API 響應
   */
  const callApiInPreviewMode = async (url, data, options = {}) => {
    // 發送請求
    try {
      const response = await request.post(url, data, options);
      return response.data;
    } catch (error) {
      console.error("API", `API 調用失敗: ${url}`, error);
      throw error;
    }
  };

  return {
    isPreviewMode,
    tempProjectId,
    isLoadingTempProject,
    getTempProject,
    callApiInPreviewMode,
  };
}
