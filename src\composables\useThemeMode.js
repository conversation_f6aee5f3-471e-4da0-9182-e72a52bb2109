import { computed } from "vue";
import { useThemeStore } from "@/stores/theme";

/**
 * 提供暗黑模式相關狀態和方法的可組合函數
 * @returns {Object} 返回isDark計算屬性和toggleTheme方法
 */
export function useThemeMode() {
  const themeStore = useThemeStore();

  // 是否為暗黑模式的計算屬性
  const isDark = computed(() => themeStore.isDark);

  // 切換主題的方法
  const toggleTheme = () => {
    themeStore.toggleDark();
  };

  return {
    isDark,
    toggleTheme,
  };
}
