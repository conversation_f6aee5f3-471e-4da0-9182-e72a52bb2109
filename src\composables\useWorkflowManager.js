import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useFlowStore } from "@/stores/flowStore";
import {
  useFlowInstance,
  FLOW_PHASES,
  EXECUTION_ACTIONS,
} from "@/composables/useFlowInstance";
import { NODE_STATES } from "@/constants/nodeStates";
import { globalEventBus, NodeEventType } from "@/utils/eventBus";
import { useFlowCache } from "./useFlowCache";
import { debounce } from "lodash-es";

/**
 * 工作流管理器 Composable
 * 專注於處理工作流的流轉邏輯、節點依賴關係和執行順序的管理
 */
export function useWorkflowManager() {
  const flowStore = useFlowStore();
  const {
    updateSharedData,
    getSharedData,
    updateFlowContextField,
    trackExecutionProgress,
  } = useFlowInstance();

  // 初始化緩存系統
  const flowCache = useFlowCache(flowStore.currentInstance?.id);

  // 當前正在執行的節點ID
  const currentExecutingNodeId = ref(null);
  // 執行隊列
  const executionQueue = ref([]);
  // 執行歷史
  const executionHistory = ref([]);
  // 是否正在執行
  const isExecuting = ref(false);
  // 執行錯誤
  const executionError = ref(null);
  // 節點完成狀態追蹤（用於並行節點）
  const nodeCompletionTracker = ref({});
  // 節點依賴關係（儲存節點的前置節點）
  const nodeDependencies = ref({});
  // 工作流是否已完成
  const isWorkflowCompleted = ref(false);

  // 事件處理去重機制
  const processedEvents = ref(new Map());
  const EVENT_DEBOUNCE_TIME = 100; // 100ms內的重複事件將被忽略

  // 檢查事件是否已經處理過（去重）
  function isEventProcessed(nodeId, status, timestamp) {
    const eventKey = `${nodeId}-${status}`;
    const lastProcessedTime = processedEvents.value.get(eventKey);
    const currentTime = new Date(timestamp).getTime();

    if (
      lastProcessedTime &&
      currentTime - lastProcessedTime < EVENT_DEBOUNCE_TIME
    ) {
      return true; // 事件已經在短時間內處理過
    }

    // 記錄這次事件處理時間
    processedEvents.value.set(eventKey, currentTime);
    return false;
  }

  /**
   * 從緩存或 store 獲取節點狀態
   * @param {string} nodeId - 節點ID
   * @returns {Object} - 節點狀態
   */
  const getNodeStateFromCache = async (nodeId) => {
    try {
      // 先從緩存中獲取
      const cachedState = await flowCache.getNodeCache(nodeId);
      if (cachedState) {
        return cachedState;
      }

      // 如果緩存中沒有，則從 store 獲取
      const storeState = flowStore.getNodeStateById(nodeId);
      if (storeState) {
        // 更新緩存
        await flowCache.updateNodeCache(nodeId, storeState);
        return storeState;
      }

      return null;
    } catch (error) {
      // console.error(
      //   "getNodeStateFromCache",
      //   `獲取節點 ${nodeId} 狀態失敗`,
      //   error
      // );
      return null;
    }
  };

  /**
   * 獲取節點的下一個節點
   * @param {string} nodeId - 當前節點ID
   * @returns {Array} - 下一個節點的ID數組
   */
  const getNextNodes = (nodeId) => {
    if (!flowStore.currentInstance?.edges) {
      return [];
    }

    // 找出從當前節點出發的所有邊
    const outgoingEdges = flowStore.currentInstance.edges.filter(
      (edge) => edge.source === nodeId
    );

    // 獲取目標節點ID
    return outgoingEdges.map((edge) => edge.target);
  };

  /**
   * 獲取節點的上一個節點
   * @param {string} nodeId - 當前節點ID
   * @returns {Array} - 上一個節點的ID數組
   */
  const getPreviousNodes = (nodeId) => {
    if (!flowStore.currentInstance?.edges) {
      return [];
    }

    // 找出指向當前節點的所有邊
    const incomingEdges = flowStore.currentInstance.edges.filter(
      (edge) => edge.target === nodeId
    );

    // 獲取來源節點ID 並更新依賴關係
    const previousNodeIds = incomingEdges.map((edge) => edge.source);

    // 更新節點依賴關係
    nodeDependencies.value[nodeId] = previousNodeIds;

    return previousNodeIds;
  };

  /**
   * 統一的前置節點數據處理方法 - 獲取並合併所有前置節點的輸出數據
   * @param {string} nodeId - 當前節點ID
   * @returns {Object} - 合併後的前置節點輸出數據，包含錯誤處理
   */
  const getPreviousNodesOutputData = (nodeId) => {
    const previousNodeIds = getPreviousNodes(nodeId);
    const flowContext = flowStore.currentInstance?.context || {};
    const previousNodeOutputs = {};

    if (previousNodeIds.length === 0) {
      return previousNodeOutputs;
    }

    // 記錄合併過程中的衝突
    const conflicts = [];

    previousNodeIds.forEach((id) => {
      const nodeData = flowContext.sharedData?.[id]?.detail || {};

      // 遍歷前置節點的所有數據欄位
      for (const field in nodeData) {
        if (previousNodeOutputs[field]) {
          // 欄位已存在，需要合併資料
          const existingValue = previousNodeOutputs[field];
          const newValue = nodeData[field];

          if (field === "timestamp") {
            // === timestamp 欄位特殊處理 ===
            // 取最大（最新）的 timestamp
            const maxTimestamp = [existingValue, newValue].sort().reverse()[0];
            previousNodeOutputs[field] = maxTimestamp;
            // 不記錄衝突，不拋錯
          } else if (Array.isArray(existingValue) && Array.isArray(newValue)) {
            // 兩者都是陣列，合併去重
            previousNodeOutputs[field] = [
              ...new Set([...existingValue, ...newValue]),
            ];
          } else if (
            typeof existingValue === "object" &&
            typeof newValue === "object" &&
            existingValue !== null &&
            newValue !== null &&
            !Array.isArray(existingValue) &&
            !Array.isArray(newValue)
          ) {
            // 兩者都是物件，深度合併
            previousNodeOutputs[field] = { ...existingValue, ...newValue };
          } else if (existingValue !== newValue) {
            // 數據類型不匹配或值不同，記錄衝突
            conflicts.push({
              field,
              existingValue,
              newValue,
              conflictingNodeId: id,
              message: `前置節點欄位 ${field} 存在衝突`,
            });

            // 拋出錯誤，讓節點處理
            throw new Error(
              `前置節點數據合併失敗：欄位 "${field}" 存在不可合併的衝突\n` +
                `現有值: ${JSON.stringify(existingValue)}\n` +
                `新值: ${JSON.stringify(newValue)} (來自節點 ${id})\n` +
                `請檢查前置節點的輸出數據格式是否一致`
            );
          } else {
            // 值相同，保持現有值
            previousNodeOutputs[field] = existingValue;
          }
        } else {
          // 欄位不存在，直接賦值
          previousNodeOutputs[field] = nodeData[field];
        }
      }
    });

    // 如果有衝突但沒有拋出錯誤，記錄警告
    if (conflicts.length > 0) {
      console.warn(
        `useWorkflowManager`,
        `前置節點數據合併過程中發現 ${conflicts.length} 個衝突:`,
        conflicts
      );
    }

    return previousNodeOutputs;
  };

  /**
   * 統一的前置節點數據提取方法 - 直接從當前流程狀態提取和合併前置節點數據
   * @param {string} nodeId - 當前節點ID
   * @returns {Object} - 包含所有前置節點信息的完整對象
   */
  const extractPreviousNodeData = (nodeId) => {
    const previousNodeIds = getPreviousNodes(nodeId);
    const flowContext = flowStore.currentInstance?.context || {};

    // 獲取合併後的前置節點數據
    const mergedPreviousOutputs = getPreviousNodesOutputData(nodeId);

    // 獲取前置節點詳細映射
    const previousNodesDetails = getPreviousNodesDetails(nodeId);

    // 獲取全局變量
    const globalVariables = flowContext.globalVariables || {};

    // 收集所有前置節點的輸出數據映射
    const previousOutputs = {};
    previousNodeIds.forEach((prevNodeId) => {
      const prevNodeData = flowContext.sharedData?.[prevNodeId];
      if (prevNodeData?.detail) {
        previousOutputs[prevNodeId] = prevNodeData.detail;
      }
    });

    // 為了保持向後兼容，如果只有一個前置節點，也提供單獨的字段
    const singlePreviousNode =
      previousNodeIds.length === 1 ? previousNodeIds[0] : null;

    return {
      // 單個前置節點信息（當只有一個前置節點時，為了向後兼容）
      previousNodeId: singlePreviousNode,
      previousNodeOutput: singlePreviousNode
        ? previousOutputs[singlePreviousNode] || {}
        : {},

      // 合併後的所有前置節點數據（主要使用這個）
      mergedPreviousOutputs,

      // 前置節點詳細映射 { nodeId: outputData }
      previousNodesDetails,

      // 全局變量
      globalVariables,

      // 原始的前置節點輸出映射 { nodeId: outputData }
      previousOutputs,

      // 前置節點ID列表
      previousNodeIds,
    };
  };

  /**
   * 獲取前置節點的詳細信息（包含節點ID和輸出數據的映射）
   * @param {string} nodeId - 當前節點ID
   * @returns {Object} - 前置節點詳細信息 { nodeId: outputData }
   */
  const getPreviousNodesDetails = (nodeId) => {
    const previousNodeIds = getPreviousNodes(nodeId);
    const flowContext = flowStore.currentInstance?.context || {};
    const previousNodesDetails = {};

    previousNodeIds.forEach((id) => {
      previousNodesDetails[id] = flowContext.sharedData?.[id]?.detail || {};
    });

    return previousNodesDetails;
  };

  /**
   * 檢查節點是否可以執行
   * @param {string} nodeId - 節點ID
   * @returns {boolean} - 是否可以執行
   */
  const canExecuteNode = async (nodeId) => {
    // 獲取上一個節點（依賴節點）
    const previousNodes = getPreviousNodes(nodeId);

    // 如果沒有上一個節點，則可以執行
    if (previousNodes.length === 0) {
      return true;
    }

    // 檢查所有上一個節點是否都已完成
    const allDependenciesCompleted = await Promise.all(
      previousNodes.map(async (prevNodeId) => {
        // 從緩存獲取節點狀態
        const nodeState = await getNodeStateFromCache(prevNodeId);

        // 記錄節點狀態
        // console.log(
        //   "canExecuteNode",
        //   `檢查依賴節點 ${prevNodeId} 的狀態: ${JSON.stringify(nodeState)}`
        // );

        // 判斷是否完成（COMPLETED）或等待中（PENDING）
        const isCompleted = nodeState?.status === NODE_STATES.COMPLETED;
        const isPending = nodeState?.status === NODE_STATES.PENDING;

        // 記錄依賴狀態用於調試
        if (!isCompleted) {
          // console.log(
          //   "canExecuteNode",
          //   `節點 ${nodeId} 的依賴節點 ${prevNodeId} 狀態為 ${
          //     nodeState?.status || "未知"
          //   }，尚未完成${isPending ? '（處於等待互動狀態）' : ''}`
          // );
        } else {
          // console.log(
          //   "canExecuteNode",
          //   `節點 ${nodeId} 的依賴節點 ${prevNodeId} 已完成`
          // );

          // 同步更新 nodeCompletionTracker
          if (nodeCompletionTracker.value[prevNodeId] !== true) {
            nodeCompletionTracker.value[prevNodeId] = true;
            // console.log(
            //   "canExecuteNode",
            //   `更新節點 ${prevNodeId} 在追蹤器中的狀態為已完成`
            // );
          }
        }

        // PENDING 狀態表示流程暫停，依賴節點尚未真正完成
        return isCompleted;
      })
    );

    const result = allDependenciesCompleted.every(Boolean);

    if (result) {
      // console.log(
      //   "canExecuteNode",
      //   `節點 ${nodeId} 的所有依賴節點已完成，可以執行`
      // );
    }

    return result;
  };

  /**
   * 執行節點
   * @param {string} nodeId - 節點ID
   * @param {Object} context - 執行上下文
   * @returns {Promise<Object>} - 執行結果
   */
  const executeNode = async (nodeId, context = {}) => {
    if (!nodeId) {
      throw new Error("未提供節點ID");
    }

    if (!flowStore.currentInstance) {
      throw new Error("沒有活動的流程實例");
    }

    // 檢查節點是否可以執行
    const canExecute = await canExecuteNode(nodeId);
    if (!canExecute) {
      console.warn(
        "useWorkflowManager",
        `節點 ${nodeId} 的前置節點尚未完成，無法執行`
      );
      return;
    }

    try {
      // 設置當前執行節點
      currentExecutingNodeId.value = nodeId;
      isExecuting.value = true;
      executionError.value = null;

      // 更新節點狀態為執行中
      await flowStore.updateNodeState(flowStore.currentInstance.id, nodeId, {
        status: NODE_STATES.RUNNING,
      });

      // 記錄執行開始
      executionHistory.value.push({
        nodeId,
        action: "start",
        timestamp: new Date().toISOString(),
        context,
      });

      // 也記錄到流程上下文中
      await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.STARTED, {
        context,
      });

      // 增強執行上下文，添加全局變數
      const enhancedContext = {
        ...context,
        globalVariables:
          flowStore.currentInstance?.context?.globalVariables || {},
      };

      // 添加前置節點的輸出數據到上下文
      const previousNodes = getPreviousNodes(nodeId);
      if (previousNodes.length > 0) {
        // 收集所有前置節點的輸出數據
        const allPreviousOutputs = {};
        previousNodes.forEach((prevNodeId) => {
          const prevNodeData = getSharedData(prevNodeId);
          if (prevNodeData?.output) {
            allPreviousOutputs[prevNodeId] = prevNodeData.output;
          }
        });

        enhancedContext.previousOutputs = allPreviousOutputs;

        // 添加合併後的前置節點數據（統一使用這個方法）
        enhancedContext.mergedPreviousOutputs =
          getPreviousNodesOutputData(nodeId);
        enhancedContext.previousNodesDetails = getPreviousNodesDetails(nodeId);
      }

      // 觸發節點執行事件
      const event = new CustomEvent("flow:executeNode", {
        detail: {
          nodeId,
          ...enhancedContext,
        },
      });

      // console.log(
      //   "useWorkflowManager",
      //   `工作流管理器觸發節點 ${nodeId} 執行事件，上下文:`,
      //   enhancedContext
      // );

      window.dispatchEvent(event);
      // console.log(
      //   "useWorkflowManager",
      //   `工作流管理器已觸發節點 ${nodeId} 的執行`
      // );

      return true;
    } catch (error) {
      console.error(
        "useWorkflowManager",
        `工作流管理器執行節點 ${nodeId} 時發生錯誤:`,
        error
      );
      executionError.value = error;

      // 更新節點狀態為錯誤
      await flowStore.updateNodeState(flowStore.currentInstance.id, nodeId, {
        status: NODE_STATES.ERROR,
        error: error.message || "執行節點時發生未知錯誤",
      });

      // 記錄執行錯誤
      executionHistory.value.push({
        nodeId,
        action: "error",
        timestamp: new Date().toISOString(),
        error: error.message,
      });

      // 也記錄到流程上下文中
      await trackExecutionProgress(nodeId, EXECUTION_ACTIONS.ERROR, {
        error: error.message,
      });

      throw error;
    } finally {
      isExecuting.value = false;
      currentExecutingNodeId.value = null;
    }
  };

  /**
   * 執行工作流
   * @param {string} startNodeId - 起始節點ID，如果不提供則自動查找入口節點
   * @returns {Promise<void>}
   */
  const executeWorkflow = async (startNodeId = null) => {
    if (!flowStore.currentInstance) {
      throw new Error("沒有活動的流程實例");
    }

    try {
      // 清空執行隊列和歷史
      executionQueue.value = [];
      executionHistory.value = [];
      executionError.value = null;
      nodeCompletionTracker.value = {};
      nodeDependencies.value = {};

      // 同時更新上下文中的流程階段
      await updateFlowContextField(
        "executionPhase",
        FLOW_PHASES.INITIALIZATION
      );

      // 如果沒有提供起始節點，則查找入口節點（沒有入邊的節點）
      if (!startNodeId) {
        const nodes = flowStore.currentInstance.nodes || [];
        const edges = flowStore.currentInstance.edges || [];

        // 找出沒有入邊的節點作為入口節點
        const entryNodes = nodes.filter((node) => {
          return !edges.some((edge) => edge.target === node.id);
        });

        if (entryNodes.length === 0) {
          throw new Error("找不到工作流的入口節點");
        }

        // 使用第一個入口節點
        startNodeId = entryNodes[0].id;
        // console.log(
        //   "executeWorkflow",
        //   `使用自動識別的入口節點: ${startNodeId}`
        // );
      }

      // 預先分析並構建整個工作流的依賴關係
      buildWorkflowDependencies();

      // 將起始節點加入執行隊列
      executionQueue.value.push(startNodeId);
      nodeCompletionTracker.value[startNodeId] = false;

      // 更新上下文中的流程階段
      await updateFlowContextField("executionPhase", FLOW_PHASES.PROCESSING);

      // 執行起始節點
      await executeNode(startNodeId);

      console.log("executeWorkflow", `工作流從節點 ${startNodeId} 開始執行`);
    } catch (error) {
      console.error("executeWorkflow", "執行工作流時發生錯誤:", error);
      executionError.value = error;

      // 更新上下文中的流程階段
      await updateFlowContextField("executionPhase", FLOW_PHASES.ERROR);

      throw error;
    }
  };

  /**
   * 預先分析並構建整個工作流的依賴關係
   */
  const buildWorkflowDependencies = () => {
    const nodes = flowStore.currentInstance?.nodes || [];
    const edges = flowStore.currentInstance?.edges || [];

    // 為每個節點構建依賴關係
    nodes.forEach((node) => {
      const nodeId = node.id;

      // 找出指向該節點的所有邊
      const incomingEdges = edges.filter((edge) => edge.target === nodeId);

      // 獲取來源節點ID
      const previousNodeIds = incomingEdges.map((edge) => edge.source);

      // 存儲依賴關係
      nodeDependencies.value[nodeId] = previousNodeIds;

      // 初始化完成狀態
      nodeCompletionTracker.value[nodeId] = false;

      // console.log(`節點 ${nodeId} 的依賴節點: [${previousNodeIds.join(", ")}]`);
    });
  };

  /**
   * 檢查整個工作流是否完成
   */
  const checkWorkflowCompletion = () => {
    // 先檢查是否有節點正在執行
    if (currentExecutingNodeId.value) {
      // console.log(
      //   "checkWorkflowCompletion",
      //   `工作流尚未完成，節點 ${currentExecutingNodeId.value} 正在執行中`
      // );
      return false;
    }

    // 確保所有節點都已被追蹤
    if (!flowStore.currentInstance?.nodes) {
      // console.log(
      //   "checkWorkflowCompletion",
      //   `無法檢查工作流完成狀態，當前實例沒有節點信息`
      // );
      return false;
    }

    // 獲取所有節點ID
    const allNodeIds = flowStore.currentInstance.nodes.map((node) => node.id);

    // 檢查是否所有節點都在追蹤器中
    for (const nodeId of allNodeIds) {
      if (nodeCompletionTracker.value[nodeId] === undefined) {
        // 更新節點狀態
        const nodeState = flowStore.getNodeStateById(nodeId);
        const isCompleted = nodeState.status === NODE_STATES.COMPLETED;
        nodeCompletionTracker.value[nodeId] = isCompleted;
        // console.log(
        //   "checkWorkflowCompletion",
        //   `節點 ${nodeId} 添加到完成追蹤器，狀態: ${nodeState.status}，是否完成: ${isCompleted}`
        // );
      }
    }

    // 檢查是否所有節點都已完成
    const pendingNodes = allNodeIds.filter(
      (nodeId) => !nodeCompletionTracker.value[nodeId]
    );

    if (pendingNodes.length === 0 && executionQueue.value.length === 0) {
      if (isWorkflowCompleted.value) return true; // 已經結束就不再處理

      console.log(
        "[useWorkflowManager] 所有節點已完成，工作流程結束，觸發自動同步事件"
      );
      isWorkflowCompleted.value = true;

      // === 自動觸發同步事件 ===
      window.dispatchEvent(
        new CustomEvent("flow:autoSyncCacheToServer", {
          detail: {
            instanceId: flowStore.currentInstance?.id,
            timestamp: new Date().toISOString(),
          },
        })
      );

      // 觸發工作流完成事件
      const event = new CustomEvent("workflow:completed", {
        detail: {
          instanceId: flowStore.currentInstance?.id,
          timestamp: new Date().toISOString(),
          completedNodes: nodeCompletionTracker.value,
        },
      });
      window.dispatchEvent(event);

      // 同時使用事件總線發送事件
      globalEventBus.emit("workflow:completed", {
        instanceId: flowStore.currentInstance?.id,
        timestamp: new Date().toISOString(),
        completedNodes: nodeCompletionTracker.value,
      });

      // 更新上下文中的流程階段
      updateFlowContextField("executionPhase", FLOW_PHASES.COMPLETED).catch(
        (error) => {
          console.error("更新流程階段失敗", error);
        }
      );

      return true;
    } else {
      // 檢查未完成的節點狀態
      const pendingNodeStates = {};
      for (const nodeId of pendingNodes) {
        const nodeState = flowStore.getNodeStateById(nodeId);
        pendingNodeStates[nodeId] = nodeState.status;
      }

      // console.log(
      //   "checkWorkflowCompletion",
      //   `工作流尚未完成，未完成節點: ${pendingNodes.length}，執行隊列: ${executionQueue.value.length}`,
      //   pendingNodeStates
      // );

      // 檢查執行隊列中是否有節點需要強制重新檢查
      if (executionQueue.value.length > 0) {
        setTimeout(() => {
          // console.log("checkWorkflowCompletion", `定時重新檢查執行隊列...`);
          checkQueueForExecutableNodes();
        }, 500); // 500ms 後重新檢查
      }

      return false;
    }
  };

  // 使用節流函數包裝同步操作
  const throttledSyncToServer = debounce(async () => {
    if (flowCache.isSyncing || flowCache.pendingChanges === 0) return;

    try {
      flowCache.isSyncing = true;
      const cache = await flowCache.readCache();

      // 只同步有變化的節點
      const changedNodes = Object.entries(cache.nodes)
        .filter(([nodeId, nodeData]) => {
          const originalNode = flowStore.currentInstance?.nodes.find(
            (n) => n.id === nodeId
          );
          return (
            !originalNode ||
            originalNode.status !== nodeData.status ||
            originalNode.result !== nodeData.result
          );
        })
        .reduce((acc, [nodeId, nodeData]) => {
          acc[nodeId] = nodeData;
          return acc;
        }, {});

      if (Object.keys(changedNodes).length > 0) {
        await flowStore.updateFlowInstance(flowStore.currentInstance.id, {
          nodes: changedNodes,
        });
        flowCache.lastSyncTime = new Date().toISOString();
        flowCache.pendingChanges = 0;
      }
    } catch (error) {
      // console.error("useWorkflowManager", "同步到伺服器失敗", error);
    } finally {
      flowCache.isSyncing = false;
    }
  }, 5000); // 5秒節流

  /**
   * 修改 updateNodeState 函數
   * @param {string} nodeId - 節點ID
   * @param {Object} data - 節點狀態
   */
  const updateNodeState = async (nodeId, data) => {
    try {
      // 驗證節點 ID
      if (!nodeId || typeof nodeId !== "string") {
        // console.error("useWorkflowManager", "無效的節點 ID", nodeId);
        return;
      }

      // 確保 data 物件存在
      const nodeData = {
        status: data.status || "pending",
        result: data.result || null,
        error: data.error || null,
        lastUpdate: new Date().toISOString(),
        ...data,
      };

      await flowCache.updateNodeCache(nodeId, nodeData);
      throttledSyncToServer();
    } catch (error) {
      // console.error("useWorkflowManager", "更新節點狀態失敗", error);
    }
  };

  /**
   * 檢查依賴節點狀態
   * @param {string} nodeId 當前節點ID
   * @returns {boolean} 是否所有依賴節點都已完成
   */
  const checkDependentNodes = (nodeId) => {
    const node = flowStore.currentInstance?.nodes.find((n) => n.id === nodeId);
    if (!node) return true;

    const dependencies = node.dependencies || [];
    return dependencies.every((depId) => {
      const depNode = flowStore.currentInstance?.nodes.find(
        (n) => n.id === depId
      );
      return depNode?.status === NODE_STATES.COMPLETED;
    });
  };

  /**
   * 處理節點完成事件
   * @param {string} nodeId - 節點ID
   * @param {Object} result - 節點結果
   * @param {Error} error - 錯誤對象（如果有）
   */
  const handleNodeCompleted = async (nodeId, result, error) => {
    console.log("DO handleNodeCompleted!");
    try {
      // 驗證節點 ID
      if (!nodeId || typeof nodeId !== "string") {
        // console.error(
        //   "useWorkflowManager",
        //   "處理節點完成事件：無效的節點 ID",
        //   nodeId
        // );
        return;
      }

      // 防止重複處理同一節點的完成事件
      if (nodeCompletionTracker.value[nodeId] === true) {
        console.log(
          "useWorkflowManager",
          `節點 ${nodeId} 已經處理過完成事件，跳過重複處理`
        );
        return;
      }

      console.log("useWorkflowManager", `處理節點 ${nodeId} 完成事件`);

      // 更新節點狀態
      await updateNodeState(nodeId, {
        status: NODE_STATES.COMPLETED,
        result,
        error,
        lastUpdate: new Date().toISOString(),
      });

      // 更新完成追蹤器
      nodeCompletionTracker.value[nodeId] = true;

      // 獲取下一個節點
      const nextNodes = getNextNodes(nodeId);
      console.log(
        "useWorkflowManager",
        `節點 ${nodeId} 的下一個節點:`,
        nextNodes
      );

      // 檢查並執行下一個節點
      for (const nextNodeId of nextNodes) {
        // 檢查節點是否已在執行隊列中
        if (executionQueue.value.includes(nextNodeId)) {
          console.log(
            "useWorkflowManager",
            `節點 ${nextNodeId} 已在執行隊列中`
          );
          continue;
        }

        // 檢查節點是否已完成
        if (nodeCompletionTracker.value[nextNodeId]) {
          console.log("useWorkflowManager", `節點 ${nextNodeId} 已完成`);
          continue;
        }

        // 檢查節點是否可以執行
        if (await canExecuteNode(nextNodeId)) {
          // console.log("useWorkflowManager", `準備執行下一個節點 ${nextNodeId}`);
          try {
            // 獲取上一個節點的結果作為輸入
            const prevNodeData = await flowCache.getNodeCache(nodeId);
            const input = {
              previousNodeResult: prevNodeData?.result,
              previousNodeId: nodeId,
            };

            // 執行下一個節點
            await executeNode(nextNodeId, input);
          } catch (error) {
            console.error(
              "useWorkflowManager",
              `執行節點 ${nextNodeId} 時發生錯誤:`,
              error
            );
          }
        } else {
          // 如果節點還不能執行，加入執行隊列
          // console.log(
          //   "useWorkflowManager",
          //   `將節點 ${nextNodeId} 加入執行隊列`
          // );
          if (!executionQueue.value.includes(nextNodeId)) {
            executionQueue.value.push(nextNodeId);
          }
        }
      }

      // 檢查工作流程是否完成
      const allNodesCompleted = Object.values(
        flowStore.currentInstance?.nodes || {}
      ).every((node) => nodeCompletionTracker.value[node.id] === true);
      console.log(
        "useWorkflowManager",
        `檢查工作流程完成狀態：所有節點是否已完成: ${allNodesCompleted}`,
        nodeCompletionTracker
      );
      if (allNodesCompleted) {
        console.log(
          "useWorkflowManager",
          "所有節點已完成，準備結束工作流程",
          nodeCompletionTracker.value
        );
        // 這裡只需要檢查流程是否完成，不需要呼叫 handleFlowCompleted
        // 因為 checkWorkflowCompletion 已經包含了所有流程結束處理
        checkWorkflowCompletion();
      } else {
        // 檢查執行隊列中的節點
        await checkQueueForExecutableNodes();
      }
    } catch (error) {
      console.error(
        "useWorkflowManager",
        `處理節點 ${nodeId} 完成事件失敗`,
        error
      );
    }
  };

  /**
   * 處理節點錯誤
   * @param {string} nodeId - 節點ID
   * @param {Error} error - 錯誤對象
   */
  const handleNodeError = async (nodeId, error) => {
    try {
      // 更新節點狀態為錯誤
      await updateNodeState(nodeId, {
        status: NODE_STATES.ERROR,
        error: error.message || "未知錯誤",
        executionTime:
          Date.now() -
          (nodeCompletionTracker.value[nodeId]?.startTime || Date.now()),
        lastExecuted: new Date().toISOString(),
      });

      // 更新節點完成追蹤器
      nodeCompletionTracker.value[nodeId] = {
        ...nodeCompletionTracker.value[nodeId],
        error: true,
        endTime: Date.now(),
      };
    } catch (err) {
      // console.error("handleNodeError", "處理節點錯誤失敗", err);
    }
  };

  /**
   * 檢查執行隊列中是否有節點可以執行
   */
  const checkQueueForExecutableNodes = async () => {
    // 創建一個隊列副本，以便在迭代過程中可以安全地修改原始隊列
    const queueCopy = [...executionQueue.value];

    // console.log(
    //   "checkQueueForExecutableNodes",
    //   `檢查執行隊列中的節點，當前隊列: [${queueCopy.join(", ")}]`
    // );

    // 首先從緩存更新所有節點的完成狀態
    await syncAllNodesState();

    for (const nodeId of queueCopy) {
      // 如果節點已經完成，從隊列中移除
      if (nodeCompletionTracker.value[nodeId]) {
        const index = executionQueue.value.indexOf(nodeId);
        if (index > -1) {
          executionQueue.value.splice(index, 1);
          // console.log(
          //   "checkQueueForExecutableNodes",
          //   `節點 ${nodeId} 已完成，從執行隊列中移除`
          // );
        }
        continue;
      }

      // 從緩存重新檢查節點狀態
      const nodeState = await getNodeStateFromCache(nodeId);
      // console.log(
      //   "checkQueueForExecutableNodes",
      //   `節點 ${nodeId} 的當前狀態: ${JSON.stringify(nodeState)}`
      // );

      if (nodeState?.status === NODE_STATES.COMPLETED) {
        // 節點已完成但追蹤器未更新，同步狀態
        nodeCompletionTracker.value[nodeId] = true;
        // console.log(
        //   "checkQueueForExecutableNodes",
        //   `節點 ${nodeId} 已完成但追蹤器未更新，已同步狀態`
        // );

        // 從隊列中移除
        const index = executionQueue.value.indexOf(nodeId);
        if (index > -1) {
          executionQueue.value.splice(index, 1);
        }
        continue;
      }

      // 檢查節點依賴
      const dependencies =
        nodeDependencies.value[nodeId] || getPreviousNodes(nodeId);

      // 直接從緩存檢查依賴節點狀態
      const depStates = {};
      for (const depId of dependencies) {
        const depState = await getNodeStateFromCache(depId);
        depStates[depId] = depState?.status;

        // 同步更新依賴節點的完成狀態
        if (depState?.status === NODE_STATES.COMPLETED) {
          nodeCompletionTracker.value[depId] = true;
        } else if (depState?.status === NODE_STATES.PENDING) {
          // PENDING 狀態的節點不算完成，確保追蹤器正確標記
          nodeCompletionTracker.value[depId] = false;
        }
      }
      // console.log(
      //   "checkQueueForExecutableNodes",
      //   `節點 ${nodeId} 的依賴節點狀態:`,
      //   depStates
      // );

      // 檢查節點是否可以執行
      const canExecute = await canExecuteNode(nodeId);

      if (canExecute) {
        // console.log(
        //   "checkQueueForExecutableNodes",
        //   `執行隊列中的節點 ${nodeId} 現在可以執行了，所有依賴都已完成`
        // );

        // 從隊列中移除
        const index = executionQueue.value.indexOf(nodeId);
        if (index > -1) {
          executionQueue.value.splice(index, 1);
        }

        try {
          // 執行節點
          await executeNode(nodeId, {});
          // console.log(
          //   "checkQueueForExecutableNodes",
          //   `節點 ${nodeId} 執行已觸發`
          // );
        } catch (error) {
          // console.error(
          //   "checkQueueForExecutableNodes",
          //   `觸發節點 ${nodeId} 執行時發生錯誤:`,
          //   error
          // );
        }
      } else {
        // 找出未完成的依賴
        const pendingDeps = dependencies.filter(async (depId) => {
          const depState = await getNodeStateFromCache(depId);
          return depState?.status !== NODE_STATES.COMPLETED;
        });

        // console.log(
        //   "checkQueueForExecutableNodes",
        //   `節點 ${nodeId} 還不能執行，部分依賴尚未完成: [${pendingDeps.join(
        //     ", "
        //   )}]`
        // );
      }
    }

    // 檢查工作流是否已完成
    checkWorkflowCompletion();
  };

  /**
   * 同步所有節點的狀態
   */
  const syncAllNodesState = async () => {
    if (!flowStore.currentInstance?.nodes) {
      console.warn(
        "syncAllNodesState",
        "無法同步節點狀態：當前實例沒有節點信息"
      );
      return;
    }

    // console.log("syncAllNodesState", "開始同步所有節點狀態");

    // 獲取所有節點ID
    const allNodeIds = flowStore.currentInstance.nodes.map((node) => node.id);

    // 同步每個節點的狀態
    for (const nodeId of allNodeIds) {
      // 從緩存獲取節點狀態
      const nodeState = await getNodeStateFromCache(nodeId);

      // 更新節點完成狀態
      if (nodeState?.status === NODE_STATES.COMPLETED) {
        nodeCompletionTracker.value[nodeId] = true;
      } else {
        nodeCompletionTracker.value[nodeId] = false;
        // console.log(
        //   "useWorkflowManager syncAllNodesState",
        //   `同步節點 ${nodeId} 狀態為未完成: ${nodeState?.status}${nodeState?.status === NODE_STATES.PENDING ? '（等待互動）' : ''}`
        // );
      }
    }

    // console.log("useWorkflowManager syncAllNodesState", "節點狀態同步完成");
  };

  /**
   * 設置節點狀態監聽器
   * 當節點狀態變化時，自動處理後續流程
   */
  const setupNodeStateListeners = () => {
    // 監聽全局事件
    const handleNodeStateChange = async (event) => {
      const { nodeId, status, result, error, timestamp } = event.detail;

      // 檢查事件是否已經處理過（去重）
      if (isEventProcessed(nodeId, status, timestamp)) {
        console.log(
          "useWorkflowManager",
          `跳過重複的節點狀態變更事件: ${nodeId}-${status}`
        );
        return;
      }

      console.log(
        "useWorkflowManager setupNodeStateListeners",
        `接收到節點 ${nodeId} 狀態變更事件: ${status}, 時間戳: ${timestamp}`
      );

      if (status === NODE_STATES.COMPLETED) {
        // console.log(
        //   "useWorkflowManager setupNodeStateListeners",
        //   `準備處理節點 ${nodeId} 的完成事件，結果:`,
        //   result
        // );
        await handleNodeCompleted(nodeId, result, error);
      } else if (status === NODE_STATES.ERROR || status === "failed") {
        // console.log(
        //   "setupNodeStateListeners",
        //   `準備處理節點 ${nodeId} 的錯誤事件，錯誤:`,
        //   error
        // );
        await handleNodeError(nodeId, error);
      } else if (status === NODE_STATES.PENDING) {
        // 節點進入 PENDING 狀態，暫停流程
        // console.log(
        //   "useWorkflowManager setupNodeStateListeners",
        //   `節點 ${nodeId} 進入 PENDING 狀態，流程暫停等待用戶互動`
        // );

        // 更新節點狀態為等待互動
        nodeCompletionTracker.value[nodeId] = false;

        // 清空當前執行的節點（如果是當前節點）
        if (currentExecutingNodeId.value === nodeId) {
          currentExecutingNodeId.value = null;
          isExecuting.value = false;
        }

        // PENDING 狀態下不觸發後續節點執行
        // 流程將在節點從 PENDING → COMPLETED 時恢復
      }
    };

    // 處理節點重置事件
    const handleNodeReset = async (event) => {
      console.log("DO handleNodeReset!");
      const { nodeId, nodeName, timestamp } = event.detail;

      // console.log(
      //   "useWorkflowManager setupNodeStateListeners",
      //   `接收到節點 ${nodeId} 重置事件，重置完成追蹤狀態, 時間戳: ${timestamp}`
      // );

      // 重置該節點的完成追蹤狀態
      nodeCompletionTracker.value[nodeId] = false;

      // 遞歸重置所有後續節點的完成追蹤狀態
      const resetDownstreamNodes = (currentNodeId) => {
        const nextNodes = getNextNodes(currentNodeId);
        for (const nextNodeId of nextNodes) {
          if (nodeCompletionTracker.value[nextNodeId] === true) {
            // console.log(
            //   "setupNodeStateListeners",
            //   `重置下游節點 ${nextNodeId} 的完成狀態`
            // );
            nodeCompletionTracker.value[nextNodeId] = false;

            // 遞歸重置該節點的下游節點
            resetDownstreamNodes(nextNodeId);
          }
        }
      };

      // 從重置的節點開始，重置所有下游節點
      resetDownstreamNodes(nodeId);

      // 獲取直接的下一個節點
      const nextNodes = getNextNodes(nodeId);
      // console.log(
      //   "setupNodeStateListeners",
      //   `節點 ${nodeId} 重置後，檢查下一個節點:`,
      //   nextNodes
      // );

      // 檢查並將下一個節點加入執行隊列
      for (const nextNodeId of nextNodes) {
        // 檢查節點是否已在執行隊列中
        if (!executionQueue.value.includes(nextNodeId)) {
          // console.log(
          //   "setupNodeStateListeners",
          //   `將節點 ${nextNodeId} 加入執行隊列`
          // );
          executionQueue.value.push(nextNodeId);
        }
      }

      // 重置工作流完成標誌
      isWorkflowCompleted.value = false;

      // 檢查執行隊列中的節點
      await checkQueueForExecutableNodes();
    };

    // 移除舊的事件監聽器（如果有的話）
    window.removeEventListener("flow:nodeStateChange", handleNodeStateChange);
    window.removeEventListener("node:stateChange", handleNodeStateChange);
    window.removeEventListener("flow:nodeReset", handleNodeReset);

    // 添加新的事件監聽器
    window.addEventListener("flow:nodeStateChange", handleNodeStateChange);
    window.addEventListener("node:stateChange", handleNodeStateChange);
    window.addEventListener("flow:nodeReset", handleNodeReset);

    // 返回清理函數
    return () => {
      window.removeEventListener("flow:nodeStateChange", handleNodeStateChange);
      window.removeEventListener("node:stateChange", handleNodeStateChange);
      window.removeEventListener("flow:nodeReset", handleNodeReset);
    };
  };

  // 設置節點狀態監聽器
  setupNodeStateListeners();

  return {
    // 狀態
    currentExecutingNodeId,
    executionQueue,
    executionHistory,
    isExecuting,
    executionError,
    nodeCompletionTracker,
    nodeDependencies,

    // 方法
    executeNode,
    executeWorkflow,
    getNextNodes,
    getPreviousNodes,
    canExecuteNode,
    handleNodeCompleted,
    handleNodeError,
    setupNodeStateListeners,
    buildWorkflowDependencies,
    checkQueueForExecutableNodes,
    checkWorkflowCompletion,
    updateNodeState,

    // 新增的前置節點數據處理方法
    getPreviousNodesOutputData,
    getPreviousNodesDetails,
    extractPreviousNodeData,

    // 添加緩存相關的狀態
    pendingChanges: flowCache.pendingChanges,
    lastSyncTime: flowCache.lastSyncTime,
    isSyncing: flowCache.isSyncing,
  };
}
