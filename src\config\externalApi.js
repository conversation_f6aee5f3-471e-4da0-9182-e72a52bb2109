// 外部 API 服務配置
const externalApiConfig = {
  // FastAPI 服務基礎 URL
  baseURL: process.env.EXTERNAL_API_URL || "http://localhost:8000",

  apiKey: process.env.EXTERNAL_API_KEY || "your-api-key-here",

  // 請求超時設置（毫秒）
  timeout: 300000, // 延長至 5 分鐘

  // 針對長時間運算的請求配置
  longRunningRequests: {
    // 機器學習相關請求的超時時間
    mlTimeout: 600000, // 10 分鐘
    // 統計分析相關請求的超時時間
    analysisTimeout: 300000, // 5 分鐘
  },

  // 重試配置
  retry: {
    maxRetries: 3,
    retryDelay: 1000,
  },

  // 快取配置
  cache: {
    enabled: true,
    ttl: 300000, // 5分鐘
  },

  // 請求頭配置
  headers: {
    "Content-Type": "application/json",
  },
};

module.exports = externalApiConfig;
