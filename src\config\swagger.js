const swaggerJsdoc = require("swagger-jsdoc");

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "SFDA IYM API",
      version: "1.0.0",
      description: "SFDA IYM API 文檔",
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 3001}`,
        description: "開發環境",
      },
      {
        url: "http://localhost:3001",
        description: "本地環境",
      },
    ],
  },
  apis: ["./src/routes/*.js"], // 指定API路由文件的位置
};

const swaggerSpec = swaggerJsdoc(options);

module.exports = swaggerSpec;
