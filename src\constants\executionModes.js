// 節點執行模式配置

// 執行模式詳細配置 (Array 結構，便於遍歷和展示)
export const EXECUTION_MODE_CONFIGS = [
  {
    key: "AUTO",
    value: "auto",
    label: "自動執行",
    description: "節點自動執行，無需用戶介入",
    icon: "play-circle",
  },
  {
    key: "MANUAL",
    value: "manual",
    label: "手動確認",
    description: "需要用戶手動點擊執行",
    icon: "hand-pointer",
  },
  {
    key: "INTERACTIVE",
    value: "interactive",
    label: "互動式",
    description: "需要用戶輸入或選擇",
    icon: "message-square",
  },
  {
    key: "CONDITIONAL",
    value: "conditional",
    label: "條件式",
    description: "根據前置節點結果決定執行方式",
    icon: "git-branch",
  },
];

// 執行模式常量 (Object 結構，便於引用)
export const EXECUTION_MODES = EXECUTION_MODE_CONFIGS.reduce((acc, config) => {
  acc[config.key] = config.value;
  return acc;
}, {});

// 執行模式對應的顯示類型
export const EXECUTION_MODE_TYPES = {
  [EXECUTION_MODES.AUTO]: "success",
  [EXECUTION_MODES.MANUAL]: "primary",
  [EXECUTION_MODES.INTERACTIVE]: "warning",
  [EXECUTION_MODES.CONDITIONAL]: "info",
};

// 取得執行模式配置
export function getExecutionModeConfig(value) {
  return EXECUTION_MODE_CONFIGS.find((config) => config.value === value);
}

// 取得執行模式標籤
export function getExecutionModeLabel(value) {
  const config = getExecutionModeConfig(value);
  return config?.label || value;
}

// 取得執行模式描述
export function getExecutionModeDescription(value) {
  const config = getExecutionModeConfig(value);
  return config?.description || "";
}

// 取得執行模式圖示
export function getExecutionModeIcon(value) {
  const config = getExecutionModeConfig(value);
  return config?.icon || "circle";
}

// 取得執行模式顯示類型
export function getExecutionModeType(value) {
  return (
    EXECUTION_MODE_TYPES[value] || EXECUTION_MODE_TYPES[EXECUTION_MODES.AUTO]
  );
}

// 驗證執行模式是否有效
export function isValidExecutionMode(value) {
  return EXECUTION_MODE_CONFIGS.some((config) => config.value === value);
}

// 取得所有執行模式值的陣列
export function getAllExecutionModeValues() {
  return EXECUTION_MODE_CONFIGS.map((config) => config.value);
}
