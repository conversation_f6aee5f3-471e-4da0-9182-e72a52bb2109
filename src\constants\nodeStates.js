// 節點狀態常量
export const NODE_STATES = {
  IDLE: "idle",
  RUNNING: "running",
  COMPLETED: "completed",
  ERROR: "error",
  PAUSED: "paused",
  PENDING: "pending",
  DEFAULT: "default",
  FAILED: "failed",
};

// 節點狀態對應的顯示類型
export const NODE_STATUS_TYPES = {
  [NODE_STATES.IDLE]: "info",
  [NODE_STATES.RUNNING]: "warning",
  [NODE_STATES.COMPLETED]: "success",
  [NODE_STATES.ERROR]: "danger",
  [NODE_STATES.PAUSED]: "warning",
  [NODE_STATES.PENDING]: "info",
  [NODE_STATES.DEFAULT]: "info",
  [NODE_STATES.FAILED]: "danger",
};

// 節點狀態對應的顯示文字
export const NODE_STATUS_TEXTS = {
  [NODE_STATES.IDLE]: "待執行",
  [NODE_STATES.RUNNING]: "執行中",
  [NODE_STATES.COMPLETED]: "已完成",
  [NODE_STATES.ERROR]: "錯誤",
  [NODE_STATES.PAUSED]: "已暫停",
  [NODE_STATES.PENDING]: "等待中",
  [NODE_STATES.DEFAULT]: "預設",
  [NODE_STATES.FAILED]: "失敗",
};

// 取得狀態對應的顯示類型
export function getNodeStatusType(status) {
  return NODE_STATUS_TYPES[status] || NODE_STATUS_TYPES[NODE_STATES.DEFAULT];
}

// 取得狀態對應的顯示文字
export function getNodeStatusText(status) {
  return NODE_STATUS_TEXTS[status] || status;
}
