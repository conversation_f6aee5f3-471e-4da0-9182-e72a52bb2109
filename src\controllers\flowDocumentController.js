const { PrismaClient } = require("@prisma/client");
const { handlePrismaError } = require("../utils/errorHandler");

const path = require("path");
const fs = require("fs");
const prisma = new PrismaClient();
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { getServerUrl } = require("../utils/serverUrl");
const projectDirectoryService = require("../services/projectDirectoryService");
const { logger } = require("../utils/logger");
// 取得所有文檔
const getAllDocuments = async (req, res) => {
  try {
    const documents = await prisma.flowDocument.findMany({
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            projectNumber: true,
          },
        },
        instance: {
          select: {
            id: true,
          },
        },
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
    successResponse(res, 200, documents);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 根據專案 ID 取得文檔
const getDocumentsByProject = async (req, res) => {
  const { projectId } = req.params;
  try {
    const documents = await prisma.flowDocument.findMany({
      where: { projectId },
      include: {
        project: true,
        instance: true,
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
    successResponse(res, 200, documents);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 根據實例 ID 取得文檔
const getDocumentsByInstance = async (req, res) => {
  const { instanceId } = req.params;
  try {
    const documents = await prisma.flowDocument.findMany({
      where: { instanceId },
      include: {
        project: true,
        instance: true,
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
    successResponse(res, 200, documents);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 取得單一文檔
const getDocumentById = async (req, res) => {
  const { id } = req.params;
  try {
    const document = await prisma.flowDocument.findUnique({
      where: { id },
      include: {
        project: true,
        instance: true,
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
    if (!document) {
      return errorResponse(res, 404, "找不到指定的文檔");
    }
    successResponse(res, 200, document);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 上傳文檔
const uploadDocument = async (req, res) => {
  try {
    const { projectId, instanceId, docType, projectNumber, templateName } =
      req.body;
    const serverUrl = getServerUrl();
    logger.debug("serverUrl", serverUrl);

    // 如果提供了專案編號和範本名稱，則將檔案移動到對應目錄
    if (projectNumber && templateName && req.file) {
      try {
        // 確保目標目錄存在
        const templateDirPath = projectDirectoryService.ensureTemplateDirectory(
          projectNumber,
          templateName
        );

        // 原始檔案路徑
        const originalFilePath = req.file.path;

        // 新檔案路徑（在專案/範本目錄下）
        const newFileName = req.file.filename;
        const newFilePath = path.join(templateDirPath, newFileName);

        // 創建目標目錄（如果不存在）
        await fs.promises.mkdir(path.dirname(newFilePath), { recursive: true });

        // 移動檔案
        await fs.promises.rename(originalFilePath, newFilePath);

        // 更新檔案路徑
        req.file.path = newFilePath.replace(/\\/g, "/");

        logger.log(`檔案已移動到專案目錄: ${newFilePath}`);
      } catch (dirError) {
        logger.error("移動檔案到專案目錄失敗:", dirError);
        // 不影響主流程，僅記錄錯誤
      }
    }

    // const fileUrl = getFileUrl(serverUrl, req.file.path);
    // fileUrl = serverUrl + req.file.path;
    fileUrl = `${serverUrl}uploads/projects/${projectNumber}/${templateName}/${req.file.filename}`;

    logger.debug("fileUrl", fileUrl);
    // 檢查專案是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return errorResponse(res, 404, "專案不存在");
    }

    // 如果有 instanceId，檢查工作流程實例是否存在
    if (instanceId) {
      const instance = await prisma.flowInstance.findUnique({
        where: { id: instanceId },
      });

      if (!instance) {
        return errorResponse(res, 404, "工作流程實例不存在");
      }
    }

    // 建立文檔記錄
    const document = await prisma.flowDocument.create({
      data: {
        name: req.file.filename,
        docType,
        url: fileUrl,
        project: {
          connect: { id: projectId },
        },
        ...(instanceId && {
          instance: {
            connect: { id: instanceId },
          },
        }),
        creator: {
          connect: { id: req.user.id },
        },
        // 儲存專案編號和範本名稱到metadata
        metadata: {
          projectNumber: projectNumber || null,
          templateName: templateName || null,
          originalPath: req.file.originalname,
        },
      },
      include: {
        project: true,
        instance: true,
        creator: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 201, document);
  } catch (error) {
    logger.error("上傳文檔失敗:", error);
    errorResponse(res, 500, "上傳文檔失敗");
  }
};

// 更新文檔資訊
const updateDocument = async (req, res) => {
  const { id } = req.params;
  const { docType, metadata } = req.body;

  try {
    const document = await prisma.flowDocument.update({
      where: { id },
      data: {
        docType,
        metadata: metadata ? JSON.parse(metadata) : undefined,
      },
      include: {
        project: true,
        instance: true,
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });
    successResponse(res, 200, document);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

const deleteDocument = async (req, res) => {
  const { id } = req.params;

  const document = await prisma.flowDocument.findUnique({
    where: { id },
  });

  if (!document) {
    return errorResponse(res, 404, "找不到指定的文檔");
  }

  try {
    // 取得檔案名稱
    const fileName = document.name;
    // 取得專案編號和範本名稱
    const projectNumber = document.metadata?.projectNumber;
    const templateName = document.metadata?.templateName;

    // 刪除資料庫記錄
    await prisma.flowDocument.delete({
      where: { id },
    });

    // 如果有專案編號和範本名稱，嘗試刪除實體檔案
    if (projectNumber && templateName && fileName) {
      try {
        // 取得範本目錄路徑
        const templateDirPath =
          projectDirectoryService.getTemplateDirectoryPath(
            projectNumber,
            templateName
          );

        // 完整檔案路徑
        const filePath = path.join(templateDirPath, fileName);

        // 檢查檔案是否存在
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        } else {
          logger.warn("================>檔案不存在");
        }
      } catch (fileError) {
        logger.error("刪除實體檔案失敗:", fileError);
        // 不影響主流程，僅記錄錯誤
      }
    }

    successResponse(res, 200, { message: "文檔已成功刪除" });
  } catch (error) {
    logger.error("刪除文檔失敗:", error);
    handlePrismaError(error, res);
  }
};

module.exports = {
  getAllDocuments,
  getDocumentsByProject,
  getDocumentsByInstance,
  getDocumentById,
  uploadDocument,
  updateDocument,
  deleteDocument,
};
