const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { handlePrismaError } = require("../utils/errorHandler");
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const NodeExecutorFactory = require("../services/nodeExecutors");
const { asyncHandler } = require("../utils/asyncHandler");
const { logger } = require("../utils/logger");
const { ensureDirectoryExists, getFullPath } = require("../utils/file");
const projectDirectoryService = require("../services/projectDirectoryService");

/**
 * 獲取所有流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.getAllInstances = async (req, res) => {
  try {
    const { projectId, status, simplified, showDeleted } = req.query;
    const where = {
      // 預設排除已軟刪除的流程實例，除非明確要求顯示
      ...(showDeleted !== "true" && { status: { not: "deleted" } }),
    };

    if (projectId) {
      where.projectId = projectId;
    }

    if (status) {
      // 如果特別請求查看已刪除的流程實例，則允許
      if (status === "deleted") {
        where.status = "deleted";
      } else if (showDeleted !== "true") {
        // 只有在沒有要求顯示已刪除時，才套用狀態過濾
        // 否則遵照請求的狀態過濾
        where.status = status;
      }
    }

    // 構建查詢參數
    const queryOptions = {
      where,
      orderBy: {
        createdAt: "desc",
      },
    };

    // 基本的選擇欄位
    const defaultSelect = {
      id: true,
      status: true,
      description: true,
      createdAt: true,
      updatedAt: true,
      project: {
        select: {
          id: true,
          name: true,
          projectNumber: true,
        },
      },
      template: {
        select: {
          id: true,
          name: true,
        },
      },
      creator: {
        select: {
          id: true,
          username: true,
          avatar: true,
        },
      },
      updater: {
        select: {
          id: true,
          username: true,
          avatar: true,
        },
      },
    };

    let instances;
    // 根據是否為精簡模式決定返回的欄位
    if (simplified === "true") {
      // 精簡模式：使用基本欄位，只返回節點數量
      queryOptions.select = defaultSelect;

      // 獲取實例列表
      instances = await prisma.flowInstance.findMany(queryOptions);
      successResponse(res, 200, instances);
    } else {
      // 完整模式：包含所有必要的欄位和關聯
      queryOptions.select = {
        ...defaultSelect,
        nodes: true,
        //edges: true,
        //nodeStates: true,
        //nodeData: true,
      };

      // successResponse(res, 200, instances);
      instances = await prisma.flowInstance.findMany(queryOptions);
      const processedResult = instances.map((item) => {
        const nodes = item.nodes.map((node) => {
          return {
            data: node.data,
            style: node.style,
          };
        });
        delete item.nodes;
        return {
          ...item,
          nodes,
        };
      });
      successResponse(res, 200, processedResult);
    }
  } catch (error) {
    logger.error("獲取流程實例列表失敗:", error);
    handlePrismaError(error, res);
  }
};

/**
 * 根據ID獲取流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.getInstanceById = async (req, res) => {
  try {
    const { id } = req.params;
    const instance = await prisma.flowInstance.findUnique({
      where: { id },
      include: {
        project: {
          select: {
            name: true,
            projectNumber: true,
          },
        },
        template: {
          select: {
            name: true,
            nodes: true,
            edges: true,
          },
        },
        creator: {
          select: {
            username: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            username: true,
            avatar: true,
          },
        },
        documents: true,
      },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 對已軟刪除的實例，除非明確要求查看刪除的實例，否則返回404
    const { showDeleted } = req.query;
    if (instance.status === "deleted" && showDeleted !== "true") {
      return errorResponse(res, 404, "流程實例已刪除");
    }

    successResponse(res, 200, instance);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

/**
 * 創建流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.createInstance = async (req, res) => {
  try {
    const { projectId, templateId, context } = req.body;
    const userId = req.user.id;

    // 檢查專案是否存在
    // 檢查專案和模板是否存在
    const [project, template] = await Promise.all([
      prisma.project.findUnique({ where: { id: projectId } }),
      prisma.flowTemplate.findUnique({ where: { id: templateId } }),
    ]);

    if (!project) {
      return errorResponse(res, 404, "專案不存在");
    }

    if (!template) {
      return errorResponse(res, 404, "流程模板不存在");
    }

    // 創建流程實例(分析流程)
    const instance = await prisma.flowInstance.create({
      data: {
        projectId,
        templateId,
        context: context || {},
        nodes: template.nodes,
        edges: template.edges,
        status: "active", // active，completed，cancelled
        nodeStates: {},
        logs: [
          {
            type: "SYSTEM",
            message: "流程實例已創建",
            timestamp: new Date().toISOString(),
          },
        ],
        createdBy: userId,
        updatedBy: userId,
      },
      include: {
        project: {
          select: {
            name: true,
            projectNumber: true,
          },
        },
        template: {
          select: {
            name: true,
          },
        },
        creator: {
          select: {
            username: true,
            avatar: true,
          },
        },
      },
    });

    // 創建專案下的範本目錄
    try {
      const templateDirPath = projectDirectoryService.ensureTemplateDirectory(
        project.projectNumber,
        template.name
      );
      logger.log(`已創建流程實例目錄: ${templateDirPath}`);
    } catch (dirError) {
      logger.error("創建目錄失敗:", dirError);
      // 不影響主流程，僅記錄錯誤
    }

    successResponse(res, 201, instance);
  } catch (error) {
    logger.error("創建流程實例錯誤:", error);
    handlePrismaError(error, res);
  }
};

/**
 * 更新流程實例 (TODO: 還需要這個 fn 嗎? 前端已改成存在 indexdb)
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.updateInstance = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      context,
      nodes,
      edges,
      nodeData,
      nodeStates,
      logs,
      _deleteFileNode,
      fileId,
    } = req.body;
    const userId = req.user.id;

    const instance = await prisma.flowInstance.findUnique({
      where: { id },
      include: {
        project: true,
      },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 處理檔案節點刪除的情況
    if (_deleteFileNode === true && fileId) {
      try {
        // 刪除相關的 FlowDocument 記錄
        const deletedDocument = await prisma.flowDocument.deleteMany({
          where: {
            id: fileId,
          },
        });

        logger.log(
          `已刪除檔案記錄: ${fileId}, 刪除數量: ${deletedDocument.count}`
        );

        // 如果只是刪除檔案節點，且沒有提供其他更新資料，直接返回成功響應
        if (!nodes && !edges && !nodeData && !nodeStates && !context && !logs) {
          return successResponse(res, 200, {
            message: "檔案節點已刪除",
            deletedDocument,
          });
        }

        // 如果同時提供了 nodes 和 edges，繼續執行下面的流程實例更新邏輯
        logger.log(
          `檔案記錄已刪除，繼續更新流程實例結構 (nodes: ${!!nodes}, edges: ${!!edges})`
        );
      } catch (fileDeleteError) {
        logger.error(`刪除檔案記錄失敗:`, fileDeleteError);
        // 不影響主流程，繼續執行（可選地，可以在此返回錯誤響應）
        // return errorResponse(res, 500, "刪除檔案記錄失敗");
      }
    }

    // 檢查是否有 _isDataUpdate 查詢參數
    const isDataUpdateParam = req.query._isDataUpdate === "true";

    // 如果是更新節點數據或狀態，則允許在任何狀態下更新
    const isDataUpdate =
      isDataUpdateParam || // 如果有 _isDataUpdate 查詢參數，則視為數據更新
      nodeData !== undefined ||
      nodeStates !== undefined ||
      logs !== undefined ||
      context !== undefined; // 上下文更新也視為數據更新

    // 檢查流程實例狀態，只有非終止狀態可以更新結構
    if (
      !isDataUpdate &&
      (instance.status === "completed" || instance.status === "cancelled")
    ) {
      return errorResponse(res, 400, "已完成或已取消的流程實例不能更新結構");
    }
    // 自動建立 DataSnapshot
    if (!_deleteFileNode) {
      try {
        const isContextEmpty = Object.keys(context || {}).length === 0;
        const isNodeDataEmpty = Object.keys(nodeData || {}).length === 0;
        if (isContextEmpty || isNodeDataEmpty) {
          throw new Error("context 或 nodeData 為空");
        }
        // 從 instance 獲取 projectId
        const projectId = instance.projectId;

        // 準備 DataSnapshot 資料
        await prisma.dataSnapshot.create({
          data: {
            projectId: projectId,
            flowInstanceId: id,
            projectDetail: JSON.parse(JSON.stringify(instance.project || {})),
            nodes: nodes || instance.nodes,
            edges: edges || instance.edges,
            nodeData: nodeData || instance.nodeData || {},
            context: context || instance.context || {},
            description: `自動建立的數據快照 - ${new Date().toLocaleString()}`,
            createdBy: userId,
          },
        });

        logger.log(`已為流程實例 ${id} 創建數據快照`);
      } catch (snapshotError) {
        logger.error(`建立數據快照失敗:`, snapshotError);
        // 不影響主流程，僅記錄錯誤
      }
    }

    const updatedInstance = await prisma.flowInstance.update({
      where: { id },
      data: {
        context: context || instance.context,
        nodes: nodes || instance.nodes,
        edges: edges || instance.edges,
        nodeData: nodeData || instance.nodeData,
        nodeStates: nodeStates || instance.nodeStates,
        logs: logs
          ? {
              push: logs,
            }
          : {
              push: {
                type: "SYSTEM",
                message: "流程實例已更新",
                timestamp: new Date().toISOString(),
              },
            },
        updatedBy: userId,
      },
      include: {
        project: {
          select: {
            name: true,
            projectNumber: true,
          },
        },
        template: {
          select: {
            name: true,
          },
        },
        updater: {
          select: {
            username: true,
            avatar: true,
          },
        },
      },
    });

    console.log("isfefefef");
    successResponse(res, 200, updatedInstance);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

/**
 * 刪除流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.deleteInstance = async (req, res) => {
  try {
    const { id } = req.params;
    const { force } = req.query;
    const user = req.user;

    // 添加日誌輸出，用於調試
    logger.log("用戶資訊:", {
      userId: user.id,
      force,
    });

    // 直接從資料庫獲取用戶及其角色
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.id },
      include: { userRoles: true },
    });

    if (!userWithRoles) {
      return errorResponse(res, 404, "用戶不存在");
    }

    const userRoles = userWithRoles.userRoles || [];

    // 檢查流程實例是否存在
    const instance = await prisma.flowInstance.findUnique({
      where: { id },
      include: {
        //fileNodes: true,
        documents: true,
      },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 檢查流程實例是否已經被刪除
    if (instance.status === "deleted") {
      return errorResponse(res, 400, "此流程實例已被刪除");
    }

    // 檢查用戶是否有權限刪除
    if (instance.createdBy !== user.id) {
      // 檢查用戶是否為管理員
      const isAdmin = userRoles.some(
        (role) =>
          role.name.toUpperCase() === "ADMIN" ||
          role.name.toUpperCase() === "SUPERADMIN"
      );

      // 添加日誌輸出，用於調試
      logger.log("刪除流程實例請求:", {
        instanceId: id,
        force,
        userRoles: userRoles.map((r) => r.name),
        isAdmin,
        instanceStatus: instance.status,
      });

      const isForceDelete = (force === "true" || force === true) && isAdmin;
      logger.log("強制刪除檢查:", {
        force,
        forceType: typeof force,
        isAdmin,
        isForceDelete,
        condition1: force === "true",
        condition2: force === true,
      });

      // 如果不是管理員強制刪除，則只允許刪除非完成和非取消狀態的實例
      if (
        !isForceDelete &&
        (instance.status === "completed" || instance.status === "cancelled")
      ) {
        return errorResponse(
          res,
          400,
          "已完成或已取消的流程實例需要管理員權限才能標記為刪除"
        );
      }
    }

    // 使用事務確保軟刪除與相關日誌更新
    const result = await prisma.$transaction(async (tx) => {
      // 1. 更新流程實例狀態為 deleted (軟刪除)
      const updatedInstance = await tx.flowInstance.update({
        where: { id },
        data: {
          status: "deleted",
          logs: {
            push: {
              type: "SYSTEM",
              message: "流程實例已標記為刪除",
              timestamp: new Date().toISOString(),
              userId: user.id,
            },
          },
          updatedBy: user.id,
        },
      });

      logger.log(`流程實例 ${id} 已軟刪除，狀態更新為: deleted`);

      return updatedInstance;
    });

    return successResponse(res, 200, {
      message: "流程實例標記為刪除成功",
      status: "deleted",
    });
  } catch (error) {
    logger.error("刪除流程實例錯誤:", error);
    return errorResponse(res, 500, "刪除流程實例失敗");
  }
};

/**
 * 啟動流程實例 //TODO: 沒用到
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.startInstance = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const instance = await prisma.flowInstance.findUnique({
      where: { id },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 檢查流程實例狀態，只有非終止狀態可以啟動
    if (instance.status === "completed" || instance.status === "cancelled") {
      return errorResponse(res, 400, "已完成或已取消的流程實例不能啟動");
    }

    const updatedInstance = await prisma.flowInstance.update({
      where: { id },
      data: {
        status: "running",
        startedAt: new Date(),
        updatedBy: userId,
        logs: {
          push: {
            type: "SYSTEM",
            message: "流程實例開始執行",
            timestamp: new Date().toISOString(),
          },
        },
      },
    });

    // TODO: 這裡需要實現實際的流程執行邏輯
    // 可以使用消息隊列或其他異步處理方式

    successResponse(res, 200, updatedInstance);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 使用新的執行器工廠
const executeNodeLogic = async (node, input, context) => {
  return await NodeExecutorFactory.executeNode(node, input, context);
};

/**
 * TODO: 這裡需要重構，不應該跟客訴綁定,應該沒用到
 * 執行節點
 * @param {object} req - 請求對象
 * @param {object} res - 響應對象
 * @param {function} next - 下一個中間件
 * @returns {Promise<void>}
 */
exports.executeNode = asyncHandler(async (req, res, next) => {
  const { id: instanceId, nodeId } = req.params;
  const input = req.body || {};

  logger.log(`執行節點請求 - 實例ID: ${instanceId}, 節點ID: ${nodeId}`);
  logger.log("輸入數據:", JSON.stringify(input, null, 2));

  if (!instanceId) {
    return errorResponse(res, 400, "缺少流程實例ID");
  }

  if (!nodeId) {
    return errorResponse(res, 400, "缺少節點ID");
  }

  // 檢查輸入數據是否為空
  if (!input || Object.keys(input).length === 0) {
    logger.error("輸入數據為空");
    return errorResponse(res, 400, "輸入數據不能為空");
  }

  // 檢查是否有 _isDataUpdate 標記，如果有，則將請求視為數據更新而不是結構更新
  const isDataUpdate = input._isDataUpdate === true;

  // 移除 _isDataUpdate 標記，避免它被保存到數據庫中
  if (input._isDataUpdate !== undefined) {
    delete input._isDataUpdate;
  }

  try {
    // 使用事務確保數據一致性
    const result = await prisma.$transaction(async (prisma) => {
      // 獲取流程實例
      const flowInstance = await prisma.flowInstance.findUnique({
        where: { id: instanceId },
        include: {
          template: {
            select: {
              name: true,
              nodes: true,
              edges: true,
            },
          },
        },
      });

      if (!flowInstance) {
        throw new Error(`找不到流程實例 ${instanceId}`);
      }

      // 如果有 isDataUpdate 標記，則將請求視為數據更新而不是結構更新
      // 這樣即使流程實例狀態不是 draft，也可以執行節點
      if (
        !isDataUpdate &&
        (flowInstance.status === "completed" ||
          flowInstance.status === "cancelled")
      ) {
        // 檢查是否是數據更新（nodeData, nodeStates, logs）
        // 如果不是，則拒絕請求
        const isNodeDataUpdate =
          input.nodeData !== undefined ||
          input.nodeStates !== undefined ||
          input.logs !== undefined;

        if (!isNodeDataUpdate) {
          throw new Error("已完成或已取消的流程實例不能更新結構");
        }
      }

      // 獲取節點數據
      const nodeData = flowInstance.nodeData?.[nodeId] || {};
      logger.log("節點數據:", JSON.stringify(nodeData, null, 2));

      // 合併節點數據和輸入數據
      const mergedInput = {
        ...nodeData,
        ...input,
      };
      logger.log("合併後的輸入數據:", JSON.stringify(mergedInput, null, 2));

      // 確保客訴單號被正確傳遞
      const complaintId = mergedInput.complaintId || input.complaintId;
      const complaintDetail =
        mergedInput.complaintDetail || input.complaintDetail;

      logger.log(`客訴單號: ${complaintId}, 客訴詳情:`, complaintDetail);

      // 獲取節點類型
      let nodeType = mergedInput.nodeType || nodeData.type;

      // 如果沒有節點類型，嘗試從節點數據中獲取
      if (!nodeType) {
        // 從流程定義中獲取節點
        const templateNodes = flowInstance.template?.nodes || [];
        const node = templateNodes.find((n) => n.id === nodeId);

        if (node) {
          nodeType = node.type || node.data?.type;
          logger.log(`從流程定義中獲取節點類型: ${nodeType}`);
        }
      }

      // 如果仍然沒有節點類型，嘗試從標籤或其他屬性推斷
      if (
        !nodeType &&
        (nodeData.label === "客訴單號選擇器" ||
          input.label === "客訴單號選擇器")
      ) {
        nodeType = "ComplaintSelectorNode";
        logger.log(`從標籤推斷節點類型: ${nodeType}`);
      }

      if (!nodeType) {
        throw new Error(`無法確定節點 ${nodeId} 的類型`);
      }

      // 更新節點狀態為運行中
      const nodeStates = {
        ...flowInstance.nodeStates,
        [nodeId]: {
          ...flowInstance.nodeStates?.[nodeId],
          status: "running",
          startTime: new Date().toISOString(),
          retryCount: (flowInstance.nodeStates?.[nodeId]?.retryCount || 0) + 1,
        },
      };

      // 更新流程實例
      await prisma.flowInstance.update({
        where: { id: instanceId },
        data: {
          nodeStates,
          // 更新節點數據，確保保存客訴單號等信息
          nodeData: {
            ...flowInstance.nodeData,
            [nodeId]: {
              ...nodeData,
              ...input,
              type: nodeType, // 確保保存節點類型
              complaintId, // 確保保存客訴單號
              complaintDetail, // 確保保存客訴詳情
            },
          },
        },
      });

      try {
        logger.log(`開始執行節點 ${nodeId}，類型: ${nodeType}`);
        // 執行節點
        const startTime = Date.now();
        const result = await NodeExecutorFactory.executeNode(
          {
            id: nodeId,
            type: nodeType,
            data: {
              ...mergedInput,
              complaintId, // 確保傳遞客訴單號
              complaintDetail, // 確保傳遞客訴詳情
            },
          },
          {
            ...mergedInput,
            complaintId, // 確保傳遞客訴單號
            complaintDetail, // 確保傳遞客訴詳情
          },
          { flowInstance }
        );
        const endTime = Date.now();
        const executionTime = (endTime - startTime) / 1000; // 轉換為秒

        logger.log(`節點 ${nodeId} 執行完成，結果:`, result);

        // 更新節點狀態為完成
        const updatedNodeStates = {
          ...nodeStates,
          [nodeId]: {
            ...nodeStates[nodeId],
            status: "completed",
            endTime: new Date().toISOString(),
            executionTime,
          },
        };

        // 更新節點上下文
        const nodeContext = {
          ...flowInstance.nodeContext,
          [nodeId]: {
            input: {
              ...mergedInput,
              complaintId, // 確保保存客訴單號
              complaintDetail, // 確保保存客訴詳情
            },
            output: result,
            executionTime,
          },
        };

        // 更新流程實例
        const updatedFlowInstance = await prisma.flowInstance.update({
          where: { id: instanceId },
          data: {
            nodeStates: updatedNodeStates,
            nodeContext,
            // 更新節點數據，確保保存客訴單號等信息
            nodeData: {
              ...flowInstance.nodeData,
              [nodeId]: {
                ...nodeData,
                ...input,
                type: nodeType,
                complaintId, // 確保保存客訴單號
                complaintDetail, // 確保保存客訴詳情
              },
            },
          },
        });

        return updatedFlowInstance;
      } catch (error) {
        logger.error(`執行節點 ${nodeId} 時發生錯誤:`, error);

        // 獲取詳細的錯誤信息
        const errorDetails = {
          message: error.message,
          stack: error.stack,
          code: error.code,
          name: error.name,
        };

        // 根據錯誤類型提供建議
        let suggestion = "請檢查輸入數據並重試";
        if (error.message.includes("不支持的節點類型")) {
          suggestion = "該節點類型不受支持，請聯繫系統管理員";
        } else if (error.message.includes("缺少必要的")) {
          suggestion = "請確保提供所有必要的輸入數據";
        } else if (error.code === "ECONNREFUSED") {
          suggestion = "無法連接到外部服務，請檢查網絡連接";
        }

        // 更新節點狀態為失敗，但不更改流程實例狀態
        const updatedNodeStates = {
          ...nodeStates,
          [nodeId]: {
            ...nodeStates[nodeId],
            status: "failed",
            error: error.message,
            endTime: new Date().toISOString(),
            errorDetails,
            suggestion,
          },
        };

        // 更新流程實例
        const updatedFlowInstance = await prisma.flowInstance.update({
          where: { id: instanceId },
          data: {
            nodeStates: updatedNodeStates,
            // 保持流程實例狀態不變
            // status: "running",
            // 更新節點數據，確保保存客訴單號等信息
            nodeData: {
              ...flowInstance.nodeData,
              [nodeId]: {
                ...nodeData,
                ...input,
                type: nodeType,
                complaintId, // 確保保存客訴單號
                complaintDetail, // 確保保存客訴詳情
              },
            },
          },
        });

        return updatedFlowInstance;
      }
    });

    // 返回成功響應
    return successResponse(res, 200, result, "節點執行成功");
  } catch (error) {
    logger.error("執行節點時發生錯誤:", error);
    return errorResponse(res, 500, "執行節點時發生錯誤", error);
  }
});

exports.getInstanceLogs = async (req, res) => {
  try {
    const { id } = req.params;

    const instance = await prisma.flowInstance.findUnique({
      where: { id },
      select: { logs: true },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    successResponse(res, 200, instance.logs);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

/**
 * 獲取節點日誌
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.getNodeLogs = async (req, res) => {
  try {
    const { id, nodeId } = req.params;

    const instance = await prisma.flowInstance.findUnique({
      where: { id },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    const nodeLogs = instance.logs.filter((log) => log.nodeId === nodeId);
    successResponse(res, 200, nodeLogs);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

/**
 * 恢復已刪除的流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.restoreInstance = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // 檢查流程實例是否存在
    const instance = await prisma.flowInstance.findUnique({
      where: { id },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 檢查流程實例是否已被刪除
    if (instance.status !== "deleted") {
      return errorResponse(res, 400, "此流程實例未被刪除，無需恢復");
    }

    // 檢查用戶是否有權限恢復
    if (instance.createdBy !== user.id) {
      // 檢查用戶是否為管理員
      const userWithRoles = await prisma.user.findUnique({
        where: { id: user.id },
        include: { userRoles: true },
      });

      if (!userWithRoles) {
        return errorResponse(res, 404, "用戶不存在");
      }

      const userRoles = userWithRoles.userRoles || [];
      const isAdmin = userRoles.some(
        (role) =>
          role.name.toUpperCase() === "ADMIN" ||
          role.name.toUpperCase() === "SUPERADMIN"
      );

      if (!isAdmin) {
        return errorResponse(res, 403, "您沒有權限恢復此流程實例");
      }
    }

    // 恢復流程實例（將狀態恢復為原先的狀態或設為active）
    const originalStatus = "active"; // 預設恢復為active狀態，如果需要恢復為原始狀態，可以考慮在軟刪除時保存原始狀態

    const result = await prisma.$transaction(async (tx) => {
      const restoredInstance = await tx.flowInstance.update({
        where: { id },
        data: {
          status: originalStatus,
          logs: {
            push: {
              type: "SYSTEM",
              message: "流程實例已從刪除狀態恢復",
              timestamp: new Date().toISOString(),
              userId: user.id,
            },
          },
          updatedBy: user.id,
        },
      });

      logger.log(
        `流程實例 ${id} 已從刪除狀態恢復，目前狀態：${originalStatus}`
      );

      return restoredInstance;
    });

    return successResponse(res, 200, {
      message: "流程實例恢復成功",
      instance: result,
    });
  } catch (error) {
    logger.error("恢復流程實例錯誤:", error);
    return errorResponse(res, 500, "恢復流程實例失敗");
  }
};
