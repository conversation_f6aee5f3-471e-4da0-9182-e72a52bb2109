/**
 * 恢復已刪除的流程實例
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.restoreInstance = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // 檢查流程實例是否存在
    const instance = await prisma.flowInstance.findUnique({
      where: { id },
    });

    if (!instance) {
      return errorResponse(res, 404, "流程實例不存在");
    }

    // 檢查流程實例是否已被刪除
    if (instance.status !== "deleted") {
      return errorResponse(res, 400, "此流程實例未被刪除，無需恢復");
    }

    // 檢查用戶是否有權限恢復
    if (instance.createdBy !== user.id) {
      // 檢查用戶是否為管理員
      const userWithRoles = await prisma.user.findUnique({
        where: { id: user.id },
        include: { userRoles: true },
      });

      if (!userWithRoles) {
        return errorResponse(res, 404, "用戶不存在");
      }

      const userRoles = userWithRoles.userRoles || [];
      const isAdmin = userRoles.some(
        (role) =>
          role.name.toUpperCase() === "ADMIN" ||
          role.name.toUpperCase() === "SUPERADMIN"
      );

      if (!isAdmin) {
        return errorResponse(res, 403, "您沒有權限恢復此流程實例");
      }
    }

    // 恢復流程實例（將狀態恢復為原先的狀態或設為active）
    const originalStatus = "active"; // 預設恢復為active狀態，如果需要恢復為原始狀態，可以考慮在軟刪除時保存原始狀態

    const result = await prisma.$transaction(async (tx) => {
      const restoredInstance = await tx.flowInstance.update({
        where: { id },
        data: {
          status: originalStatus,
          logs: {
            push: {
              type: "SYSTEM",
              message: "流程實例已從刪除狀態恢復",
              timestamp: new Date().toISOString(),
              userId: user.id,
            },
          },
          updatedBy: user.id,
        },
      });

      logger.log(
        `流程實例 ${id} 已從刪除狀態恢復，目前狀態：${originalStatus}`
      );

      return restoredInstance;
    });

    return successResponse(res, 200, {
      message: "流程實例恢復成功",
      instance: result,
    });
  } catch (error) {
    logger.error("恢復流程實例錯誤:", error);
    return errorResponse(res, 500, "恢復流程實例失敗");
  }
};
