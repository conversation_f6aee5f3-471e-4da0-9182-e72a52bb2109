const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient({
  log: ["query", "info", "warn", "error"],
});
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { logger } = require("../utils/logger");

// 獲取所有工作流程模板
exports.getAllTemplates = async (req, res) => {
  // 獲取查詢參數
  const { isDeleted, simplified = true } = req.query;

  try {
    // 如果 simplified 為 true，則只獲取模板名稱和 id, createdAt, updatedAt,creator,updater,status
    const where = {};

    if (isDeleted) {
      where.isDeleted = isDeleted;
    }
    const queryOptions = {
      where,
      orderBy: {
        createdAt: "desc",
      },
    };

    queryOptions.select = {
      id: true,
      name: true,
      description: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      thumbnail: true,
      nodes: true,
      edges: true,
      metadata: true,
      creator: {
        select: {
          id: true,
          username: true,
          avatar: true,
        },
      },
      updater: {
        select: {
          id: true,
          username: true,
          avatar: true,
        },
      },
    };

    const templates = await prisma.flowTemplate.findMany(queryOptions);

    // 將資料庫中的 isDeleted 標記映射到前端的 status="deleted"
    // const mappedTemplates = templates.map((template) => {
    //   // 如果前端傳來的狀態是 "deleted"，則設定 isDeleted = true
    //   if (template.status === "deleted") {
    //     return {
    //       ...template,
    //       isDeleted: true,
    //     };
    //   }
    //   return template;
    // });

    console.log("templates size:", templates.length);
    successResponse(res, 200, templates);
  } catch (error) {
    logger.error(`獲取所有工作流程模板失敗:`, error);
    errorResponse(res, 500, "獲取工作流程模板失敗");
  }
};

// 根據 ID 獲取工作流程模板
exports.getTemplateById = async (req, res) => {
  try {
    const { id } = req.params;

    const template = await prisma.flowTemplate.findFirst({
      where: {
        id,
        isDeleted: false, // 只獲取未軟刪除的模板
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    if (!template) {
      return errorResponse(res, 404, "找不到工作流程模板");
    }

    successResponse(res, 200, template);
  } catch (error) {
    logger.error(`獲取工作流程模板失敗:`, error);
    errorResponse(res, 500, "獲取工作流程模板失敗");
  }
};

// 創建新的工作流程模板
exports.createTemplate = async (req, res) => {
  try {
    const {
      name,
      type,
      description,
      version,
      status = "inactive",
      nodes = [],
      edges = [],
      metadata,
      createdBy,
      updatedBy,
      thumbnail,
    } = req.body;

    const template = await prisma.flowTemplate.create({
      data: {
        name,
        type,
        description,
        version,
        status,
        nodes,
        edges,
        metadata: metadata || null,
        thumbnail,
        createdBy,
        updatedBy,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 201, template);
  } catch (error) {
    logger.error("Error creating template:", error);
    errorResponse(res, 500, "創建工作流程模板失敗");
  }
};

// 更新工作流程模板
exports.updateTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      version,
      status,
      nodes,
      edges,
      thumbnail,
      metadata,
      updatedBy,
    } = req.body;

    // 檢查是否需要處理「已刪除」狀態
    if (status === "deleted") {
      // 執行軟刪除（更新 isDeleted 標記為 true）
      const template = await prisma.flowTemplate.update({
        where: { id },
        data: {
          status,
          isDeleted: true,
          updatedBy,
          updatedAt: new Date(),
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              avatar: true,
            },
          },
          updater: {
            select: {
              id: true,
              username: true,
              avatar: true,
            },
          },
        },
      });

      successResponse(res, 200, template);
      return;
    }

    // 正常更新流程
    const template = await prisma.flowTemplate.update({
      where: { id },
      data: {
        name,
        description,
        type,
        version,
        thumbnail,
        status,
        ...(nodes && { nodes }),
        ...(edges && { edges }),
        ...(metadata && { metadata }),
        updatedBy,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 200, template);
  } catch (error) {
    errorResponse(res, 500, "更新工作流程模板失敗");
  }
};

// 刪除工作流程模板
exports.deleteTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { permanent } = req.query; // 新增參數，用於判斷是永久刪除還是軟刪除

    logger.info(`準備刪除模板，id: ${id}, permanent: ${!!permanent}`);

    // 先檢查記錄是否存在
    const template = await prisma.flowTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      logger.warn(`找不到要刪除的工作流程模板，id: ${id}`);
      return errorResponse(res, 404, "找不到要刪除的工作流程模板");
    }

    // 檢查該模板是否被流程實例引用
    const instanceCount = await prisma.flowInstance.count({
      where: { templateId: id },
    });

    if (instanceCount > 0) {
      logger.warn(
        `欲刪除的工作流程模板 ${id} 有 ${instanceCount} 個流程實例引用，這些實例將顯示範本已刪除`
      );
    }

    // 如果是永久刪除請求，且模板狀態已經是 deleted，則執行實際刪除
    if (permanent === "true" && template.status === "deleted") {
      await prisma.flowTemplate.delete({
        where: { id },
      });

      logger.info(`永久刪除工作流程模板成功，id: ${id}`);
      successResponse(res, 204, null);
      return;
    }

    // 否則執行軟刪除（更新 status 為 deleted）
    await prisma.flowTemplate.update({
      where: { id },
      data: {
        status: "deleted",
        isDeleted: true,
        updatedAt: new Date(),
      },
    });

    logger.info(`軟刪除工作流程模板成功，id: ${id}`);
    successResponse(res, 204, null);
  } catch (error) {
    logger.error(`刪除工作流程模板失敗:`, error);
    errorResponse(res, 500, "刪除工作流程模板失敗");
  }
};

// 複製工作流程模板
exports.cloneTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { createdBy, updatedBy } = req.body;

    // 獲取原始模板
    const sourceTemplate = await prisma.flowTemplate.findFirst({
      where: {
        id,
        isDeleted: false, // 確保只複製未刪除的模板
      },
    });

    if (!sourceTemplate) {
      return errorResponse(res, 404, "找不到工作流程模板");
    }

    // 創建新的模板
    const newTemplate = await prisma.flowTemplate.create({
      data: {
        name: `${sourceTemplate.name} (複製)`,
        description: sourceTemplate.description,
        type: sourceTemplate.type,
        version: "1.0.0", // 重置版本號
        status: "inactive", // 設置為停用狀態
        nodes: sourceTemplate.nodes,
        edges: sourceTemplate.edges,
        metadata: sourceTemplate.metadata,
        createdBy,
        updatedBy,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 201, newTemplate);
  } catch (error) {
    errorResponse(res, 500, "複製工作流程模板失敗");
  }
};

// 更新模板狀態
exports.updateTemplateStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, updatedBy } = req.body;

    // 僅允許 active/inactive/deleted
    if (!["active", "inactive", "deleted"].includes(status)) {
      return errorResponse(res, 400, "無效的狀態（僅允許啟用、停用、已刪除）");
    }

    // 若為 deleted，執行軟刪除
    if (status === "deleted") {
      const template = await prisma.flowTemplate.update({
        where: { id, isDeleted: false },
        data: {
          status,
          isDeleted: true,
          updatedBy,
          updatedAt: new Date(),
        },
        include: {
          creator: { select: { id: true, username: true, avatar: true } },
          updater: { select: { id: true, username: true, avatar: true } },
        },
      });
      return successResponse(res, 200, template);
    }

    // 一般狀態切換
    const template = await prisma.flowTemplate.update({
      where: { id, isDeleted: false },
      data: {
        status,
        updatedBy,
        updatedAt: new Date(),
      },
      include: {
        creator: { select: { id: true, username: true, avatar: true } },
        updater: { select: { id: true, username: true, avatar: true } },
      },
    });
    successResponse(res, 200, template);
  } catch (error) {
    if (error.code === "P2025") {
      return errorResponse(res, 404, "找不到工作流程模板或模板已被刪除");
    }
    errorResponse(res, 500, "更新工作流程模板狀態失敗");
  }
};

// 創建模板
exports.createFlowTemplate = async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      status,
      nodes,
      edges,
      createdBy,
      thumbnail,
    } = req.body;

    // 檢查模板名稱是否已存在
    const existingTemplate = await prisma.flowTemplate.findFirst({
      where: {
        name,
        isDeleted: false,
      },
    });

    if (existingTemplate) {
      return errorResponse(res, 400, "模板名稱已存在");
    }

    // 創建模板
    const template = await prisma.flowTemplate.create({
      data: {
        name,
        description,
        type,
        status,
        nodes,
        edges,
        thumbnail, // 添加縮圖欄位
        createdBy: {
          connect: {
            id: createdBy,
          },
        },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return successResponse(res, 201, "模板創建成功", template);
  } catch (error) {
    console.error("創建模板失敗:", error);
    return errorResponse(res, 500, "創建模板失敗", error);
  }
};

// 更新模板
exports.updateFlowTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      status,
      nodes,
      edges,
      updatedBy,
      thumbnail,
    } = req.body;

    // 檢查模板是否存在
    const existingTemplate = await prisma.flowTemplate.findFirst({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!existingTemplate) {
      return errorResponse(res, 404, "模板不存在");
    }

    // 如果更改了名稱，檢查新名稱是否已存在
    if (name !== existingTemplate.name) {
      const nameExists = await prisma.flowTemplate.findFirst({
        where: {
          name,
          id: {
            not: id,
          },
          isDeleted: false,
        },
      });

      if (nameExists) {
        return errorResponse(res, 400, "模板名稱已存在");
      }
    }

    // 更新模板
    const template = await prisma.flowTemplate.update({
      where: {
        id,
      },
      data: {
        name,
        description,
        type,
        status,
        nodes,
        edges,
        thumbnail, // 添加縮圖欄位
        updatedBy: {
          connect: {
            id: updatedBy,
          },
        },
        updatedAt: new Date(),
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return successResponse(res, 200, "模板更新成功", template);
  } catch (error) {
    console.error("更新模板失敗:", error);
    return errorResponse(res, 500, "更新模板失敗", error);
  }
};

prisma.$on("query", (e) => {
  console.log("SQL:", e.query);
  console.log("Params:", e.params);
  // 你可以選擇自己組合 query 與 params
});
