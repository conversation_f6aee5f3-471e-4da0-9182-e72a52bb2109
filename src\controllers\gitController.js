const path = require("path");
const fs = require("fs");
const axios = require("axios"); // 需要安裝 axios
const { successResponse, errorResponse } = require("../utils/jsonResponse");

// 定義 GitLab 專案與分支信息
const GITLAB_PROJECTS = {
  frontend: {
    id: "368", // 替換為實際的 GitLab 專案 ID
    name: "frontend",
    token: "t-WzJPhBHxaB8mUahBt_",
    defaultBranch: "experiment",
  },
  backend: {
    id: "365", // 替換為實際的 GitLab 專案 ID
    name: "backend",
    token: "********************",
    defaultBranch: "experiment",
  },
  analysis: {
    id: "366", // 替換為實際的 GitLab 專案 ID
    name: "analysis",
    token: "HcKdJCYdpRax8NBxQMAV",
    defaultBranch: "main",
  },
};

// GitLab API 的基本 URL
const GITLAB_API_URL = "https://gitlab.flexium.com.cn/api/v4"; // 替換為你的 GitLab 實例 URL

const getGitHistory = async (req, res) => {
  try {
    const {
      project = "frontend", // 默認為前端專案
      branch,
      since = "1 month ago",
      maxCount = 100,
    } = req.query;

    // 獲取專案配置
    const projectConfig = GITLAB_PROJECTS[project];
    if (!projectConfig) {
      return errorResponse(res, 400, `未找到專案配置: ${project}`);
    }

    // 使用專案的默認分支，如果沒有提供特定分支
    const targetBranch = branch || projectConfig.defaultBranch;

    // 計算 since 參數對應的日期（GitLab API 使用 ISO 日期格式）
    let sinceDate = new Date();
    if (since.includes("month")) {
      const months = parseInt(since) || 1;
      sinceDate.setMonth(sinceDate.getMonth() - months);
    } else if (since.includes("week")) {
      const weeks = parseInt(since) || 1;
      sinceDate.setDate(sinceDate.getDate() - weeks * 7);
    } else if (since.includes("year")) {
      const years = parseInt(since) || 1;
      sinceDate.setFullYear(sinceDate.getFullYear() - years);
    } else if (since.includes("day")) {
      const days = parseInt(since) || 1;
      sinceDate.setDate(sinceDate.getDate() - days);
    }

    const sinceDateISO = sinceDate.toISOString();
    console.log(
      `${GITLAB_API_URL}/projects/${encodeURIComponent(projectConfig.id)}/repository/commits`
    );
    // 從 GitLab API 獲取提交歷史
    const response = await axios.get(
      `${GITLAB_API_URL}/projects/${encodeURIComponent(projectConfig.id)}/repository/commits`,
      {
        headers: {
          "PRIVATE-TOKEN": projectConfig.token,
        },
        params: {
          ref_name: targetBranch,
          since: sinceDateISO,
          per_page: maxCount,
        },
      }
    );

    // 獲取提交詳情（GitLab API 不會在列表 API 中返回詳細的更改統計）
    const commits = await Promise.all(
      response.data.map(async (commit) => {
        try {
          // 獲取單個提交的詳細信息
          const commitDetail = await axios.get(
            `${GITLAB_API_URL}/projects/${encodeURIComponent(projectConfig.id)}/repository/commits/${commit.id}`,
            {
              headers: {
                "PRIVATE-TOKEN": projectConfig.token,
              },
            }
          );

          // 格式化提交資訊
          return {
            hash: commit.id,
            author: commit.author_name,
            email: commit.author_email,
            date: new Date(commit.created_at).toLocaleString("zh-TW"),
            subject: commit.title,
            body: commit.message,
            stats: {
              filesChanged: commitDetail.data.stats?.total || 0,
              insertions: commitDetail.data.stats?.additions || 0,
              deletions: commitDetail.data.stats?.deletions || 0,
            },
            branch: targetBranch,
          };
        } catch (error) {
          console.error(`獲取提交 ${commit.id} 的詳細信息失敗:`, error.message);
          return {
            hash: commit.id,
            author: commit.author_name,
            email: commit.author_email,
            date: new Date(commit.created_at).toLocaleString("zh-TW"),
            subject: commit.title,
            body: commit.message,
            stats: {
              filesChanged: 0,
              insertions: 0,
              deletions: 0,
            },
            branch: targetBranch,
          };
        }
      })
    );

    successResponse(res, 200, { commits });
  } catch (error) {
    console.error("獲取 GitLab 提交歷史時出錯:", error.message);
    errorResponse(res, 500, error.message);
  }
};

const getGitBranch = async (req, res) => {
  try {
    const { project = "frontend" } = req.query;

    // 獲取專案配置
    const projectConfig = GITLAB_PROJECTS[project];
    if (!projectConfig) {
      return errorResponse(res, 400, `未找到專案配置: ${project}`);
    }

    // 從 GitLab API 獲取分支列表
    const response = await axios.get(
      `${GITLAB_API_URL}/projects/${encodeURIComponent(projectConfig.id)}/repository/branches`,
      {
        headers: {
          "PRIVATE-TOKEN": projectConfig.token,
        },
      }
    );
    console.log(response.data);

    // 格式化分支列表
    const branches = response.data.map((branch) => branch.name);

    successResponse(res, 200, { branches });
  } catch (error) {
    console.error("獲取 GitLab 分支列表時出錯:", error.message);
    errorResponse(res, 500, error.message);
  }
};

module.exports = {
  getGitHistory,
  getGitBranch,
};
