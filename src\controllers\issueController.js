/**
 * 問題回報控制器
 */
const fs = require("fs").promises;
const path = require("path");
const { PrismaClient } = require("@prisma/client");
const { fullPaths, urlPaths } = require("../config/paths");
const { AppError } = require("../middlewares/errorHandler");
const { successResponse, errorResponse } = require("../utils/jsonResponse");
const { logger } = require("../utils/logger");

const prisma = new PrismaClient();

/**
 * 驗證截圖文件
 * @param {Object} file 文件對象
 * @throws {AppError} 驗證失敗時拋出錯誤
 */
const validateScreenshot = (file) => {
  const { issue } = require("../config/paths").paths;

  if (!file) {
    throw new AppError("請選擇要上傳的截圖", 400);
  }

  // 驗證文件類型
  if (!issue.allowedTypes.includes(file.mimetype)) {
    throw new AppError("只支援 JPG、PNG、GIF 格式的圖片", 400);
  }

  // 驗證文件大小
  if (file.size > issue.maxSize) {
    throw new AppError(
      `檔案大小不能超過 ${issue.maxSize / 1024 / 1024}MB`,
      400
    );
  }
};

/**
 * 刪除文件
 * @param {String} filePath 文件路徑
 * @returns {Promise<void>}
 */
const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
    logger.info("檔案刪除成功", { path: filePath });
  } catch (error) {
    logger.error("檔案刪除失敗", { error: error.message, path: filePath });
    // 不拋出錯誤，僅記錄
  }
};

/**
 * 獲取截圖URL
 * @param {String} filename 文件名
 * @returns {String} 截圖URL
 */
const getScreenshotUrl = (filename) => {
  if (!filename) return null;
  return `${urlPaths.issuePrefix}/${filename}`;
};

/**
 * 建立問題單
 * @param {Object} data 問題單數據
 * @param {String} reporterId 回報者ID
 * @returns {Promise<Object>} 建立的問題單
 */
const createIssue = async (data, reporterId) => {
  try {
    const issue = await prisma.issueTicket.create({
      data: {
        ...data,
        reporterId,
      },
    });

    logger.info("問題單建立成功", { issueId: issue.id, reporterId });
    return issue;
  } catch (error) {
    logger.error("問題單建立失敗", { error: error.message, data, reporterId });
    throw new AppError("問題單建立失敗", 500);
  }
};

/**
 * 獲取所有問題單
 * @param {Object} filters 過濾條件
 * @returns {Promise<Array>} 問題單列表
 */
const getAllIssues = async (filters = {}) => {
  try {
    const { status, category, systemCode, priority, type } = filters;

    // 構建查詢條件
    const where = {};
    if (status) where.status = status;
    if (category) where.category = category;
    if (systemCode) where.systemCode = systemCode;
    if (priority) where.priority = priority;
    if (type) where.type = type;

    const issues = await prisma.issueTicket.findMany({
      where,
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    return issues;
  } catch (error) {
    logger.error("獲取問題單列表失敗", { error: error.message, filters });
    throw new AppError("獲取問題單列表失敗", 500);
  }
};

/**
 * 通過ID獲取問題單
 * @param {String} id 問題單ID
 * @returns {Promise<Object>} 問題單
 */
const getIssueById = async (id) => {
  try {
    const issue = await prisma.issueTicket.findUnique({
      where: { id },
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    if (!issue) {
      throw new AppError("問題單不存在", 404);
    }

    return issue;
  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error("獲取問題單失敗", { error: error.message, id });
    throw new AppError("獲取問題單失敗", 500);
  }
};

/**
 * 更新問題單
 * @param {String} id 問題單ID
 * @param {Object} data 更新數據
 * @returns {Promise<Object>} 更新後的問題單
 */
const updateIssue = async (id, data) => {
  try {
    // 檢查問題單是否存在
    const exists = await prisma.issueTicket.findUnique({
      where: { id },
    });

    if (!exists) {
      throw new AppError("問題單不存在", 404);
    }

    const updatedIssue = await prisma.issueTicket.update({
      where: { id },
      data,
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
    });

    logger.info("問題單更新成功", { issueId: id });
    return updatedIssue;
  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error("更新問題單失敗", { error: error.message, id, data });
    throw new AppError("更新問題單失敗", 500);
  }
};

/**
 * 刪除問題單
 * @param {String} id 問題單ID
 * @returns {Promise<Object>} 刪除的問題單
 */
const deleteIssue = async (id) => {
  try {
    // 檢查問題單是否存在
    const issue = await prisma.issueTicket.findUnique({
      where: { id },
    });

    if (!issue) {
      throw new AppError("問題單不存在", 404);
    }

    // 如果有截圖，刪除截圖文件
    if (issue.screenshot) {
      const screenshotPath = path.join(fullPaths.issueDir, issue.screenshot);
      try {
        await fs.unlink(screenshotPath);
        logger.info("問題單截圖刪除成功", {
          issueId: id,
          path: screenshotPath,
        });
      } catch (fileError) {
        logger.warn("問題單截圖刪除失敗", {
          error: fileError.message,
          path: screenshotPath,
        });
        // 繼續處理，不中斷流程
      }
    }

    // 刪除問題單
    const deletedIssue = await prisma.issueTicket.delete({
      where: { id },
    });

    logger.info("問題單刪除成功", { issueId: id });
    return deletedIssue;
  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error("刪除問題單失敗", { error: error.message, id });
    throw new AppError("刪除問題單失敗", 500);
  }
};

/**
 * 控制器 - 取得所有問題單
 */
exports.getAllIssues = async (req, res) => {
  try {
    const { status, category, systemCode, priority, type } = req.query;

    // 構建查詢條件
    const where = {};
    if (status) where.status = status;
    if (category) where.category = category;
    if (systemCode) where.systemCode = systemCode;
    if (priority) where.priority = priority;
    if (type) where.type = type;

    const issues = await prisma.issueTicket.findMany({
      where,
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    successResponse(res, 200, issues);
  } catch (error) {
    logger.error("獲取問題單列表失敗", { error: error.message });
    errorResponse(res, 500, error.message || "獲取問題單列表失敗");
  }
};

/**
 * 控制器 - 通過ID獲取問題單
 */
exports.getIssueById = async (req, res) => {
  try {
    const { id } = req.params;
    const issue = await getIssueById(id);

    successResponse(res, 200, issue);
  } catch (error) {
    logger.error("獲取問題單失敗", { error: error.message, id: req.params.id });
    errorResponse(res, 500, error.message || "獲取問題單失敗");
  }
};

/**
 * 控制器 - 建立問題單
 */
exports.createIssue = async (req, res) => {
  try {
    const {
      title,
      content,
      type,
      category,
      systemCode = "IYM",
      priority,
      status = "open",
      comment,
      assigneeId,
      screenshot: newScreenshot,
    } = req.body;

    // 獲取當前用戶ID
    const reporterId = req.user.id;

    // 獲取上傳的截圖文件名
    const screenshot = newScreenshot
      ? newScreenshot
      : req.file
        ? req.file.filename
        : null;

    // 建立問題單
    const issue = await createIssue(
      {
        title,
        content,
        type,
        category,
        systemCode,
        priority,
        status,
        comment,
        assigneeId,
        screenshot,
      },
      reporterId
    );

    successResponse(res, 201, issue);
  } catch (error) {
    // 如果上傳過程中出錯，刪除已上傳的文件
    if (req.file) {
      await deleteFile(req.file.path);
    }

    logger.error("建立問題單失敗", { error: error.message, body: req.body });
    errorResponse(res, 500, error.message || "建立問題單失敗");
  }
};

/**
 * 控制器 - 更新問題單
 */
exports.updateIssue = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      type,
      category,
      systemCode,
      priority,
      status,
      comment,
      assigneeId,
      screenshot: newScreenshot,
    } = req.body;

    // 取得原始問題單資料
    const originalIssue = await getIssueById(id);

    // 處理截圖更新
    let screenshot = originalIssue.screenshot;

    // 如果上傳了新截圖(前端在表單處理前就上傳了，所以這段根本不需要!!)
    if (req.file) {
      logger.info("新截圖上傳", {
        filename: req.file.filename,
        originalScreenshot: originalIssue.screenshot,
      });

      // 如果原本有截圖，刪除原始檔案
      if (originalIssue.screenshot) {
        const oldFilePath = path.join(
          fullPaths.issueDir,
          originalIssue.screenshot
        );
        await deleteFile(oldFilePath);
      }

      // 更新為新的截圖檔名
      screenshot = req.file.filename;
    }

    logger.info("準備更新問題單", {
      id,
      newScreenshot: screenshot,
      hasNewFile: !!req.file,
    });

    screenshot = newScreenshot;

    // 更新問題單
    const updatedIssue = await updateIssue(id, {
      ...(title && { title }),
      ...(content && { content }),
      ...(type && { type }),
      ...(category && { category }),
      ...(systemCode && { systemCode }),
      ...(priority && { priority }),
      ...(status && { status }),
      ...(comment !== undefined && { comment }),
      ...(assigneeId !== undefined && { assigneeId }),
      screenshot, // 直接更新 screenshot，不需要條件運算符
    });

    logger.info("問題單更新完成", {
      id,
      updatedScreenshot: updatedIssue.screenshot,
    });

    successResponse(res, 200, updatedIssue);
  } catch (error) {
    // 如果上傳過程中出錯，刪除已上傳的文件
    if (req.file) {
      await deleteFile(req.file.path);
    }

    logger.error("更新問題單失敗", {
      error: error.message,
      id: req.params.id,
      body: req.body,
      file: req.file,
    });
    errorResponse(res, 500, error.message || "更新問題單失敗");
  }
};

/**
 * 控制器 - 刪除問題單
 */
exports.deleteIssue = async (req, res) => {
  try {
    const { id } = req.params;

    // 刪除問題單（含相關文件刪除）
    const deletedIssue = await deleteIssue(id);

    res.json({
      message: "問題單刪除成功",
      data: deletedIssue,
    });
  } catch (error) {
    logger.error("刪除問題單失敗", { error: error.message, id: req.params.id });
    res.status(error.statusCode || 500).json({
      message: error.message || "刪除問題單失敗",
    });
  }
};

/**
 * 控制器 - 上傳截圖
 */
exports.uploadScreenshot = async (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).json({ message: "請選擇要上傳的截圖" });
    }

    // 驗證文件
    try {
      validateScreenshot(file);
    } catch (error) {
      // 驗證失敗時刪除上傳的文件
      await deleteFile(file.path);
      return errorResponse(res, 400, error.message);
    }

    // 返回文件信息
    successResponse(res, 200, {
      filename: file.filename,
      path: getScreenshotUrl(file.filename),
      size: file.size,
      mimetype: file.mimetype,
    });
  } catch (error) {
    // 如果上傳過程中出錯，刪除已上傳的文件
    if (req.file) {
      await deleteFile(req.file.path);
    }

    logger.error("上傳截圖失敗", { error: error.message });
    errorResponse(res, 500, error.message || "上傳截圖失敗");
  }
};

// 將所有函數導出，方便測試
exports._internal = {
  validateScreenshot,
  deleteFile,
  getScreenshotUrl,
  createIssue,
  getAllIssues,
  getIssueById,
  updateIssue,
  deleteIssue,
};
