const { PrismaClient } = require("@prisma/client");
const { handlePrismaError } = require("../utils/errorHandler");
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { logger } = require("../utils/logger");

const prisma = new PrismaClient({
  // log: ["query", "info", "warn", "error"],
});

// 取得所有員工資料
const getAllEmployees = async (req, res) => {
  try {
    // 取得分頁參數，若無則使用預設值
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 25;

    // 計算要跳過的資料筆數
    const skip = (page - 1) * pageSize;

    // 取得總筆數
    const total = await prisma.orgEmployee.count();

    // 取得分頁後的資料
    const employees = await prisma.orgEmployee.findMany({
      skip,
      take: pageSize,
    });

    // 回傳分頁資訊與資料
    successResponse(res, 200, {
      data: employees,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    });
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 根據帳號代碼取得員工資料
const getEmployeeByAccountGuid = async (req, res) => {
  const { accountGuid } = req.params;
  try {
    const employee = await prisma.orgEmployee.findUnique({
      where: { accountGuid },
    });
    if (!employee) {
      return errorResponse(res, 404, "找不到指定的員工資料");
    }
    successResponse(res, 200, employee);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 根據員工編號取得員工資料
const getEmployeeByEmployeeNo = async (req, res) => {
  const { employeeNo } = req.params;
  try {
    const employee = await prisma.orgEmployee.findUnique({
      where: { employeeNo },
    });
    if (!employee) {
      return errorResponse(res, 404, "找不到指定的員工資料");
    }
    successResponse(res, 200, employee);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 根據部門代碼取得員工資料
const getEmployeesByGroupId = async (req, res) => {
  const { groupId } = req.params;
  try {
    const employees = await prisma.orgEmployee.findMany({
      where: { groupId },
    });
    successResponse(res, 200, employees);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 建立員工資料
const createEmployee = async (req, res) => {
  try {
    const employeeData = req.body;

    // 檢查必要欄位
    if (!employeeData.accountGuid) {
      return errorResponse(res, 400, "帳號代碼為必填欄位");
    }

    const employee = await prisma.orgEmployee.create({
      data: employeeData,
    });

    successResponse(res, 201, employee);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 更新員工資料
const updateEmployee = async (req, res) => {
  const { accountGuid } = req.params;
  const updateData = req.body;

  try {
    const employee = await prisma.orgEmployee.update({
      where: { accountGuid },
      data: updateData,
    });
    successResponse(res, 200, employee);
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 刪除員工資料
const deleteEmployee = async (req, res) => {
  const { accountGuid } = req.params;

  try {
    await prisma.orgEmployee.delete({
      where: { accountGuid },
    });
    successResponse(res, 200, { message: "員工資料已成功刪除" });
  } catch (error) {
    handlePrismaError(error, res);
  }
};

// 搜尋員工資料
const searchEmployees = async (req, res) => {
  const {
    name,
    employeeNo,
    email,
    groupId,
    page = 1,
    pageSize = 25,
    sortBy = "employeeNo", // 預設第一欄
    sortOrder = "descending", // 預設降序
  } = req.query;

  try {
    const whereClause = {};

    if (name || employeeNo) {
      whereClause.OR = [
        name ? { name: { contains: name } } : {},
        employeeNo ? { employeeNo: { contains: employeeNo } } : {},
      ].filter((condition) => Object.keys(condition).length > 0);
    }
    if (email) {
      whereClause.email = { contains: email };
    }
    if (groupId) {
      whereClause.groupId = groupId;
    }

    // 計算總筆數
    const total = await prisma.orgEmployee.count({
      where: whereClause,
    });

    // 分頁查詢
    const employees = await prisma.orgEmployee.findMany({
      where: whereClause,
      skip: (Number(page) - 1) * Number(pageSize),
      take: Number(pageSize),
      orderBy: {
        [sortBy]: sortOrder.replace("ending", "").toLowerCase(),
      },
    });
    logger.debug("employeessss", whereClause, employees.length);
    // prisma.$on("query", (e) => {
    //   console.log(e);
    // });
    successResponse(res, 200, {
      data: employees,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    });
  } catch (error) {
    handlePrismaError(error, res);
  }
};

module.exports = {
  getAllEmployees,
  getEmployeeByAccountGuid,
  getEmployeeByEmployeeNo,
  getEmployeesByGroupId,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  searchEmployees,
};
