const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient({
  log: ["query"],
});
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { logger } = require("../utils/logger");

/**
 * 獲取所有部門列表
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.getAllGroups = async (req, res) => {
  try {
    const groups = await prisma.orgGroup.findMany({
      orderBy: [{ lev: "asc" }, { lft: "asc" }],
    });
    successResponse(res, 200, {
      message: "獲取部門列表成功",
      data: groups,
    });
  } catch (error) {
    logger.error("獲取部門列表失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

/**
 * 獲取部門樹狀結構
 */
exports.getGroupTree = async (req, res) => {
  try {
    const { parentGroupId = "ROOT" } = req.query;
    console.log(parentGroupId);
    // 獲取指定父節點的子節點
    const groups = await prisma.orgGroup.findMany({
      where: {
        parentGroupId,
      },
      orderBy: [{ lev: "asc" }, { lft: "asc" }],
    });

    // 檢查每個節點是否有子節點，並統計人數
    const groupsWithHasChildren = await Promise.all(
      groups.map(async (group) => {
        // 檢查是否有子節點
        const hasChildren = await prisma.orgGroup.findFirst({
          where: { parentGroupId: group.groupId },
        });

        // 統計該部門下的人數
        const employeeCount = await prisma.orgEmployee.count({
          where: { groupId: group.groupId },
        });

        return {
          ...group,
          hasChildren: !!hasChildren,
          children: hasChildren ? [] : null,
          employeeCount, // 新增員工數量欄位
        };
      })
    );

    successResponse(res, 200, {
      message: "獲取部門樹狀結構成功",
      data: groupsWithHasChildren,
    });
  } catch (error) {
    logger.error("獲取部門樹狀結構失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

/**
 * 根據 ID 獲取部門資訊
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.getGroupById = async (req, res) => {
  try {
    const { groupId } = req.params;
    const group = await prisma.orgGroup.findUnique({
      where: { groupId },
    });

    if (!group) {
      return res.status(404).json({
        status: "error",
        message: "部門不存在",
      });
    }

    successResponse(res, 200, {
      message: "獲取部門資訊成功",
      data: group,
    });
  } catch (error) {
    logger.error("獲取部門資訊失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

/**
 * 創建部門
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.createGroup = async (req, res) => {
  try {
    const {
      groupId,
      groupType,
      groupName,
      parentGroupId,
      groupCode,
      active,
      companyId,
    } = req.body;

    // 檢查必要欄位
    if (!groupId || !groupName) {
      return res.status(400).json({
        status: "error",
        message: "部門代碼和名稱為必填欄位",
      });
    }

    // 檢查部門代碼是否已存在
    const existingGroup = await prisma.orgGroup.findUnique({
      where: { groupId },
    });

    if (existingGroup) {
      return res.status(400).json({
        status: "error",
        message: "部門代碼已存在",
      });
    }

    // 如果有父部門，檢查父部門是否存在
    if (parentGroupId) {
      const parentGroup = await prisma.orgGroup.findUnique({
        where: { groupId: parentGroupId },
      });

      if (!parentGroup) {
        return res.status(400).json({
          status: "error",
          message: "父部門不存在",
        });
      }
    }

    // 創建部門
    const newGroup = await prisma.orgGroup.create({
      data: {
        groupId,
        groupType,
        groupName,
        parentGroupId,
        groupCode,
        active: active ?? true,
        companyId,
        lft: 0,
        rgt: 0,
        lev: 0,
      },
    });

    successResponse(res, 201, {
      message: "創建部門成功",
      data: newGroup,
    });
  } catch (error) {
    logger.error("創建部門失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

/**
 * 更新部門
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.updateGroup = async (req, res) => {
  try {
    const { groupId } = req.params;
    const {
      groupType,
      groupName,
      parentGroupId,
      groupCode,
      active,
      companyId,
    } = req.body;

    // 檢查部門是否存在
    const existingGroup = await prisma.orgGroup.findUnique({
      where: { groupId },
    });

    if (!existingGroup) {
      return res.status(404).json({
        status: "error",
        message: "部門不存在",
      });
    }

    // 如果有父部門，檢查父部門是否存在且不能是自己
    if (parentGroupId) {
      if (parentGroupId === groupId) {
        return res.status(400).json({
          status: "error",
          message: "不能將自己設為父部門",
        });
      }

      const parentGroup = await prisma.orgGroup.findUnique({
        where: { groupId: parentGroupId },
      });

      if (!parentGroup) {
        return res.status(400).json({
          status: "error",
          message: "父部門不存在",
        });
      }
    }

    // 更新部門
    const updatedGroup = await prisma.orgGroup.update({
      where: { groupId },
      data: {
        groupType,
        groupName,
        parentGroupId,
        groupCode,
        active,
        companyId,
        updatedAt: new Date(),
      },
    });

    successResponse(res, 200, {
      message: "更新部門成功",
      data: updatedGroup,
    });
  } catch (error) {
    logger.error("更新部門失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

/**
 * 刪除部門
 * @param {Object} req - Express 請求對象
 * @param {Object} res - Express 響應對象
 */
exports.deleteGroup = async (req, res) => {
  try {
    const { groupId } = req.params;

    // 檢查部門是否存在
    const existingGroup = await prisma.orgGroup.findUnique({
      where: { groupId },
    });

    if (!existingGroup) {
      return res.status(404).json({
        status: "error",
        message: "部門不存在",
      });
    }

    // 檢查是否有子部門
    const childGroups = await prisma.orgGroup.findMany({
      where: { parentGroupId: groupId },
    });

    if (childGroups.length > 0) {
      return res.status(400).json({
        status: "error",
        message: "該部門下有子部門，無法刪除",
      });
    }

    // 檢查是否有員工屬於該部門
    const employees = await prisma.orgEmployee.findMany({
      where: { groupId },
    });

    if (employees.length > 0) {
      return res.status(400).json({
        status: "error",
        message: "該部門下有員工，無法刪除",
      });
    }

    // 刪除部門
    await prisma.orgGroup.delete({
      where: { groupId },
    });

    successResponse(res, 200, {
      message: "部門刪除成功",
    });
  } catch (error) {
    logger.error("刪除部門失敗:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};
