const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { ensureDirectoryExists, getFullPath } = require("../utils/file");
const projectDirectoryService = require("../services/projectDirectoryService");
const { logger } = require("../utils/logger");
// 生成專案編號
const generateProjectNumber = (systemCode) => {
  const date = new Date();
  const dateStr = date.toISOString().replace(/[-:]/g, "").slice(0, 8);
  const randomStr = Math.random().toString(36).substring(2, 7).toUpperCase();
  return `${systemCode}_${dateStr}_${randomStr}`;
};

// 創建新專案
exports.createProject = async (req, res) => {
  try {
    const { name, description, status } = req.body;
    const userId = req.user.id;
    const defaultSystemCode = process.env.DEFAULT_SYSTEM_CODE || "IYM";

    // 驗證狀態值
    const validStatuses = ["draft", "active", "completed"];
    if (status && !validStatuses.includes(status)) {
      return errorResponse(res, 400, "無效的狀態值", validStatuses);
    }

    // 生成專案編號
    const projectNumber = generateProjectNumber(defaultSystemCode);

    // 創建專案目錄
    try {
      const projectDirPath =
        projectDirectoryService.ensureProjectDirectory(projectNumber);

      logger.log(`已創建專案目錄: ${projectDirPath}`);
    } catch (dirError) {
      logger.error("創建專案目錄失敗:", dirError);
      // 不影響主流程，僅記錄錯誤
    }

    const project = await prisma.project.create({
      data: {
        name,
        description,
        status: status || "draft",
        systemCode: defaultSystemCode,
        projectNumber: projectNumber,
        createdBy: userId,
        updatedBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 201, {
      message: "專案創建成功",
      project,
    });
  } catch (error) {
    logger.error("創建專案錯誤:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

// 獲取所有專案
exports.getAllProjects = async (req, res) => {
  try {
    const { includeWorkflows, status, limit, offset } = req.query;
    const include = {
      creator: {
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
        },
      },
      updater: {
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
        },
      },
    };

    // 如果需要包含工作流程實例資訊
    if (includeWorkflows === "true") {
      include.flowInstances = {
        select: {
          id: true,
          status: true,
          createdAt: true,
          template: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      };
    }

    const projects = await prisma.project.findMany({
      include,
      where: {
        ...(status ? { status } : { status: { not: "temporary" } }),
      },
      ...(limit ? { take: parseInt(limit) } : {}),
      ...(offset ? { skip: parseInt(offset) } : {}),
      orderBy: {
        updatedAt: "desc",
      },
    });

    successResponse(res, 200, projects);
  } catch (error) {
    logger.debug("獲取專案列表錯誤:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

// 獲取單個專案
exports.getProjectById = async (req, res) => {
  try {
    const { id } = req.params;
    const { includeWorkflows } = req.query;

    const include = {
      creator: {
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
        },
      },
      updater: {
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
        },
      },
    };

    // 如果需要包含工作流程實例資訊
    if (includeWorkflows === "true") {
      include.workflowInstances = {
        include: {
          workflowTemplate: {
            select: {
              id: true,
              templateName: true,
              templateCategory: true,
            },
          },
          creator: {
            select: {
              id: true,
              username: true,
              avatar: true,
            },
          },
          nodeInstances: {
            select: {
              id: true,
              status: true,
              startTime: true,
              endTime: true,
            },
          },
        },
      };
    }

    const project = await prisma.project.findUnique({
      where: { id },
      include,
    });

    if (!project) {
      return errorResponse(res, 404, "專案不存在");
    }

    successResponse(res, 200, project);
  } catch (error) {
    logger.error("獲取專案詳情錯誤:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

// 更新專案
exports.updateProject = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, status } = req.body;
    const userId = req.user.id;

    // 驗證狀態值
    const validStatuses = ["draft", "active", "completed"];
    if (status && !validStatuses.includes(status)) {
      return errorResponse(res, 400, "無效的狀態值", validStatuses);
    }

    const project = await prisma.project.update({
      where: { id },
      data: {
        name,
        description,
        status,
        updatedBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
        updater: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    successResponse(res, 200, {
      message: "專案更新成功",
      project,
    });
  } catch (error) {
    if (error.code === "P2025") {
      return errorResponse(res, 404, "專案不存在");
    }
    logger.error("更新專案錯誤:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

// 刪除專案
exports.deleteProject = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info("================>deleteProject", id);

    // 先檢查專案是否存在
    const project = await prisma.project.findUnique({
      where: { id },
    });

    if (!project) {
      return errorResponse(res, 404, "專案不存在");
    }

    // 單獨檢查是否存在相關的工作流程實例
    const flowInstanceCount = await prisma.flowInstance.count({
      where: {
        projectId: id,
      },
    });

    if (flowInstanceCount > 0) {
      return errorResponse(
        res,
        400,
        "無法刪除專案，請先刪除相關的工作流程實例"
      );
    }

    // 記錄專案目錄路徑，但不刪除目錄
    try {
      const projectDirPath = projectDirectoryService.getProjectDirectoryPath(
        project.projectNumber
      );
      logger.log(`專案刪除，但保留目錄: ${projectDirPath}`);
    } catch (dirError) {
      logger.error("獲取專案目錄路徑失敗:", dirError);
    }

    await prisma.project.delete({
      where: { id },
    });

    res.json({ message: "專案刪除成功" });
  } catch (error) {
    logger.error("刪除專案錯誤:", error);
    errorResponse(res, 500, "伺服器錯誤");
  }
};

exports.getFlowInstanceById = async (req, res) => {
  try {
    const { projectId } = req.params;
    const { simplified = "true" } = req.query;

    // 檢查專案是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return res.status(404).json({ message: "專案不存在" });
    }

    // 獲取專案的工作流程實例
    const instances = await prisma.flowInstance.findMany({
      where: { projectId },
      ...(simplified === "true"
        ? {
            select: {
              id: true,
              status: true,
              updatedAt: true,
              createdAt: true,
              template: {
                select: {
                  id: true,
                  name: true,
                },
              },
              creator: {
                select: {
                  id: true,
                  username: true,
                  avatar: true,
                },
              },
            },
          }
        : {
            include: {
              template: {
                select: {
                  id: true,
                  name: true,
                },
              },
              creator: {
                select: {
                  id: true,
                  username: true,
                  avatar: true,
                },
              },
            },
          }),
      orderBy: {
        createdAt: "desc",
      },
    });

    successResponse(res, 200, instances);
  } catch (error) {
    logger.error("獲取專案工作流程實例失敗:", error);
    errorResponse(res, 500, "獲取專案工作流程實例失敗");
  }
};
