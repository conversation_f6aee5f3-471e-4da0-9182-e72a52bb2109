import { useUserStore } from "../stores/user";

/**
 * 權限控制指令
 * 用法：
 * 1. 單一權限：v-permission="'MANAGE_USERS'"
 * 2. 多個權限（任一權限）：v-permission="['MANAGE_USERS', 'VIEW_USERS']"
 * 3. 多個權限（所有權限）：v-permission.all="['MANAGE_USERS', 'VIEW_USERS']"
 * 4. 角色控制：v-permission.role="'ADMIN'"
 */
export const permission = {
  mounted(el, binding) {
    const userStore = useUserStore();
    const { value, modifiers } = binding;

    // 檢查是否使用角色模式
    if (modifiers.role) {
      if (!userStore.hasRole(value)) {
        el.parentNode?.removeChild(el);
      }
      return;
    }

    // 檢查是否需要所有權限
    if (modifiers.all) {
      if (!userStore.hasAllPermissions(value)) {
        el.parentNode?.removeChild(el);
      }
      return;
    }

    // 默認檢查是否有任一權限
    if (!userStore.hasAnyPermission(value)) {
      el.parentNode?.removeChild(el);
    }
  },
};

/**
 * 管理員控制指令
 * 用法：v-admin
 * 只有 ADMIN 或 SUPERADMIN 角色的用戶才能看到元素
 */
export const admin = {
  mounted(el, binding) {
    const userStore = useUserStore();

    if (!userStore.isAdmin) {
      el.parentNode?.removeChild(el);
    }
  },
};
