# 節點執行模式設計規劃

## 概述

本文件詳細描述了 SFDA IYM 前端專案中節點執行模式的設計規劃，特別是互動式節點的實作方式。基於對現有程式碼的分析，本文件反映實際的架構現況並提出相應的擴充方案。

## 執行模式類型

### 1. 執行模式常數定義

系統定義四種執行模式，放置於 src/constants/executionModes.js 中：
- AUTO (自動執行)
- MANUAL (手動確認) 
- INTERACTIVE (互動式)
- CONDITIONAL (條件式)

### 2. 各模式特性

**AUTO - 自動執行**
- 說明：節點自動執行，無需用戶介入
- 用戶介入：無
- 執行流程：直接執行完成

**MANUAL - 手動確認**  
- 說明：需要用戶手動點擊執行
- 用戶介入：點擊執行按鈕
- 執行流程：需要用戶手動觸發

**INTERACTIVE - 互動式**
- 說明：需要用戶輸入或選擇
- 用戶介入：選擇、輸入、配置等操作
- 執行流程：可多次中斷等待用戶操作

**CONDITIONAL - 條件式**
- 說明：根據前置節點結果決定執行方式
- 用戶介入：依條件而定
- 執行流程：動態決定執行模式

## 互動式節點執行流程

### 核心執行邏輯

執行流程採用可重複中斷的設計：

1. **開始執行** - 節點開始處理
2. **獲取前置數據** - 從前置節點或全域變數獲取必要資料
3. **準備互動環境** - 獲取選項清單、初始化互動元件等
4. **檢查互動需求** - 評估當前是否需要用戶操作
5. **條件分支**：
   - **需要互動** → 中斷執行，等待用戶操作
   - **無需互動** → 跳至步驟8
6. **用戶操作** - 用戶完成必要的選擇或輸入
7. **點擊繼續執行** - 用戶確認操作後觸發繼續
8. **重新檢查互動需求** - 驗證所有互動需求是否已滿足
9. **條件分支**：
   - **仍需互動** → 回到步驟5
   - **已滿足** → 繼續下一步
10. **執行後續分析功能** - 圖表生成、計算處理等
11. **完成節點執行** - 返回最終結果

### 關鍵特性

**可重複中斷**
節點可以多次中斷等待用戶操作，每次繼續執行時都會重新評估互動需求。

**狀態持久化**
每次中斷時保存當前狀態，確保用戶操作不會遺失。

**動態檢查**
每次繼續執行時重新評估互動需求，支援複雜的多步驟互動流程。

**依賴處理** 
支援多個互動元件間的依賴關係，如上方選擇影響下方選項的情況。

## 實作架構

### 1. 節點元件結構

節點元件使用 BaseNode 作為基礎，設定執行模式為 INTERACTIVE。所有內容（包括說明文字、互動元件、結果顯示等）統一放置在 BaseNode 的預設 slot 中，不需要進行內容分離。

**執行事件綁定方式**：
handleRun 調用方式：
- 透過 nodeRef：`@run="nodeRef.handleRun(processFunction)"`（使用 BaseNode 的標準執行邏輯）

### 2. 核心處理函數

所有節點都有標準的 processFunction 作為主要處理邏輯。在互動式節點中，此函數負責：

- 資料準備階段：獲取前置資料、初始化必要數據
- 互動需求檢查：評估當前狀態是否需要用戶介入
- 中斷邏輯：當需要互動時返回特定狀態值觸發中斷
- 執行後續功能：當互動完成後執行分析、計算等功能
- 返回最終結果：建構完整的節點輸出

### 3. 互動需求檢查機制

透過專門的檢查函數評估各種互動需求：

- 選擇需求檢查：如是否選擇了必要的變數
- 參數需求檢查：如是否設定了必要的分析參數  
- 驗證需求檢查：如資料格式是否正確
- 依賴需求檢查：如前置條件是否滿足

每種檢查返回是否需要互動以及具體原因，系統根據第一個未滿足的需求進行中斷。

## BaseNode 互動支援需求

### 預期的 BaseNode 功能

BaseNode 需要支援以下互動相關功能以完整實作互動式節點：

**執行模式識別**
BaseNode 需要根據 execution-mode 屬性調整行為模式，在互動模式下啟用相應的互動邏輯。

**中斷狀態處理**  
當 processFunction 返回包含 requiresInteraction 標記的結果時，BaseNode 需要：
- 設定節點狀態為 PENDING
- 啟用繼續執行按鈕
- 暫停工作流程執行（不觸發後續節點）

**繼續執行按鈕管理**
- 在執行模式為 INTERACTIVE 且節點狀態為 PENDING 時顯示繼續執行按鈕
- 當節點狀態為 COMPLETED 時隱藏繼續執行按鈕
- 用戶點擊後重新調用 processFunction

**內容顯示控制**
BaseNode 不需要特別管理內容區域的分離，節點元件在預設 slot 中自由組織所有內容，包括說明、互動元件和結果顯示。

**注意**：實際的節點狀態判斷和管理主要在以下位置進行：
- useNodeExecution.js - 節點執行邏輯的核心管理
- useWorkflowManager.js - 工作流程管理
- flowStore.js - 流程狀態管理
- useFlowInstance.js - 流程實例管理

BaseNode 主要負責狀態的展示和用戶互動介面，而非狀態的決策和管理。

## 狀態管理

### 節點狀態定義

節點狀態定義（位於 src/constants/nodeStates.js）：

- IDLE: "idle" - 閒置狀態
- RUNNING: "running" - 執行中
- COMPLETED: "completed" - 已完成
- ERROR: "error" - 錯誤狀態
- PAUSED: "paused" - 暫停狀態
- PENDING: "pending" - 等待狀態
- DEFAULT: "default" - 預設狀態
- FAILED: "failed" - 失敗狀態

### 狀態流轉

互動式節點的狀態流轉：
IDLE → RUNNING → PENDING (等待互動) → RUNNING → COMPLETED

在出現錯誤時可從任何狀態轉為 ERROR，並可能回到 IDLE 重新開始。

### 狀態管理架構

節點狀態的判斷和管理主要在以下 composables 和 stores 中進行：
- **useNodeExecution.js** - 單個節點執行的生命週期管理
- **useWorkflowManager.js** - 工作流的流轉邏輯和節點依賴關係管理
- **flowStore.js** - 流程狀態的全域管理
- **useFlowInstance.js** - 流程實例的上下文管理

#### 雙層協作設計

系統採用**雙層協作架構**確保執行邏輯與 UI 狀態的有效同步：

**執行邏輯層（useNodeExecution）**
- 負責執行邏輯決策（何時需要互動、何時完成）
- 管理流程狀態轉換（RUNNING → PENDING → COMPLETED）
- 處理錯誤和重試邏輯
- 與後端 API 通信

**UI 狀態層（BaseNode）**
- 負責 UI 狀態的響應式管理
- 處理模板渲染和用戶交互
- 管理本地狀態與全局狀態的同步
- 控制視覺效果和動畫

#### 組件通信機制

useNodeExecution 通過 nodeRef 調用 BaseNode 的方法來更新狀態，BaseNode 接收到狀態更新後會：
1. 更新本地響應式狀態（立即觸發 UI 更新）
2. 同步到 flowStore（持久化存儲）
3. 發送事件通知其他組件

#### 設計原則

**職責分離清晰**：邏輯層專注執行決策，UI 層專注狀態響應  
**響應式系統配合**：充分利用 Vue 的響應式機制進行 UI 更新  
**組件封裝原則**：通過明確的接口進行跨組件通信  
**可測試性**：各層可獨立測試，便於單元測試和集成測試

#### 狀態同步保證

系統提供雙向同步機制：
- **向下同步**：useNodeExecution → BaseNode → flowStore
- **向上同步**：flowStore → BaseNode（通過 watch 監聽狀態變化）

這種設計確保狀態的一致性，並支援複雜的互動場景下的狀態管理。

## 流程執行邏輯調整需求

### 1. useNodeExecution.js 調整

**互動結果處理**
- 需要檢查 processFunction 返回結果是否包含 `requiresInteraction: true`
- 當需要互動時，設定節點狀態為 PENDING 而非 COMPLETED
- 保存當前執行狀態，支援後續繼續執行

**狀態管理優化**
- 確保 PENDING 狀態下節點不會被標記為已完成
- 支援從 PENDING 狀態重新進入 RUNNING 狀態
- 處理多次中斷和繼續的狀態循環

### 2. useWorkflowManager.js 調整

**依賴檢查邏輯**
- 修改節點完成判斷邏輯，PENDING 狀態的節點不應被視為已完成
- 確保處於 PENDING 狀態的節點不會觸發後續節點執行
- 當節點從 PENDING 轉為 COMPLETED 時，重新檢查並觸發後續節點

**執行流程控制**
- 支援暫停工作流程執行（當節點進入 PENDING 狀態時）
- 支援恢復工作流程執行（當節點從 PENDING 轉為 COMPLETED 時）

### 3. flowStore.js 調整

**狀態追蹤**
- 擴充狀態追蹤以支援 PENDING 狀態
- 確保 PENDING 狀態的節點在 UI 中正確顯示
- 支援 PENDING 狀態與其他狀態間的正確流轉

### 4. useFlowInstance.js 調整

**執行結果處理**
- 處理 processFunction 返回的 `requiresInteraction` 標記
- 當檢測到需要互動時，避免將節點標記為完成
- 保存互動上下文，支援後續繼續執行

**錯誤處理優化**
- 確保在互動過程中的錯誤能正確處理和恢復
- 支援互動狀態下的重試機制

## 實際應用範例

### SelectDiscreteVariablesProcess 節點

這是一個典型的互動式節點範例：

**互動需求**
用戶必須選擇至少一個離散變數才能繼續分析。

**執行流程**
1. 首次執行時獲取可用變數選項，並自動選擇預設值
2. 檢查是否有選擇變數，如果沒有則中斷等待用戶選擇
3. 用戶選擇變數後點擊繼續執行按鈕
4. 重新檢查選擇是否滿足需求
5. 如果滿足則保存選擇結果並執行後續分析
6. 如果仍不滿足則再次中斷等待

### 複雜互動節點範例

**多步驟互動節點**
某些節點可能需要多個步驟的互動：

步驟1：選擇分析變數
步驟2：設定分析參數  
步驟3：確認資料正確性

每個步驟都可能觸發中斷，系統會根據當前進度提示用戶需要完成的操作。

**條件式互動節點**
根據資料特性動態決定是否需要互動，如：
- 資料量大時需要用戶確認處理方式
- 資料品質差時需要用戶選擇清理策略
- 發現異常值時需要用戶決定處理方法

## 未來擴充方向

**條件式互動**
根據資料特性、前置節點結果等條件動態決定互動需求。

**互動歷史**
記錄用戶的操作歷史，支援操作復原和重做功能。

**批次互動**
支援多個節點的批次互動設定，提高工作流程設定效率。

**互動範本**
建立常用互動模式的範本，加速節點開發和配置。

**智慧建議**
根據歷史操作和資料特性提供智慧操作建議。

## 注意事項

**架構相容性**
所有設計必須與現有的 BaseNode 架構完全相容，不能破壞既有的節點功能。

**效能考量**
避免頻繁的狀態檢查和資料重新載入，確保互動過程的流暢性。

**用戶體驗**
確保互動流程直觀易用，提供清楚的操作指引和即時回饋。

**錯誤處理**
建立完善的錯誤處理和恢復機制，確保互動過程中的異常不會影響整體系統。

**測試策略**
建立完整的測試案例覆蓋各種互動情境和邊界條件。
