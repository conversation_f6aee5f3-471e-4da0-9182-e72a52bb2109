# 前端流程系統說明文檔

## 系統概述

本文檔描述了前端流程系統的架構和主要組件。該系統是一個基於 Vue 3 的工作流程設計和執行平台，允許用戶創建、編輯和執行各種業務流程。系統採用模塊化設計，包含節點定義、流程模板、流程實例和執行引擎等核心組件。

## 系統架構

```
┌─────────────────────────────────────────────────────────────────┐
│                        前端流程系統架構                           │
└─────────────────────────────────────────────────────────────────┘
                               │
           ┌──────────────────┼──────────────────┐
           ▼                   ▼                  ▼
┌─────────────────┐   ┌─────────────────┐  ┌─────────────────┐
│    視圖層 (Views) │   │  組件層 (Components) │  │  存儲層 (Stores)  │
└─────────────────┘   └─────────────────┘  └─────────────────┘
           │                   │                  │
           │                   │                  │
           ▼                   ▼                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                     可組合函數層 (Composables)                    │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                         API 層 (API Layer)                       │
└─────────────────────────────────────────────────────────────────┘
```

## 核心組件說明

### 1. 流程節點 (Flow Nodes)

位置：`components/flow-nodes/`

流程節點是工作流的基本構建單元，分為兩類：

- **基礎節點 (Base Nodes)**：提供通用功能，如 HTTP 請求節點
- **業務節點 (Business Nodes)**：提供特定業務邏輯，如投訴選擇器、缺陷分析等

節點註冊機制通過 `index.js` 統一管理，使用 Vue Flow 的 `addNode` 方法將節點組件註冊到系統中。

### 2. 流程視圖 (Flow Views)

位置：`views/flow/`

流程視圖層包含三個主要部分：

- **實例視圖 (Instance)**：用於查看和操作流程實例
- **節點定義視圖 (Node Definitions)**：用於管理節點類型和配置
- **模板視圖 (Templates)**：用於創建和編輯流程模板

### 3. 流程存儲 (Flow Stores)

位置：`stores/`

使用 Pinia 進行狀態管理，主要包含：

- **flowStore.js**：管理流程實例的狀態和操作，包括創建、更新、啟動流程實例，以及節點執行和日誌記錄。系統設計要求每個節點必須執行完成，不支持暫停或停止功能
- **flowTemplate.js**：管理流程模板的狀態，主要用於設置模板名稱和麵包屑顯示
- **node.js**：管理節點類型和分類的狀態

### 4. 可組合函數 (Composables)

位置：`composables/` 和 `composables/flow/`

提供可重用的邏輯和功能：

- **useFlowInstance.js**：提供流程實例操作，包括創建臨時實例、執行節點、管理上下文等
- **useWorkflowManager.js**：負責工作流的流轉邏輯，包括節點執行、狀態管理和數據傳遞
- **flow/useFlowNodes.js**：管理節點定義、拖放操作和節點配置
- **flow/useFlowCanvas.js**：管理流程設計畫布，包括元素操作、歷史記錄、快捷鍵等
- **flow/useFlowEdges.js**：管理節點之間的連接關係
- **flow/useFlowLayout.js**：處理流程圖的自動佈局
- **flow/useFlowTemplate.js**：處理流程模板的操作
- **flow/useFileNode.js**：處理文件節點的特殊邏輯

## 工作流程生命週期

系統設計遵循「每個節點必須執行完成」的原則，不支持暫停或停止操作。工作流一旦啟動，每個節點將按順序依次執行，直到所有節點都完成為止。

```
┌─────────────────┐
│  創建流程模板    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  創建流程實例    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│    啟動流程      │ ← 啟動後流程必須執行到完成，不支持暫停或停止
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│    執行節點      │◄─────┐
└────────┬────────┘      │
         │               │
         ▼               │
┌─────────────────┐      │
│  節點執行完成?   │──否──┘
└────────┬────────┘
         │ 是
         ▼
┌─────────────────┐
│  有下一個節點?   │──是──┐
└────────┬────────┘      │
         │ 否            │
         ▼               │
┌─────────────────┐      │
│    流程完成      │      │
└─────────────────┘      │
                         │
                         ▼
                  ┌─────────────────┐
                  │  執行下一個節點  │
                  └─────────────────┘
```

## 數據流

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    節點 A       │────▶│    節點 B       │────▶│    節點 C       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                       共享數據 (Shared Data)                     │
└─────────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                     全局變量 (Global Variables)                  │
└─────────────────────────────────────────────────────────────────┘
```

## 節點狀態流轉

```
┌─────────────┐
│   初始化    │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│   準備就緒   │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│   執行中     │
└──────┬──────┘
       │
       ▼
┌─────────────────────┐
│      執行結果?       │
└─────────┬───────────┘
          │
  ┌───────┴───────┐
  ▼               ▼
┌─────────┐   ┌─────────┐
│  成功   │   │  失敗   │
└─────────┘   └─────────┘
```

## 主要功能

1. **流程設計**：通過拖放方式設計流程，連接節點，配置節點參數
2. **流程執行**：執行整個流程或單個節點，查看執行結果和日誌。遵循「每個節點必須執行完成」的原則，不支持中斷執行
3. **流程管理**：創建、編輯、刪除流程模板和實例
4. **數據傳遞**：節點間數據傳遞，共享數據和全局變量管理
5. **狀態管理**：節點和流程狀態的管理和監控
6. **歷史記錄**：流程執行歷史和操作歷史的記錄和查看

## 技術棧

- **前端框架**：Vue 3
- **狀態管理**：Pinia
- **UI 組件**：Element Plus
- **流程圖**：Vue Flow
- **工具庫**：VueUse

## 擴展節點

要添加新的節點類型，需要：

1. 在 `components/flow-nodes/business/` 或 `components/flow-nodes/base/` 創建新的節點組件
2. 在 `components/flow-nodes/index.js` 中註冊節點
3. 在後端 API 中添加節點定義
4. 實現節點的執行邏輯

## 最佳實踐

1. 使用可組合函數 (Composables) 分離業務邏輯和 UI 邏輯
2. 使用 Pinia 存儲管理全局狀態
3. 使用 API 模塊化管理後端請求
4. 使用 Vue Flow 進行流程圖的渲染和交互
5. 使用 Element Plus 構建一致的 UI 界面
