# 前置節點數據處理指南

## 概述

為了簡化節點中獲取前置節點數據的操作，我們在工作流框架中提供了統一的前置節點數據處理方法。每個節點現在都可以方便地獲取前置節點的輸出數據，避免重複的數據處理邏輯。

## 主要功能

### 1. useWorkflowManager 中的新方法

#### `getPreviousNodesOutputData(nodeId)`
- **功能**: 獲取並合併前置節點的輸出數據
- **參數**: `nodeId` - 當前節點ID
- **返回**: 合併後的前置節點輸出數據對象
- **合併邏輯**:
  - 陣列數據：合併並去重
  - 物件數據：深度合併
  - 衝突處理：記錄警告並以新值覆蓋舊值

#### `getSinglePreviousNodeOutput(nodeId)`
- **功能**: 獲取單個前置節點的輸出數據（適用於只有一個前置節點的情況）
- **參數**: `nodeId` - 當前節點ID
- **返回**: 第一個前置節點的輸出數據

#### `getPreviousNodesDetails(nodeId)`
- **功能**: 獲取前置節點的詳細信息映射
- **參數**: `nodeId` - 當前節點ID
- **返回**: `{ nodeId: outputData }` 格式的映射對象

### 2. useNodeExecution 中的便利方法

#### `extractPreviousNodeData(inputData)`
- **功能**: 從 inputData 中提取前置節點數據的便利方法
- **參數**: `inputData` - 節點執行的輸入數據
- **返回**: 包含前置節點信息的結構化對象

返回對象結構：
```javascript
{
  // 單個前置節點信息（當只有一個前置節點時）
  previousNodeId: string | null,
  previousNodeOutput: object,
  
  // 合併後的所有前置節點數據
  mergedPreviousOutputs: object,
  
  // 前置節點詳細映射 { nodeId: outputData }
  previousNodesDetails: object,
  
  // 全局變量
  globalVariables: object,
  
  // 原始的前置節點輸出映射
  previousOutputs: object,
}
```

## 使用方式

### 方式一：直接從 inputData 獲取（推薦）

在節點的 `processFunction` 中：

```javascript
const processFunction = async (inputData) => {
  // 使用便利方法提取前置節點數據
  const {
    previousNodeId,
    previousNodeOutput,
    mergedPreviousOutputs,
    previousNodesDetails,
    globalVariables,
  } = extractPreviousNodeData(inputData);

  // 現在可以直接使用這些數據
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  
  // 您的業務邏輯...
};
```

### 方式二：使用 useWorkflowManager 方法

```javascript
const processFunction = async (inputData) => {
  const { getPreviousNodesOutputData, getPreviousNodesDetails } = useWorkflowManager();
  
  // 獲取合併後的前置節點輸出數據
  const mergedOutputs = getPreviousNodesOutputData(props.id);
  
  // 獲取詳細的節點映射信息
  const nodesDetails = getPreviousNodesDetails(props.id);
  
  // 您的業務邏輯...
};
```

## 自動增強的 inputData

工作流管理器會自動為每個節點的 `inputData` 添加以下信息：

- `sourceNodeId`: 前一個節點的ID（單前置節點情況）
- `sourceNodeOutput`: 前一個節點的輸出（單前置節點情況）
- `mergedPreviousOutputs`: 合併後的所有前置節點數據
- `previousNodesDetails`: 前置節點詳細映射
- `globalVariables`: 全局變量
- `previousOutputs`: 原始的前置節點輸出映射

## 數據合併邏輯

當多個前置節點有相同的輸出欄位時，系統會自動進行智能合併：

1. **陣列數據**: 合併並去重
   ```javascript
   // 前置節點A輸出: { items: ["a", "b"] }
   // 前置節點B輸出: { items: ["b", "c"] }
   // 合併結果: { items: ["a", "b", "c"] }
   ```

2. **物件數據**: 深度合併
   ```javascript
   // 前置節點A輸出: { config: { x: 1, y: 2 } }
   // 前置節點B輸出: { config: { y: 3, z: 4 } }
   // 合併結果: { config: { x: 1, y: 3, z: 4 } }
   ```

3. **其他類型**: 以新值覆蓋，並記錄警告

## 最佳實踐

1. **優先使用 extractPreviousNodeData**: 這是最簡潔的方式，推薦在所有節點中使用

2. **檢查數據有效性**: 始終檢查從前置節點獲取的數據是否有效
   ```javascript
   if (!part_no || part_no.length < 1) {
     throw new Error("缺少必要參數「品目」");
   }
   ```

3. **使用備用數據源**: 優先使用前置節點數據，然後使用全局變量作為備用
   ```javascript
   const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
   ```

4. **記錄調試信息**: 在開發階段，記錄前置節點數據有助於調試
   ```javascript
   printLog("前置節點數據:", mergedPreviousOutputs);
   ```

## 注意事項

- 這些方法只能在節點的 `processFunction` 中使用
- 如果節點沒有前置節點，相關數據會是空對象或 null
- 數據合併是自動進行的，無需手動處理
- 系統會自動處理數據衝突並記錄警告

## 遷移指南

如果您有現有的節點需要更新，可以按以下步驟進行：

1. 在 `nodeExecution` 解構中添加 `extractPreviousNodeData`
2. 在 `processFunction` 開始時使用 `extractPreviousNodeData(inputData)` 
3. 移除手動的前置節點數據處理邏輯
4. 使用提取出的 `mergedPreviousOutputs` 和 `globalVariables`

這樣可以讓您的節點代碼更簡潔、更一致，並減少錯誤的可能性。
