# 統一前置節點數據處理指南

## 概述

我們已經將前置節點數據的處理邏輯統一到 `useWorkflowManager` 中，提供了通用的方法來處理所有前置節點的數據合併，無論前置節點有多少個。

## 主要改進

### 1. 移除單個前置節點的特殊處理
- 不再針對單個前置節點提供特別的 `sourceNodeId` 和 `sourceNodeOutput` 
- 統一使用合併邏輯處理所有前置節點，確保一致性

### 2. 增強的錯誤處理
- 當前置節點數據存在不可合併的衝突時，會拋出詳細的錯誤信息
- 明確指出衝突的欄位、值和來源節點

### 3. 統一的數據提取方法
- 在 `useWorkflowManager` 中提供 `extractPreviousNodeData(nodeId)` 方法
- 在 `useNodeExecution` 中的 `extractPreviousNodeData(inputData)` 會自動使用工作流管理器的方法

## 使用方式

### 在節點的 processFunction 中

```javascript
const processFunction = async (inputData) => {
  // 使用統一的方法提取前置節點數據
  // 注意：extractPreviousNodeData 會直接從工作流狀態中獲取數據，
  // 而不是從 inputData 參數中獲取
  const {
    mergedPreviousOutputs,
    previousNodesDetails, 
    globalVariables,
    previousNodeIds,
  } = extractPreviousNodeData(inputData);

  // 直接從 mergedPreviousOutputs 獲取需要的數據
  const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
  const work_order = mergedPreviousOutputs.lot || globalVariables?.lot || [];
  
  // 驗證必要參數
  if (!part_no || part_no.length < 1) {
    throw new Error(`缺少必要參數「品目」!`);
  }
  if (!work_order || work_order.length < 1) {
    throw new Error(`缺少必要參數「工單」!`);
  }
  
  // 您的業務邏輯...
};
```

### 重要說明

- `extractPreviousNodeData(inputData)` 函數會**忽略** `inputData` 參數
- 它會直接從工作流狀態中獲取前置節點數據並進行合併
- `inputData` 參數保留是為了保持 API 一致性，但實際不會使用其中的數據

## 返回的數據結構

```javascript
{
  // 合併後的所有前置節點數據 - 主要使用這個
  mergedPreviousOutputs: {
    partNo: "ZE18113-A",
    lot: ["Z05300156", "Z05100063"],
    // ... 其他合併後的欄位
  },
  
  // 前置節點詳細映射 { nodeId: outputData }
  previousNodesDetails: {
    "node-1": { partNo: "ZE18113-A", ... },
    "node-2": { lot: ["Z05300156"], ... }
  },
  
  // 全局變量
  globalVariables: {
    partNo: "ZE18113-A",
    // ... 其他全局變量
  },
  
  // 前置節點ID列表
  previousNodeIds: ["node-1", "node-2"],
  
  // 原始的前置節點輸出映射
  previousOutputs: {
    "node-1": { partNo: "ZE18113-A", ... },
    "node-2": { lot: ["Z05300156"], ... }
  }
}
```

## 數據合併規則

1. **陣列**: 合併去重 `[...new Set([...array1, ...array2])]`
2. **物件**: 深度合併 `{ ...obj1, ...obj2 }`
3. **衝突處理**: 當相同欄位的值不同且無法合併時，拋出錯誤

## 錯誤處理

當前置節點數據存在衝突時，系統會拋出詳細的錯誤：

```
前置節點數據合併失敗：欄位 "partNo" 存在不可合併的衝突
現有值: "ZE18113-A"
新值: "ZE18113-B" (來自節點 node-2)
請檢查前置節點的輸出數據格式是否一致
```

## 遷移指南

如果您有現有的節點需要更新：

1. 移除對 `previousNodeId` 和 `previousNodeOutput` 的使用
2. 統一使用 `mergedPreviousOutputs` 獲取前置節點數據
3. 確保您的節點能正確處理數據合併衝突的錯誤

### 遷移前

```javascript
const {
  previousNodeId,
  previousNodeOutput,
  mergedPreviousOutputs,
  globalVariables,
} = extractPreviousNodeData(inputData);

// 需要處理單個前置節點的特殊邏輯
const part_no = previousNodeOutput.partNo || mergedPreviousOutputs.partNo || globalVariables?.partNo;
```

### 遷移後

```javascript
const {
  mergedPreviousOutputs,
  globalVariables,
  previousNodeIds,
} = extractPreviousNodeData(inputData);

// 統一使用合併後的數據
const part_no = mergedPreviousOutputs.partNo || globalVariables?.partNo || "";
```

## 節點遷移檢查清單

如果您發現其他節點仍在使用舊的處理模式，請按照以下步驟進行遷移：

### 1. 檢查模板中的事件綁定
舊的方式：
```vue
@run="handleRun"
```

新的方式：
```vue
@run="nodeRef.handleRun(processFunction)"
```

### 2. 移除不必要的 handleRun 包裝方法
刪除：
```javascript
const handleRun = async () => {
  nodeRef.value.handleRun(processFunction);
};
```

### 3. 清理 defineExpose
舊的方式：
```javascript
defineExpose({
  handleRun,
  // processFunction,
});
```

新的方式：
```javascript
// 暴露方法給父元件（目前不需要暴露任何方法）
defineExpose({});
```

### 4. 確保使用統一的前置節點數據處理
在 `processFunction` 中使用：
```javascript
const previousData = extractPreviousNodeData(inputData);
```

### 5. 驗證錯誤處理
確保對於數據衝突有適當的錯誤處理機制。

---

**注意**: 這個統一方法已經在 `BasicSPIInput.vue` 和 `SelectChiSquareDiscreteVariablesProcess.vue` 中實現並驗證。其他節點可以參考這兩個文件進行遷移。
