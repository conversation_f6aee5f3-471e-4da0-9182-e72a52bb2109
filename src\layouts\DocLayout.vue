<template>
  <div class="doc-layout">
    <!-- 頂部導航欄 -->
    <header class="doc-header">
      <div
        class="flex items-center justify-between px-4 h-12 border-b dark:border-gray-700">
        <div class="flex items-center space-x-4">
          <router-link
            to="/"
            class="flex items-center space-x-2">
            <img
              src="/flexium_logo.png"
              alt="Logo"
              class="h-6" />
            <h1 class="text-lg font-medium text-light-mode dark:text-dark-mode">
              IYM 開發者文檔 (Just for FUN)
            </h1>
          </router-link>
        </div>
        <div class="flex items-center space-x-4">
          <el-switch
            v-model="isDark"
            class="ml-2"
            inline-prompt
            :active-icon="Moon"
            :inactive-icon="Sun"
            @change="handleThemeChange" />
        </div>
      </div>
    </header>

    <!-- 主要內容區 -->
    <div class="doc-content">
      <!-- 左側導航欄 -->
      <DocSidebar class="doc-sidebar" />

      <!-- 右側內容區 -->
      <main class="doc-main">
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component
              :is="Component"
              :key="$route.fullPath" />
          </keep-alive>
        </router-view>
      </main>
      <el-backtop
        target=".doc-main"
        :visibility-height="400"
        :right="100"
        :bottom="100" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, provide, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Search, Github, Moon, Sun } from "lucide-vue-next";
import DocSidebar from "@/views/docs/components/DocSidebar.vue";
import { useThemeMode } from "@/composables/useThemeMode";

const route = useRoute();
const router = useRouter();
const { isDark, toggleTheme } = useThemeMode();

// 提供 inDocLayout 狀態給子組件
//provide("inDocLayout", true);

// 監視路由變化，自動捲動到頂部
watch(
  () => route.fullPath,
  () => {
    console.log("DocLayout: 路由變化");
    setTimeout(() => {
      const mainContent = document.querySelector(".doc-main");
      if (mainContent) {
        console.log("DocLayout: 捲動到頂部");
        mainContent.scrollTop = 0;
      }
    }, 0);
  }
);

// 處理主題切換
const handleThemeChange = () => {
  toggleTheme();
};
</script>

<style lang="scss" scoped>
.doc-layout {
  @apply h-screen flex flex-col bg-light-mode dark:bg-dark-mode;

  .doc-header {
    @apply bg-light-mode dark:bg-dark-mode border-b dark:border-gray-700;
  }

  .doc-content {
    @apply flex flex-1 overflow-hidden;

    .doc-sidebar {
      @apply w-48 border-r dark:border-gray-700 overflow-y-auto;
    }

    .doc-main {
      @apply flex-1 overflow-y-auto p-6;

      &.doc-main-full {
        @apply w-full;
      }
    }
  }
}
</style>
