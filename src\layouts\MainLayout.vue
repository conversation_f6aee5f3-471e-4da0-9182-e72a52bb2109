<template>
  <div class="h-screen flex flex-col overflow-hidden">
    <!-- 頂部導航欄 -->
    <AppHeader />

    <!-- 主要內容區 - 考慮頂部導航欄的高度 !TODO: 需要固定? -->
    <div class="flex flex-1 pt-12 overflow-hidden">
      <!-- 左側導航欄 -->
      <AppSidebar ref="sidebarRef" />

      <!-- 右側內容區 - 考慮左側導航欄的寬度 -->
      <el-container
        class="content-container overflow-hidden"
        :class="sidebarCollapsed ? 'collapsed' : ''">
        <el-header
          v-show="showContentHeader"
          class="content-header bg-light-mode dark:bg-dark-mode border-b border-light-mode dark:border-dark-mode !h-10 flex items-center justify-between px-6"
          :class="{ collapsed: sidebarCollapsed }">
          <div class="flex items-center">
            <!-- <h2 class="text-lg font-medium">{{ pageTitle }}</h2> -->
            <!-- 麵包屑 -->
            <el-breadcrumb
              v-if="breadcrumbs.length > 0"
              class="ml-0"
              :separator-icon="ArrowRight">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path">
                <h3
                  class="text-sm font-medium text-gray-700 dark:text-dark-mode">
                  {{ item.title }}
                </h3>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div
            id="header-actions"
            class="flex items-center space-x-2"></div>
        </el-header>

        <el-main
          id="main-content"
          class="!p-0 bg-light-mode dark:bg-dark-mode main-content"
          :class="{ 'has-header': showContentHeader }">
          <router-view v-slot="{ Component }">
            <keep-alive :include="cachedViews">
              <component
                :is="Component"
                :key="$route.fullPath" />
            </keep-alive>
          </router-view>
        </el-main>
      </el-container>
    </div>

    <!-- 網路狀態提示 -->
    <!-- <NetworkStatus /> -->
  </div>
</template>

<script setup>
import AppSidebar from "@/components/AppSidebar.vue";
import AppHeader from "@/components/AppHeader.vue";
import NetworkStatus from "@/components/NetworkStatus.vue";
import { ArrowRight } from "@element-plus/icons-vue";

import { useFlowTemplateStore } from "@/stores/flowTemplate";
import { useFlowStore } from "@/stores/flowStore";
import { useProjectStore } from "@/stores/project";
const route = useRoute();
const router = useRouter();
const sidebarRef = ref(null);
const flowTemplateStore = useFlowTemplateStore();
const flowStore = useFlowStore();
const projectStore = useProjectStore();
const sidebarCollapsed = computed(() => sidebarRef.value?.isCollapse || false);

// 根據路由 meta 決定是否顯示內容區的 header
const showContentHeader = computed(() => {
  return route.meta.showContentHeader === true;
});

// 根據當前路由設置頁面標題
const pageTitle = computed(() => {
  return route.meta.title || "";
});

// 需要被緩存的視圖
const cachedViews = computed(() => {
  return router
    .getRoutes()
    .filter((route) => route.meta?.keepAlive)
    .map((route) => route.name);
});

// 生成麵包屑
const breadcrumbs = computed(() => {
  const matched = route.matched;
  const result = [];

  matched.forEach((route) => {
    // 處理工作流程範本設計頁面
    if (route.name === "FlowTemplateDesign") {
      result.push({
        path: "/flow-templates",
        title: "工作流程範本",
      });
      result.push({
        //path: route.path,
        title: flowTemplateStore.templateName + " 設計" || "工作流程範本設計",
      });
    }
    // 處理工作流程實例詳情頁面
    else if (route.name === "FlowInstanceDetail") {
      // 使用新的麵包屑路徑管理
      const breadcrumbPath = flowStore.getBreadcrumbPath;

      if (breadcrumbPath && breadcrumbPath.length > 0) {
        // 使用 flowStore 中設置的麵包屑路徑
        breadcrumbPath.forEach((item) => {
          result.push({
            path: item.path,
            title: item.name,
          });
        });
      } else {
        // 如果沒有設置麵包屑路徑，則使用默認的麵包屑
        // 檢查是否從專案詳情頁進入
        if (flowStore.fromProject) {
          // 從專案詳情頁進入，顯示 專案管理 -> 專案名稱 -> 流程實例名稱
          result.push({
            path: "/projects",
            title: "專案管理",
          });
          result.push({
            path: `/projects/${flowStore.projectId}`,
            title: flowStore.breadcrumbInstance?.project?.name || "專案詳情",
          });
          result.push({
            title: flowStore.breadcrumbInstance?.template?.name || "流程實例",
          });
        } else {
          // 從流程實例管理頁進入，顯示 流程實例管理 -> 流程實例名稱
          result.push({
            path: "/flow-instances",
            title: "工作流程實例",
          });
          result.push({
            title:
              flowStore.breadcrumbInstance?.template?.name || "流程實例詳情",
          });
        }
      }
    }
    // 處理專案詳情頁面
    else if (route.name === "project-detail") {
      result.push({
        path: "/projects",
        title: "專案管理",
      });
      result.push({
        //path: route.path,
        title: projectStore.projectName || "載入中...",
      });
    }
    // 處理報表詳情頁面
    else if (route.name === "report-detail") {
      result.push({
        path: "/report",
        title: "報表管理",
      });
      result.push({
        // path: route.path,
        title: `報表詳情`,
      });
      result.push({
        title: flowStore.breadcrumbInstance?.template?.name || "載入中...",
      });
    }
    // 其他一般路由的處理
    else if (!route.meta?.hidden) {
      result.push({
        path: route.path,
        title: route.meta?.title || route.name,
      });
    }
  });
  return result;
});
</script>

<style>
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

html.dark ::-webkit-scrollbar-track {
  background: var(--color-dark-bg);
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

html.dark ::-webkit-scrollbar-thumb {
  background: #666;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.el-menu.el-menu--horizontal {
  @apply border-0;
}

.el-button.is-circle {
  @apply !bg-transparent !border-0 !text-white hover:!bg-blue-700 !flex !items-center !justify-center;
}

.el-menu--vertical:not(.el-menu--collapse) {
  @apply !w-48;
}

.el-menu--collapse {
  @apply !w-16;
}

/* 確保內容區域不會被固定元素遮擋 */
.el-main {
  height: calc(100vh - 3.3rem);
  overflow-y: auto;
}

.el-menu-item {
  @apply !border-l-4 !border-transparent;
}

.el-menu-item.is-active {
  @apply !border-l-4 !border-blue-500 !bg-blue-50;
}

html.dark .el-menu-item.is-active {
  @apply !bg-blue-900/30;
}

.el-menu-item:hover {
  @apply !bg-gray-50;
}

html.dark .el-menu-item:hover {
  @apply !bg-gray-800;
}

.content-container {
  @apply flex-1;
  margin-left: 12rem;
  transition: margin-left 0.1s ease-in-out;
}

.content-container.collapsed {
  /* @apply ml-16; */
  margin-left: 4rem;
}

.el-dropdown-menu {
  @apply !py-1;
}

.el-dropdown-item {
  @apply !px-4 !py-2 !text-sm flex items-center;
}

.content-header {
  @apply fixed right-0;
  width: calc(100% - 12rem);
  z-index: 8;
  transition: width 0.1s ease-in-out;
}

.content-header.collapsed {
  width: calc(100% - 4rem);
}

.has-header {
  padding-top: 44px !important; /* 40px (header) + 4px (margin) */
}

/* 麵包屑導航定制 */
:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: var(--el-text-color-primary);
  font-weight: 600;
}

html.dark :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: var(--el-text-color-primary);
}

:deep(.el-breadcrumb__separator) {
  color: var(--el-text-color-secondary);
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner.is-link) {
  color: var(--el-text-color-secondary);
}

:deep(.el-breadcrumb__inner) {
  @apply !px-0.5;
}

/* 載入中的動畫效果 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.el-breadcrumb__inner:empty::after {
  content: "載入中...";
  @apply text-gray-400;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
