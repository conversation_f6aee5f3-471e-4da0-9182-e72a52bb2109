import { createApp } from "vue";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
// 引入 Element Plus 暗黑模式 CSS
import "element-plus/theme-chalk/dark/css-vars.css";
import "./style.css";
import App from "./App.vue";
import zhTw from "element-plus/dist/locale/zh-tw.mjs";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { useUserStore } from "./stores/user";
import { useThemeStore } from "./stores/theme";
import router from "./router";
// 導入自定義指令
import directives from "./directives";
import hljs from "highlight.js";
import "highlight.js/styles/github-dark.css"; // 可以選擇不同的樣式主題
import * as LucideIcons from "lucide-vue-next";

// 配置 NProgress
NProgress.configure({
  easing: "ease",
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.3,
});

// 設定 highlight.js
hljs.configure({
  languages: [
    "javascript",
    "typescript",
    "html",
    "css",
    "scss",
    "json",
    "bash",
    "shell",
    "python",
    "java",
    "php",
  ],
});

// 移除全局載入狀態
const removeGlobalLoadingElement = () => {
  // 添加 loaded 類到應用容器
  const appElement = document.getElementById("app");
  if (appElement) {
    appElement.classList.add("loaded");
  }

  // 移除 index.html 中的初始載入元素
  const initialLoadingElement = document.getElementById("app-loading");
  if (initialLoadingElement) {
    initialLoadingElement.style.opacity = "0";
    initialLoadingElement.style.visibility = "hidden"; // 添加隱藏

    // 延遲移除元素，確保過渡動畫完成
    setTimeout(() => {
      if (initialLoadingElement.parentNode) {
        initialLoadingElement.parentNode.removeChild(initialLoadingElement);
      }
    }, 500); // 增加延遲以匹配 CSS 過渡時間
  }

  // 移除動態創建的載入元素
  const loadingElement = document.getElementById("global-initializing");
  if (loadingElement) {
    loadingElement.style.opacity = "0";
    loadingElement.style.visibility = "hidden"; // 添加隱藏

    setTimeout(() => {
      if (loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
      }
    }, 500);
  }
};

// 更新載入消息
const updateLoadingMessage = (message) => {
  const loadingTextElement = document.querySelector(
    "#app-loading .loading-text"
  );
  if (loadingTextElement) {
    loadingTextElement.textContent = message;
  }
};

// 處理初始路由重定向
const handleInitialRouteRedirect = async () => {
  // 檢查當前URL，如果不是登入頁，且沒有 token，直接在瀏覽器端重定向到登入頁
  const currentPath = window.location.pathname;
  const token = localStorage.getItem("token");

  // 排除不需要認證的頁面
  const isGuestPage = currentPath === "/login" || currentPath === "/register";

  // 檢查是否需要認證且用戶未登入
  if (!isGuestPage && !token) {
    updateLoadingMessage("重定向到登入頁面...");

    // 保存當前頁面為重定向目標
    const redirectTarget = currentPath !== "/" ? currentPath : "";
    const redirectQuery = redirectTarget
      ? `?redirect=${encodeURIComponent(redirectTarget)}`
      : "";

    // 使用瀏覽器 history API 替換當前 URL，避免在路由歷史中留下記錄
    window.history.replaceState(null, "登入", `/login${redirectQuery}`);

    return true; // 已重定向
  }

  return false; // 無需重定向
};

// 進階應用初始化函數
const initializeApp = async () => {
  try {
    updateLoadingMessage("初始化應用...");

    // 進行路由預檢查，確保未認證用戶不會看到主頁
    const redirected = await handleInitialRouteRedirect();

    // 創建所有必要的實例
    const app = createApp(App);
    const pinia = createPinia();
    app.use(pinia);

    // 初始化主題
    updateLoadingMessage("載入主題設定...");
    const themeStore = useThemeStore();
    themeStore.initTheme();

    // 處理已登入用戶
    const token = localStorage.getItem("token");
    if (token) {
      updateLoadingMessage("驗證身份...");

      try {
        const userStore = useUserStore();
        await userStore.fetchUser();
      } catch (error) {
        console.error("恢復用戶狀態失敗:", error);
        localStorage.removeItem("token");

        // 如果尚未重定向，且非登入頁，則重定向到登入頁
        if (!redirected && window.location.pathname !== "/login") {
          updateLoadingMessage("重定向到登入頁面...");
          const redirectQuery =
            window.location.pathname !== "/"
              ? `?redirect=${encodeURIComponent(window.location.pathname)}`
              : "";
          window.history.replaceState(null, "登入", `/login${redirectQuery}`);
        }
      }
    }

    // 配置 Element Plus
    updateLoadingMessage("載入UI元件...");
    app.use(ElementPlus, {
      locale: zhTw,
    });

    // 註冊所有圖示
    Object.entries(LucideIcons).forEach(([name, component]) => {
      app.component(name, component);
    });

    // 使用路由
    app.use(router);

    // 註冊自定義指令
    app.use(directives);

    // 註冊全局指令 v-highlight
    app.directive("highlight", {
      mounted(el) {
        const blocks = el.querySelectorAll("pre code");
        blocks.forEach((block) => {
          hljs.highlightElement(block);
        });
      },
      updated(el) {
        const blocks = el.querySelectorAll("pre code");
        blocks.forEach((block) => {
          hljs.highlightElement(block);
        });
      },
    });

    updateLoadingMessage("啟動應用...");

    // 確保在掛載前已完成所有預處理
    await Promise.resolve();

    // 延遲掛載以確保沒有閃爍
    setTimeout(() => {
      app.mount("#app");

      // 等待應用完全渲染後再移除載入畫面
      setTimeout(() => {
        removeGlobalLoadingElement();
      }, 200);
    }, 100);
  } catch (error) {
    console.error("應用初始化失敗:", error);
    updateLoadingMessage("應用載入失敗，請重新整理頁面");
  }
};

// 啟動應用初始化流程
initializeApp();
