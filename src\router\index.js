/**
 * @fileoverview 路由配置
 * @version 1.0.0
 * @since 2025-02-14
 * @description !!注意: meta 的 showContentHeader 除了影響 content-header 的顯示外，
 * 如果子頁面有用到 Teleport 就一定要設成 true
 */

import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "@/stores/user";
import { ElMessage } from "element-plus";
import docsRoutes from "./modules/docs";
import NProgress from "nprogress";

// 路由配置
const routes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/auth/Login.vue"),
    meta: {
      guest: true,
      requiresAuth: false,
      layout: "blank",
      title: "登入",
      icon: "User",
    },
  },
  {
    path: "/",
    redirect: "/home",
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/views/home/<USER>"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "首頁",
      icon: "Home",
      showContentHeader: false,
    },
  },
  {
    path: "/projects",
    name: "Projects",
    component: () => import("@/views/projects/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "專案管理",
      icon: "FolderGit2",
      showContentHeader: true,
    },
  },
  {
    path: "/projects/:id",
    name: "project-detail",
    component: () => import("@/views/projects/detail.vue"),
    meta: {
      requiresAuth: true,
      showContentHeader: true,
      hidden: true,
      title: "專案詳情",
    },
  },
  {
    path: "/report",
    name: "Report",
    component: () => import("@/views/report/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "報表管理",
      icon: "FileBarChart",
      showContentHeader: true,
    },
  },
  {
    path: "/report/:id",
    name: "report-detail",
    component: () => import("@/views/report/detail.vue"),
    meta: {
      requiresAuth: true,
      showContentHeader: true,
      hidden: true,
      title: "報表詳情",
    },
  },
  {
    path: "/favorites",
    name: "Favorites",
    component: () => import("@/views/favorite/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "我的關注",
      icon: "Star",
      showContentHeader: false,
    },
  },
  {
    path: "/flow-instances",
    name: "FlowInstances",
    component: () => import("@/views/flow/instance/list/index.vue"),
    meta: {
      title: "流程實例管理",
      icon: "GitCompareArrows",
      keepAlive: true,
      requiresAuth: true,
      showContentHeader: true,
    },
  },
  {
    path: "/flow-instances/:id",
    name: "FlowInstanceDetail",
    component: () => import("@/views/flow/instance/detail/index.vue"),
    meta: {
      title: "流程實例詳情",
      icon: "GitBranch",
      requiresAuth: true,
      showContentHeader: true,
      hidden: true,
    },
  },
  {
    path: "/flow-node-definitions",
    name: "FlowNodeDefinitions",
    component: () => import("@/views/flow/node-definitions/index.vue"),
    meta: {
      title: "節點定義管理",
      icon: "Component",
      requiresAuth: true,
      requiresAdmin: true,
      showContentHeader: true,
    },
  },
  {
    path: "/flow-templates",
    name: "FlowTemplates",
    component: () => import("@/views/flow/templates/list/index.vue"),
    meta: {
      title: "流程模板管理",
      icon: "Workflow",
      requiresAuth: true,
      requiresAdmin: true,
      showContentHeader: true,
    },
  },
  {
    path: "/flow-templates/:id/design",
    name: "FlowTemplateDesign",
    component: () => import("@/views/flow/templates/design/index.vue"),
    meta: {
      title: "流程模板設計",
      icon: "Workflow",
      requiresAuth: true,
      requiresAdmin: true,
      showContentHeader: true,
      hidden: true,
    },
  },
  // {
  //   path: "/flow-documents",
  //   name: "FlowDocuments",
  //   component: () => import("@/views/flow/FlowDocumentList.vue"),
  //   meta: {
  //     title: "文檔管理",
  //     icon: "FileText",
  //     requiresAuth: true,
  //     showContentHeader: true,
  //   },
  // },
  {
    path: "/documents",
    name: "Documents",
    component: () => import("@/views/documents/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "文件管理 👮‍♂️",
      icon: "FileText",
      showContentHeader: true,
    },
  },
  {
    path: "/git",
    name: "Git",
    component: () => import("@/views/git/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      requiresAdmin: true,
      title: "Git 提交歷史 👮‍♂️",
      icon: "GitGraph",
      showContentHeader: true,
    },
  },
  // {
  //   path: "/files",
  //   name: "Files",
  //   component: () => import("@/views/files/index.vue"),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //     title: "文件管理",
  //     icon: "FileText",
  //     showContentHeader: true,
  //   },
  // },
  // {
  //   path: "/analysis",
  //   name: "Analysis",
  //   component: () => import("@/views/analysis/index.vue"),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //     title: "數據分析",
  //     icon: "LineChart",
  //     showContentHeader: true,
  //   },
  // },
  // {
  //   path: "/settings",
  //   name: "Settings",
  //   component: () => import("@/views/settings/index.vue"),
  //   meta: {
  //     keepAlive: true,
  //     requiresAuth: true,
  //     requiresAdmin: true,
  //     title: "系統設置",
  //     icon: "Settings",
  //     showContentHeader: true,
  //   },
  // },
  {
    path: "/rbac",
    name: "RBAC",
    component: () => import("@/views/rbac/index.vue"),
    meta: {
      title: "權限管理 👮‍♂️",
      icon: "UserRoundCog",
      permissions: ["MANAGE_ROLES", "VIEW_ROLES", "VIEW_PERMISSIONS"],
      showContentHeader: false,
    },
    children: [
      {
        path: "roles",
        name: "RoleManagement",
        component: () => import("@/views/rbac/components/RoleManagement.vue"),
        meta: {
          title: "角色管理",
          permissions: ["MANAGE_ROLES", "VIEW_ROLES"],
        },
      },
      {
        path: "permissions",
        name: "PermissionList",
        component: () => import("@/views/rbac/components/PermissionList.vue"),
        meta: {
          title: "權限列表",
          permissions: ["VIEW_PERMISSIONS"],
        },
      },
      {
        path: "user-roles",
        name: "UserRoleManagement",
        component: () =>
          import("@/views/rbac/components/UserRoleManagement.vue"),
        meta: {
          title: "用戶角色",
          permissions: ["ASSIGN_ROLES", "VIEW_ROLES"],
        },
      },
    ],
  },
  {
    path: "/api-test",
    name: "ApiTest",
    component: () => import("@/views/api-test/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      requiresAdmin: true,
      title: "API 測試 👮‍♂️",
      showContentHeader: false,
    },
  },
  {
    path: "/issues",
    name: "Issues 👮‍♂️",
    component: () => import("@/views/issues/index.vue"),
    meta: {
      keepAlive: true,
      requiresAuth: true,
      title: "問題工單管理 👮‍♂️",
      icon: "AlertCircle",
      showContentHeader: true,
    },
  },
  {
    path: "/issues/:id",
    name: "IssueDetail",
    component: () => import("@/views/issues/detail.vue"),
    meta: {
      requiresAuth: true,
      showContentHeader: true,
      hidden: true,
      title: "問題工單詳情",
    },
  },
  {
    path: "/docs",
    name: "Docs",
    component: () => import("@/views/docs/components/DocContainer.vue"),
    meta: {
      requiresAuth: true,
      title: "開發者文檔",
      icon: "CodeSquare",
      openInNewWindow: true,
      layout: "doc",
    },
    children: docsRoutes,
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（例如使用瀏覽器的前進/後退按鈕），則使用它
    if (savedPosition) {
      return savedPosition;
    }
    // 否則捲動至頂部
    return { top: 0 };
  },
});

// 路由守衛
router.beforeEach(async (to, from, next) => {
  // 在路由切換開始時顯示進度條
  NProgress.start();

  // 設置頁面標題
  document.title = `${to.meta.title} - SFDA IYM` || "SFDA IYM";

  const userStore = useUserStore();
  const token = localStorage.getItem("token");

  // 需要認證的頁面
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    // 如果沒有用戶資訊但有 token，先獲取用戶資訊
    if ((!userStore.user || !userStore.isAuthenticated) && token) {
      try {
        await userStore.fetchUser();
      } catch (error) {
        localStorage.removeItem("token");
        NProgress.done();
        next({
          path: "/login",
          query: { redirect: to.fullPath },
        });
        return;
      }
    }

    // 如果沒有認證，轉到登入頁
    if (!userStore.isAuthenticated) {
      NProgress.done();
      next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
      return;
    }

    // 檢查管理員權限
    if (to.meta.requiresAdmin && !userStore.isAdmin) {
      ElMessage.error("需要管理員權限");
      NProgress.done();
      next(from.path);
      return;
    }

    // 檢查特定權限
    if (
      to.meta.permissions &&
      !userStore.hasAnyPermission(to.meta.permissions)
    ) {
      ElMessage.error("權限不足");
      NProgress.done();
      next(from.path);
      return;
    }

    next();
  }
  // 訪客頁面（如登入頁）
  else if (to.matched.some((record) => record.meta.guest)) {
    if (userStore.isAuthenticated) {
      NProgress.done();
      next("/");
    } else if (token) {
      try {
        await userStore.fetchUser();
        NProgress.done();
        next("/");
      } catch (error) {
        localStorage.removeItem("token");
        next();
      }
    } else {
      next();
    }
  }
  // 其他頁面
  else {
    next();
  }
});

// 全局後置鉤子
router.afterEach(() => {
  // 在路由切換完成後關閉進度條
  NProgress.done();
});

export default router;
