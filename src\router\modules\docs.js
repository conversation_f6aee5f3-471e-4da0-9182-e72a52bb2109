const routes = [
  {
    path: "",
    name: "DocsHome",
    component: () => import("@/views/docs/index.vue"),

    meta: {
      title: "開發者文檔",
      icon: "Document",
      layout: "doc",
    },
  },
  {
    path: "introduction",
    name: "DocsIntroduction",
    component: () => import("@/views/docs/introduction.vue"),
    meta: {
      title: "介紹",
      icon: "Info",
      order: 1,
      layout: "doc",
    },
  },
  {
    path: "overview",
    name: "DocsOverview",
    component: () => import("@/views/docs/overview.vue"),
    meta: {
      title: "系統概述",
      icon: "Home",
      order: 2,
      layout: "doc",
    },
  },
  {
    path: "getting-started",
    name: "DocsGettingStarted",
    component: () => import("@/views/docs/getting-started.vue"),
    meta: {
      title: "快速開始",
      icon: "Rocket",
      order: 3,
      layout: "doc",
    },
  },
  {
    path: "development-guide",
    name: "DocsDevelopmentGuide",
    component: () => import("@/views/docs/development-guide.vue"),
    meta: {
      title: "開發指南",
      icon: "Code",
      order: 4,
      layout: "doc",
    },
  },
  {
    path: "analysis",
    name: "DocsAnalysis",
    meta: {
      title: "數據分析",
      icon: "DataAnalysis",
      order: 5,
      layout: "doc",
    },
    children: [
      {
        path: "overview",
        name: "DocsAnalysisOverview",
        component: () => import("@/views/docs/analysis/overview.vue"),
        meta: {
          title: "分析系統概述",
          order: 1,
          layout: "doc",
        },
      },
      {
        path: "process",
        name: "DocsAnalysisProcess",
        component: () => import("@/views/docs/analysis/process.vue"),
        meta: {
          title: "數據處理流程",
          order: 2,
          layout: "doc",
        },
      },
      {
        path: "models",
        name: "DocsAnalysisModels",
        component: () => import("@/views/docs/analysis/models.vue"),
        meta: {
          title: "分析模型說明",
          order: 3,
          layout: "doc",
        },
      },
    ],
  },
  {
    path: "backend",
    name: "DocsBackend",
    meta: {
      title: "後端系統",
      icon: "Monitor",
      order: 6,
      layout: "doc",
    },
    children: [
      {
        path: "overview",
        name: "DocsBackendOverview",
        component: () => import("@/views/docs/backend/overview.vue"),
        meta: {
          title: "後端系統概述",
          order: 1,
          layout: "doc",
        },
      },
      {
        path: "api",
        name: "DocsBackendApi",
        component: () => import("@/views/docs/backend/api.vue"),
        meta: {
          title: "API 文檔",
          order: 2,
          layout: "doc",
        },
      },
      {
        path: "database",
        name: "DocsBackendDatabase",
        component: () => import("@/views/docs/backend/database.vue"),
        meta: {
          title: "數據庫設計",
          order: 3,
          layout: "doc",
        },
      },
    ],
  },
  {
    path: "frontend",
    name: "DocsFrontend",
    meta: {
      title: "前端開發",
      icon: "Grid",
      order: 7,
      layout: "doc",
    },
    children: [
      {
        path: "overview",
        name: "DocsFrontendOverview",
        component: () => import("@/views/docs/frontend/overview.vue"),
        meta: {
          title: "前端系統概述",
          order: 1,
          layout: "doc",
        },
      },
      {
        path: "components",
        name: "DocsFrontendComponents",
        component: () => import("@/views/docs/frontend/components.vue"),
        meta: {
          title: "組件說明",
          order: 2,
          layout: "doc",
        },
      },
      {
        path: "state",
        name: "DocsFrontendState",
        component: () => import("@/views/docs/frontend/state.vue"),
        meta: {
          title: "狀態管理",
          order: 3,
          layout: "doc",
        },
      },
    ],
  },
];

export default routes;
