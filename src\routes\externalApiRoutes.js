const express = require("express");
const router = express.Router();
const externalApiService = require("../services/externalApiService");
const { asyncHandler } = require("../utils/asyncHandler");
const { validateRequest } = require("../middlewares/validateRequest");
const { authenticateToken, authorize } = require("../middlewares/auth");
const Joi = require("joi");

/**
 * @swagger
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   responses:
 *     UnauthorizedError:
 *       description: 未提供認證令牌或令牌無效
 *     ForbiddenError:
 *       description: 沒有必要權限
 */

/**
 * @swagger
 * tags:
 *   name: ExternalAPI
 *   description: 外部 API 代理
 */

// 所有路由都需要認證和權限
router.use(authenticateToken);
router.use(authorize("USE_EXTERNAL_API"));

// 統一的 url 轉發
router.post(
  "/:path",
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(req.params.path, req.body);
    res.json(result);
  })
);

/**
 * @swagger
 * /api/external/analysis/correlation:
 *   post:
 *     summary: 執行相關性分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: object
 *                 description: 分析數據
 *               parameters:
 *                 type: object
 *                 description: 分析參數
 *     responses:
 *       200:
 *         description: 分析結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 correlationMatrix:
 *                   type: array
 *                   description: 相關性矩陣
 *                 significantPairs:
 *                   type: array
 *                   description: 顯著相關對
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/analysis/correlation",
  validateRequest({
    body: Joi.object({
      data: Joi.object().required(),
      parameters: Joi.object(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "analysis/correlation",
      req.body
    );
    res.json(result);
  })
);

/**
 * @swagger
 * /api/external/analysis/anova:
 *   post:
 *     summary: 執行方差分析 (ANOVA)
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: object
 *                 description: 分析數據
 *               parameters:
 *                 type: object
 *                 description: 分析參數
 *     responses:
 *       200:
 *         description: 分析結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 anovaTable:
 *                   type: object
 *                   description: ANOVA 表
 *                 pValue:
 *                   type: number
 *                   description: P 值
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/analysis/anova",
  validateRequest({
    body: Joi.object({
      data: Joi.object().required(),
      parameters: Joi.object(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post("analysis/anova", req.body);
    res.json(result);
  })
);

/**
 * @swagger
 * /api/external/analysis/descriptive:
 *   post:
 *     summary: 執行描述性統計分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               method:
 *                 type: string
 *                 description: 分析方法，固定為 "descriptive"
 *               data:
 *                 type: object
 *                 description: 分析數據
 *                 properties:
 *                   data:
 *                     type: array
 *                     items:
 *                       type: number
 *                     description: 要分析的數值數組
 *     responses:
 *       200:
 *         description: 分析結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 method:
 *                   type: string
 *                   description: 分析方法
 *                 success:
 *                   type: boolean
 *                   description: 是否成功
 *                 result:
 *                   type: object
 *                   description: 分析結果
 *                   properties:
 *                     sample_size:
 *                       type: integer
 *                       description: 樣本數量
 *                     mean:
 *                       type: number
 *                       description: 平均數
 *                     median:
 *                       type: number
 *                       description: 中位數
 *                     std:
 *                       type: number
 *                       description: 標準差
 *                     variance:
 *                       type: number
 *                       description: 變異數
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/analysis/descriptive",
  validateRequest({
    body: Joi.object({
      method: Joi.string().valid("descriptive").required(),
      data: Joi.object({
        data: Joi.array().items(Joi.number()).required(),
      }).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "statistics/descriptive",
      req.body
    );
    res.json(result);
  })
);

/**
 * @swagger
 * /api/external/complaints/{id}:
 *   get:
 *     summary: 獲取客訴詳情
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客訴 ID
 *     responses:
 *       200:
 *         description: 客訴詳情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: 客訴 ID
 *                 title:
 *                   type: string
 *                   description: 客訴標題
 *                 description:
 *                   type: string
 *                   description: 客訴描述
 *                 status:
 *                   type: string
 *                   description: 客訴狀態
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.get(
  "/complaints/:id",
  asyncHandler(async (req, res) => {
    const result = await externalApiService.get(`complaints/${req.params.id}`);
    res.json(result);
  })
);

/**
 * @swagger
 * /api/external/complaints:
 *   get:
 *     summary: 獲取客訴列表
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 客訴狀態過濾
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每頁數量
 *     responses:
 *       200:
 *         description: 客訴列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.get(
  "/complaints",
  asyncHandler(async (req, res) => {
    const result = await externalApiService.get("complaints", req.query);
    res.json(result);
  })
);

//==================IYM==================
/** 獲取良率數據
 * @swagger
 * /api/external/iym/main_method/yield:
 *   post:
 *     summary: 獲取良率數據
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *     responses:
 *       200:
 *         description: 成功取得良率數據
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 method:
 *                   type: string
 *                   example: "yield"
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 result:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       不良大項代碼:
 *                         type: string
 *                       defect_pcs:
 *                         type: number
 *                       cumulative_count:
 *                         type: number
 *                       cumulative_percentage:
 *                         type: number
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/main_method/yield",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/main_method/yield",
      req.body
    );
    res.json(result);
  })
);

/** 獲取各製程不良率
 * @swagger
 * /api/external/iym/main_method/get_defect_rate_by_process:
 *   post:
 *     summary: ！！！目前前端未使用！！！Identify Key Stations(獲取各製程不良率)
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *               - defect_code
 *               - process_name
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *               defect_code:
 *                 type: string
 *                 description: 不良項目
 *                 example: "S03"
 *               process_name:
 *                 type: string
 *                 description: 製程名稱
 *                 example: "(R212-S01)RTR VCP全局面鍍銅"
 *     responses:
 *       200:
 *         description: 成功獲取各製程不良率
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   process_name:
 *                     type: string
 *                     description: 製程名稱
 *                     example: "(R212-S01)RTR VCP全局面鍍銅"
 *                   step:
 *                     type: string
 *                     description: 步驟
 *                     example: "1"
 *                   line:
 *                     type: string
 *                     description: 線體
 *                     example: "L1"
 *                   mean_defect_rate:
 *                     type: number
 *                     description: 平均不良率
 *                     example: 2.35
 *                   count:
 *                     type: integer
 *                     description: 數量
 *                     example: 145
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/main_method/get_defect_rate_by_process",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
      defect_code: Joi.string().min(1).required(),
      process_name: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/main_method/get_defect_rate_by_process",
      req.body
    );
    res.json(result);
  })
);

/** 取得製程名稱清單
 * @swagger
 * /api/external/iym/main_method/process_name_list:
 *   post:
 *     summary: 取得製程名稱清單
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *     responses:
 *       200:
 *         description: 成功取得製程名稱清單
 *         content:
 *           application/json:
 *             example: ["製程1", "製程2", "製程3"]
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/main_method/process_name_list",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/main_method/process_name_list",
      req.body
    );
    res.json(result);
  })
);

/** 取得卡方離散變數清單
 * @swagger
 * /api/external/iym/main_method/chi_square_discrete_variables_list_spi:
 *   post:
 *     summary: 取得卡方離散變數清單
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "ZE18113-A"
 *               work_order:
 *                 type: string
 *                 description: 工單號碼
 *                 example: "*********, *********, *********"
 *     responses:
 *       200:
 *         description: 成功取得變數清單
 *         content:
 *           application/json:
 *             example: ["變數1", "變數2", "變數3"]
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/main_method/chi_square_discrete_variables_list_spi",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/main_method/chi_square_discrete_variables_list_spi",
      req.body
    );
    res.json(result);
  })
);

/** 根據品目代碼取得 LOT 清單
 * @swagger
 * /api/external/iym/main_method/lot_list_by_partno:
 *   post:
 *     summary: 取得 LOT 清單
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             start_date: "2025-01-01"
 *             end_date: "2025-03-31"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "ZE18113-A"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (選填)
 *                 example: "2025-01-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (選填)
 *                 example: "2025-03-31"
 *     responses:
 *       200:
 *         description: 成功取得 LOT 清單
 *         content:
 *           application/json:
 *             example: ["*********", "*********", "*********"]
 *             schema:
 *               type: array
 *               items:
 *                 type: string
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/main_method/lot_list_by_partno",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      start_date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/),
      end_date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/main_method/lot_list_by_partno",
      req.body
    );
    res.json(result);
  })
);

//==================IYM基礎統計==================
/** 計算平均值
 * @swagger
 * /api/external/iym/basic_statistics/mean:
 *   post:
 *     summary: 計算平均值
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算平均值
 *         content:
 *           application/json:
 *             example:
 *               MD: 12.3
 *               TD: 8.7
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/mean",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/mean",
      req.body
    );
    res.json(result);
  })
);

/** 計算中位數
 * @swagger
 * /api/external/iym/basic_statistics/median:
 *   post:
 *     summary: 計算中位數
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算中位數
 *         content:
 *           application/json:
 *             example:
 *               MD: 12.0
 *               TD: 8.5
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/median",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/median",
      req.body
    );
    res.json(result);
  })
);

/** 計算眾數
 * @swagger
 * /api/external/iym/basic_statistics/mode:
 *   post:
 *     summary: 計算眾數
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算眾數
 *         content:
 *           application/json:
 *             example:
 *               MD: 11.5
 *               TD: 8.0
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/mode",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/mode",
      req.body
    );
    res.json(result);
  })
);

/** 計算標準差
 * @swagger
 * /api/external/iym/basic_statistics/std:
 *   post:
 *     summary: 計算標準差
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算標準差
 *         content:
 *           application/json:
 *             example:
 *               MD: 2.1
 *               TD: 1.5
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/std",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/std",
      req.body
    );
    res.json(result);
  })
);

/** 計算四分位數
 * @swagger
 * /api/external/iym/basic_statistics/quartiles:
 *   post:
 *     summary: 計算四分位數
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算四分位數
 *         content:
 *           application/json:
 *             example:
 *               MD:
 *                 q1: 10.2
 *                 q3: 14.1
 *               TD:
 *                 q1: 7.5
 *                 q3: 9.8
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 properties:
 *                   q1:
 *                     type: number
 *                   q3:
 *                     type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/quartiles",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/quartiles",
      req.body
    );
    res.json(result);
  })
);

/** 計算最大值
 * @swagger
 * /api/external/iym/basic_statistics/max:
 *   post:
 *     summary: 計算最大值
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算最大值
 *         content:
 *           application/json:
 *             example:
 *               MD: 16.5
 *               TD: 11.2
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/max",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/max",
      req.body
    );
    res.json(result);
  })
);

/** 計算最小值
 * @swagger
 * /api/external/iym/basic_statistics/min:
 *   post:
 *     summary: 計算最小值
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算最小值
 *         content:
 *           application/json:
 *             example:
 *               MD: 8.0
 *               TD: 6.0
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/min",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/min",
      req.body
    );
    res.json(result);
  })
);

/** 基礎統計量分析
 * @swagger
 * /api/external/iym/basic_statistics/basic_statistics:
 *   post:
 *     summary: 基礎統計量分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********"
 *             features: ["MD", "TD"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算基礎統計量
 *         content:
 *           application/json:
 *             example:
 *               MD:
 *                 mean: 12.3
 *                 std: 2.1
 *                 median: 12.0
 *                 mode: 11.5
 *                 min: 8.0
 *                 max: 16.5
 *                 q1: 10.2
 *                 q3: 14.1
 *               TD:
 *                 mean: 8.7
 *                 std: 1.5
 *                 median: 8.5
 *                 mode: 8.0
 *                 min: 6.0
 *                 max: 11.2
 *                 q1: 7.5
 *                 q3: 9.8
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 properties:
 *                   mean:
 *                     type: number
 *                   std:
 *                     type: number
 *                   median:
 *                     type: number
 *                   mode:
 *                     type: number
 *                   min:
 *                     type: number
 *                   max:
 *                     type: number
 *                   q1:
 *                     type: number
 *                   q3:
 *                     type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/basic_statistics",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/basic_statistics",
      req.body
    );
    res.json(result);
  })
);

/** 計算相關係數矩陣
 * @swagger
 * /api/external/iym/basic_statistics/correlation_matrix:
 *   post:
 *     summary: 計算相關係數矩陣
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             features: ["MD", "TD", "PRINT_PRESSURE", "HUMIDITY", "PRINT_SPEED", "TEMPRATURE"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功計算相關係數矩陣
 *         content:
 *           application/json:
 *             example:
 *               MD:
 *                 MD: 1.0
 *                 TD: 0.12
 *                 PRINT_PRESSURE: -0.03
 *               TD:
 *                 MD: 0.12
 *                 TD: 1.0
 *                 PRINT_PRESSURE: 0.05
 *               PRINT_PRESSURE:
 *                 MD: -0.03
 *                 TD: 0.05
 *                 PRINT_PRESSURE: 1.0
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 additionalProperties:
 *                   type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/basic_statistics/correlation_matrix",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/basic_statistics/correlation_matrix",
      req.body
    );
    res.json(result);
  })
);

//==================IYM統計檢定==================
/** 卡方檢定
 * @swagger
 * /api/external/iym/test_statistics/chi_square:
 *   post:
 *     summary: 卡方檢定分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *               - defect_code
 *               - process_name
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *               defect_code:
 *                 type: string
 *                 description: 不良項目
 *                 example: "S03"
 *               process_name:
 *                 type: string
 *                 description: 製程名稱
 *                 example: "(R212-S01)RTR VCP全局面鍍銅,(L260-00)LPSM 清潔"
 *     responses:
 *       200:
 *         description: 成功執行卡方檢定分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   Variable:
 *                     type: string
 *                     example: "1-人員工號"
 *                   Chi2:
 *                     type: number
 *                     example: 15.23
 *                   P-Value:
 *                     type: number
 *                     example: 0.0012
 *                   DOF:
 *                     type: integer
 *                     example: 3
 *                   Exp:
 *                     type: array
 *                     items:
 *                       type: array
 *                       items:
 *                         type: number
 *                     example: [[6.95, 9.04], [5.23, 6.77]]
 *                   Step:
 *                     type: string
 *                     example: "1"
 *                   process_name:
 *                     type: string
 *                     example: "(R212-S01)RTR VCP全局面鍍銅,(L260-00)LPSM 清潔"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/test_statistics/chi_square",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
      defect_code: Joi.string().min(1).required(),
      process_name: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/test_statistics/chi_square",
      req.body
    );
    res.json(result);
  })
);

/** SPI卡方檢定
 * @swagger
 * /api/external/iym/test_statistics/chi_square_spi:
 *   post:
 *     summary: SPI卡方檢定
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "*********, *********, *********"
 *             columns: ["FEEDER_NO", "REEL_NO", "SQUEEGEE_NO", "PD_SN", "CD_SN", "PRINT_DIRECTION"]
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - columns
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼清單(以逗號分隔)
 *               columns:
 *                 type: array
 *                 description: 要分析的欄位列表
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 成功執行卡方檢定分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   feature_col:
 *                     type: string
 *                     description: 特徵欄位名稱
 *                   p:
 *                     type: number
 *                     description: P值
 *                   dof:
 *                     type: number
 *                     description: 自由度
 *                   significant_count:
 *                     type: number
 *                     description: 顯著次數
 *                   Significant_rate:
 *                     type: number
 *                     description: 顯著比率
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/iym/test_statistics/chi_square_spi",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      columns: Joi.array().items(Joi.string()).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/test_statistics/chi_square_spi",
      req.body
    );
    res.json(result);
  })
);

/** Kruskal-Wallis 檢定分析
 * @swagger
 * /api/external/iym/test_statistics/kruskal_wallis_test_analysis:
 *   post:
 *     summary: 特徵顯著性H檢定分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             features: [
 *               "MD",
 *               "TD",
 *               "PRINT_PRESSURE",
 *               "HUMIDITY",
 *               "PRINT_SPEED",
 *               "TEMPRATURE",
 *               "solder_life"
 *             ]
 *             target: "ooc"
 *             num_iterations: 100
 *             p_threshold: 0.05
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *               num_iterations:
 *                 type: integer
 *                 description: 迭代次數(選填，預設為100)
 *                 default: 100
 *               p_threshold:
 *                 type: number
 *                 description: 顯著性閾值(選填，預設為0.05)
 *                 default: 0.05
 *     responses:
 *       200:
 *         description: 成功執行H檢定分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 significant_features:
 *                   type: array
 *                   items:
 *                     type: string
 *                 significant_analysis:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       Feature:
 *                         type: string
 *                       Significant_Count:
 *                         type: integer
 *                       Significant_Rate:
 *                         type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/test_statistics/kruskal_wallis_test_analysis",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().required(),
      work_order: Joi.string().required(),
      features: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
      num_iterations: Joi.number().integer().default(100),
      p_threshold: Joi.number().default(0.05),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/test_statistics/kruskal_wallis_test_analysis",
      req.body
    );
    res.json(result);
  })
);

/** KDE密度圖數據分析
 * @swagger
 * /api/external/iym/test_statistics/kde_analysis_for_features:
 *   post:
 *     summary: KDE密度圖數據分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             features: [
 *               "PRINT_PRESSURE",
 *               "solder_life",
 *               "MD",
 *               "PRINT_SPEED",
 *               "HUMIDITY",
 *               "tension_range",
 *               "TD",
 *               "tension_mean",
 *               "SQUEEGEE_SUM_COUNT"
 *             ]
 *             target: "ooc"
 *             normal_group: "in_control"
 *             defect_group: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               features:
 *                 type: array
 *                 description: 要分析的特徵列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *               normal_group:
 *                 type: string
 *                 description: 正常組的標籤(選填，預設為in_control)
 *                 default: "in_control"
 *               defect_group:
 *                 type: string
 *                 description: 不良組的標籤(選填，預設為ooc)
 *                 default: "ooc"
 *     responses:
 *       200:
 *         description: 成功執行KDE密度圖數據分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 properties:
 *                   normal:
 *                     type: object
 *                     properties:
 *                       x:
 *                         type: array
 *                         items:
 *                           type: number
 *                       y:
 *                         type: array
 *                         items:
 *                           type: number
 *                   defect:
 *                     type: object
 *                     properties:
 *                       x:
 *                         type: array
 *                         items:
 *                           type: number
 *                       y:
 *                         type: array
 *                         items:
 *                           type: number
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/test_statistics/kde_analysis_for_features",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
      normal_group: Joi.string().default("in_control"),
      defect_group: Joi.string().default("ooc"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/test_statistics/kde_analysis_for_features",
      req.body
    );
    res.json(result);
  })
);

//==================IYM機器學習==================
/** 隨機森林分析
 * @swagger
 * /api/external/iym/machine_learning/random_forest:
 *   post:
 *     summary: 隨機森林分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             start_date: "2024-07-01"
 *             end_date: "2024-11-01"
 *             part_no: "QE2825"
 *             defect_code: "S03"
 *             process_name: "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合"
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *               - defect_code
 *               - process_name
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *               defect_code:
 *                 type: string
 *                 description: 不良項目
 *                 example: "S03"
 *               process_name:
 *                 type: string
 *                 description: 製程名稱
 *                 example: "(R212-S01)RTR VCP全局面鍍銅"
 *     responses:
 *       200:
 *         description: 成功執行隨機森林分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 feature_importance_mapping:
 *                   type: object
 *                   description: 特徵重要性映射
 *                 feature_importance:
 *                   type: array
 *                   description: 特徵重要性列表
 *                 importance_plot:
 *                   type: string
 *                   description: Base64編碼的重要性圖
 *                 model_performance:
 *                   type: object
 *                   properties:
 *                     train_score:
 *                       type: number
 *                     test_score:
 *                       type: number
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/iym/machine_learning/random_forest",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
      defect_code: Joi.string().min(1).required(),
      process_name: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/random_forest",
      req.body
    );
    res.json(result);
  })
);

/** 隨機森林分類分析(用於參數分析)
 * @swagger
 * /api/external/iym/machine_learning/random_forest_categorical_for_parameter:
 *   post:
 *     summary: Random Forest 分類模型分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             variables: ["TEST_NUMBER", "solder_life", "PRINT_SPEED", "PRINT_PRESSURE", "SNAP_OFF_DISTANCE", "SNAP_OFF_SPEED", "TEMPRATURE", "HUMIDITY", "TD", "MD", "tension_mean", "tension_range", "SQUEEGEE_SUM_COUNT"]
 *             target: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *     responses:
 *       200:
 *         description: 成功執行隨機森林分類分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   Feature:
 *                     type: string
 *                     description: 特徵名稱
 *                   Importance:
 *                     type: number
 *                     description: 重要性分數
 *                   Model:
 *                     type: string
 *                     description: 模型名稱
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/random_forest_categorical_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/random_forest_categorical_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** XGBoost分析
 * @swagger
 * /api/external/iym/machine_learning/xgboost:
 *   post:
 *     summary: XGBoost分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             start_date: "2024-07-01"
 *             end_date: "2024-11-01"
 *             part_no: "QE2825"
 *             defect_code: "S03"
 *             process_name: "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合"
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *               - defect_code
 *               - process_name
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *               defect_code:
 *                 type: string
 *                 description: 不良項目
 *                 example: "S03"
 *               process_name:
 *                 type: string
 *                 description: 製程名稱
 *                 example: "(R212-S01)RTR VCP全局面鍍銅"
 *     responses:
 *       200:
 *         description: 成功執行XGBoost分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 feature_importance_mapping:
 *                   type: object
 *                   description: 特徵重要性映射
 *                 feature_importance:
 *                   type: array
 *                   description: 特徵重要性列表
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/iym/machine_learning/xgboost",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
      defect_code: Joi.string().min(1).required(),
      process_name: Joi.string().min(1).required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/xgboost",
      req.body
    );
    res.json(result);
  })
);

/** XGBoost分類分析(用於參數分析)
 * @swagger
 * /api/external/iym/machine_learning/xgboost_categorical_for_parameter:
 *   post:
 *     summary: XGBoost分類模型分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             variables: ["TEST_NUMBER", "solder_life", "PRINT_SPEED", "PRINT_PRESSURE", "SNAP_OFF_DISTANCE", "SNAP_OFF_SPEED", "TEMPRATURE", "HUMIDITY", "TD", "MD", "tension_mean", "tension_range", "SQUEEGEE_SUM_COUNT"]
 *             target: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *     responses:
 *       200:
 *         description: 成功執行XGBoost分類分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 feature_importance:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       Feature:
 *                         type: string
 *                         description: 特徵名稱
 *                       Importance:
 *                         type: number
 *                         description: 重要性分數
 *                       Model:
 *                         type: string
 *                         description: 模型名稱
 *                 model_performance:
 *                   type: object
 *                   properties:
 *                     accuracy:
 *                       type: number
 *                       description: 準確率
 *                     precision:
 *                       type: number
 *                       description: 精確率
 *                     recall:
 *                       type: number
 *                       description: 召回率
 *                     f1_score:
 *                       type: number
 *                       description: F1分數
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/xgboost_categorical_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/xgboost_categorical_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** 可靠性指數評估
 * @swagger
 * /api/external/iym/machine_learning/reliability_index:
 *   post:
 *     summary: 可靠性指數評估(EX：RF vs XGB)
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             feature_importance_1: [
 *               {
 *                 "Feature": "solder_life",
 *                 "Importance": 0.00019951766385310668,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "PRINT_SPEED",
 *                 "Importance": 1.712310435072384e-7,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "tension_mean",
 *                 "Importance": 1.5579203320128908e-7,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "HUMIDITY",
 *                 "Importance": 1.498579645258702e-7,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "TEST_NUMBER",
 *                 "Importance": 1.211715813787839e-7,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "TEMPRATURE",
 *                 "Importance": 4.267610978771807e-8,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "PRINT_PRESSURE",
 *                 "Importance": 3.101267042316524e-8,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "MD",
 *                 "Importance": 3.5517779809471264e-9,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "TD",
 *                 "Importance": 3.479796082955196e-9,
 *                 "Model": "Logistic Regression"
 *               },
 *               {
 *                 "Feature": "SNAP_OFF_DISTANCE",
 *                 "Importance": 3.4274247304284203e-9,
 *                 "Model": "Logistic Regression"
 *               }
 *             ]
 *             feature_importance_2: [
 *               {
 *                 "Feature": "MD",
 *                 "Importance": 0.3149832191460309,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "TD",
 *                 "Importance": 0.19828760447823346,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "PRINT_PRESSURE",
 *                 "Importance": 0.16793835821211572,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "HUMIDITY",
 *                 "Importance": 0.11585180600292593,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "SQUEEGEE_SUM_COUNT",
 *                 "Importance": 0.07434898189677736,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "PRINT_SPEED",
 *                 "Importance": 0.06669988506237098,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "TEMPRATURE",
 *                 "Importance": 0.061890145201545455,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "TEST_NUMBER",
 *                 "Importance": 0,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "solder_life",
 *                 "Importance": 0,
 *                 "Model": "Random Forest"
 *               },
 *               {
 *                 "Feature": "SNAP_OFF_DISTANCE",
 *                 "Importance": 0,
 *                 "Model": "Random Forest"
 *               }
 *             ]
 *           schema:
 *             type: object
 *             required:
 *               - feature_importance_1
 *               - feature_importance_2
 *             properties:
 *               feature_importance_1:
 *                 type: array
 *                 description: 第一個模型特徵重要性列表
 *                 items:
 *                   type: object
 *                   properties:
 *                     Feature:
 *                       type: string
 *                       description: 特徵名稱
 *                     Importance:
 *                       type: number
 *                       description: 重要性分數
 *                     Model:
 *                       type: string
 *                       description: 模型名稱
 *               feature_importance_2:
 *                 type: array
 *                 description: 第二個模型特徵重要性列表
 *                 items:
 *                   type: object
 *                   properties:
 *                     Feature:
 *                       type: string
 *                       description: 特徵名稱
 *                     Importance:
 *                       type: number
 *                       description: 重要性分數
 *                     Model:
 *                       type: string
 *                       description: 模型名稱
 *     responses:
 *       200:
 *         description: 成功計算可靠性指數
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reliability_index:
 *                   type: number
 *                   description: 可靠性指數
 *                 rf_importance_score:
 *                   type: array
 *                   description: RF模型特徵重要性分數
 *                   items:
 *                     type: object
 *                     properties:
 *                       Feature:
 *                         type: string
 *                         description: 特徵名稱
 *                       Importance:
 *                         type: number
 *                         description: 重要性分數
 *                       Model:
 *                         type: string
 *                         description: 模型名稱
 *                 xgb_importance_score:
 *                   type: array
 *                   description: XGBoost模型特徵重要性分數
 *                   items:
 *                     type: object
 *                     properties:
 *                       Feature:
 *                         type: string
 *                         description: 特徵名稱
 *                       Importance:
 *                         type: number
 *                         description: 重要性分數
 *                       Model:
 *                         type: string
 *                         description: 模型名稱
 *                 execution_times:
 *                   type: object
 *                   description: 執行時間統計
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/reliability_index",
  validateRequest({
    body: Joi.object({
      feature_importance_1: Joi.array()
        .items(
          Joi.object({
            Feature: Joi.string().required(),
            Importance: Joi.number().required(),
            Model: Joi.string().required(),
          })
        )
        .required(),
      feature_importance_2: Joi.array()
        .items(
          Joi.object({
            Feature: Joi.string().required(),
            Importance: Joi.number().required(),
            Model: Joi.string().required(),
          })
        )
        .required(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/reliability_index",
      req.body
    );
    res.json(result);
  })
);

/** 隨機森林與XGBoost模型比較分析
 * @swagger
 * /api/external/iym/machine_learning/rf_xgb_comparison:
 *   post:
 *     summary: 隨機森林與XGBoost模型比較分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             start_date: "2024-07-01"
 *             end_date: "2024-11-01"
 *             part_no: "QE2825"
 *             defect_code: "S03"
 *             process_name: "(R233-01)RTR蝕刻-剝膜,(R223-S01)RTR曝光,(R232-02)RTR局部銅電剝膜,(R221-01)RTR 乾膜前處理,(R233-03)RTR線路顯影,(R212-S01)RTR VCP全局面鍍銅,(R220-S01)RTR乾膜壓合"
 *           schema:
 *             type: object
 *             required:
 *               - start_date
 *               - end_date
 *               - part_no
 *               - defect_code
 *               - process_name
 *             properties:
 *               start_date:
 *                 type: string
 *                 format: date
 *                 description: 開始日期 (YYYY-MM-DD)
 *                 example: "2024-10-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 description: 結束日期 (YYYY-MM-DD)
 *                 example: "2024-11-01"
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "QE2825"
 *               defect_code:
 *                 type: string
 *                 description: 不良項目
 *                 example: "S03"
 *               process_name:
 *                 type: string
 *                 description: 製程名稱
 *                 example: "(R212-S01)RTR VCP全局面鍍銅"
 *               top_n:
 *                 type: integer
 *                 description: 選填，預設5
 *     responses:
 *       200:
 *         description: 成功執行模型比較
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 random_forest_result:
 *                   type: object
 *                   description: 隨機森林分析結果
 *                 xgboost_result:
 *                   type: object
 *                   description: XGBoost分析結果
 *                 reliability_index_result:
 *                   type: object
 *                   description: 可靠性指數結果
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
router.post(
  "/iym/machine_learning/rf_xgb_comparison",
  validateRequest({
    body: Joi.object({
      start_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      end_date: Joi.string()
        .pattern(/^\d{4}-\d{2}-\d{2}$/)
        .required(),
      part_no: Joi.string().min(1).required(),
      defect_code: Joi.string().min(1).required(),
      process_name: Joi.string().min(1).required(),
      top_n: Joi.number().integer().min(1).default(5),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/rf_xgb_comparison",
      req.body
    );
    res.json(result);
  })
);

/** 邏輯迴歸分析(用於參數分析)
 * @swagger
 * /api/external/iym/machine_learning/logistic_regression_for_parameter:
 *   post:
 *     summary: Logistic Regression 分類模型分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             variables: ["TEST_NUMBER", "solder_life", "PRINT_SPEED", "PRINT_PRESSURE", "SNAP_OFF_DISTANCE", "SNAP_OFF_SPEED", "TEMPRATURE", "HUMIDITY", "TD", "MD", "tension_mean", "tension_range", "SQUEEGEE_SUM_COUNT"]
 *             target: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *     responses:
 *       200:
 *         description: 成功執行邏輯迴歸分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   Feature:
 *                     type: string
 *                     description: 特徵名稱
 *                   Importance:
 *                     type: number
 *                     description: 重要性分數
 *                   Model:
 *                     type: string
 *                     description: 模型名稱
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/logistic_regression_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/logistic_regression_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** 決策樹交互作用分析
 * @swagger
 * /api/external/iym/machine_learning/decision_tree_interaction_for_parameter:
 *   post:
 *     summary: 決策樹交互作用分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             variables: [
 *               "TEST_NUMBER",
 *               "solder_life",
 *               "PRINT_SPEED",
 *               "PRINT_PRESSURE",
 *               "SNAP_OFF_DISTANCE",
 *               "SNAP_OFF_SPEED",
 *               "TEMPRATURE",
 *               "HUMIDITY",
 *               "TD",
 *               "MD",
 *               "tension_mean",
 *               "tension_range",
 *               "SQUEEGEE_SUM_COUNT"
 *             ]
 *             target: "ooc_rate"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc_rate)
 *                 default: "ooc_rate"
 *               method:
 *                 type: string
 *                 description: 分析方法(選填，預設為自動判斷)
 *                 enum: ["anova", "class"]
 *     responses:
 *       200:
 *         description: 成功執行決策樹交互作用分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   Node:
 *                     type: string
 *                   RulePath:
 *                     type: string
 *                   Prediction:
 *                     type: number
 *                   Importance:
 *                     type: number
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/machine_learning/decision_tree_interaction_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().required(),
      work_order: Joi.string().required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc_rate"),
      method: Joi.string().valid("anova", "class"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/decision_tree_interaction_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** Lasso回歸模型分析
 * @swagger
 * /api/external/iym/machine_learning/lasso_for_parameter:
 *   post:
 *     summary: Lasso回歸模型分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             variables: [
 *               "TEST_NUMBER",
 *               "solder_life",
 *               "PRINT_SPEED",
 *               "PRINT_PRESSURE",
 *               "SNAP_OFF_DISTANCE",
 *               "SNAP_OFF_SPEED",
 *               "TEMPRATURE",
 *               "HUMIDITY",
 *               "TD",
 *               "MD",
 *               "tension_mean",
 *               "tension_range",
 *               "SQUEEGEE_SUM_COUNT"
 *             ]
 *             target: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *     responses:
 *       200:
 *         description: 成功執行Lasso回歸分析
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   Feature:
 *                     type: string
 *                     description: 特徵名稱
 *                   Importance:
 *                     type: number
 *                     description: 重要性分數
 *                   Model:
 *                     type: string
 *                     description: 模型名稱(Lasso)
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/lasso_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/lasso_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** MARS交互作用分析
 * @swagger
 * /api/external/iym/machine_learning/mars_interaction_for_parameter:
 *   post:
 *     summary: MARS交互作用分析
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "*********, *********, *********"
 *             variables: [
 *               "TEST_NUMBER",
 *               "solder_life",
 *               "PRINT_SPEED",
 *               "PRINT_PRESSURE",
 *               "SNAP_OFF_DISTANCE",
 *               "SNAP_OFF_SPEED",
 *               "TEMPRATURE",
 *               "HUMIDITY",
 *               "TD",
 *               "MD",
 *               "tension_mean",
 *               "tension_range",
 *               "SQUEEGEE_SUM_COUNT"
 *             ]
 *             target: "ooc"
 *             interaction_number: 5
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - variables
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *               work_order:
 *                 type: string
 *                 description: 工單號碼(以逗號分隔)
 *               variables:
 *                 type: array
 *                 description: 要分析的參數列表
 *                 items:
 *                   type: string
 *               target:
 *                 type: string
 *                 description: 目標變數(選填，預設為ooc)
 *                 default: "ooc"
 *               interaction_number:
 *                 type: integer
 *                 description: 返回的交互作用項數量(選填，預設為3)
 *                 default: 3
 *     responses:
 *       200:
 *         description: 成功執行MARS交互作用分析
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 interaction_terms:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       Feature:
 *                         type: string
 *                         description: 互動項名稱
 *                       Coefficient:
 *                         type: number
 *                         description: 係數
 *                       Importance:
 *                         type: number
 *                         description: 重要性分數
 *       401:
 *         $ref: "#/components/responses/UnauthorizedError"
 *       403:
 *         $ref: "#/components/responses/ForbiddenError"
 */
router.post(
  "/iym/machine_learning/mars_interaction_for_parameter",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      variables: Joi.array().items(Joi.string()).required(),
      target: Joi.string().default("ooc"),
      interaction_number: Joi.number().integer().min(1).default(3),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/mars_interaction_for_parameter",
      req.body
    );
    res.json(result);
  })
);

/** 取得SOM分群分析結果
 * @swagger
 * /api/external/iym/machine_learning/som_clustering:
 *   post:
 *     summary: 使用 SOM 進行資料分群
 *     tags: [ExternalAPI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             part_no: "ZE18113-A"
 *             work_order: "Z04C00541, *********, *********, *********, *********"
 *             features: ["MD", "TD", "PRINT_PRESSURE", "HUMIDITY", "PRINT_SPEED", "TEMPRATURE"]
 *             target: "ooc"
 *           schema:
 *             type: object
 *             required:
 *               - part_no
 *               - work_order
 *               - features
 *             properties:
 *               part_no:
 *                 type: string
 *                 description: 品目代碼
 *                 example: "ZE18113-A"
 *               work_order:
 *                 type: string
 *                 description: 工單號碼，多個用逗號分隔
 *                 example: "Z04C00541, *********, *********, *********, *********"
 *               features:
 *                 type: array
 *                 description: 要分析的特徵欄位
 *                 items:
 *                   type: string
 *                 example: ["MD", "TD", "PRINT_PRESSURE", "HUMIDITY", "PRINT_SPEED", "TEMPRATURE"]
 *               target:
 *                 type: string
 *                 description: 目標變數 (選填，預設為 "ooc")
 *                 example: "ooc"
 *     responses:
 *       200:
 *         description: 成功取得 SOM 分群結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 threshold:
 *                   type: number
 *                   description: 分群閾值
 *                   example: 0.45
 *                 qe:
 *                   type: number
 *                   description: 量化誤差
 *                   example: 0.1234
 *                 num_clusters:
 *                   type: integer
 *                   description: 分群數量
 *                   example: 2
 *                 coord_info:
 *                   type: object
 *                   description: 座標點資訊
 *                   additionalProperties:
 *                     type: object
 *                     properties:
 *                       cluster:
 *                         type: integer
 *                         description: 群集編號
 *                         example: 1
 *                       count:
 *                         type: integer
 *                         description: 數量
 *                         example: 10
 *                       ooc_ratio:
 *                         type: number
 *                         description: OOC 比例
 *                         example: 0.2
 *                 data:
 *                   type: array
 *                   description: 分群後的資料
 *                   items:
 *                     type: object
 *                     properties:
 *                       som_cluster:
 *                         type: integer
 *                         description: 分群標籤
 *                         example: 1
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       400:
 *         description: 請求參數錯誤
 *       500:
 *         description: 伺服器內部錯誤
 */
router.post(
  "/iym/machine_learning/som_clustering",
  validateRequest({
    body: Joi.object({
      part_no: Joi.string().min(1).required(),
      work_order: Joi.string().min(1).required(),
      features: Joi.array().items(Joi.string()).min(1).required(),
      target: Joi.string(),
    }),
  }),
  asyncHandler(async (req, res) => {
    const result = await externalApiService.post(
      "iym/machine_learning/som_clustering",
      req.body
    );
    res.json(result);
  })
);

module.exports = router;
