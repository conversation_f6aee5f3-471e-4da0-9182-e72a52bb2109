const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middlewares/auth");
const checkPermission = require("../middlewares/checkPermission");
const flowInstanceController = require("../controllers/flowInstanceController");

// 將 authenticateToken 中間件應用到所有路由
router.use(authenticateToken);

/**
 * @swagger
 * components:
 *   schemas:
 *     FlowInstance:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 流程實例ID
 *         projectId:
 *           type: string
 *           description: 專案ID
 *         templateId:
 *           type: string
 *           description: 模板ID
 *         nodes:
 *           type: array
 *           description: 節點列表
 *         edges:
 *           type: array
 *           description: 邊緣列表
 *         status:
 *           type: string
 *           enum: [draft, running, paused, completed, failed, stopped]
 *         createdAt:
 *           type: string
 *           description: 創建日期
 *         updatedAt:
 *           type: string
 *           description: 更新日期
 */

/**
 * @swagger
 * tags:
 *   name: FlowInstances
 *   description: 工作流程實例
 */

/**
 * @swagger
 * /api/flow-instances:
 *   get:
 *     summary: 獲取所有流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *         description: 按專案ID過濾
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, running, paused, completed, failed, stopped, deleted]
 *         description: 按狀態過濾
 *       - in: query
 *         name: showDeleted
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: 是否包含已刪除的流程實例。設為true時將返回包括已刪除在內的所有流程實例，忽略status的deleted值
 *     responses:
 *       200:
 *         description: 成功獲取流程實例列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/FlowInstance'
 */
router.get(
  "/",
  checkPermission(["VIEW_FLOW_INSTANCES"]),
  flowInstanceController.getAllInstances
);

/**
 * @swagger
 * /api/flow-instances/{id}:
 *   get:
 *     summary: 獲取單個流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流程實例ID
 *       - in: query
 *         name: showDeleted
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: 是否顯示已刪除的流程實例，設為true時將返回已刪除的流程實例數據
 *     responses:
 *       200:
 *         description: 成功獲取流程實例
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/FlowInstance'
 */
router.get(
  "/:id",
  checkPermission(["VIEW_FLOW_INSTANCES"]),
  flowInstanceController.getInstanceById
);

/**
 * @swagger
 * /api/flow-instances:
 *   post:
 *     summary: 創建流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *               - templateId
 *               - nodes
 *               - edges
 *             properties:
 *               projectId:
 *                 type: string
 *               templateId:
 *                 type: string
 *               nodes:
 *                 type: array
 *               edges:
 *                 type: array
 *     responses:
 *       201:
 *         description: 流程實例創建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/FlowInstance'
 */
router.post(
  "/",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.createInstance
);

/**
 * @swagger
 * /api/flow-instances/{id}:
 *   put:
 *     summary: 更新流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nodes:
 *                 type: array
 *               edges:
 *                 type: array
 *     responses:
 *       200:
 *         description: 流程實例更新成功
 */
router.put(
  "/:id",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.updateInstance
);

/**
 * @swagger
 * /api/flow-instances/{id}/start:
 *   put:
 *     summary: 啟動流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 流程實例啟動成功
 */
router.put(
  "/:id/start",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.startInstance
);

/**
 * @swagger
 * /api/flow-instances/{id}/nodes/{nodeId}/execute:
 *   post:
 *     summary: 執行節點
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流程實例ID
 *       - in: path
 *         name: nodeId
 *         required: true
 *         schema:
 *           type: string
 *         description: 節點ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               input:
 *                 type: object
 *                 description: 節點輸入數據
 *     responses:
 *       200:
 *         description: 節點執行成功
 */
router.post(
  "/:id/nodes/:nodeId/execute",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.executeNode
);

/**
 * @swagger
 * /api/flow-instances/{id}/logs:
 *   get:
 *     summary: 獲取實例日誌
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 成功獲取日誌
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   type:
 *                     type: string
 *                   nodeId:
 *                     type: string
 *                   message:
 *                     type: string
 *                   timestamp:
 *                     type: string
 *                     format: date-time
 */
router.get(
  "/:id/logs",
  checkPermission(["VIEW_FLOW_INSTANCES"]),
  flowInstanceController.getInstanceLogs
);

/**
 * @swagger
 * /api/flow-instances/{id}/nodes/{nodeId}/logs:
 *   get:
 *     summary: 獲取節點日誌
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: nodeId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 成功獲取節點日誌
 */
router.get(
  "/:id/nodes/:nodeId/logs",
  checkPermission(["VIEW_FLOW_INSTANCES"]),
  flowInstanceController.getNodeLogs
);

/**
 * @swagger
 * /api/flow-instances/{id}:
 *   delete:
 *     summary: 刪除流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流程實例ID
 *       - in: query
 *         name: force
 *         schema:
 *           type: boolean
 *         description: 管理員強制刪除標誌，設為true時允許管理員刪除任何狀態的流程實例
 *     responses:
 *       200:
 *         description: 流程實例刪除成功
 *       400:
 *         description: 只有草稿和失敗狀態的流程實例可以刪除，或者需要管理員權限進行強制刪除
 *       404:
 *         description: 流程實例不存在
 */
router.delete(
  "/:id",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.deleteInstance
);

/**
 * @swagger
 * /api/flow-instances/{id}/restore:
 *   post:
 *     summary: 恢復已刪除的流程實例
 *     tags: [FlowInstances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 流程實例ID
 *     responses:
 *       200:
 *         description: 流程實例恢復成功
 *       400:
 *         description: 流程實例未被刪除，無需恢復
 *       404:
 *         description: 流程實例不存在
 */
router.post(
  "/:id/restore",
  checkPermission(["MANAGE_FLOW_INSTANCES"]),
  flowInstanceController.restoreInstance
);

module.exports = router;
