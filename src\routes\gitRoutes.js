const express = require("express");
const router = express.Router();
const { authenticateToken } = require("../middlewares/auth");
const gitController = require("../controllers/gitController");

/**
 * @swagger
 * components:
 *   schemas:
 *     GitCommit:
 *       type: object
 *       properties:
 *         sha:
 *           type: string
 *           description: 提交的 SHA 值
 *         message:
 *           type: string
 *           description: 提交訊息
 *         author:
 *           type: object
 *           description: 作者
 *     GitBranch:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 分支名稱
 *         commit:
 *           type: object
 *           description: 提交訊息
 *         author:
 *           type: object
 *           description: 作者
 *         date:
 *           type: string
 *           description: 提交日期
 */

/**
 * @swagger
 * /api/git/git-history:
 *   get:
 *     tags: [Git]
 *     summary: 獲取 Git 歷史
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: project
 *         description: 專案名稱(frontend, backend, analysis)
 *         required: false
 *         schema:
 *           type: string

 *       - in: query
 *         name: branch
 *         description: 分支名稱(analysis 是 main, frontend 是 experiment, backend 是 experiment)
 *         required: false
 *         schema:
 *           type: string
 *       - in: query
 *         name: since
 *         description: 時間範圍
 *         required: false
 *         schema:
 *           type: string
 *       - in: query
 *         name: maxCount
 *         description: 最大數量
 *         required: false
 *         schema:
 *           type: number
 *     responses:
 *       200:
 *         description: 成功獲取 Git 歷史
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/GitCommit'
 *       500:
 *         description: 獲取 Git 歷史時出錯
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: 錯誤訊息
 */
router.get("/git-history", authenticateToken, gitController.getGitHistory);

/**
 * @swagger
 * /api/git/git-branch:
 *   get:
 *     tags: [Git]
 *     summary: 獲取 Git 分支
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: project
 *         description: 專案名稱
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 成功獲取 Git 分支
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/GitBranch'
 *       500:
 *         description: 獲取 Git 分支時出錯
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: 錯誤訊息
 */
router.get("/git-branch", authenticateToken, gitController.getGitBranch);

module.exports = router;
