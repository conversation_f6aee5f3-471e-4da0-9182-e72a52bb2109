const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const { v4: uuidv4 } = require("uuid");
const issueController = require("../controllers/issueController");
const { authenticateToken } = require("../middlewares/auth");
const { logger } = require("../utils/logger");
const { fullPaths } = require("../config/paths");

/**
 * @swagger
 * components:
 *   schemas:
 *     IssueTicket:
 *       type: object
 *       required:
 *         - title
 *         - content
 *         - type
 *         - category
 *         - priority
 *       properties:
 *         id:
 *           type: string
 *           description: 問題單唯一識別碼
 *         title:
 *           type: string
 *           description: 問題標題
 *         content:
 *           type: string
 *           description: 問題內容 (Markdown 格式)
 *         type:
 *           type: string
 *           enum: [bug, feature, task]
 *           description: 問題類型
 *         category:
 *           type: string
 *           enum: [frontend, backend, analysis]
 *           description: 問題分類
 *         systemCode:
 *           type: string
 *           default: IYM
 *           description: 系統代碼
 *         priority:
 *           type: string
 *           enum: [low, medium, high, critical]
 *           description: 優先級
 *         status:
 *           type: string
 *           enum: [open, in_progress, resolved, closed]
 *           default: open
 *           description: 問題狀態
 *         screenshot:
 *           type: string
 *           description: 截圖檔案路徑
 *         comment:
 *           type: string
 *           description: 評論/備註
 *         reporterId:
 *           type: string
 *           description: 回報者ID
 *         assigneeId:
 *           type: string
 *           description: 負責人ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 建立時間
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新時間
 */

// 配置截圖存儲
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, fullPaths.issueDir);
  },
  filename: (req, file, cb) => {
    const fileExt = path.extname(file.originalname);
    const fileName = `${uuidv4()}${fileExt}`;
    cb(null, fileName);
  },
});

const uploadScreenshot = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

/**
 * @swagger
 * /api/issues:
 *   get:
 *     tags: [Issues]
 *     summary: 獲取所有問題單
 *     description: 獲取所有問題單，可以根據狀態、分類等進行篩選
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [open, in_progress, resolved, closed]
 *         description: 問題狀態
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [frontend, backend, analysis]
 *         description: 問題分類
 *       - in: query
 *         name: systemCode
 *         schema:
 *           type: string
 *         description: 系統代碼
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: 優先級
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [bug, feature, task]
 *         description: 問題類型
 *     responses:
 *       200:
 *         description: 成功獲取問題單列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/IssueTicket'
 *       401:
 *         description: 未授權
 */
router.get("/", authenticateToken, issueController.getAllIssues);

/**
 * @swagger
 * /api/issues/upload:
 *   post:
 *     tags: [Issues]
 *     summary: 上傳問題截圖
 *     description: 單獨上傳問題截圖
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - screenshot
 *             properties:
 *               screenshot:
 *                 type: string
 *                 format: binary
 *                 description: 問題截圖
 *     responses:
 *       200:
 *         description: 截圖上傳成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 filename:
 *                   type: string
 *                   description: 儲存的檔案名稱
 *                 path:
 *                   type: string
 *                   description: 檔案存取路徑
 *                 size:
 *                   type: number
 *                   description: 檔案大小
 *                 mimetype:
 *                   type: string
 *                   description: 檔案類型
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 */
router.post(
  "/upload",
  authenticateToken,
  uploadScreenshot.single("screenshot"),
  issueController.uploadScreenshot
);

/**
 * @swagger
 * /api/issues/{id}:
 *   get:
 *     tags: [Issues]
 *     summary: 獲取特定問題單
 *     description: 根據ID獲取特定問題單的詳細資訊
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 問題單ID
 *     responses:
 *       200:
 *         description: 成功獲取問題單
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IssueTicket'
 *       401:
 *         description: 未授權
 *       404:
 *         description: 問題單不存在
 */
router.get("/:id", authenticateToken, issueController.getIssueById);

/**
 * @swagger
 * /api/issues:
 *   post:
 *     tags: [Issues]
 *     summary: 創建問題單
 *     description: 創建新的問題單，可以同時上傳截圖
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - content
 *               - type
 *               - category
 *               - priority
 *             properties:
 *               title:
 *                 type: string
 *                 description: 問題標題
 *               content:
 *                 type: string
 *                 description: 問題內容 (Markdown 格式)
 *               type:
 *                 type: string
 *                 enum: [bug, feature, task]
 *                 description: 問題類型
 *               category:
 *                 type: string
 *                 enum: [frontend, backend, analysis]
 *                 description: 問題分類
 *               systemCode:
 *                 type: string
 *                 default: IYM
 *                 description: 系統代碼
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 description: 優先級
 *               status:
 *                 type: string
 *                 enum: [open, in_progress, resolved, closed]
 *                 default: open
 *                 description: 問題狀態
 *               comment:
 *                 type: string
 *                 description: 評論/備註
 *               assigneeId:
 *                 type: string
 *                 description: 負責人ID
 *               screenshot:
 *                 type: string
 *                 format: binary
 *                 description: 問題截圖
 *     responses:
 *       201:
 *         description: 問題單創建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IssueTicket'
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 */
router.post(
  "/",
  authenticateToken,
  uploadScreenshot.single("screenshot"),
  issueController.createIssue
);

/**
 * @swagger
 * /api/issues/{id}:
 *   put:
 *     tags: [Issues]
 *     summary: 更新問題單
 *     description: 更新特定問題單的資訊，可以同時更新截圖
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 問題單ID
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: 問題標題
 *               content:
 *                 type: string
 *                 description: 問題內容 (Markdown 格式)
 *               type:
 *                 type: string
 *                 enum: [bug, feature, task]
 *                 description: 問題類型
 *               category:
 *                 type: string
 *                 enum: [frontend, backend, analysis]
 *                 description: 問題分類
 *               systemCode:
 *                 type: string
 *                 description: 系統代碼
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 description: 優先級
 *               status:
 *                 type: string
 *                 enum: [open, in_progress, resolved, closed]
 *                 description: 問題狀態
 *               comment:
 *                 type: string
 *                 description: 評論/備註
 *               assigneeId:
 *                 type: string
 *                 description: 負責人ID
 *               screenshot:
 *                 type: string
 *                 format: binary
 *                 description: 問題截圖
 *     responses:
 *       200:
 *         description: 問題單更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IssueTicket'
 *       400:
 *         description: 請求參數錯誤
 *       401:
 *         description: 未授權
 *       404:
 *         description: 問題單不存在
 */
router.put(
  "/:id",
  authenticateToken,
  uploadScreenshot.single("screenshot"),
  issueController.updateIssue
);

/**
 * @swagger
 * /api/issues/{id}:
 *   delete:
 *     tags: [Issues]
 *     summary: 刪除問題單
 *     description: 刪除特定問題單
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: 問題單ID
 *     responses:
 *       200:
 *         description: 問題單刪除成功
 *       401:
 *         description: 未授權
 *       404:
 *         description: 問題單不存在
 */
router.delete("/:id", authenticateToken, issueController.deleteIssue);

module.exports = router;
