const express = require("express");
const router = express.Router();
const orgEmployeeController = require("../controllers/orgEmployeeController");
const { authenticateToken } = require("../middlewares/auth");

/**
 * @swagger
 * components:
 *   schemas:
 *     OrgEmployee:
 *       type: object
 *       properties:
 *         accountGuid:
 *           type: string
 *           description: 帳號代碼
 *         groupId:
 *           type: string
 *           description: 部門關聯
 *         groupName:
 *           type: string
 *           description: 部門名稱
 *         groupCode:
 *           type: string
 *           description: 部門代碼
 *         name:
 *           type: string
 *           description: 姓名
 *         nickname:
 *           type: string
 *           description: 暱稱
 *         email:
 *           type: string
 *           description: 電子郵件
 *         employeeNo:
 *           type: string
 *           description: 員工編號
 */

/**
 * @swagger
 * /api/org-employees:
 *   get:
 *     tags: [OrgEmployees]
 *     summary: 取得所有員工資料
 *     description: 取得所有員工資料
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功取得所有員工資料
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrgEmployee'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 25
 *                     total:
 *                       type: integer
 *                       example: 100
 *                     totalPages:
 *                       type: integer
 *                       example: 4
 */
router.get("/", authenticateToken, orgEmployeeController.getAllEmployees);

/**
 * @swagger
 * /api/org-employees/search:
 *   get:
 *     tags: [OrgEmployees]
 *     summary: 搜尋員工資料
 *     description: 根據條件搜尋員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 姓名關鍵字
 *       - in: query
 *         name: employeeNo
 *         schema:
 *           type: string
 *         description: 員工編號
 *       - in: query
 *         name: email
 *         schema:
 *           type: string
 *         description: 電子郵件
 *       - in: query
 *         name: groupId
 *         schema:
 *           type: string
 *         description: 部門代碼
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 頁碼
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 25
 *         description: 每頁筆數
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           default: employeeNo
 *         description: 排序欄位
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           default: desc
 *           enum: [asc, desc]
 *         description: 排序方式
 *     responses:
 *       200:
 *         description: 成功取得搜尋結果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrgEmployee'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 25
 *                     total:
 *                       type: integer
 *                       example: 100
 *                     totalPages:
 *                       type: integer
 *                       example: 4
 */
router.get("/search", authenticateToken, orgEmployeeController.searchEmployees);

/**
 * @swagger
 * /api/org-employees/account/{accountGuid}:
 *   get:
 *     tags: [OrgEmployees]
 *     summary: 根據帳號代碼取得員工資料
 *     description: 根據帳號代碼取得員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountGuid
 *         schema:
 *           type: string
 *         description: 帳號代碼
 *     responses:
 *       200:
 *         description: 成功取得員工資料
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OrgEmployee'
 */
router.get(
  "/account/:accountGuid",
  authenticateToken,
  orgEmployeeController.getEmployeeByAccountGuid
);

/**
 * @swagger
 * /api/org-employees/employee-no/{employeeNo}:
 *   get:
 *     tags: [OrgEmployees]
 *     summary: 根據員工編號取得員工資料
 *     description: 根據員工編號取得員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: employeeNo
 *         schema:
 *           type: string
 *         description: 員工編號
 *     responses:
 *       200:
 *         description: 成功取得員工資料
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OrgEmployee'
 */
router.get(
  "/employee-no/:employeeNo",
  authenticateToken,
  orgEmployeeController.getEmployeeByEmployeeNo
);

/**
 * @swagger
 * /api/org-employees/group/{groupId}:
 *   get:
 *     tags: [OrgEmployees]
 *     summary: 根據部門代碼取得員工資料
 *     description: 根據部門代碼取得員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: groupId
 *         schema:
 *           type: string
 *         description: 部門代碼
 *     responses:
 *       200:
 *         description: 成功取得部門員工資料
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/OrgEmployee'
 */
router.get(
  "/group/:groupId",
  authenticateToken,
  orgEmployeeController.getEmployeesByGroupId
);

/**
 * @swagger
 * /api/org-employees:
 *   post:
 *     tags: [OrgEmployees]
 *     summary: 建立員工資料
 *     description: 建立新的員工資料
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OrgEmployee'
 *     responses:
 *       201:
 *         description: 成功建立員工資料
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OrgEmployee'
 */
router.post("/", authenticateToken, orgEmployeeController.createEmployee);

/**
 * @swagger
 * /api/org-employees/{accountGuid}:
 *   put:
 *     tags: [OrgEmployees]
 *     summary: 更新員工資料
 *     description: 根據帳號代碼更新員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountGuid
 *         schema:
 *           type: string
 *         description: 帳號代碼
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OrgEmployee'
 *     responses:
 *       200:
 *         description: 成功更新員工資料
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OrgEmployee'
 */
router.put(
  "/:accountGuid",
  authenticateToken,
  orgEmployeeController.updateEmployee
);

/**
 * @swagger
 * /api/org-employees/{accountGuid}:
 *   delete:
 *     tags: [OrgEmployees]
 *     summary: 刪除員工資料
 *     description: 根據帳號代碼刪除員工資料
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountGuid
 *         schema:
 *           type: string
 *         description: 帳號代碼
 *     responses:
 *       200:
 *         description: 成功刪除員工資料
 */
router.delete(
  "/:accountGuid",
  authenticateToken,
  orgEmployeeController.deleteEmployee
);

module.exports = router;
