const express = require("express");
const router = express.Router();
const orgGroupController = require("../controllers/orgGroupController");
const { authenticateToken } = require("../middlewares/auth");

/**
 * @swagger
 * components:
 *   schemas:
 *     OrgGroups:
 *       type: object
 *       properties:
 *         groupId:
 *           type: string
 *           description: 群組代碼
 *         groupType:
 *           type: string
 *           description: 群組類別（Department...）
 *         groupName:
 *           type: string
 *           description: 群組名稱
 *         parentGroupId:
 *           type: string
 *           description: 父群組代碼
 *         lft:
 *           type: integer
 *           description: 左排序
 *         rgt:
 *           type: integer
 *           description: 右排序
 *         lev:
 *           type: integer
 *           description: 階層
 *         groupCode:
 *           type: string
 *           description: 群組代號
 *         active:
 *           type: boolean
 *           description: 是否啟用
 *         companyId:
 *           type: string
 *           description: 公司別/ID
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新時間
 */

/**
 * @swagger
 * /api/org-groups:
 *   get:
 *     summary: 獲取所有部門列表
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功獲取部門列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrgGroups'
 */
router.get("/", authenticateToken, orgGroupController.getAllGroups);

/**
 * @swagger
 * /api/org-groups/tree:
 *   get:
 *     summary: 獲取部門樹狀結構
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: parentGroupId
 *         schema:
 *           type: string
 *         description: 父部門ID，預設值為 ROOT
 *     responses:
 *       200:
 *         description: 成功獲取部門樹狀結構
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrgGroups'
 */
router.get("/tree", authenticateToken, orgGroupController.getGroupTree);

/**
 * @swagger
 * /api/org-groups/{groupId}:
 *   get:
 *     summary: 根據 ID 獲取部門資訊
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: 部門 ID
 *     responses:
 *       200:
 *         description: 成功獲取部門資訊
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/OrgGroups'
 */
router.get("/:groupId", authenticateToken, orgGroupController.getGroupById);

/**
 * @swagger
 * /api/org-groups:
 *   post:
 *     summary: 創建部門
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - groupId
 *               - groupName
 *             properties:
 *               groupId:
 *                 type: string
 *               groupType:
 *                 type: string
 *               groupName:
 *                 type: string
 *               parentGroupId:
 *                 type: string
 *               groupCode:
 *                 type: string
 *               active:
 *                 type: boolean
 *               companyId:
 *                 type: string
 *     responses:
 *       201:
 *         description: 部門創建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/OrgGroups'
 */
router.post("/", authenticateToken, orgGroupController.createGroup);

/**
 * @swagger
 * /api/org-groups/{groupId}:
 *   put:
 *     summary: 更新部門
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: 部門 ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               groupType:
 *                 type: string
 *               groupName:
 *                 type: string
 *               parentGroupId:
 *                 type: string
 *               groupCode:
 *                 type: string
 *               active:
 *                 type: boolean
 *               companyId:
 *                 type: string
 *     responses:
 *       200:
 *         description: 部門更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/OrgGroups'
 */
router.put("/:groupId", authenticateToken, orgGroupController.updateGroup);

/**
 * @swagger
 * /api/org-groups/{groupId}:
 *   delete:
 *     summary: 刪除部門
 *     tags: [OrgGroups]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: groupId
 *         required: true
 *         schema:
 *           type: string
 *         description: 部門 ID
 *     responses:
 *       200:
 *         description: 部門刪除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.delete("/:groupId", authenticateToken, orgGroupController.deleteGroup);

module.exports = router;
