const express = require("express");
const router = express.Router();
const { PrismaClient } = require("@prisma/client");
const { authenticateToken } = require("../middlewares/auth");
const { asyncHandler } = require("../utils/asyncHandler");
const { body, validationResult } = require("express-validator");
const { errorResponse, successResponse } = require("../utils/jsonResponse");
const { logger } = require("../utils/logger");

const prisma = new PrismaClient();

/**
 * @swagger
 * /api/report/projects-instances:
 *   post:
 *     summary: 獲取所有專案及其對應的實例
 *     tags: [Report]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   example: "3e0c7c3c-9984-40dd-ab55-d4f152259e91"
 *                 description: 選填的用戶ID陣列，用於過濾這些用戶創建或更新的數據
 *           example:
 *             userIds: ["3e0c7c3c-9984-40dd-ab55-d4f152259e91", "de934bfb-01c1-4477-adbf-63698b87a628"]
 *     responses:
 *       200:
 *         description: 成功獲取專案及實例列表
 *       400:
 *         description: 請求驗證失敗
 *         content:
 *           application/json:
 *             example:
 *               status: "error"
 *               message: "請求驗證失敗"
 *               errors: [
 *                 {
 *                   msg: "userIds必須是陣列",
 *                   param: "userIds",
 *                   location: "body"
 *                 }
 *               ]
 */
router.post(
  "/projects-instances",
  authenticateToken,
  [
    body("userIds")
      .optional()
      .isArray()
      .withMessage("userIds必須是陣列")
      .custom((value) => {
        if (value && !Array.isArray(value)) {
          throw new Error("userIds必須是陣列");
        }
        return true;
      }),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorResponse(res, 400, {
        message: "請求驗證失敗",
        errors: errors.array(),
      });
    }

    try {
      const { userIds } = req.body;

      // 構建查詢條件
      const where = {};
      if (userIds && userIds.length > 0) {
        where.OR = [
          { createdBy: { in: userIds } },
          { updatedBy: { in: userIds } },
        ];
      }

      // 獲取所有專案及其關聯的實例
      const projects = await prisma.project.findMany({
        where,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          flowInstances: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              updatedAt: true,
              creator: {
                select: {
                  id: true,
                  username: true,
                  avatar: true,
                },
              },
              template: {
                select: {
                  // id: true,
                  name: true,
                  description: true,
                },
              },
            },
            orderBy: {
              createdAt: "asc",
            },
          },
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      // 為每個專案的實例添加流水號
      const processedProjects = projects.map((project) => ({
        ...project,
        flowInstances: project.flowInstances.map((instance, index) => ({
          ...instance,
          sequenceNumber: index + 1, // 添加流水號
        })),
      }));

      successResponse(res, 200, processedProjects);
    } catch (error) {
      logger.error("獲取專案及實例列表失敗:", error);
      errorResponse(res, 500, "獲取專案及實例列表失敗");
    }
  })
);

module.exports = router;
