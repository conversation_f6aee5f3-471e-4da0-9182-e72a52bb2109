const express = require("express");
const router = express.Router();
const { PrismaClient } = require("@prisma/client");
const { authenticateToken } = require("../middlewares/auth");
const { asyncHandler } = require("../utils/asyncHandler");
const { validateRequest } = require("../middlewares/validateRequest");
const { body, param, validationResult } = require("express-validator");

const prisma = new PrismaClient();

/**
 * @swagger
 * /api/favorites:
 *   post:
 *     summary: 新增關注項目
 *     tags: [UserFavorites]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - resourceId
 *               - name
 *               - path
 *             properties:
 *               type:
 *                 type: string
 *                 description: 資源類型 (project/flow/report)
 *                 enum: [project, flow, report]
 *               resourceId:
 *                 type: string
 *                 description: 資源 ID (projectId/flowInstanceId/reportId)
 *               path:
 *                 type: string
 *                 description: URL 路徑
 *           example:
 *             type: "project"
 *             resourceId: "20938b55-3e21-4a36-bf8d-7ae092149137"
 *             path: "/projects/20938b55-3e21-4a36-bf8d-7ae092149137"
 *             createdBy: "85a371e8-bca9-42da-a7e0-45f7778b5749"
 *     responses:
 *       201:
 *         description: 關注成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: 請求格式錯誤或已存在相同關注
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 */
router.post(
  "/",
  authenticateToken,
  [
    body("type")
      .isString()
      .isIn(["project", "flow", "report"])
      .withMessage("資源類型必須是 project、flow 或 report"),
    body("path").isString().withMessage("URL 路徑必須是字串"),
    body("resourceId").isString().withMessage("資源 ID 必須是字串"),
  ],
  asyncHandler(async (req, res) => {
    // 驗證請求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: "請求驗證失敗",
        errors: errors.array(),
      });
    }

    const { type, path, resourceId } = req.body;
    const userId = req.user.id;

    try {
      const favorite = await prisma.userFavorite.upsert({
        where: {
          type_resourceId_createdBy: {
            type,
            resourceId,
            createdBy: userId,
          },
        },
        update: {},
        create: {
          type,
          path,
          resourceId,
          createdBy: userId,
        },
      });

      res.status(201).json({
        message: "關注成功",
        data: favorite,
      });
    } catch (error) {
      // 處理可能的錯誤
      console.error("關注失敗:", error);
      res.status(500).json({
        message: "關注處理失敗",
        error: error.message,
      });
    }
  })
);

/**
 * @swagger
 * /api/favorites:
 *   get:
 *     summary: 獲取用戶的所有關注
 *     tags: [UserFavorites]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: createdBy
 *         schema:
 *           type: string
 *         description: 創建者ID (可選，不傳則返回當前用戶的關注)
 *         example: "85a371e8-bca9-42da-a7e0-45f7778b5749"
 *     responses:
 *       200:
 *         description: 成功獲取關注列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *             example:
 *               data: [
 *                 {
 *                   "type": "project",
 *                   "path": "/projects/20938b55-3e21-4a36-bf8d-7ae092149137",
 *                   "resourceId": "20938b55-3e21-4a36-bf8d-7ae092149137",
 *                   "createdAt": "2023-07-15T08:30:00.000Z",
 *                   "createdBy": "85a371e8-bca9-42da-a7e0-45f7778b5749"
 *                 }
 *               ]
 *       401:
 *         description: 未授權
 *       500:
 *         description: 伺服器錯誤
 */
router.get(
  "/",
  authenticateToken,
  asyncHandler(async (req, res) => {
    const { createdBy } = req.query;
    const userId = createdBy || req.user.id;

    const favorites = await prisma.userFavorite.findMany({
      where: {
        createdBy: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    res.status(200).json({
      data: favorites,
    });
  })
);

/**
 * @swagger
 * /api/favorites/{type}/{resourceId}:
 *   delete:
 *     summary: 刪除關注項目
 *     tags: [UserFavorites]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         schema:
 *           type: string
 *           enum: [project, flow, report]
 *         required: true
 *         description: 資源類型
 *       - in: path
 *         name: resourceId
 *         schema:
 *           type: string
 *         required: true
 *         description: 資源ID
 *     responses:
 *       200:
 *         description: 刪除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: 未授權
 *       404:
 *         description: 關注項目不存在
 *       500:
 *         description: 伺服器錯誤
 */
router.delete(
  "/:type/:resourceId",
  authenticateToken,
  [
    param("type")
      .isString()
      .isIn(["project", "flow", "report"])
      .withMessage("資源類型必須是 project、flow 或 report"),
    param("resourceId").isString().withMessage("資源 ID 必須是字串"),
  ],
  asyncHandler(async (req, res) => {
    // 驗證請求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: "請求驗證失敗",
        errors: errors.array(),
      });
    }

    const { type, resourceId } = req.params;
    const userId = req.user.id;

    try {
      // 嘗試刪除記錄
      const deleteResult = await prisma.userFavorite.delete({
        where: {
          type_resourceId_createdBy: {
            type,
            resourceId,
            createdBy: userId,
          },
        },
      });

      res.status(200).json({
        message: "已移除關注",
      });
    } catch (error) {
      // 如果記錄不存在
      if (error.code === "P2025") {
        return res.status(404).json({
          message: "關注項目不存在",
        });
      }

      // 其他錯誤
      console.error("刪除關注失敗:", error);
      res.status(500).json({
        message: "刪除關注失敗",
        error: error.message,
      });
    }
  })
);

/**
 * @swagger
 * /api/favorites/details:
 *   post:
 *     summary: 批次獲取關注項目的名稱和描述
 *     tags: [UserFavorites]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - type
 *                     - resourceId
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [project, flow, report]
 *                     resourceId:
 *                       type: string
 *           example:
 *             items: [
 *               {
 *                 "type": "project",
 *                 "resourceId": "10780e79-2bfe-444f-86e9-d13cf3209d7c"
 *               },
 *               {
 *                 "type": "project",
 *                 "resourceId": "20938b55-3e21-4a36-bf8d-7ae092149137"
 *               },
 *               {
 *                 "type": "project",
 *                 "resourceId": "ea3a951a-d8c5-4c12-a966-a126ed9ce929"
 *               },
 *               {
 *                 "type": "flow",
 *                 "resourceId": "0efd0bb9-1d57-49a6-a43b-ef21ded4190b"
 *               },
 *               {
 *                 "type": "flow",
 *                 "resourceId": "213ae84e-a83a-4592-9280-69dba24a2403"
 *               },
 *               {
 *                 "type": "flow",
 *                 "resourceId": "26f61887-42b1-4ef2-9b39-6263b9a606c5"
 *               },
 *                {"type": "report",
 *                 "resourceId": "eb11e197-121f-49aa-86a9-0afbc0c57d6d"
 *                },
 *             ]
 *     responses:
 *       200:
 *         description: 成功獲取詳細資訊
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       resourceId:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *             example:
 *               data: [
 *                 {
 *                   "type": "project",
 *                   "resourceId": "10780e79-2bfe-444f-86e9-d13cf3209d7c",
 *                   "name": "專案A",
 *                   "description": "這是專案A的描述"
 *                 },
 *                 {
 *                   "type": "flow",
 *                   "resourceId": "0efd0bb9-1d57-49a6-a43b-ef21ded4190b",
 *                   "name": "流程B",
 *                   "description": "這是流程B的描述"
 *                 }
 *               ]
 */

router.post(
  "/details",
  authenticateToken,
  [
    body("items").isArray().withMessage("items 必須是陣列"),
    body("items.*.type")
      .isString()
      .isIn(["project", "flow", "report"])
      .withMessage("資源類型必須是 project、flow 或 report"),
    body("items.*.resourceId").isString().withMessage("資源 ID 必須是字串"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: "請求驗證失敗",
        errors: errors.array(),
      });
    }

    const { items } = req.body;

    // 按類型分組
    const groupedItems = items.reduce((acc, item) => {
      if (!acc[item.type]) {
        acc[item.type] = [];
      }
      acc[item.type].push(item.resourceId);
      return acc;
    }, {});

    const results = [];

    // 處理專案類型
    if (groupedItems.project?.length > 0) {
      const projects = await prisma.project.findMany({
        where: {
          id: {
            in: groupedItems.project,
          },
        },
        select: {
          id: true,
          name: true,
          description: true,
        },
      });

      results.push(
        ...projects.map((p) => ({
          type: "project",
          resourceId: p.id,
          name: p.name,
          description: p.description,
        }))
      );
    }

    // 處理流程類型和報表類型
    if (groupedItems.flow?.length > 0 || groupedItems.report?.length > 0) {
      const allIds = [
        ...(groupedItems.flow || []),
        ...(groupedItems.report || []),
      ];

      const flowInstances = await prisma.flowInstance.findMany({
        where: {
          id: {
            in: allIds,
          },
        },
        select: {
          id: true,
          template: {
            select: {
              name: true,
              description: true,
            },
          },
        },
      });

      // 為每個 flowInstance 檢查它是否存在於 flow 和 report 陣列中
      flowInstances.forEach((fi) => {
        if (groupedItems.flow?.includes(fi.id)) {
          results.push({
            type: "flow",
            resourceId: fi.id,
            name: fi.template.name,
            description: fi.template.description,
          });
        }

        if (groupedItems.report?.includes(fi.id)) {
          results.push({
            type: "report",
            resourceId: fi.id,
            name: fi.template.name,
            description: fi.template.description,
          });
        }
      });
    }

    res.status(200).json({
      data: results,
    });
  })
);

module.exports = router;
