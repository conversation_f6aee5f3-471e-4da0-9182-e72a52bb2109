const axios = require("axios");

// TODO:棄用 測試外部 API 連接
const testExternalApi = async (req, res) => {
  const url = "http://10.8.32.46:3001/api/v1/workspace/qms/stream-chat";
  const headers = {
    accept: "application/json",
    Authorization: "Bearer 3FJ7QRW-DGK46J5-PT2AP8K-C0XWAPY",
    "Content-Type": "application/json",
  };
  const data = {
    message: "vue 3 中，什麼是 markRaw?  請用繁體中文回覆",
    mode: "chat",
    sessionId: "identifier-to-partition-chats-by-external-id",
    attachments: [],
    reset: false,
  };

  try {
    console.log(data);
    const response = await axios.post(url, data, { headers });
    console.log(response);
  } catch (error) {
    console.log("外部 API 測試失敗:", error);
  }
};

testExternalApi();
