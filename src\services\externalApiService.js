require("dotenv").config();
const axios = require("axios");
const config = require("../config/externalApi");
const { logger } = require("../utils/logger");

/**
 * 外部 API 服務
 * 用於與第三方 API（如 Python 分析服務）進行通信
 */
class ExternalApiService {
  constructor() {
    // 從配置中獲取外部 API 的基礎 URL
    const baseHost = config.baseURL || "http://127.0.0.1:8000";
    this.baseURL = `${baseHost}/api/v1`;
    //logger.info(`外部 API 基礎 URL: ${this.baseURL}`);

    // 從配置中獲取 API Key
    this.apiKey = process.env.DEFAULT_API_KEY || config?.apiKey;
    this.apiKeyHeader = "X-API-Key";
    //logger.info(`外部 API Key 頭部: ${this.apiKeyHeader}`);

    if (!this.apiKey) {
      logger.warn(
        "警告: API Key 未設置，請確認環境變數 DEFAULT_API_KEY 或配置檔案中的 apiKey"
      );
    } else {
      //logger.info(`API Key 已設置 (前綴: ${this.apiKey.substring(0, 8)}...)`);
    }

    // 創建 axios 實例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: config.timeout,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        [this.apiKeyHeader]: this.apiKey,
      },
      // 添加額外的配置選項
      maxRedirects: 5,
      validateStatus: (status) => status >= 200 && status < 500, // 只有狀態碼大於等於500才會被視為錯誤
      proxy: false, // 禁用代理
      // 使用自定義的 DNS 查找
      lookup: undefined,
      // 添加額外的 http agent 配置
      httpAgent: new (require("http").Agent)({
        keepAlive: true,
        timeout: 0,
        family: 4, // 強制使用 IPv4
      }),
    });

    // 添加請求攔截器
    this.client.interceptors.request.use(
      (config) => {
        const fullUrl = `${config.baseURL}${config.url}`;
        //TODO: o回頭開
        // logger.info("發送外部API請求", {
        //   method: config.method,
        //   url: fullUrl,
        //   params: config.params,
        //   data: config.data,
        //   headers: {
        //     ...config.headers,
        //     [this.apiKeyHeader]: "******", // 隱藏實際的 API Key
        //   },
        // });
        return config;
      },
      (error) => {
        // logger.error("外部API請求錯誤", {
        //   message: error.message,
        //   stack: error.stack,
        // });
        return Promise.reject(error);
      }
    );

    // 添加響應攔截器
    this.client.interceptors.response.use(
      (response) => {
        // logger.info("收到外部API響應", {
        //   status: response.status,
        //   statusText: response.statusText,
        //   headers: response.headers,
        //   data: response.data,
        // });
        return response.data;
      },
      (error) => {
        logger.error("外部API響應錯誤", {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          config: {
            url: error.config?.url,
            method: error.config?.method,
            baseURL: error.config?.baseURL,
          },
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * 決定請求的超時時間
   * @param {string} url - 請求路徑
   * @returns {number} - 超時時間（毫秒）
   */
  getTimeoutForRequest(url) {
    if (url.includes("machine_learning")) {
      return config.longRunningRequests.mlTimeout;
    } else if (url.includes("analysis") || url.includes("basic_stats")) {
      return config.longRunningRequests.analysisTimeout;
    }
    return config.timeout;
  }

  /**
   * 發送 GET 請求到外部 API
   * @param {string} url - 請求路徑
   * @param {Object} params - 查詢參數
   * @returns {Promise<Object>} - 響應數據
   */
  async get(url, params = {}) {
    try {
      return await this.client.get(url, {
        params,
        timeout: this.getTimeoutForRequest(url),
      });
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * 發送 POST 請求到外部 API
   * @param {string} url - 請求路徑
   * @param {Object} data - 請求體數據
   * @returns {Promise<Object>} - 響應數據
   */
  async post(url, data = {}) {
    try {
      return await this.client.post(url, data, {
        timeout: this.getTimeoutForRequest(url),
      });
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * 發送 PUT 請求到外部 API
   * @param {string} url - 請求路徑
   * @param {Object} data - 請求體數據
   * @returns {Promise<Object>} - 響應數據
   */
  async put(url, data = {}) {
    try {
      return await this.client.put(url, data);
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * 發送 DELETE 請求到外部 API
   * @param {string} url - 請求路徑
   * @returns {Promise<Object>} - 響應數據
   */
  async delete(url) {
    try {
      return await this.client.delete(url);
    } catch (error) {
      this.handleError(error);
    }
  }

  handleError(error) {
    if (error.response) {
      // 伺服器回應錯誤
      throw new Error(error.response.data.detail || "外部API請求失敗");
    } else if (error.request) {
      // 請求發送失敗
      throw new Error("無法連接到外部API服務");
    } else {
      // 其他錯誤
      throw error;
    }
  }
}

// 創建並導出單例實例
const externalApiService = new ExternalApiService();
module.exports = externalApiService;
