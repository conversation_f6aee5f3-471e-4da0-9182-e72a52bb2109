/**
 * 使用 activedirectory 這個套件來連接 LDAP
 */
const ldap = require("ldapjs");

const ldapService = {
  // 網域帳號認証
  checkLdap: function (username, password, domain = "flexium") {
    return new Promise((resolve, reject) => {
      // 網域定義
      const DOMAIN_DEF = {
        flexiumcn: "flexium.com.cn",
        junkun: "junkun.com.cn",
        flexium: "flexium.local",
      };

      // 網域伺服器
      const domainServer = DOMAIN_DEF[domain];

      try {
        const client = ldap.createClient({
          url: "LDAP://" + domainServer,
          connectTimeout: 5000, // 連線超時時間設為5秒
          timeout: 10000, // 操作超時時間設為10秒
        });

        // 監聽錯誤事件，防止未處理的錯誤導致程序崩潰
        client.on("error", (err) => {
          console.log(`LDAP 連線錯誤: ${err.message}`, err);
          // 關閉客戶端連線
          try {
            client.destroy();
          } catch (e) {
            console.log("關閉 LDAP 客戶端時發生錯誤:", e.message);
          }
          resolve(false);
        });

        const ldapUser = `${username}@${domainServer}`; // 'cn=admin,dc=example,dc=com';

        client.bind(ldapUser, password, (err) => {
          if (err) {
            console.log("LDAP 驗證錯誤:", err.message);
            // 關閉客戶端連線
            try {
              client.destroy();
            } catch (e) {
              console.log("關閉 LDAP 客戶端時發生錯誤:", e.message);
            }
            resolve(false);
          } else {
            console.log("網域帳號登入成功!");
            // 關閉客戶端連線
            try {
              client.destroy();
            } catch (e) {
              console.log("關閉 LDAP 客戶端時發生錯誤:", e.message);
            }
            resolve(true);
          }
        });
      } catch (error) {
        console.log(`LDAP 服務初始化錯誤: ${error.message}`, error);
        resolve(false);
      }
    });
  },
};

module.exports = ldapService;
