/**
 * 專案目錄服務
 * 處理專案和流程實例相關的目錄創建和管理
 *  支援多層目錄結構，可以處理專案、流程實例和節點三層目錄 (節點用不到，保留)
 *  未來可考慮的優化方向：
 *  添加目錄清理功能，可選擇性地刪除不再需要的目錄
 *  實現目錄權限控制，確保安全性
 *  添加目錄大小統計和空間管理功能
 *  實現目錄備份和恢復功能
 */

const { ensureDirectoryExists, getFullPath } = require("../utils/file");

/**
 * 確保專案目錄存在
 * @param {string} projectNumber - 專案編號
 * @returns {string} 專案目錄路徑
 */
const ensureProjectDirectory = (projectNumber) => {
  if (!projectNumber) {
    throw new Error("專案編號不能為空");
  }

  const projectDirPath = getFullPath(`uploads/projects/${projectNumber}`);
  ensureDirectoryExists(projectDirPath);

  return projectDirPath;
};

/**
 * 確保流程實例目錄存在
 * @param {string} projectNumber - 專案編號
 * @param {string} templateName - 範本名稱
 * @returns {string} 流程實例目錄路徑
 */
const ensureTemplateDirectory = (projectNumber, templateName) => {
  if (!projectNumber || !templateName) {
    throw new Error("專案編號和範本名稱不能為空");
  }

  // 先確保專案目錄存在
  ensureProjectDirectory(projectNumber);

  // 創建範本目錄
  const templateDirPath = getFullPath(
    `uploads/projects/${projectNumber}/${templateName}`
  );
  ensureDirectoryExists(templateDirPath);

  return templateDirPath;
};

/**
 * 確保流程實例的節點目錄存在
 * @param {string} projectNumber - 專案編號
 * @param {string} templateName - 範本名稱
 * @param {string} nodeId - 節點ID
 * @returns {string} 節點目錄路徑
 */
const ensureNodeDirectory = (projectNumber, templateName, nodeId) => {
  if (!projectNumber || !templateName || !nodeId) {
    throw new Error("專案編號、範本名稱和節點ID不能為空");
  }

  // 先確保範本目錄存在
  ensureTemplateDirectory(projectNumber, templateName);

  // 創建節點目錄
  const nodeDirPath = getFullPath(
    `uploads/projects/${projectNumber}/${templateName}/${nodeId}`
  );
  ensureDirectoryExists(nodeDirPath);

  return nodeDirPath;
};

/**
 * 獲取專案目錄路徑
 * @param {string} projectNumber - 專案編號
 * @returns {string} 專案目錄路徑
 */
const getProjectDirectoryPath = (projectNumber) => {
  if (!projectNumber) {
    throw new Error("專案編號不能為空");
  }

  return getFullPath(`uploads/projects/${projectNumber}`);
};

/**
 * 獲取流程實例目錄路徑
 * @param {string} projectNumber - 專案編號
 * @param {string} templateName - 範本名稱
 * @returns {string} 流程實例目錄路徑
 */
const getTemplateDirectoryPath = (projectNumber, templateName) => {
  if (!projectNumber || !templateName) {
    throw new Error("專案編號和範本名稱不能為空");
  }

  return getFullPath(`uploads/projects/${projectNumber}/${templateName}`);
};

/**
 * 獲取節點目錄路徑
 * @param {string} projectNumber - 專案編號
 * @param {string} templateName - 範本名稱
 * @param {string} nodeId - 節點ID
 * @returns {string} 節點目錄路徑
 */
const getNodeDirectoryPath = (projectNumber, templateName, nodeId) => {
  if (!projectNumber || !templateName || !nodeId) {
    throw new Error("專案編號、範本名稱和節點ID不能為空");
  }

  return getFullPath(
    `uploads/projects/${projectNumber}/${templateName}/${nodeId}`
  );
};

module.exports = {
  ensureProjectDirectory,
  ensureTemplateDirectory,
  ensureNodeDirectory,
  getProjectDirectoryPath,
  getTemplateDirectoryPath,
  getNodeDirectoryPath,
};
