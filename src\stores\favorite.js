import { defineStore } from "pinia";
import { ref } from "vue";
import {
  getFavorites,
  addFavorite,
  deleteFavorite,
  getFavoriteDetails,
} from "@/api/modules/favorites";
import { ElMessage } from "element-plus";

export const useFavoriteStore = defineStore("favorite", () => {
  // 所有關注列表
  const favorites = ref([]);
  // 關注類型
  const favoriteTypes = [
    { value: "project", label: "專案", path: "/projects" },
    { value: "flow", label: "流程", path: "/flow-instances" },
    { value: "report", label: "報表", path: null }, // 預留報表路由
  ];
  // 載入狀態
  const loading = ref(false);
  // 詳細資訊
  const favoriteDetails = ref({});

  // 獲取用戶關注
  const fetchFavorites = async (userId) => {
    if (!userId) return;

    try {
      loading.value = true;
      const response = await getFavorites(userId);
      favorites.value = response.data || [];
      return favorites.value;
    } catch (error) {
      console.error("獲取關注失敗:", error);
      //ElMessage.error("獲取關注列表失敗");
      return [];
    } finally {
      loading.value = false;
    }
  };

  // 根據類型獲取分類後的關注項目
  const getCategoryFavorites = (type) => {
    return favorites.value
      .filter((item) => item.type === type)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)); // 依照 createdAt 降序排序（新的在前）
  };

  // 檢查某個資源是否已被關注
  const isFavorited = (type, resourceId) => {
    return favorites.value.some(
      (item) => item.type === type && item.resourceId === resourceId
    );
  };

  // 添加關注
  const addToFavorite = async (data) => {
    if (!data.type || !data.resourceId || !data.name || !data.path) {
      console.error("關注資料不完整");
      return false;
    }

    try {
      const response = await addFavorite(data);
      // 更新本地狀態
      const found = favorites.value.find(
        (item) => item.type === data.type && item.resourceId === data.resourceId
      );

      if (!found) {
        // 將新關注項目加入陣列
        favorites.value = [
          // 新項目放在最前面
          { ...response.data },
          // 展開現有項目
          ...favorites.value,
        ];
      }

      ElMessage.success("已添加到關注");
      return true;
    } catch (error) {
      console.error("添加關注失敗:", error);
      if (error.response?.data?.error === "DUPLICATE_RESOURCE") {
        ElMessage.warning("此資源已被其他用戶關注");
      } else {
        ElMessage.error("添加關注失敗");
      }
      return false;
    }
  };

  // 移除關注
  const removeFromFavorite = async (type, resourceId) => {
    try {
      await deleteFavorite({ type, resourceId });

      // 從列表中移除
      favorites.value = favorites.value.filter(
        (fav) => !(fav.type === type && fav.resourceId === resourceId)
      );

      ElMessage.success("已移除關注項目");
      return true;
    } catch (error) {
      console.error("移除關注失敗:", error);
      ElMessage.error("移除關注失敗");
      return false;
    }
  };

  // 獲取關注詳細資訊
  const fetchFavoriteDetails = async (items, reset = false) => {
    if (!items?.length) return;
    // console.log("獲取關注詳細資訊.items:", items);

    // 檢查資料都有就避免重複執行
    if (
      !items.some((item) => {
        return !favoriteDetails.value[`${item.type}_${item.resourceId}`];
      }) &&
      !reset
    ) {
      console.log("已存在關注詳細資訊，無需重新獲取。");
      return;
    }

    // 清空之前的詳細資訊
    favoriteDetails.value = {};

    try {
      loading.value = true;
      const { data } = await getFavoriteDetails(items);

      // 回傳的 data 直接就是陣列，不需要再取 data.data
      if (Array.isArray(data)) {
        // 初始化一個新的物件來存儲結果
        const details = {};

        // 將每個項目加入到 details 物件中
        data.forEach((item) => {
          if (item.type && item.resourceId) {
            details[`${item.type}_${item.resourceId}`] = item;
          }
        });

        // 一次性更新 favoriteDetails
        favoriteDetails.value = details;
      }
      // console.log("處理後的關注詳細資訊:", favoriteDetails.value);
    } catch (error) {
      console.error("獲取關注詳細資訊失敗:", error);
      ElMessage.error("獲取關注詳細資訊失敗");
    } finally {
      loading.value = false;
    }
  };

  // 根據 type 和 resourceId 獲取詳細資訊
  const getFavoriteDetail = (type, resourceId) => {
    if (!type || !resourceId || !favoriteDetails.value) return null;
    return favoriteDetails.value[`${type}_${resourceId}`] || null;
  };

  return {
    favorites,
    favoriteTypes,
    loading,
    fetchFavorites,
    getCategoryFavorites,
    isFavorited,
    addToFavorite,
    removeFromFavorite,
    fetchFavoriteDetails,
    getFavoriteDetail,
  };
});
