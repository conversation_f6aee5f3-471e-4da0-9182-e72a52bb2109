import { defineStore } from "pinia";
import { ref } from "vue";

export const useThemeStore = defineStore("theme", () => {
  // 從本地儲存讀取暗黑模式狀態，默認為亮色模式
  const isDark = ref(localStorage.getItem("theme") === "dark");

  // 切換暗黑模式
  function toggleDark() {
    isDark.value = !isDark.value;
    // 保存到本地儲存
    localStorage.setItem("theme", isDark.value ? "dark" : "light");
    // 更新 html 元素的 class
    if (isDark.value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }

  // 設置初始主題
  function initTheme() {
    if (isDark.value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }

  return {
    isDark,
    toggleDark,
    initTheme,
  };
});
