import { defineStore } from "pinia";
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  getUserRoles,
  uploadAvatar,
} from "@/api/modules/user";
import {
  login,
  logout,
  getCurrentUser,
  getUserPermissions,
} from "@/api/modules/auth";
import { ref, computed } from "vue";

// 定義系統中所有可能的權限
const ALL_PERMISSIONS = [
  "MANAGE_ROLES",
  "MANAGE_PERMISSIONS",
  "ASSIGN_ROLES",
  "VIEW_ROLES",
  "VIEW_PERMISSIONS",
  "MANAGE_USERS",
  "VIEW_USERS",
  "MANAGE_PROJECTS",
  "VIEW_PROJECTS",
];

export const useUserStore = defineStore("user", () => {
  const user = ref(null);
  const token = ref(localStorage.getItem("token") || null);
  const userPermissions = ref([]);
  const users = ref([]);
  const usersLoading = ref(false);

  const isAuthenticated = computed(() => !!token.value);

  // 用於存儲進行中的請求
  let fetchUserPromise = null;

  // 檢查是否有任一權限
  const hasAnyPermission = (permissions) => {
    // 如果用戶未登入或沒有 roles，直接返回 false
    if (!user.value || !user.value.roles) return false;

    const aaa = user.value.roles.some(
      (role) => role.name === "ADMIN" || role.name === "SUPERADMIN"
    );

    // 如果是超級管理員，直接返回 true
    if (
      user.value.roles.some(
        (role) => role.name === "ADMIN" || role.name === "SUPERADMIN"
      )
    ) {
      return true;
    }

    // 獲取用戶所有權限
    const userPermissions = new Set();
    user.value.roles.forEach((role) => {
      role.permissions.forEach((permission) => {
        userPermissions.add(permission.name);
      });
    });

    // 檢查是否有任一所需權限
    return Array.isArray(permissions)
      ? permissions.some((permission) => userPermissions.has(permission))
      : userPermissions.has(permissions);
  };

  // 檢查是否有所有權限
  const hasAllPermissions = (permissions) => {
    // 如果用戶未登入或沒有 roles，直接返回 false
    if (!user.value || !user.value.roles) return false;

    // 如果是超級管理員，直接返回 true
    if (user.value.roles.some((role) => role.name === "SUPERADMIN")) {
      return true;
    }

    // 獲取用戶所有權限
    const userPermissions = new Set();
    user.value.roles.forEach((role) => {
      role.permissions.forEach((permission) => {
        userPermissions.add(permission.name);
      });
    });

    // 檢查是否有所有所需權限
    return Array.isArray(permissions)
      ? permissions.every((permission) => userPermissions.has(permission))
      : userPermissions.has(permissions);
  };

  // 檢查是否有特定角色
  const hasRole = (roleName) => {
    // 如果用戶未登入或沒有 roles，直接返回 false
    if (!user.value || !user.value.roles) return false;

    return user.value.roles.some((role) => role.name === roleName);
  };

  // 檢查是否為管理員
  const isAdmin = computed(() => {
    // 如果用戶未登入或沒有 roles，直接返回 false
    if (!user.value || !user.value.roles) return false;

    // 檢查是否有 ADMIN 或 SUPERADMIN 角色
    return user.value.roles.some((role) =>
      ["ADMIN", "SUPERADMIN"].includes(role.name)
    );
  });

  // 登入
  const handleLogin = async (credentials) => {
    try {
      const response = await login(credentials);
      // console.log("登入回應:", response); // 用於調試

      // 檢查回應格式
      if (!response.data) {
        throw new Error("伺服器回應格式錯誤");
      }

      const { token: newToken, user: userData } = response.data;

      if (!newToken || !userData) {
        throw new Error("伺服器回應缺少必要資料");
      }

      token.value = newToken;
      user.value = userData;
      localStorage.setItem("token", newToken);

      // 獲取用戶所有權限
      const permissionSet = new Set();
      if (userData.roles) {
        userData.roles.forEach((role) => {
          if (role.permissions) {
            role.permissions.forEach((permission) => {
              permissionSet.add(permission.name);
            });
          }
        });
      }

      // 設置用戶權限
      userPermissions.value = Array.from(permissionSet);

      return response;
    } catch (error) {
      // console.error("登入失敗:", error);
      if (error.response?.message) {
        throw new Error(error.response.message);
      } else {
        throw new Error(error.message || "登入失敗，請稍後再試");
      }
    }
  };

  // 登出
  const handleLogout = async () => {
    try {
      await logout();
      token.value = null;
      user.value = null;
      localStorage.removeItem("token");
      userPermissions.value = [];
      users.value = [];
    } catch (error) {
      console.error("登出失敗:", error);
      throw error;
    }
  };

  // 獲取當前用戶信息，實現去重機制
  const fetchUser = async () => {
    // 如果已經有用戶資訊，直接返回
    if (user.value) {
      return { data: user.value };
    }

    // 如果已經有請求在進行中，返回該請求
    if (fetchUserPromise) {
      return fetchUserPromise;
    }

    try {
      fetchUserPromise = getCurrentUser();
      const response = await fetchUserPromise;

      if (!response.data) {
        throw new Error("未獲取到用戶資訊");
      }

      user.value = response.data;
      return response;
    } catch (error) {
      console.error("獲取用戶信息失敗:", error);

      // TODO 有相同的功能，應該提出! 等候測試完成...
      // 保存當前頁面為重定向目標
      const currentPath = window.location.pathname;
      const redirectTarget = currentPath !== "/" ? currentPath : "";
      const redirectQuery = redirectTarget
        ? `?redirect=${encodeURIComponent(redirectTarget)}`
        : "";
      const url = `/login${redirectQuery}`;
      // 使用瀏覽器 history API 替換當前 URL，避免在路由歷史中留下記錄
      window.history.replaceState(null, "登入", url);
      // 跳轉到登入頁面
      window.location.href = url;

      throw error;
    } finally {
      // 請求完成後重置 Promise
      setTimeout(() => {
        fetchUserPromise = null;
      }, 3000); // 3秒內不重複請求
    }
  };

  // 權限相關
  const fetchUserPermissions = async () => {
    try {
      if (user.value?.role === "SUPER_ADMIN" || user.value?.role === "ADMIN") {
        userPermissions.value = ALL_PERMISSIONS;
        return;
      }

      const permissions = await getUserPermissions();
      userPermissions.value = permissions;
    } catch (error) {
      console.error("獲取權限失敗:", error);
      userPermissions.value = [];
    }
  };

  // 用戶管理相關
  const fetchUsers = async () => {
    usersLoading.value = true;
    try {
      const response = await getUsers();
      users.value = response;
    } catch (error) {
      console.error("獲取用戶列表失敗:", error);
      throw error;
    } finally {
      usersLoading.value = false;
    }
  };

  // 創建用戶
  const createUserInfo = async (userData) => {
    try {
      const newUser = await createUser(userData);
      await fetchUsers();
      return newUser;
    } catch (error) {
      console.error("創建用戶失敗:", error);
      throw error;
    }
  };

  // 更新用戶
  const updateUserInfo = async (userId, userData) => {
    try {
      await updateUser(userId, userData);
      await fetchUsers();
    } catch (error) {
      console.error("更新用戶失敗:", error);
      throw error;
    }
  };

  // 刪除用戶
  const deleteUserInfo = async (userId) => {
    try {
      await deleteUser(userId);
      await fetchUsers();
    } catch (error) {
      console.error("刪除用戶失敗:", error);
      throw error;
    }
  };

  // 獲取用戶角色
  const getUserRolesInfo = async (userId) => {
    try {
      // 直接使用導入的 getUserRoles 函數
      const response = await getUserRoles(userId);
      return response;
    } catch (error) {
      console.error("獲取用戶角色失敗:", error);
      throw error;
    }
  };

  // 上傳用戶頭像
  const uploadUserAvatar = async (userId, file) => {
    try {
      const response = await uploadAvatar(userId, file);
      await fetchUsers(); // 重新獲取用戶列表以更新頭像
      return response;
    } catch (error) {
      console.error("上傳頭像失敗:", error);
      throw error;
    }
  };

  return {
    user,
    token,
    isAuthenticated,
    isAdmin,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    handleLogin,
    handleLogout,
    fetchUser,
    userPermissions,
    users,
    usersLoading,
    fetchUserPermissions,
    fetchUsers,
    createUserInfo,
    updateUserInfo,
    deleteUserInfo,
    getUserRolesInfo,
    uploadUserAvatar,
  };
});
