@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Element Plus 主題顏色 */
  --el-color-primary: #2a6df4;
  --el-color-primary-light-3: #5891f7;
  --el-color-primary-light-5: #86b0f9;
  --el-color-primary-light-7: #b4cefb;
  --el-color-primary-light-8: #c8ddfc;
  --el-color-primary-light-9: #dcebfd;
  --el-color-primary-dark-2: #2258c3;

  /* 系統字體 */
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 全局顏色變數 */
  --color-light-bg: #ffffff;
  --color-dark-bg: #1a1a1a;
  --color-light-text: #333333;
  --color-dark-text: #e5e7eb;
}

/* 全局移除 focus 樣式 */
*:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* 全局移除 Element Plus 組件的 focus 樣式 */
.el-input__inner:focus,
.el-textarea__inner:focus,
.el-select:focus,
.el-select .el-input__inner:focus,
.el-button:focus,
.el-radio:focus,
.el-checkbox:focus,
.el-switch:focus,
.el-slider:focus,
.el-date-editor:focus,
.el-cascader:focus,
.el-color-picker:focus,
.el-upload:focus,
.el-form-item:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* 全域字體大小設置 */
.el-menu-item {
  font-size: 16px !important;
  height: 50px !important;
  line-height: 50px !important;
}

.el-menu-item .lucide {
  margin-right: 8px;
  width: 24px;
  height: 24px;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f8fafc;
  color: #1a1a1a;
}

html.dark body {
  background-color: #121212;
  color: #e5eaf3;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: none;
}

.card {
  padding: 2em;
}

#app {
  @apply h-screen;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Element Plus 全局樣式覆蓋 */
.el-button--primary {
  --el-button-hover-bg-color: var(--el-color-primary-dark-2);
  --el-button-hover-border-color: var(--el-button-hover-bg-color);
}

.el-menu.el-menu--horizontal {
  --el-menu-bg-color: transparent;
  --el-menu-text-color: #fff;
  --el-menu-hover-text-color: #fff;
  --el-menu-active-color: #fff;
}

.el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* NProgress 自定義樣式 */
#nprogress .bar {
  background: #007aff !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #007aff, 0 0 5px #007aff !important;
}

/* JSON Viewer 自定義樣式 */
.custom-json-viewer {
  background-color: #f9fafb !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  border: 1px solid #e5e7eb !important;
}

.custom-json-viewer .jv-container {
  background: none !important;
}

.custom-json-viewer .jv-container .jv-code {
  padding: 0 !important;
  background: none !important;
}

.custom-json-viewer .jv-container .jv-key {
  color: #2563eb !important;
}

.custom-json-viewer .jv-container .jv-item.jv-string {
  color: #059669 !important;
}

.custom-json-viewer .jv-container .jv-item.jv-number {
  color: #d97706 !important;
}

.custom-json-viewer .jv-container .jv-item.jv-boolean {
  color: #7c3aed !important;
}

.custom-json-viewer .jv-container .jv-item.jv-null {
  color: #dc2626 !important;
}

.el-table thead th {
  color: black;
  background-color: #f9fbfc !important;
}

/* 暗黑模式下的 JSON Viewer 樣式 */
html.dark .custom-json-viewer {
  background-color: #1a1a1a !important;
  border-color: #374151 !important;
}

html.dark .custom-json-viewer .jv-container .jv-key {
  color: #5891f7 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-string {
  color: #7ce0a3 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-number {
  color: #f3a95a !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-boolean {
  color: #b39ddb !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-null {
  color: #ef5350 !important;
}

/* 表格在暗黑模式下的樣式 */
.el-table thead th {
  color: black;
  background-color: #f9fbfc !important;
}

/* 暗黑模式下表格頭部樣式 */
html.dark .el-table thead th {
  color: #e0e0e0 !important;
  background-color: #1d1e1f !important;
}

html.dark .el-table {
  --el-table-header-bg-color: #1d1e1f !important;
  --el-table-header-text-color: #e0e0e0 !important;
  --el-table-border-color: #303133 !important;
  --el-table-bg-color: #1a1a1a !important;
  --el-table-tr-bg-color: #1a1a1a !important;
  --el-table-row-hover-bg-color: #262727 !important;
}

/* 暗黑模式下標籤頁樣式 */
html.dark .el-tabs__item {
  color: #a3a6ad !important;
}

html.dark .el-tabs__item.is-active {
  color: var(--el-color-primary) !important;
}

html.dark .el-tabs__active-bar {
  background-color: var(--el-color-primary) !important;
}

html.dark .el-tabs__nav-wrap::after {
  background-color: #303133 !important;
}

/* 暗黑模式下頂部標籤頁樣式 */
html.dark .el-tabs {
  background-color: #1a1a1a !important;
}

html.dark .el-tabs__header {
  background-color: #1a1a1a !important;
  border-bottom-color: #303133 !important;
}

html.dark .el-tabs__content {
  background-color: #1a1a1a !important;
}

html.dark .el-tab-pane {
  background-color: #1a1a1a !important;
}

/* RBAC管理頁面的頂部標籤樣式 */
html.dark .rbac-management .el-tabs {
  background-color: #1a1a1a !important;
}

html.dark .rbac-management .el-tabs__header {
  background-color: #1a1a1a !important;
  border-bottom-color: #303133 !important;
}

html.dark .rbac-management .el-tabs__nav {
  background-color: #1a1a1a !important;
}

html.dark .rbac-management .el-tabs__item {
  background-color: transparent !important;
  color: #a3a6ad !important;
  border-bottom: 1px solid #303133 !important;
}

html.dark .rbac-management .el-tabs__item.is-active {
  background-color: transparent !important;
  color: var(--el-color-primary) !important;
}

html.dark .rbac-management .el-tabs__content {
  background-color: #1a1a1a !important;
}

/* 移除 Vue Flow 元素的 focus 樣式 */
.vue-flow__node:focus,
.vue-flow__node:focus-visible,
.vue-flow__edge:focus,
.vue-flow__edge:focus-visible,
.vue-flow__edge-path:focus,
.vue-flow__edge-path:focus-visible,
.vue-flow__handle:focus,
.vue-flow__handle:focus-visible,
.vue-flow__edge-button:focus,
.vue-flow__edge-button:focus-visible,
.vue-flow__edge-label:focus,
.vue-flow__edge-label:focus-visible,
.vue-flow__edge-text:focus,
.vue-flow__edge-text:focus-visible,
.vue-flow__connection-line:focus,
.vue-flow__connection-line:focus-visible,
.vue-flow__viewport:focus,
.vue-flow__viewport:focus-visible,
.vue-flow__pane:focus,
.vue-flow__pane:focus-visible,
.vue-flow__background:focus,
.vue-flow__background:focus-visible,
.vue-flow__minimap:focus,
.vue-flow__minimap:focus-visible,
.vue-flow__controls:focus,
.vue-flow__controls:focus-visible,
.vue-flow__controls-button:focus,
.vue-flow__controls-button:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* 移除 Element Plus 圖標和下拉菜單的 focus 樣式 */
.el-icon:focus,
.el-icon:focus-visible,
.el-dropdown:focus,
.el-dropdown:focus-visible,
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:focus-visible,
.el-tooltip:focus,
.el-tooltip:focus-visible,
.el-tooltip__trigger:focus,
.el-tooltip__trigger:focus-visible,
.el-badge:focus,
.el-badge:focus-visible,
.el-popover:focus,
.el-popover:focus-visible,
.el-popover__reference:focus,
.el-popover__reference:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* 暗黑模式特定變數 */
html.dark {
  color-scheme: dark;
  --app-bg-color: var(--color-dark-bg);
  --app-text-color: var(--color-dark-text);
}

html {
  --app-bg-color: var(--color-light-bg);
  --app-text-color: var(--color-light-text);
}

/* Element Plus 元件暗黑模式樣式 */
/* 按鈕(el-button)暗黑模式樣式 */
html.dark .el-button {
  --el-button-bg-color: #292c31;
  --el-button-border-color: #334155;
  --el-button-hover-bg-color: #334155;
  --el-button-hover-border-color: #475569;
  --el-button-active-bg-color: #334155;
  --el-button-active-border-color: #475569;
  --el-button-text-color: #e2e8f0;
}

html.dark .el-button.el-button--primary {
  --el-button-bg-color: #184792;
  --el-button-hover-bg-color: #1d4ed8 !important;
  --el-button-hover-border-color: #1d4ed8 !important;
}

html.dark .el-button.el-button--success {
  --el-button-bg-color: #1b9a49;
  --el-button-hover-bg-color: #15803d !important;
  --el-button-hover-border-color: #15803d !important;
}

html.dark .el-button.el-button--warning {
  --el-button-bg-color: #987911;
  --el-button-hover-bg-color: #b79117 !important;
  --el-button-hover-border-color: #987911 !important;
}

html.dark .el-button.el-button--danger {
  --el-button-bg-color: #8c2020;
  --el-button-hover-bg-color: #b92222 !important;
  --el-button-hover-border-color: #b92222 !important;
}
html.dark .el-button.el-button--info {
  --el-button-hover-bg-color: #475569 !important;
  --el-button-hover-border-color: #475569 !important;
}

html.dark .el-button.el-button--text {
  --el-button-hover-text-color: #60a5fa !important;
}

/* Vue Flow 暗黑模式樣式 */
html.dark .vue-flow {
  background-color: #121212 !important;
}

html.dark .vue-flow__background {
  background-color: #121212 !important;
}

html.dark .vue-flow__background-pattern {
  color: #2c2c2c !important;
}

html.dark .vue-flow__edge {
  stroke: #666 !important;
}

html.dark .vue-flow__edge-path {
  stroke: #666 !important;
}

html.dark .vue-flow__controls {
  background-color: #1a1a1a !important;
  border-color: #333 !important;
}

html.dark .vue-flow__controls-button {
  background-color: #252525 !important;
  border-color: #444 !important;
  color: #e0e0e0 !important;
}

html.dark .vue-flow__controls-button:hover {
  background-color: #333 !important;
}

html.dark .vue-flow__controls-button svg {
  fill: #e0e0e0 !important;
}

html.dark .vue-flow__minimap {
  background-color: #1a1a1a !important;
  border-color: #333 !important;
  opacity: 0.2 !important;
}

/* Dialog 預覽窗口暗黑模式樣式 */
html.dark .el-dialog.is-fullscreen {
  background-color: #121212 !important;
}

html.dark .el-dialog__header {
  background-color: #1a1a1a !important;
  border-bottom-color: #333 !important;
}

html.dark .el-dialog__body {
  background-color: #1a1a1a !important;
}

html.dark .el-dialog__footer {
  background-color: #1a1a1a !important;
  border-top-color: #333 !important;
}

/* 節點預覽區域暗黑模式樣式 */
html.dark .node-preview-container {
  background-color: #1a1a1a !important;
}

/* html.dark .bg-gray-50 {
  background-color: #1a1a1a !important;
} */

/* 媒體查詢 - 淺色模式調整 */
@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
}

.el-drawer__title {
  font-size: 20px !important;
  padding: 5px !important;
}
/* Element Plus Drawer 深色模式樣式 */
html.dark .el-drawer {
  background-color: var(--color-dark-bg) !important;
  color: var(--color-dark-text) !important;
}

html.dark .el-drawer__header {
  color: #e2e8f0 !important;
  font-size: 26px !important;
}

html.dark .el-drawer__body {
  background-color: #212121 !important;
  color: #e2e8f0 !important;
}

html.dark .el-drawer__close-btn {
  color: #94a3b8 !important;
}

html.dark .el-drawer__close-btn:hover {
  color: #e2e8f0 !important;
}

/* 確保深色模式下 JSON Viewer 樣式能被重置 */
html.dark .custom-json-viewer .jv-container {
  color: #e2e8f0 !important;
}

html.dark .custom-json-viewer .jv-container .jv-key {
  color: #93c5fd !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-string {
  color: #86efac !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-number {
  color: #fdba74 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-boolean {
  color: #f472b6 !important;
}

html.dark .custom-json-viewer .jv-container .jv-item.jv-null {
  color: #94a3b8 !important;
}

html.dark .custom-json-viewer .jv-container .jv-icon {
  color: #94a3b8 !important;
}

/* JSON Viewer 深色模式核心樣式，確保優先級 */
html.dark .jv-container {
  background: none !important;
  color: #e2e8f0 !important;
}

html.dark .jv-container .jv-code {
  background: none !important;
  color: #e2e8f0 !important;
}

html.dark .jv-container .jv-item {
  color: #e2e8f0 !important;
}

html.dark .jv-container .jv-key {
  color: #93c5fd !important;
}

html.dark .jv-container .jv-item.jv-string {
  color: #86efac !important;
}

html.dark .jv-container .jv-item.jv-number {
  color: #fdba74 !important;
}

html.dark .jv-container .jv-item.jv-boolean {
  color: #f472b6 !important;
}

html.dark .jv-container .jv-item.jv-null {
  color: #94a3b8 !important;
}

html.dark .jv-container .jv-item.jv-array,
html.dark .jv-container .jv-item.jv-object {
  color: #e2e8f0 !important;
}

html.dark .jv-container .jv-icon {
  color: #94a3b8 !important;
}

html.dark .jv-container .jv-ellipsis {
  color: #94a3b8 !important;
}

html.dark .jv-container .jv-more:after {
  background-color: #94a3b8 !important;
}

/* 自定義深色模式類別 */
@layer utilities {
  .bg-dark-mode {
    background-color: var(--color-dark-bg) !important;
  }

  .bg-light-mode {
    background-color: var(--color-light-bg) !important;
  }

  .text-dark-mode {
    color: var(--color-dark-text) !important;
  }

  .text-light-mode {
    color: var(--color-light-text) !important;
  }

  /* 向下兼容舊版類別，避免現有代碼失效 */
  .bg-mode {
    background-color: var(--app-bg-color) !important;
  }

  .text-mode {
    color: var(--app-text-color) !important;
  }
}

/* ElTable的暗黑模式樣式 */
html.dark .el-table {
  --el-table-bg-color: var(--color-dark-bg) !important;
  --el-table-tr-bg-color: var(--color-dark-bg) !important;
  --el-table-border-color: #334155 !important;
  --el-table-header-bg-color: #1e293b !important;
  /* 其他顏色保持不變 */
}

/* 確保暗黑模式下 Dialog 樣式 */
html.dark .el-dialog {
  background-color: var(--color-dark-bg) !important;
}

.el-notification .el-notification--error {
  color: white !important;
}

.el-notification .el-notification__title {
  color: white !important;
}

.el-notification .el-notification__content {
  color: white !important;
}

/* 代碼高亮樣式 */
pre {
  @apply rounded-lg overflow-auto bg-gray-800 dark:bg-gray-900 p-4 my-4;
  max-height: 500px;
}

code {
  @apply font-mono text-sm;
}

:not(pre) > code {
  @apply bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm border border-gray-200 dark:border-gray-700;
}

.hljs {
  @apply rounded-md;
  background: transparent !important;
}

/* 暗黑模式下的代碼高亮樣式調整 */
html.dark pre {
  @apply bg-gray-900 border border-gray-700;
}

html.dark .hljs {
  @apply text-gray-200;
}

html.dark .hljs-title,
html.dark .hljs-section,
html.dark .hljs-selector-id,
html.dark .hljs-selector-class {
  @apply text-blue-400;
}

html.dark .hljs-attr,
html.dark .hljs-attribute,
html.dark .hljs-literal,
html.dark .hljs-meta,
html.dark .hljs-number,
html.dark .hljs-operator {
  @apply text-orange-400;
}

html.dark .hljs-keyword {
  @apply text-purple-400;
}

html.dark .hljs-string,
html.dark .hljs-meta-string {
  @apply text-green-400;
}

html.dark .hljs-built_in,
html.dark .hljs-class .hljs-title {
  @apply text-yellow-400;
}

html.dark .hljs-comment,
html.dark .hljs-quote {
  @apply text-gray-500;
}

/* 移除 EdgeWithButton 組件的 focus 樣式 */
.vue-flow__edge-button-circle:focus,
.vue-flow__edge-button-circle:focus-visible,
.vue-flow__edge-button svg:focus,
.vue-flow__edge-button svg:focus-visible,
.vue-flow__edge-button path:focus,
.vue-flow__edge-button path:focus-visible,
.vue-flow__edge-button circle:focus,
.vue-flow__edge-button circle:focus-visible,
.vue-flow__edge-button g:focus,
.vue-flow__edge-button g:focus-visible,
.vue-flow__edge-button text:focus,
.vue-flow__edge-button text:focus-visible {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}
