/**
 * NOTE:棄用
 * 日期工具函數
 */

/**
 * 格式化日期為本地字符串
 * @param {string|Date} date - 日期對象或 ISO 格式的日期字符串
 * @param {object} options - Intl.DateTimeFormat 選項
 * @returns {string} 格式化的日期字符串
 */
export function formatDate(date, options = {}) {
  if (!date) return "";

  const defaultOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  };

  const mergedOptions = { ...defaultOptions, ...options };

  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return new Intl.DateTimeFormat("zh-TW", mergedOptions).format(dateObj);
  } catch (error) {
    console.error("日期格式化錯誤:", error);
    return String(date);
  }
}

/**
 * 格式化日期為相對時間（例如：3分鐘前、2小時前）
 * @param {string|Date} date - 日期對象或 ISO 格式的日期字符串
 * @returns {string} 相對時間字符串
 */
export function formatRelativeTime(date) {
  if (!date) return "";

  const now = new Date();
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const diffMs = now - dateObj;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);

  if (diffSec < 60) {
    return "剛剛";
  } else if (diffMin < 60) {
    return `${diffMin}分鐘前`;
  } else if (diffHour < 24) {
    return `${diffHour}小時前`;
  } else if (diffDay < 30) {
    return `${diffDay}天前`;
  } else if (diffMonth < 12) {
    return `${diffMonth}個月前`;
  } else {
    return `${diffYear}年前`;
  }
}

/**
 * 檢查日期是否在指定天數內
 * @param {string|Date} date - 日期對象或 ISO 格式的日期字符串
 * @param {number} days - 天數
 * @returns {boolean} 是否在指定天數內
 */
export function isWithinDays(date, days) {
  if (!date) return false;

  const now = new Date();
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const diffMs = now - dateObj;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  return diffDays <= days;
}

/**
 * 獲取日期的時間部分
 * @param {string|Date} date - 日期對象或 ISO 格式的日期字符串
 * @returns {string} 時間字符串 (HH:MM)
 */
export function getTimeFromDate(date) {
  if (!date) return "";

  const dateObj = typeof date === "string" ? new Date(date) : date;

  return dateObj.toLocaleTimeString("zh-TW", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
}
