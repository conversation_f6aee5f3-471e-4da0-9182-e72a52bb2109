/**
 * 圖片處理工具函數
 */

/**
 * 壓縮圖片
 * @param {File} file - 原始圖片文件
 * @param {Object} options - 壓縮選項
 * @param {number} options.maxWidth - 最大寬度（預設 800px）
 * @param {number} options.maxHeight - 最大高度（預設 600px）
 * @param {number} options.quality - 壓縮品質 0-1（預設 0.7）
 * @param {string} options.format - 輸出格式（預設 'jpeg'）
 * @returns {Promise<string>} 壓縮後的 base64 字串
 */
export const compressImage = (file, options = {}) => {
  const {
    maxWidth = 800,
    maxHeight = 600,
    quality = 0.7,
    format = "jpeg",
  } = options;

  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.readAsDataURL(file);

      reader.onload = (e) => {
        const img = new Image();
        img.src = e.target.result;

        img.onload = () => {
          // 創建 canvas 進行壓縮
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");

          // 計算壓縮後的尺寸，保持比例
          let width = img.width;
          let height = img.height;

          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }

          if (height > maxHeight) {
            width = Math.round((width * maxHeight) / height);
            height = maxHeight;
          }

          // 設置 canvas 尺寸
          canvas.width = width;
          canvas.height = height;

          // 繪製圖像
          ctx.drawImage(img, 0, 0, width, height);

          // 轉換為 base64
          const dataUrl = canvas.toDataURL(`image/${format}`, quality);

          // 計算壓縮前後大小
          const originalSize = Math.round(file.size / 1024);
          const compressedSize = Math.round(dataUrl.length / 1.37 / 1024); // 近似計算 base64 大小

          // 返回壓縮結果與資訊
          resolve({
            dataUrl,
            info: {
              originalSize,
              compressedSize,
              width,
              height,
              compressionRatio:
                Math.round((compressedSize / originalSize) * 100) / 100,
            },
          });
        };

        img.onerror = () => {
          reject(new Error("圖片載入失敗"));
        };
      };

      reader.onerror = () => {
        reject(new Error("圖片讀取失敗"));
      };
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * 檢查圖片格式與大小
 * @param {File} file - 圖片文件
 * @param {Object} options - 檢查選項
 * @param {number} options.maxSize - 最大檔案大小（MB）
 * @param {Array<string>} options.allowedTypes - 允許的 MIME 類型
 * @returns {Object} 檢查結果 {valid, message}
 */
export const validateImage = (file, options = {}) => {
  const {
    maxSize = 5,
    allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  } = options;

  // 檢查文件類型
  const isValidType = allowedTypes.includes(file.type);
  if (!isValidType) {
    return {
      valid: false,
      message: `只能上傳 ${allowedTypes
        .map((t) => t.split("/")[1].toUpperCase())
        .join("/")} 格式的圖片`,
    };
  }

  // 檢查文件大小
  const isValidSize = file.size / 1024 / 1024 < maxSize;
  if (!isValidSize) {
    return {
      valid: false,
      message: `圖片大小不能超過 ${maxSize}MB`,
    };
  }

  return { valid: true };
};
