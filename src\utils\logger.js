/**
 * 日誌級別枚舉
 */
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

// 當前日誌級別，可以通過環境變數控制
const currentLogLevel =
  process.env.NODE_ENV === "production" ? LogLevel.WARN : LogLevel.DEBUG;

import { formatTimestamp } from "@/utils/dateUtils";

// 日誌格式化配置
const logConfig = {
  maxObjectDepth: 2, // 物件最大嵌套深度
  maxArrayLength: 5, // 數組最大顯示長度
  maxObjectKeys: 5, // 物件最大顯示屬性數量
  maxStringLength: 100, // 字符串最大長度
  truncatedSuffix: "...", // 截斷後的後綴
  expandObjects: false, // 是否展開物件（可通過環境變量控制）
};

/**
 * 統一的日誌工具
 */
export const logger = {
  /**
   * 將複雜對象轉換為可讀字符串
   * @param {any} obj - 要轉換的對象
   * @param {number} [depth=0] - 當前深度
   * @returns {string} 格式化後的字符串
   */
  _formatObject(obj, depth = 0) {
    try {
      if (obj === null) return "null";
      if (obj === undefined) return "undefined";
      if (typeof obj === "string") {
        if (obj.length > logConfig.maxStringLength) {
          return `${obj.substring(0, logConfig.maxStringLength)}${
            logConfig.truncatedSuffix
          } [字符串長度:${obj.length}]`;
        }
        return obj;
      }

      // 處理基本類型
      if (typeof obj !== "object") return String(obj);

      // 到達最大深度時不再展開
      if (depth >= logConfig.maxObjectDepth) {
        if (Array.isArray(obj)) {
          return `Array(${obj.length}) [...]`;
        }
        return `{...} [物件:${Object.keys(obj).length}屬性]`;
      }

      // 處理數組
      if (Array.isArray(obj)) {
        if (obj.length === 0) return "[]";

        const truncated = obj.length > logConfig.maxArrayLength;
        const items = truncated ? obj.slice(0, logConfig.maxArrayLength) : obj;

        const formattedItems = items.map((item) =>
          this._formatObject(item, depth + 1)
        );

        return `[${formattedItems.join(", ")}${
          truncated ? `, ... +${obj.length - logConfig.maxArrayLength}項` : ""
        }]`;
      }

      // 處理一般物件
      const keys = Object.keys(obj);
      if (keys.length === 0) return "{}";

      const truncated = keys.length > logConfig.maxObjectKeys;
      const visibleKeys = truncated
        ? keys.slice(0, logConfig.maxObjectKeys)
        : keys;

      const formattedEntries = visibleKeys.map((key) => {
        const value = this._formatObject(obj[key], depth + 1);
        return `${key}: ${value}`;
      });

      return `{${formattedEntries.join(", ")}${
        truncated ? `, ... +${keys.length - logConfig.maxObjectKeys}屬性` : ""
      }}`;
    } catch (err) {
      return `[格式化錯誤: ${err.message}]`;
    }
  },

  /**
   * 格式化所有參數
   * @param {...any} args - 要格式化的參數
   * @returns {Array} 格式化後的參數數組
   */
  _formatArgs(...args) {
    // 如果設置為展開物件，則使用 JSON.stringify
    if (logConfig.expandObjects) {
      try {
        const cache = new Set();
        return args.map((arg) => {
          if (typeof arg !== "object" || arg === null) return arg;

          return JSON.stringify(
            arg,
            (key, value) => {
              if (typeof value === "object" && value !== null) {
                if (cache.has(value)) {
                  return "[循環引用]";
                }
                cache.add(value);
              }
              return value;
            },
            2
          );
        });
      } catch (err) {
        return args.map((arg) => String(arg));
      }
    }

    // 否則使用簡潔格式
    return args.map((arg) => this._formatObject(arg));
  },

  /**
   * 設置日誌格式化配置
   * @param {Object} config - 配置項
   */
  configure(config) {
    Object.assign(logConfig, config);
  },

  debug(component, message, ...args) {
    if (currentLogLevel <= LogLevel.DEBUG) {
      console.log(
        `🏎️ [DEBUG]`,
        `${component}:${message}`,
        ...this._formatArgs(...args),
        formatTimestamp(new Date(), { showRelative: false })
      );
    }
  },

  /**
   * 輸出信息級別日誌
   * @param {string} component - 組件名稱
   * @param {string} message - 日誌訊息
   * @param {...any} args - 其他參數
   */
  info(component, message, ...args) {
    if (currentLogLevel <= LogLevel.INFO) {
      console.log(
        `ℹ️ [INFO]`,
        `${component}:${message}`,
        ...this._formatArgs(...args),
        formatTimestamp(new Date(), { showRelative: false })
      );
    }
  },

  /**
   * 輸出警告級別日誌
   * @param {string} component - 組件名稱
   * @param {string} message - 日誌訊息
   * @param {...any} args - 其他參數
   */
  warn(component, message, ...args) {
    if (currentLogLevel <= LogLevel.WARN) {
      console.warn(
        `⚠️ [WARN]`,
        `${component}:${message}`,
        ...this._formatArgs(...args),
        formatTimestamp(new Date(), { showRelative: false })
      );
    }
  },

  /**
   * 輸出錯誤級別日誌
   * @param {string} component - 組件名稱
   * @param {string} message - 日誌訊息
   * @param {...any} args - 其他參數
   */
  error(component, message, ...args) {
    if (currentLogLevel <= LogLevel.ERROR) {
      console.error(
        `❌ [ERROR]`,
        `${component}:${message}`,
        ...this._formatArgs(...args),
        formatTimestamp(new Date(), { showRelative: false })
      );
    }
  },
};
