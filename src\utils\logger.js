const winston = require("winston");
const path = require("path");
const fs = require("fs");
require("dotenv").config();

// 創建日誌目錄
const logDir = path.join(__dirname, "../../logs");
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// 定義日誌格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// 定義日誌級別
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// 日誌級別
const LogLevel = {
  ERROR: "error",
  WARN: "warn",
  INFO: "info",
  DEBUG: "debug",
};

// 從環境變數獲取控制台日誌級別，預設為 'info'
const consoleLogLevel = process.env.CONSOLE_LOG_LEVEL || "info";
// console.log("consoleLogLevel", consoleLogLevel);
const logger = winston.createLogger({
  levels,
  format: logFormat,
  transports: [
    // 控制台輸出
    new winston.transports.Console({
      level: consoleLogLevel,
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
        winston.format.printf(({ level, message, timestamp, ...meta }) => {
          const metaStr = Object.keys(meta).length
            ? JSON.stringify(meta, null, 2)
            : "";
          return `${timestamp} ${level}: ${message} ${metaStr}`;
        })
      ),
    }),
    // 檔案輸出
    new winston.transports.File({
      filename: path.join(logDir, "error.log"),
      level: "error",
    }),
    new winston.transports.File({
      filename: path.join(logDir, "all-logs.log"),
      level: "info",
    }),
  ],
});

// API 請求日誌格式化
const formatApiLog = (req, res, responseTime) => ({
  timestamp: new Date().toISOString(),
  method: req.method,
  url: req.originalUrl,
  status: res.statusCode,
  responseTime: `${responseTime}ms`,
  userAgent: req.get("user-agent"),
  ip: req.ip,
  userId: req.user?.id || "anonymous",
});

// 創建一個包裝器，確保所有日誌方法都可用
const wrappedLogger = {
  error: (message, meta = {}) => logger.error(message, meta),
  warn: (message, meta = {}) => logger.warn(message, meta),
  info: (message, meta = {}) => logger.info(message, meta),
  debug: (message, meta = {}) => logger.debug(message, meta),
  log: (level, message, meta = {}) => logger.log(level, message, meta),
};

module.exports = {
  logger: wrappedLogger,
  LogLevel,
  formatApiLog,
};
