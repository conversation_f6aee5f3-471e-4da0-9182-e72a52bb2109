import "highlight.js/styles/github.css";
import "katex/dist/katex.min.css";
import MarkdownIt from "markdown-it";
import mk from "markdown-it-katex";
import mh from "markdown-it-highlightjs";

export function renderMarkdown(content) {
  if (!content) return "";

  // 確保對 markdown 語法特殊字符的處理
  // 對於類似 ## 標題 這樣的語法，需要在 # 後面添加空格才能正確解析
  let processedContent = content;

  // 初始化 MarkdownIt 實例並啟用所有選項
  const md = new MarkdownIt({
    html: true, // 啟用 HTML 標籤
    xhtmlOut: true, // 使用 '/' 閉合單標籤 (<br />)
    breaks: true, // 轉換段落裡的 '\n' 到 <br>
    linkify: true, // 自動轉換 URL 到連結
    typographer: true, // 啟用一些語言中性替換 + 引號美化
    quotes: "\"\"''", // 引號替換對
  });

  // 添加數學公式支持
  md.use(mk);

  // 添加代碼高亮
  md.use(mh);

  // 確保列表功能被啟用
  md.enable(["heading", "list"]);

  // 自定義列表渲染規則
  md.renderer.rules.ordered_list_open = () => '<ol class="md-ordered-list">\n';
  md.renderer.rules.ordered_list_close = () => "</ol>\n";

  const result = md.render(processedContent);
  return `<div class="markdown-content">${result}</div>`;
}

// 處理內容，確保列表格式正確
function processContent(content) {
  if (!content) return "";

  // 確保有序列表的數字後面有空格
  // 例如：將 "1.項目" 轉換為 "1. 項目"
  return content.replace(/(\d+)\.([\S])/g, "$1. $2");
}
