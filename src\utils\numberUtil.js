// 格式化數字
export const formatNumber = (value) => {
  if (value === undefined || value === null) return "N/A";
  return typeof value === "number" ? value.toFixed(4) : value;
};

//------------------ 統計學計算 (由柯先生提供)------------------
// 計算標準差
export const calculateStandardDeviation = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const mean = calculateMean(data);
  const squaredDiffs = data.map((value) => Math.pow(value - mean, 2));
  const avgSquaredDiff =
    squaredDiffs.reduce((acc, val) => acc + val, 0) / data.length;
  return Math.sqrt(avgSquaredDiff);
};

// 計算平均值
export const calculateMean = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const sum = data.reduce((acc, val) => acc + val, 0);
  return sum / data.length;
};

// 計算中位數
export const calculateMedian = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const sortedData = [...data].sort((a, b) => a - b);
  const middle = Math.floor(sortedData.length / 2);

  return sortedData.length % 2 !== 0
    ? sortedData[middle]
    : (sortedData[middle - 1] + sortedData[middle]) / 2;
};

// 計算眾數

export const calculateMode = (data) => {
  if (!Array.isArray(data) || data.length === 0) return null;
  const frequencyMap = new Map();
  let maxCount = 0;
  let mode = null;

  for (const value of data) {
    const count = (frequencyMap.get(value) || 0) + 1;
    frequencyMap.set(value, count);

    if (count > maxCount) {
      maxCount = count;
      mode = value;
    }
  }

  return mode;
};

// 計算四分位數
export const calculateQuartiles = (data) => {
  if (!Array.isArray(data) || data.length === 0)
    return { q1: 0, q3: 0, iqr: 0 };
  const sortedData = [...data].sort((a, b) => a - b);
  const middle = Math.floor(sortedData.length / 2);

  const q1 = calculateMedian(sortedData.slice(0, middle));
  const q3 = calculateMedian(sortedData.slice(middle));
  const iqr = q3 - q1;

  return { q1, q3, iqr };
};

// 計算偏度
export const calculateSkewness = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const mean = calculateMean(data);
  const stdDev = calculateStandardDeviation(data);
  const cubedDiffs = data.map((value) => Math.pow(value - mean, 3));
  const avgCubedDiff =
    cubedDiffs.reduce((acc, val) => acc + val, 0) / data.length;
  return avgCubedDiff / Math.pow(stdDev, 3);
};

// 計算峰度
export const calculateKurtosis = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const mean = calculateMean(data);
  const stdDev = calculateStandardDeviation(data);

  const cubedDiffs = data.map((value) => Math.pow(value - mean, 4));
  const avgCubedDiff =
    cubedDiffs.reduce((acc, val) => acc + val, 0) / data.length;
  return avgCubedDiff / Math.pow(stdDev, 4) - 3;
};

// 計算最小值
export const calculateMin = (data) => {
  if (!Array.isArray(data) || data.length === 0) return null;
  return Math.min(...data);
};

// 計算最大值
export const calculateMax = (data) => {
  if (!Array.isArray(data) || data.length === 0) return null;
  return Math.max(...data);
};

// 計算總和
export const calculateSum = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  return data.reduce((acc, val) => acc + val, 0);
};

// 計算方差
export const calculateVariance = (data) => {
  if (!Array.isArray(data) || data.length === 0) return 0;
  const mean = calculateMean(data);
  const squaredDiffs = data.map((value) => Math.pow(value - mean, 2));
  const avgSquaredDiff =
    squaredDiffs.reduce((acc, val) => acc + val, 0) / data.length;
  return avgSquaredDiff;
};

// 計算標準化數據
export const normalizeData = (data) => {
  if (!Array.isArray(data) || data.length === 0) return [];
  const mean = calculateMean(data);
  const stdDev = calculateStandardDeviation(data);

  return data.map((value) => (value - mean) / stdDev);
};
