/**
 * 工作流調試工具
 * 用於監控和調試分流匯流執行問題
 */

export class WorkflowDebugger {
  constructor() {
    this.eventLog = [];
    this.nodeStates = new Map();
    this.isLogging = false;
  }

  /**
   * 開始記錄工作流事件
   */
  startLogging() {
    this.isLogging = true;
    this.eventLog = [];
    this.nodeStates.clear();
    
    // 監聽節點狀態變更事件
    window.addEventListener('flow:nodeStateChange', this.handleNodeStateChange.bind(this));
    window.addEventListener('workflow:completed', this.handleWorkflowCompleted.bind(this));
    
    console.log('🔍 WorkflowDebugger: 開始記錄工作流事件');
  }

  /**
   * 停止記錄工作流事件
   */
  stopLogging() {
    this.isLogging = false;
    
    window.removeEventListener('flow:nodeStateChange', this.handleNodeStateChange.bind(this));
    window.removeEventListener('workflow:completed', this.handleWorkflowCompleted.bind(this));
    
    console.log('🔍 WorkflowDebugger: 停止記錄工作流事件');
  }

  /**
   * 處理節點狀態變更事件
   */
  handleNodeStateChange(event) {
    if (!this.isLogging) return;

    const { nodeId, status, timestamp } = event.detail;
    const logEntry = {
      type: 'nodeStateChange',
      nodeId,
      status,
      timestamp,
      time: new Date().toISOString()
    };

    this.eventLog.push(logEntry);
    this.nodeStates.set(nodeId, { status, timestamp });

    console.log(`🔍 節點狀態變更: ${nodeId} -> ${status} (${timestamp})`);
  }

  /**
   * 處理工作流完成事件
   */
  handleWorkflowCompleted(event) {
    if (!this.isLogging) return;

    const logEntry = {
      type: 'workflowCompleted',
      timestamp: event.detail.timestamp,
      time: new Date().toISOString()
    };

    this.eventLog.push(logEntry);
    console.log('🔍 工作流完成');
  }

  /**
   * 分析重複事件
   */
  analyzeEventDuplication() {
    const eventCounts = new Map();
    
    this.eventLog.forEach(entry => {
      if (entry.type === 'nodeStateChange') {
        const key = `${entry.nodeId}-${entry.status}`;
        const count = eventCounts.get(key) || 0;
        eventCounts.set(key, count + 1);
      }
    });

    const duplicates = Array.from(eventCounts.entries())
      .filter(([key, count]) => count > 1)
      .map(([key, count]) => ({ event: key, count }));

    if (duplicates.length > 0) {
      console.warn('🚨 發現重複事件:', duplicates);
    } else {
      console.log('✅ 沒有發現重複事件');
    }

    return duplicates;
  }

  /**
   * 分析分流匯流執行順序
   */
  analyzeFlowExecution() {
    const nodeEvents = this.eventLog.filter(entry => entry.type === 'nodeStateChange');
    const completedNodes = nodeEvents.filter(entry => entry.status === 'completed');
    
    console.log('📊 節點完成順序:');
    completedNodes.forEach((entry, index) => {
      console.log(`  ${index + 1}. ${entry.nodeId} (${entry.timestamp})`);
    });

    return completedNodes;
  }

  /**
   * 檢查並行節點執行
   */
  checkParallelExecution() {
    const runningEvents = this.eventLog.filter(entry => 
      entry.type === 'nodeStateChange' && entry.status === 'running'
    );

    // 檢查是否有節點同時執行
    const timeGroups = new Map();
    runningEvents.forEach(entry => {
      const timeKey = Math.floor(new Date(entry.timestamp).getTime() / 1000); // 按秒分組
      if (!timeGroups.has(timeKey)) {
        timeGroups.set(timeKey, []);
      }
      timeGroups.get(timeKey).push(entry.nodeId);
    });

    const parallelExecutions = Array.from(timeGroups.entries())
      .filter(([time, nodes]) => nodes.length > 1)
      .map(([time, nodes]) => ({ time: new Date(time * 1000).toISOString(), nodes }));

    if (parallelExecutions.length > 0) {
      console.log('🔀 發現並行執行:', parallelExecutions);
    }

    return parallelExecutions;
  }

  /**
   * 生成調試報告
   */
  generateReport() {
    console.log('\n📋 工作流調試報告');
    console.log('==================');
    
    console.log(`總事件數: ${this.eventLog.length}`);
    console.log(`節點狀態數: ${this.nodeStates.size}`);
    
    const duplicates = this.analyzeEventDuplication();
    const completedNodes = this.analyzeFlowExecution();
    const parallelExecutions = this.checkParallelExecution();

    return {
      totalEvents: this.eventLog.length,
      nodeCount: this.nodeStates.size,
      duplicates,
      completedNodes,
      parallelExecutions,
      eventLog: this.eventLog
    };
  }

  /**
   * 清除記錄
   */
  clear() {
    this.eventLog = [];
    this.nodeStates.clear();
    console.log('🔍 WorkflowDebugger: 清除記錄');
  }
}

// 創建全局實例
export const workflowDebugger = new WorkflowDebugger();

// 在開發環境下自動暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.workflowDebugger = workflowDebugger;
}
