/**
 * Markdown 渲染工具
 */
import { marked } from "marked";
import DOMPurify from "dompurify";

// 配置 marked 選項
marked.setOptions({
  gfm: true, // 啟用 GitHub 風格的 Markdown
  breaks: true, // 轉換段落裡的 '\n' 到 <br>
  headerIds: true, // 為標題添加 id
  mangle: false, // 不轉義內容
  sanitize: false, // 不進行內部消毒（我們使用 DOMPurify）
});

/**
 * 安全地渲染 Markdown 內容
 * @param {string} content - Markdown 格式的內容
 * @returns {string} 渲染後的 HTML
 */
export function renderMarkdown(content) {
  if (!content) return "";

  // 先使用 marked 渲染 Markdown
  const html = marked(content);

  // 使用 DOMPurify 清理 HTML，防止 XSS 攻擊
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "p",
      "br",
      "hr",
      "ul",
      "ol",
      "li",
      "strong",
      "em",
      "del",
      "blockquote",
      "code",
      "pre",
      "a",
      "img",
      "table",
      "thead",
      "tbody",
      "tr",
      "th",
      "td",
    ],
    ALLOWED_ATTR: ["href", "src", "alt", "title", "class", "target"],
    ALLOW_DATA_ATTR: false,
    ADD_ATTR: {
      a: 'target="_blank" rel="noopener noreferrer"',
    },
  });
}
