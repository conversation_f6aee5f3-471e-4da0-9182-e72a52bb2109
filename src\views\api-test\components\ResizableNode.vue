<script setup>
import { Handle, Position } from "@vue-flow/core";
import { NodeResizer } from "@vue-flow/node-resizer";
import "@vue-flow/node-resizer/dist/style.css";
const emit = defineEmits(["resize"]);
defineProps(["data"]);

const onResize = (event, data) => {
  console.log("onResize", event, data);
  emit("resize", event, data);
};
</script>

<template>
  <NodeResizer
    :min-width="250"
    :max-width="1000"
    :min-height="280"
    :max-height="500"
    :is-visible="selected"
    :line-style="{ borderColor: '#3b82f6' }"
    :handle-style="{ backgroundColor: 'red', width: '24px', height: '24px' }"
    @resize="onResize" />

  <Handle
    type="target"
    :position="Position.Left" />
  <div style="padding: 10px">{{ data.name || data.label }}</div>
  <Handle
    type="source"
    :position="Position.Right" />
</template>
