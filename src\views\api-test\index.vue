<template>
  <div>
    <VueFlow
      :nodes="nodes"
      fit-view-on-init>
      <template #node-resizable="resizableNodeProps">
        <ResizableNode
          :data="resizableNodeProps.data"
          @resize="onNodeResize" />
      </template>
    </VueFlow>
    <el-button>我是無樣式按鈕</el-button>
    <el-button type="primary">我是主色按鈕</el-button>
    <el-button type="success">我是成功按鈕</el-button>
    <el-button type="warning">我是警告按鈕</el-button>
    <el-button type="danger">我是危險按鈕</el-button>
    <el-button type="info">我是資訊按鈕</el-button>
    <el-button
      type="primary"
      plain
      >我是主色按鈕,plain</el-button
    >
    <el-button
      type="success"
      plain
      >我是成功按鈕,plain</el-button
    >
    <el-button
      type="warning"
      plain
      >我是警告按鈕,plain</el-button
    >
    <el-button
      type="danger"
      plain
      >我是危險按鈕,plain</el-button
    >
    <el-button
      type="info"
      plain
      >我是資訊按鈕,plain</el-button
    >
  </div>

  <el-button
    type="primary"
    plain
    @click="handleClick"
    >Hello</el-button
  >
</template>
<script setup>
import { ref } from "vue";
import { VueFlow } from "@vue-flow/core";
import ResizableNode from "./components/ResizableNode.vue";
import request from "@/api/request";
const nodes = ref([
  {
    id: "1",
    type: "resizable",
    data: { label: "NodeResizer" },
    position: { x: 0, y: 0 },
    style: { background: "#fff", border: "2px solid black" },
  },
]);

const handleClick = () => {
  console.log("handleClick");
  request.post("/external/heelo/sfjoejfeofw/sifsjfojo", {
    data: {
      data: {
        data: {},
      },
    },
  });
};

const onNodeResize = (event, node, resizeDirection) => {
  console.log("節點正在調整大小:", node.id);
  console.log("調整方向:", resizeDirection);
  console.log("當前節點尺寸:", node.dimensions);
  console.log("節點位置:", node.position);
};
</script>
