<template>
  <div
    class="min-h-screen flex items-center justify-center bg-light-mode dark:bg-dark-mode">
    <div class="grid"></div>
    <div
      class="w-[420px] bg-light-mode dark:bg-dark-mode p-8 rounded-xl shadow-lg z-50 border border-light-mode dark:border-dark-mode">
      <!-- Logo -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <img
              src="/logo_flowchart.svg"
              alt="logo"
              class="w-8 h-8" />
            <span class="text-2xl text-center text-gray-600 dark:text-dark-mode"
              >IYM良率分析系統</span
            >
          </div>
          <!-- 暗黑模式切換 -->
          <div
            class="cursor-pointer flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
            @click="toggleTheme">
            <el-tooltip
              :content="isDark ? '切換亮色模式' : '切換暗色模式'"
              placement="top">
              <Sun
                v-if="isDark"
                :size="18"
                class="text-gray-600 dark:text-dark-mode" />
              <Moon
                v-else
                :size="18"
                class="text-gray-600 dark:text-dark-mode" />
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 標題 -->
      <!-- <h1 class="text-xl font-semibold text-gray-900">登入</h1> -->
      <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">
        歡迎回來！請輸入您的帳號密碼
      </p>

      <!-- 登入表單 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        @submit.prevent="handleSubmit">
        <!-- 廠區選擇 -->
        <div class="mb-4">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-dark-mode mb-1"
            >廠區</label
          >
          <el-form-item
            prop="domain"
            class="mb-0">
            <el-select
              v-model="formData.domain"
              placeholder="請選擇廠區"
              size="large"
              class="w-full">
              <el-option
                v-for="item in domainOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 帳號 -->
        <div class="mb-4">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-dark-mode mb-1"
            >帳號</label
          >
          <el-form-item
            prop="username"
            class="mb-0">
            <el-input
              v-model="formData.username"
              placeholder="請輸入您的帳號"
              size="large"
              class="login-input" />
          </el-form-item>
        </div>

        <!-- 密碼 -->
        <div class="mb-4 mt-6">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-dark-mode mb-1"
            >密碼</label
          >
          <el-form-item
            prop="password"
            class="mb-0">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="••••••••"
              size="large"
              show-password
              class="login-input" />
          </el-form-item>
        </div>

        <!-- 記住我和忘記密碼 -->
        <div class="flex items-center justify-between mb-6">
          <el-checkbox
            v-model="formData.remember"
            class="remember-me">
            記住我一週
          </el-checkbox>
          <el-link
            type="primary"
            @click="handleForgotPassword">
            忘記密碼？
          </el-link>
        </div>

        <!-- 登入按鈕 -->
        <el-button
          type="primary"
          size="large"
          class="w-full mb-4 mt-4 text-lg"
          :loading="loading"
          @click="handleSubmit">
          登 入
        </el-button>

        <!-- 測試登入按鈕 -->
        <div class="flex justify-between align-center">
          <div>
            <el-button
              type="info"
              link
              size="small"
              class="w-full"
              @click="handleTestLogin">
              ADMIN 測試帳號
            </el-button>
          </div>
          <div>
            <el-button
              type="info"
              link
              size="small"
              class="w-full"
              @click="handleTestLogin2">
              ADMIN 測試帳號2
            </el-button>
          </div>
          <div>
            <el-button
              type="success"
              link
              size="small"
              class="w-full"
              @click="handleReaderLogin">
              一般用戶測試帳號
            </el-button>
          </div>
        </div>
        <!-- Google 登入 -->
        <!-- <el-button
          size="large"
          class="w-full flex items-center justify-center space-x-2 border border-gray-300 hover:border-[#1976D2] hover:text-[#1976D2] transition-colors"
        >
          <img src="@/assets/google.svg" alt="Google" class="w-5 h-5" />
          <span>Sign in with Google</span>
        </el-button> -->

        <!-- 註冊連結 -->
        <!-- <div class="text-center mt-6">
          <p class="text-gray-600 text-sm">
            Don't have an account?
            <el-button
              link
              type="primary"
              class="sign-up-link"
              @click="handleRegister"
            >
              Sign up
            </el-button>
          </p>
        </div> -->
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from "@/stores/user";
import { useThemeMode } from "@/composables/useThemeMode";

const router = useRouter();
const route = useRoute();
const formRef = ref(null);
const loading = ref(false);
const userStore = useUserStore();
const { isDark, toggleTheme } = useThemeMode();

// 廠區選項
const domainOptions = [
  { label: "高雄廠", value: "flexium" },
  { label: "昆山廠", value: "flexiumcn" },
  { label: "郡昆廠", value: "junkun" },
  { label: "本地測試", value: "local" },
];

// 表單數據
const formData = reactive({
  domain: "",
  username: "",
  password: "",
  remember: false,
});

// 表單驗證規則
const rules = {
  domain: [{ required: true, message: "請選擇廠區", trigger: "change" }],
  username: [{ required: true, message: "請輸入帳號", trigger: "blur" }],
  password: [
    { required: true, message: "請輸入密碼", trigger: "blur" },
    { min: 6, message: "密碼長度至少為 6 個字符", trigger: "blur" },
  ],
};

// 處理登入
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    await userStore.handleLogin({
      username: formData.username,
      password: formData.password,
      domain: formData.domain,
      remember: formData.remember,
    });

    // 如果有重定向地址，則跳轉到重定向地址
    const redirectPath = route.query.redirect || "/";
    router.push(redirectPath);

    ElMessage.success("登入成功");
  } catch (error) {
    console.error("登入錯誤:", error);
    ElMessage.error(error.message || "登入失敗");
  } finally {
    loading.value = false;
  }
};

// 處理忘記密碼
const handleForgotPassword = () => {
  ElMessage.info("忘記密碼功能開發中");
};

// 處理註冊
const handleRegister = () => {
  router.push("/register");
};

// 處理測試登入
const handleTestLogin = () => {
  formData.domain = "flexium";
  formData.username = "john_hsiao";
  formData.password = "qsceszK28";
};
const handleTestLogin2 = () => {
  formData.domain = "flexium";
  formData.username = "dinoin_chen";
  formData.password = "Dinoin123";
};

// 處理一般用戶測試登入
const handleReaderLogin = () => {
  formData.domain = "local";
  formData.username = "reader001";
  formData.password = "Reader@123";
};
</script>

<style scoped>
.login-input :deep(.el-input__wrapper) {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: none !important;
}

.login-input :deep(.el-input__wrapper):hover {
  border-color: #1976d2;
}

.login-input :deep(.el-input__wrapper.is-focus) {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1) !important;
}

.el-button--primary {
  @apply !bg-[#1976D2] hover:!bg-[#1565C0] !border-none;
  height: 44px;
  border-radius: 8px;
}

.remember-me :deep(.el-checkbox__label) {
  @apply !text-gray-600 text-sm;
}

.sign-up-link {
  @apply !text-[#1976D2] font-medium hover:!text-[#1565C0];
}

.grid {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(30, 41, 59, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(30, 41, 59, 0.1) 1px, transparent 1px);
  background-size: 38px 38px;
  transform-origin: center;
  animation: gridAnimation 20s linear infinite;
}

.content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 20px;
  text-align: center;
}

h1 {
  font-size: 64px;
  line-height: 1.1;
  margin-bottom: 24px;
  background: linear-gradient(to right, #1e293b 20%, rgba(30, 41, 59, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 800;
}

@keyframes gridAnimation {
  0% {
    transform: perspective(10px) rotateX(0deg) translateY(0);
  }
  100% {
    transform: perspective(10px) rotateX(0deg) translateY(50px);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 40px;
  }
  p {
    font-size: 18px;
  }
}

/* 暗黑模式下的登入頁面樣式 */
:deep(.dark) .el-input__wrapper,
:deep(.dark) .el-select .el-input__wrapper {
  border-color: #374151;
}

:deep(.dark) .el-input__inner,
:deep(.dark) .el-select .el-input__inner {
  color: #e5e7eb;
}

:deep(.dark) .el-form-item__label {
  color: #e5e7eb;
}

:deep(.dark) .el-checkbox__label {
  color: #e5e7eb !important;
}

:deep([class*="popper"]) {
  border-color: #374151;
}

html.dark .grid {
  background-image: linear-gradient(
      rgba(229, 231, 235, 0.07) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(229, 231, 235, 0.07) 1px, transparent 1px);
}
</style>
