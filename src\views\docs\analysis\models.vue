<template>
  <DocTemplate
    title="分析模型"
    description="詳細介紹系統中的分析模型及其使用方法"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="8"
    :prev-page="{ path: '/docs/analysis/overview', title: '分析概述' }"
    :next-page="{ path: '/docs/analysis/visualization', title: '數據可視化' }"
    edit-link="https://github.com/your-repo/docs/edit/main/analysis/models.md">
    <div class="models-content">
      <h2>模型概述</h2>
      <p>
        IYM
        系統提供了多種分析模型，用於預測、優化和決策支持。這些模型基於機器學習和統計分析技術，可以幫助企業更好地理解和管理生產過程。
      </p>

      <h2>主要模型</h2>
      <div class="model-list">
        <div class="model-item">
          <h3>產量預測模型</h3>
          <p>基於歷史數據和環境因素，預測未來產量趨勢。</p>
          <ul>
            <li>支援多種預測算法（ARIMA、LSTM等）</li>
            <li>可自定義預測時間範圍</li>
            <li>提供置信區間分析</li>
          </ul>
        </div>

        <div class="model-item">
          <h3>質量控制模型</h3>
          <p>監控和預測產品質量，識別潛在問題。</p>
          <ul>
            <li>實時質量監控</li>
            <li>異常檢測和預警</li>
            <li>質量趨勢分析</li>
          </ul>
        </div>

        <div class="model-item">
          <h3>資源優化模型</h3>
          <p>優化資源分配，提高生產效率。</p>
          <ul>
            <li>資源利用率分析</li>
            <li>成本效益評估</li>
            <li>優化建議生成</li>
          </ul>
        </div>
      </div>

      <h2>模型使用指南</h2>
      <h3>數據準備</h3>
      <pre><code>// 示例：準備訓練數據
const trainingData = {
  features: ['temperature', 'humidity', 'pressure'],
  target: 'yield',
  data: [
    { temperature: 25, humidity: 60, pressure: 1013, yield: 95 },
    // ... 更多數據
  ]
};</code></pre>

      <h3>模型訓練</h3>
      <pre><code>// 示例：訓練預測模型
const model = new YieldPredictionModel({
  algorithm: 'lstm',
  epochs: 100,
  batchSize: 32
});

await model.train(trainingData);</code></pre>

      <h3>模型評估</h3>
      <p>系統提供多種評估指標：</p>
      <ul>
        <li>均方誤差 (MSE)</li>
        <li>決定係數 (R²)</li>
        <li>平均絕對誤差 (MAE)</li>
      </ul>

      <h2>最佳實踐</h2>
      <ul>
        <li>定期更新訓練數據</li>
        <li>監控模型性能</li>
        <li>進行交叉驗證</li>
        <li>保持模型版本控制</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.models-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .model-list {
    @apply space-y-8;

    .model-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-primary dark:text-primary-dark;
      }

      ul {
        @apply mt-4 pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  pre {
    @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 overflow-x-auto;
  }

  code {
    @apply font-mono text-sm;
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
