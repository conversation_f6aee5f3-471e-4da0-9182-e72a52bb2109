<template>
  <DocTemplate
    title="數據處理流程"
    description="詳細介紹 IYM 系統的數據處理流程和步驟"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="8"
    :prev-page="{ path: '/docs/analysis/overview', title: '分析系統概述' }"
    :next-page="{ path: '/docs/analysis/models', title: '分析模型說明' }">
    <div class="process-content">
      <h2>數據處理流程概述</h2>
      <p>
        IYM
        系統的數據處理流程分為數據收集、預處理、分析和可視化四個主要階段，每個階段都有其特定的處理步驟和工具。
      </p>

      <h2>數據收集階段</h2>
      <div class="process-step">
        <h3>1. 數據源接入</h3>
        <ul>
          <li>支援多種數據源：數據庫、API、文件等</li>
          <li>自動化數據採集</li>
          <li>實時數據同步</li>
        </ul>

        <h3>2. 數據驗證</h3>
        <pre><code>// 示例：數據驗證規則
const validationRules = {
  required: true,
  type: 'number',
  min: 0,
  max: 100,
  format: 'float'
};</code></pre>
      </div>

      <h2>數據預處理階段</h2>
      <div class="process-step">
        <h3>1. 數據清洗</h3>
        <ul>
          <li>處理缺失值</li>
          <li>去除異常值</li>
          <li>數據標準化</li>
        </ul>

        <h3>2. 數據轉換</h3>
        <pre><code>// 示例：數據轉換函數
function transformData(data) {
  return {
    ...data,
    timestamp: new Date(data.timestamp),
    value: parseFloat(data.value),
    category: data.category.toLowerCase()
  };
}</code></pre>
      </div>

      <h2>分析階段</h2>
      <div class="process-step">
        <h3>1. 特徵工程</h3>
        <ul>
          <li>特徵選擇</li>
          <li>特徵提取</li>
          <li>特徵組合</li>
        </ul>

        <h3>2. 模型訓練</h3>
        <pre><code>// 示例：模型訓練配置
const modelConfig = {
  algorithm: 'random_forest',
  parameters: {
    n_estimators: 100,
    max_depth: 10
  },
  validation_split: 0.2
};</code></pre>
      </div>

      <h2>可視化階段</h2>
      <div class="process-step">
        <h3>1. 數據可視化</h3>
        <ul>
          <li>圖表生成</li>
          <li>交互式展示</li>
          <li>自定義樣式</li>
        </ul>

        <h3>2. 報告生成</h3>
        <pre><code>// 示例：報告配置
const reportConfig = {
  title: '產量分析報告',
  charts: ['trend', 'distribution', 'correlation'],
  metrics: ['mean', 'std', 'min', 'max'],
  format: 'pdf'
};</code></pre>
      </div>

      <h2>最佳實踐</h2>
      <ul>
        <li>定期檢查數據質量</li>
        <li>保持數據處理流程的可追溯性</li>
        <li>優化處理性能</li>
        <li>確保數據安全性</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.process-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .process-step {
    @apply mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

    h3 {
      @apply text-dark-mode dark:text-light-mode;
    }

    ul {
      @apply pl-6 mb-4;

      li {
        @apply text-gray-600 dark:text-gray-400 mb-2;
      }
    }

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
