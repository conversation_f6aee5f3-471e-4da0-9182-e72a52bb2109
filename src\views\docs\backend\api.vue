<template>
  <DocTemplate
    title="API 文檔"
    description="詳細的後端 API 接口文檔，包含請求方法、參數和響應格式"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="10"
    :prev-page="{ path: '/docs/backend/architecture', title: '架構設計' }"
    :next-page="{ path: '/docs/backend/security', title: '安全機制' }"
    edit-link="https://github.com/your-repo/docs/edit/main/backend/api.md">
    <div class="api-content">
      <h2>API 概述</h2>
      <p>
        IYM 系統提供 RESTful API 接口，支援 JSON 格式的數據交換。所有 API
        請求都需要進行身份驗證。
      </p>

      <h2>認證機制</h2>
      <p>
        API 使用 JWT (JSON Web Token) 進行身份驗證。在請求頭中添加 Authorization
        字段：
      </p>
      <pre><code>Authorization: Bearer &lt;your_jwt_token&gt;</code></pre>

      <h2>API 端點</h2>
      <div class="api-endpoints">
        <div class="endpoint-item">
          <h3>用戶管理</h3>
          <div class="endpoint-method">
            <span class="method get">GET</span>
            <code>/api/v1/users</code>
            <span class="description">獲取用戶列表</span>
          </div>
          <div class="endpoint-method">
            <span class="method post">POST</span>
            <code>/api/v1/users</code>
            <span class="description">創建新用戶</span>
          </div>
        </div>

        <div class="endpoint-item">
          <h3>產量數據</h3>
          <div class="endpoint-method">
            <span class="method get">GET</span>
            <code>/api/v1/yield-data</code>
            <span class="description">獲取產量數據</span>
          </div>
          <div class="endpoint-method">
            <span class="method post">POST</span>
            <code>/api/v1/yield-data</code>
            <span class="description">上傳產量數據</span>
          </div>
        </div>

        <div class="endpoint-item">
          <h3>分析模型</h3>
          <div class="endpoint-method">
            <span class="method post">POST</span>
            <code>/api/v1/models/predict</code>
            <span class="description">執行模型預測</span>
          </div>
          <div class="endpoint-method">
            <span class="method get">GET</span>
            <code>/api/v1/models/status</code>
            <span class="description">獲取模型狀態</span>
          </div>
        </div>
      </div>

      <h2>請求示例</h2>
      <h3>獲取用戶列表</h3>
      <pre><code>// 請求
GET /api/v1/users
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 響應
{
  "code": 200,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "role": "admin"
      }
    ],
    "total": 1
  }
}</code></pre>

      <h2>錯誤處理</h2>
      <p>所有錯誤響應都遵循以下格式：</p>
      <pre><code>{
  "code": 400,
  "message": "錯誤描述",
  "errors": {
    "field": ["錯誤詳情"]
  }
}</code></pre>

      <h2>常見錯誤碼</h2>
      <ul>
        <li>400 - 請求參數錯誤</li>
        <li>401 - 未授權</li>
        <li>403 - 禁止訪問</li>
        <li>404 - 資源不存在</li>
        <li>500 - 服務器錯誤</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.api-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .api-endpoints {
    @apply space-y-8;

    .endpoint-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-primary dark:text-primary-dark mb-4;
      }

      .endpoint-method {
        @apply flex items-center gap-4 mb-4;

        .method {
          @apply px-3 py-1 rounded text-sm font-medium;

          &.get {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100;
          }

          &.post {
            @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100;
          }

          &.put {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100;
          }

          &.delete {
            @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100;
          }
        }

        code {
          @apply font-mono text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded;
        }

        .description {
          @apply text-gray-600 dark:text-gray-400;
        }
      }
    }
  }

  pre {
    @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 overflow-x-auto;
  }

  code {
    @apply font-mono text-sm;
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
