<template>
  <DocTemplate
    title="數據庫設計"
    description="詳細介紹 IYM 系統的數據庫結構和設計"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="10"
    :prev-page="{ path: '/docs/backend/api', title: 'API 文檔' }">
    <div class="database-content">
      <h2>數據庫概述</h2>
      <p>
        IYM 系統使用 PostgreSQL 作為主要數據庫，Redis
        作為緩存，採用關係型數據庫設計，確保數據的一致性和完整性。
      </p>

      <h2>數據庫架構</h2>
      <div class="schema-overview">
        <h3>主要表結構</h3>
        <pre><code>// Prisma Schema
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  name      String?
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model YieldData {
  id        Int      @id @default(autoincrement())
  value     Float
  timestamp DateTime
  unit      String
  source    String
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AnalysisModel {
  id          Int      @id @default(autoincrement())
  name        String
  type        String
  parameters  Json
  status      String
  version     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}</code></pre>
      </div>

      <h2>表關係</h2>
      <div class="relationships">
        <div class="relationship-item">
          <h3>用戶與數據</h3>
          <ul>
            <li>一對多關係</li>
            <li>用戶可以創建多個數據記錄</li>
            <li>數據記錄包含創建者信息</li>
          </ul>
        </div>

        <div class="relationship-item">
          <h3>模型與數據</h3>
          <ul>
            <li>多對多關係</li>
            <li>模型可以處理多個數據集</li>
            <li>數據集可以被多個模型使用</li>
          </ul>
        </div>
      </div>

      <h2>索引設計</h2>
      <div class="indexes">
        <h3>主要索引</h3>
        <pre><code>// 索引定義
CREATE INDEX idx_yield_data_timestamp ON yield_data(timestamp);
CREATE INDEX idx_yield_data_source ON yield_data(source);
CREATE INDEX idx_analysis_model_type ON analysis_model(type);</code></pre>

        <h3>索引策略</h3>
        <ul>
          <li>時間序列數據按時間戳索引</li>
          <li>常用查詢字段建立索引</li>
          <li>外鍵字段自動索引</li>
        </ul>
      </div>

      <h2>數據遷移</h2>
      <div class="migrations">
        <h3>遷移文件示例</h3>
        <pre><code>// 遷移文件
migration {
  version: "1.0.0"
  description: "初始化數據庫結構"
  up {
    createTable("users") {
      column("id", "INTEGER") { primaryKey() }
      column("email", "VARCHAR") { unique() }
      column("password", "VARCHAR")
      column("name", "VARCHAR")
      column("role", "VARCHAR")
      column("created_at", "TIMESTAMP")
      column("updated_at", "TIMESTAMP")
    }
  }
  down {
    dropTable("users")
  }
}</code></pre>
      </div>

      <h2>最佳實踐</h2>
      <ul>
        <li>使用事務確保數據一致性</li>
        <li>定期備份重要數據</li>
        <li>監控數據庫性能</li>
        <li>優化查詢語句</li>
        <li>合理使用緩存</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.database-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .schema-overview {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  .relationships {
    @apply grid grid-cols-1 md:grid-cols-2 gap-8 mb-8;

    .relationship-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      ul {
        @apply pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  .indexes {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }

    ul {
      @apply pl-6;

      li {
        @apply text-gray-600 dark:text-gray-400 mb-2;
      }
    }
  }

  .migrations {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
