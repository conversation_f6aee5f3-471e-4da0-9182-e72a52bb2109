<template>
  <DocTemplate
    title="後端系統概述"
    description="介紹 IYM 系統的後端架構和核心功能"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="6"
    :next-page="{ path: '/docs/backend/api', title: 'API 文檔' }">
    <div class="backend-overview-content">
      <h2>系統架構</h2>
      <p>
        IYM 後端系統採用現代化的微服務架構，確保系統的可擴展性、可靠性和高性能。
      </p>

      <h2>核心模塊</h2>
      <div class="module-list">
        <div class="module-item">
          <h3>用戶認證模塊</h3>
          <ul>
            <li>JWT 認證</li>
            <li>角色權限管理</li>
            <li>單點登錄</li>
            <li>安全日誌</li>
          </ul>
        </div>

        <div class="module-item">
          <h3>數據管理模塊</h3>
          <ul>
            <li>數據庫操作</li>
            <li>緩存管理</li>
            <li>文件存儲</li>
            <li>數據備份</li>
          </ul>
        </div>

        <div class="module-item">
          <h3>分析引擎模塊</h3>
          <ul>
            <li>模型訓練</li>
            <li>預測分析</li>
            <li>實時計算</li>
            <li>結果存儲</li>
          </ul>
        </div>
      </div>

      <h2>技術棧</h2>
      <div class="tech-stack">
        <div class="tech-category">
          <h3>後端框架</h3>
          <ul>
            <li>Node.js + Express</li>
            <li>TypeScript</li>
            <li>Prisma ORM</li>
          </ul>
        </div>

        <div class="tech-category">
          <h3>數據庫</h3>
          <ul>
            <li>PostgreSQL</li>
            <li>Redis</li>
            <li>MongoDB</li>
          </ul>
        </div>

        <div class="tech-category">
          <h3>分析引擎</h3>
          <ul>
            <li>Python + FastAPI</li>
            <li>NumPy + Pandas</li>
            <li>Scikit-learn</li>
          </ul>
        </div>
      </div>

      <h2>部署架構</h2>
      <div class="deployment">
        <h3>容器化部署</h3>
        <pre><code># docker-compose.yml
version: '3.8'
services:
  api:
    image: iym-api:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/iym
    depends_on:
      - db
      - redis

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=iym

  redis:
    image: redis:6
    ports:
      - "6379:6379"</code></pre>

        <h3>負載均衡</h3>
        <ul>
          <li>Nginx 反向代理</li>
          <li>自動擴展</li>
          <li>健康檢查</li>
        </ul>
      </div>

      <h2>監控與日誌</h2>
      <ul>
        <li>Prometheus + Grafana 監控</li>
        <li>ELK 日誌收集</li>
        <li>錯誤追蹤系統</li>
        <li>性能分析工具</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.backend-overview-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .module-list {
    @apply space-y-8;

    .module-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      ul {
        @apply pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  .tech-stack {
    @apply grid grid-cols-1 md:grid-cols-3 gap-8;

    .tech-category {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      ul {
        @apply pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  .deployment {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }

    ul {
      @apply pl-6;

      li {
        @apply text-gray-600 dark:text-gray-400 mb-2;
      }
    }
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
