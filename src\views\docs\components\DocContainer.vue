<template>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component
        :is="Component"
        :key="$route.fullPath" />
    </keep-alive>
  </router-view>
</template>

<script setup>
import { watch } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

// 這只是一個簡單的容器組件，用於渲染子路由

// 監視路由變化
watch(
  () => route.fullPath,
  () => {
    console.log("DocContainer: 路由變化，捲動到頂部");
    // 延遲執行，確保 DOM 已更新
    setTimeout(() => {
      const mainContent = document.querySelector(".doc-main");
      if (mainContent) {
        mainContent.scrollTop = 0;
      }
    }, 50);
  }
);
</script>
