<template>
  <div class="doc-search">
    <!-- 搜索框 -->
    <div class="search-input">
      <el-input
        v-model="searchQuery"
        placeholder="搜索文檔..."
        class="w-full"
        @keyup.enter="handleSearch">
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 搜索結果 -->
    <transition name="fade">
      <div
        v-if="showResults && searchResults.length > 0"
        class="search-results">
        <div class="results-header">
          <span>搜索結果</span>
          <el-button
            type="text"
            @click="handleClose"
            class="close-btn">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="results-list">
          <router-link
            v-for="result in searchResults"
            :key="result.path"
            :to="result.path"
            class="result-item"
            @click="handleResultClick">
            <div class="result-title">
              <el-icon><Document /></el-icon>
              {{ result.title }}
            </div>
            <div
              class="result-excerpt"
              v-html="highlightText(result.excerpt)"></div>
            <div class="result-path">{{ result.path }}</div>
          </router-link>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { Search, Close, Document } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";

const router = useRouter();
const searchQuery = ref("");
const searchResults = ref([]);
const showResults = ref(false);

// 模擬搜索功能
const handleSearch = () => {
  // TODO: 實現搜索功能
  console.log("搜索:", searchQuery.value);
};

const handleClear = () => {
  searchQuery.value = "";
  searchResults.value = [];
  showResults.value = false;
};

const handleClose = () => {
  showResults.value = false;
};

const handleResultClick = () => {
  handleClose();
  searchQuery.value = "";
};

// 高亮搜索結果中的關鍵字
const highlightText = (text) => {
  if (!searchQuery.value) return text;
  const regex = new RegExp(`(${searchQuery.value})`, "gi");
  return text.replace(regex, "<mark>$1</mark>");
};

// 快捷鍵處理
const handleShortcut = (e) => {
  if ((e.metaKey || e.ctrlKey) && e.key === "k") {
    e.preventDefault();
    const input = document.querySelector(".search-input input");
    input?.focus();
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleShortcut);
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleShortcut);
});
</script>

<style lang="scss" scoped>
.doc-search {
  @apply relative;

  .search-input {
    @apply w-full;

    :deep(.el-input__wrapper) {
      @apply bg-gray-50 dark:bg-gray-800;
    }

    :deep(.el-input__inner) {
      @apply text-gray-700 dark:text-gray-200;
    }
  }

  .search-results {
    @apply absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50;

    .results-header {
      @apply flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700;

      span {
        @apply text-sm text-gray-500 dark:text-gray-400;
      }

      .close-btn {
        @apply text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300;
      }
    }

    .results-list {
      @apply max-h-80 overflow-y-auto;

      .result-item {
        @apply block p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-0 no-underline;

        .result-title {
          @apply flex items-center gap-2 text-gray-900 dark:text-gray-100 font-medium mb-1;
        }

        .result-excerpt {
          @apply text-sm text-gray-600 dark:text-gray-400 mb-1;

          :deep(mark) {
            @apply bg-yellow-200 dark:bg-yellow-900 rounded px-0.5;
          }
        }

        .result-path {
          @apply text-xs text-gray-400 dark:text-gray-500;
        }
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  @apply transition-all duration-200;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0 transform -translate-y-2;
}
</style>
