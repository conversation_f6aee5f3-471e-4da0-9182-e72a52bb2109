<template>
  <div class="doc-sidebar-content">
    <el-menu
      class="doc-menu !w-32"
      :default-active="activeMenu"
      @select="handleSelect">
      <!-- 動態生成選單 -->
      <template
        v-for="menu in sidebarMenus"
        :key="menu.path">
        <!-- 如果沒有子選單或子選單為空 -->
        <el-menu-item
          v-if="!menu.children || menu.children.length === 0"
          :index="menu.path">
          <el-icon v-if="menu.icon">
            <component :is="getIconComponent(menu.icon)" />
          </el-icon>
          <span>{{ menu.title }}</span>
        </el-menu-item>

        <!-- 如果有子選單 -->
        <el-sub-menu
          v-else
          :index="menu.path">
          <template #title>
            <el-icon v-if="menu.icon">
              <component :is="getIconComponent(menu.icon)" />
            </el-icon>
            <span>{{ menu.title }}</span>
          </template>

          <!-- 子選單項目 -->
          <el-menu-item
            v-for="subMenu in menu.children"
            :key="subMenu.path"
            :index="subMenu.path">
            {{ subMenu.title }}
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import * as ElementPlusIcons from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();

// 當前活動的菜單項
const activeMenu = computed(() => route.path);

// 獲取圖標組件
const getIconComponent = (iconName) => {
  // 如果使用 Element Plus 圖標
  if (ElementPlusIcons[iconName]) {
    return ElementPlusIcons[iconName];
  }

  // 如果沒有找到，返回默認圖標
  return ElementPlusIcons.Document;
};

// 處理菜單選擇
const handleSelect = (index) => {
  // 檢查是否為父菜單（沒有子菜單的菜單項或有子菜單但被點擊的是父級）
  const selectedMenu = sidebarMenus.value.find((menu) => menu.path === index);

  // 如果是父菜單且有子菜單，導航到第一個子菜單
  if (
    selectedMenu &&
    selectedMenu.children &&
    selectedMenu.children.length > 0
  ) {
    const firstChild = selectedMenu.children[0];
    console.log("導航到第一個子菜單:", firstChild.path);
    router.push(firstChild.path);
  } else {
    // 直接導航到選中的路徑
    router.push(index);
  }

  // 捲動到頂部
  setTimeout(() => {
    console.log("DocSidebar: 選中菜單，捲動到頂部");
    const mainContent = document.querySelector(".doc-main");
    if (mainContent) {
      mainContent.scrollTop = 0;
    }
  }, 100);
};

// 動態計算側邊欄菜單
const sidebarMenus = computed(() => {
  // 獲取所有路由
  const allRoutes = router.getRoutes();

  // 尋找與文檔相關的所有路由
  const docRoutes = allRoutes.filter(
    (route) =>
      (route.path.startsWith("/docs") || route.path === "/docs") &&
      route.path !== "/docs/"
  );

  // 分析路由結構，組織成菜單項目
  const parentPaths = new Set();
  const routeMap = new Map();

  // 按照路徑深度排序，確保先處理父路由
  docRoutes
    .sort((a, b) => {
      const depthA = a.path.split("/").length;
      const depthB = b.path.split("/").length;
      return depthA - depthB;
    })
    .forEach((route) => {
      // 如果是首頁或隱藏路由，跳過
      if (route.name === "DocsHome" || route.meta?.hidden) {
        return;
      }

      // 處理一級路由
      if (route.path.split("/").length === 3) {
        // 例如 /docs/analysis
        const path = route.path;
        parentPaths.add(path);

        routeMap.set(path, {
          path,
          title: route.meta?.title || route.name,
          icon: route.meta?.icon,
          order: route.meta?.order || 999,
          showSidebar: false,
          children: [],
        });
      }
      // 處理二級及以下的路由
      else if (route.path.split("/").length > 3) {
        // 例如 /docs/analysis/overview
        const pathParts = route.path.split("/");
        const parentPath = "/" + pathParts.slice(1, 3).join("/"); // 例如 /docs/analysis

        // 如果父路由不存在，創建一個虛擬的父路由
        if (!routeMap.has(parentPath)) {
          routeMap.set(parentPath, {
            path: parentPath,
            title: pathParts[2], // 使用路徑段作為標題
            icon: null,
            order: 999,
            showSidebar: false,
            children: [],
          });
          parentPaths.add(parentPath);
        }

        // 添加到父路由的子路由中
        const parent = routeMap.get(parentPath);
        parent.children.push({
          path: route.path,
          title: route.meta?.title || route.name,
          order: route.meta?.order || 999,
          icon: route.meta?.icon,
        });
      }
    });

  // 對父路由和子路由進行排序
  const menuItems = Array.from(parentPaths)
    .map((path) => {
      const item = routeMap.get(path);
      // 對子路由排序
      if (item.children.length > 0) {
        item.children.sort((a, b) => a.order - b.order);
      }
      return item;
    })
    .sort((a, b) => a.order - b.order);

  return menuItems;
});
</script>

<style lang="scss" scoped>
.doc-sidebar-content {
  @apply h-full overflow-x-hidden;

  .doc-menu {
    @apply h-full border-0 bg-transparent;

    :deep(.el-menu-item) {
      @apply text-xs text-gray-700 dark:text-gray-200 px-8;
      height: 32px;
      line-height: 32px;

      &.is-active {
        @apply bg-blue-50 dark:bg-blue-900/30;
        @apply text-blue-600 dark:text-blue-300;
      }
    }

    :deep(.el-sub-menu__title) {
      @apply text-xs  text-gray-900 dark:text-white px-3;
      height: 36px;
      line-height: 36px;
    }

    :deep(.el-sub-menu .el-menu) {
      @apply bg-transparent;
    }

    :deep(.el-menu--popup) {
      @apply min-w-32 p-0;
    }

    :deep(.el-menu--vertical:not(.el-menu--collapse)) {
      @apply w-32;
    }

    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      @apply border-0;
    }
  }
}
</style>
