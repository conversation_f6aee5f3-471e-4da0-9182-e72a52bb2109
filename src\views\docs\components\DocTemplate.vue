<template>
  <div class="doc-template">
    <!-- 標題區 -->
    <div class="doc-header">
      <h1
        :id="titleId"
        class="doc-title">
        {{ title }}
      </h1>
      <p
        v-if="description"
        class="doc-description">
        {{ description }}
      </p>
      <div class="doc-meta">
        <span
          v-if="author"
          class="meta-item">
          <el-icon><User /></el-icon>
          {{ author }}
        </span>
        <span
          v-if="lastUpdated"
          class="meta-item">
          <el-icon><Calendar /></el-icon>
          {{ formatDate(lastUpdated) }}
        </span>
        <span
          v-if="readingTime"
          class="meta-item">
          <el-icon><Clock /></el-icon>
          {{ readingTime }} 分鐘閱讀
        </span>
      </div>
    </div>

    <!-- 內容區 -->
    <div
      class="doc-content"
      v-highlight>
      <slot></slot>
    </div>

    <!-- 頁腳區 -->
    <div class="doc-footer">
      <div class="nav-links">
        <router-link
          v-if="prevPage"
          :to="prevPage.path"
          class="nav-link prev">
          <el-icon><ArrowLeft /></el-icon>
          <span>
            <small>上一頁</small>
            <span>{{ prevPage.title }}</span>
          </span>
        </router-link>
        <router-link
          v-if="nextPage"
          :to="nextPage.path"
          class="nav-link next">
          <span>
            <small>下一頁</small>
            <span>{{ nextPage.title }}</span>
          </span>
          <el-icon><ArrowRight /></el-icon>
        </router-link>
      </div>
      <div class="doc-meta-footer">
        <div
          class="edit-link"
          v-if="editLink">
          <a
            :href="editLink"
            target="_blank"
            rel="noopener noreferrer">
            <el-icon><Edit /></el-icon>
            編輯此頁
          </a>
        </div>
        <div
          class="last-updated"
          v-if="lastUpdated">
          最後更新：{{ formatDate(lastUpdated) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  User,
  Calendar,
  Clock,
  ArrowLeft,
  ArrowRight,
  Edit,
} from "@element-plus/icons-vue";
import dayjs from "dayjs";
import hljs from "highlight.js";

// 檢查是否在 DocLayout 中
// const inDocLayout = inject("inDocLayout", false);

const route = useRoute();
const router = useRouter();

// 監視路由變化，自動捲動到頂部
watch(
  () => route.path,
  () => {
    // 使用 nextTick 確保 DOM 已更新
    setTimeout(() => {
      console.log("DocTemplate: 路由變化，捲動到頂部");
      const mainContent = document.querySelector(".doc-main");
      if (mainContent) {
        mainContent.scrollTop = 0;
      }

      // 在路由變化後也應用代碼高亮
      setTimeout(() => {
        highlightCode();
      }, 100);
    }, 0);
  }
);

// 當組件掛載時，確保捲動到頂部
onMounted(() => {
  console.log("DocTemplate: 組件掛載，捲動到頂部");
  const mainContent = document.querySelector(".doc-main");
  if (mainContent) {
    mainContent.scrollTop = 0;
  }

  // 應用代碼高亮
  setTimeout(() => {
    highlightCode();
  }, 100);
});

// 手動應用代碼高亮
const highlightCode = () => {
  const codeBlocks = document.querySelectorAll(".doc-content pre code");
  console.log(`找到 ${codeBlocks.length} 個代碼塊進行高亮處理`);
  codeBlocks.forEach((block) => {
    hljs.highlightElement(block);
  });
};

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: "",
  },
  author: {
    type: String,
    default: "",
  },
  lastUpdated: {
    type: [String, Date],
    default: "2025-02-10",
  },
  readingTime: {
    type: Number,
    default: 0,
  },
  editLink: {
    type: String,
    default: "",
  },
  prevPage: {
    type: Object,
    default: null,
  },
  nextPage: {
    type: Object,
    default: null,
  },
});

// 生成標題的 ID
const titleId = computed(() => {
  return props.title.toLowerCase().replace(/\s+/g, "-");
});

// 獲取所有文檔路由
const docRoutes = computed(() => {
  const docs = [];
  router.getRoutes().forEach((route) => {
    if (route.path.startsWith("/docs/") && route.meta?.title) {
      docs.push({
        path: route.path,
        title: route.meta.title,
      });
    }
  });
  return docs.sort((a, b) => a.path.localeCompare(b.path));
});

// 獲取上一頁和下一頁，如果沒有提供，則自動計算
const currentIndex = computed(() => {
  return docRoutes.value.findIndex((doc) => doc.path === route.path);
});

const computedPrevPage = computed(() => {
  if (props.prevPage) return props.prevPage;
  return currentIndex.value > 0
    ? docRoutes.value[currentIndex.value - 1]
    : null;
});

const computedNextPage = computed(() => {
  if (props.nextPage) return props.nextPage;
  return currentIndex.value < docRoutes.value.length - 1
    ? docRoutes.value[currentIndex.value + 1]
    : null;
});

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format("YYYY/MM/DD HH:mm");
};
</script>

<style lang="scss" scoped>
.doc-template {
  @apply max-w-4xl mx-auto px-8 py-6;

  .doc-header {
    @apply mb-8 pb-8 border-b border-gray-200 dark:border-gray-700;

    .doc-title {
      @apply text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4;
    }

    .doc-description {
      @apply text-lg text-gray-600 dark:text-gray-400 mb-4;
    }

    .doc-meta {
      @apply flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400;

      .meta-item {
        @apply flex items-center gap-1;
      }
    }
  }

  .doc-content {
    @apply prose dark:prose-invert max-w-none mb-12;
    @apply prose-headings:scroll-mt-20;
    @apply prose-a:text-dark-mode dark:prose-a:text-light-mode;
  }

  .doc-footer {
    @apply mt-8 pt-8 border-t border-gray-200 dark:border-gray-700;

    .nav-links {
      @apply flex justify-between mb-8;

      .nav-link {
        @apply flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-dark-mode dark:hover:text-light-mode no-underline;

        small {
          @apply block text-sm opacity-50;
        }

        span {
          @apply block;
        }

        &.prev {
          @apply text-left;
        }

        &.next {
          @apply text-right ml-auto;
        }
      }
    }

    .doc-meta-footer {
      @apply flex justify-between items-center text-sm text-gray-500 dark:text-gray-400;

      .edit-link {
        a {
          @apply flex items-center gap-1 hover:text-dark-mode dark:hover:text-light-mode;
        }
      }
    }
  }
}
</style>
