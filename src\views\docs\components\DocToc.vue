<template>
  <div class="doc-toc-content">
    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-4">目錄</h3>
    <nav class="toc-nav">
      <ul class="space-y-2">
        <li
          v-for="item in tocItems"
          :key="item.id"
          :class="[
            'toc-item',
            `level-${item.level}`,
            { active: activeId === item.id },
          ]">
          <a
            :href="`#${item.id}`"
            class="block py-1 text-sm text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-300"
            :class="{
              'text-blue-600 dark:text-blue-300 font-medium':
                activeId === item.id,
            }"
            @click.prevent="scrollToHeading(item.id)">
            {{ item.text }}
          </a>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

const tocItems = ref([]);
const activeId = ref("");

// 初始化目錄項
const initTocItems = () => {
  const headings = document.querySelectorAll("h1, h2, h3, h4");
  tocItems.value = Array.from(headings).map((heading) => ({
    id: heading.id,
    text: heading.textContent,
    level: parseInt(heading.tagName.charAt(1)),
  }));
};

// 滾動到指定標題
const scrollToHeading = (id) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

// 監控滾動位置
const handleScroll = () => {
  const headings = document.querySelectorAll("h1, h2, h3, h4");
  for (const heading of headings) {
    const rect = heading.getBoundingClientRect();
    if (rect.top >= 0 && rect.top <= window.innerHeight * 0.3) {
      activeId.value = heading.id;
      break;
    }
  }
};

// 生命週期鉤子
onMounted(() => {
  initTocItems();
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style lang="scss" scoped>
.doc-toc-content {
  .toc-nav {
    .toc-item {
      @apply transition-colors duration-200;

      &.level-1 {
        @apply pl-0;
      }
      &.level-2 {
        @apply pl-3;
      }
      &.level-3 {
        @apply pl-6;
      }
      &.level-4 {
        @apply pl-9;
      }

      &.active a {
        @apply text-blue-600 dark:text-blue-300 font-medium;
      }
    }
  }
}
</style>
