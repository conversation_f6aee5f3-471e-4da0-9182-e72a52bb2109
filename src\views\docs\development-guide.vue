<template>
  <DocTemplate
    title="開發指南"
    description="詳細介紹 IYM 系統的開發流程、規範和最佳實踐"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="10"
    :prev-page="{ path: '/docs/getting-started', title: '快速開始' }"
    :next-page="{ path: '/docs/backend/overview', title: '後端系統概述' }">
    <div class="development-guide-content">
      <h2>開發環境設置</h2>
      <p>
        在開始開發之前，請確保您已經設置好開發環境。以下是推薦的開發工具和配置：
      </p>

      <h3>IDE 推薦</h3>
      <ul>
        <li>Visual Studio Code</li>
        <li>WebStorm</li>
        <li>IntelliJ IDEA（後端開發）</li>
      </ul>

      <h3>VSCode 插件推薦</h3>
      <div class="code-block">
        <pre><code class="language-json">{
  "recommendations": [
    "vue.volar",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "editorconfig.editorconfig",
    "mikestead.dotenv",
    "christian-kohler.path-intellisense",
    "stylelint.vscode-stylelint",
    "bradlc.vscode-tailwindcss",
    "antfu.iconify",
    "streetsidesoftware.code-spell-checker"
  ]
}</code></pre>
      </div>

      <h2>代碼規範</h2>
      <p>為了保持代碼的一致性和可維護性，我們制定了以下代碼規範：</p>

      <h3>命名規範</h3>
      <div class="code-block">
        <pre><code class="language-javascript">// 文件命名：使用 kebab-case (短橫線命名法)
// 例如：user-profile.vue, auth-service.js

// 組件命名：使用 PascalCase (大駝峰命名法)
const UserProfile = defineComponent({
  name: 'UserProfile',
  // ...
});

// 變量和函數命名：使用 camelCase (小駝峰命名法)
const userData = ref({});
const fetchUserData = () => {
  // ...
};

// 常量命名：使用大寫 SNAKE_CASE (蛇形命名法)
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// CSS 類命名：使用 kebab-case (短橫線命名法)
// 例如：.user-avatar, .nav-item

// Pinia Store 命名：使用 camelCase (小駝峰命名法) 並以 'use' 開頭
export const useUserStore = defineStore('user', {
  // ...
});</code></pre>
      </div>

      <h3>Vue 組件結構</h3>
      <div class="code-block">
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div class="component-container"&gt;
    &lt;!-- 先放置結構性元素 --&gt;
    &lt;header&gt;
      &lt;h1&gt;{{ title }}&lt;/h1&gt;
    &lt;/header&gt;
    
    &lt;!-- 內容區域 --&gt;
    &lt;main&gt;
      &lt;!-- 循環渲染 --&gt;
      &lt;ul&gt;
        &lt;li v-for="item in items" :key="item.id"&gt;
          item.name 
        &lt;/li&gt;
      &lt;/ul&gt;
      
      &lt;!-- 條件渲染 --&gt;
      &lt;div v-if="showDetails" class="details"&gt;
        &lt;p&gt;{{ details }}&lt;/p&gt;
      &lt;/div&gt;
    &lt;/main&gt;
    
    &lt;!-- 最後放置交互元素 --&gt;
    &lt;footer&gt;
      &lt;button @click="handleAction"&gt;{{ buttonText }}&lt;/button&gt;
    &lt;/footer&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
// 引入
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';

// 組合式API
const route = useRoute();
const userStore = useUserStore();

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['action']);

// 響應式數據
const showDetails = ref(false);
const details = ref('');

// 計算屬性
const buttonText = computed(() => {
  return showDetails.value ? '隱藏詳情' : '顯示詳情';
});

// 方法
const handleAction = () => {
  showDetails.value = !showDetails.value;
  emit('action', showDetails.value);
};

// 生命週期鉤子
onMounted(() => {
  console.log('組件已掛載');
});
&lt;/script&gt;

&lt;style lang="scss" scoped&gt;
.component-container {
  @apply max-w-lg mx-auto p-4 bg-white dark:bg-gray-800 rounded-lg shadow;
  
  header {
    @apply mb-4 pb-2 border-b;
    
    h1 {
      @apply text-xl font-bold;
    }
  }
  
  main {
    @apply mb-4;
    
    ul {
      @apply pl-5 list-disc mb-4;
    }
    
    .details {
      @apply p-2 bg-gray-50 dark:bg-gray-700 rounded mt-2;
    }
  }
  
  footer {
    @apply flex justify-end;
    
    button {
      @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
    }
  }
}
&lt;/style&gt;</code></pre>
      </div>

      <h3>TypeScript 類型定義</h3>
      <div class="code-block">
        <pre><code class="language-typescript">// 接口定義
interface User {
  id: number;
  username: string;
  email: string;
  role: UserRole;
  profile?: UserProfile;
  createdAt: Date;
}

// 枚舉定義
enum UserRole {
  Admin = 'admin',
  Editor = 'editor',
  Viewer = 'viewer'
}

// 類型定義
type UserProfile = {
  fullName: string;
  avatar: string;
  bio?: string;
  socialLinks: {
    twitter?: string;
    github?: string;
    linkedin?: string;
  };
};




// 使用示例
async function getUserData(userId: number):  {
  try {
    const response = await fetch Data User(`/api/users/${userId}`);
    if (response.status === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
}</code></pre>
      </div>

      <h2>CSS 和樣式指南</h2>
      <p>
        本系統使用 Tailwind CSS 作為主要的樣式工具，同時結合 SCSS
        進行自定義樣式開發。
      </p>

      <h3>Tailwind CSS 使用範例</h3>
      <div class="code-block">
        <pre><code class="language-html">&lt;div class="flex flex-col md:flex-row gap-4 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md"&gt;
  &lt;div class="flex-1 min-w-0"&gt;
    &lt;h2 class="text-xl font-semibold text-gray-900 dark:text-white truncate"&gt;
      項目標題
    &lt;/h2&gt;
    &lt;p class="mt-1 text-sm text-gray-500 dark:text-gray-400"&gt;
      項目描述文字，展示基本信息。
    &lt;/p&gt;
  &lt;/div&gt;
  &lt;div class="flex items-center mt-4 md:mt-0"&gt;
    &lt;button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"&gt;
      查看詳情
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
      </div>

      <h3>SCSS 自定義樣式</h3>
      <div class="code-block"></div>

      <p>類型包括：</p>
      <ul>
        <li><code>feat</code>: 新功能</li>
        <li><code>fix</code>: 錯誤修復</li>
        <li><code>docs</code>: 僅文檔更改</li>
        <li>
          <code>style</code>: 不影響代碼含義的更改（空白、格式、缺少分號等）
        </li>
        <li><code>refactor</code>: 既不修復錯誤也不添加功能的代碼更改</li>
        <li><code>perf</code>: 提高性能的代碼更改</li>
        <li><code>test</code>: 添加缺失的測試或更正現有的測試</li>
        <li><code>build</code>: 影響構建系統或外部依賴的更改</li>
        <li><code>ci</code>: 對 CI 配置文件和腳本的更改</li>
        <li><code>chore</code>: 其他不修改 src 或測試文件的更改</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.development-guide-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4;
  }

  ul,
  ol {
    @apply pl-6 mb-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }

  .code-block {
    @apply mb-6;
  }

  code:not(pre code) {
    @apply px-1.5 py-0.5 rounded text-sm border border-gray-200 dark:border-gray-700;
  }
}
</style>
