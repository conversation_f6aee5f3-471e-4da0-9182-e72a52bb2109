<template>
  <DocTemplate
    title="組件說明"
    description="詳細介紹 IYM 系統中的常用組件及其使用方法"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="8"
    :prev-page="{ path: '/docs/frontend/overview', title: '前端系統概述' }"
    :next-page="{ path: '/docs/frontend/state', title: '狀態管理' }">
    <div class="components-content">
      <h2>組件概述</h2>
      <p>
        IYM 系統提供了一系列可復用的組件，這些組件基於 Element Plus
        和自定義設計，用於構建統一的用戶界面。
      </p>

      <h2>基礎組件</h2>
      <div class="component-list">
        <div class="component-item">
          <h3>BaseButton</h3>
          <p>基礎按鈕組件，支援多種樣式和狀態。</p>
          <pre><code class="language-html">&lt;template&gt;
  &lt;el-button
    :type="type"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :class="customClass"
    @click="handleClick"&gt;
    &lt;slot&gt;&lt;/slot&gt;
  &lt;/el-button&gt;
&lt;/template&gt;

&lt;script setup&gt;
const props = defineProps({
  type: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: 'default'
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['click']);

const handleClick = (e) => {
  emit('click', e);
};
&lt;/script&gt;</code></pre>
          <h4>Props</h4>
          <ul>
            <li>type: 按鈕類型 (primary/success/warning/danger)</li>
            <li>loading: 加載狀態</li>
            <li>disabled: 禁用狀態</li>
          </ul>
        </div>

        <div class="component-item">
          <h3>BaseInput</h3>
          <p>基礎輸入框組件，支援表單驗證。</p>
          <pre><code class="language-html">&lt;template&gt;
  &lt;div class="base-input"&gt;
    &lt;label v-if="label" :for="inputId" class="form-label"&gt;{{ label }}&lt;/label&gt;
    &lt;el-input
      :id="inputId"
      v-model="inputValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
    &gt;
      &lt;template v-if="$slots.prefix" #prefix&gt;
        &lt;slot name="prefix"&gt;&lt;/slot&gt;
      &lt;/template&gt;
      &lt;template v-if="$slots.suffix" #suffix&gt;
        &lt;slot name="suffix"&gt;&lt;/slot&gt;
      &lt;/template&gt;
    &lt;/el-input&gt;
    &lt;div v-if="errorMessage" class="error-message"&gt;{{ errorMessage }}&lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, computed, watch } from 'vue';
import { v4 as uuidv4 } from 'uuid';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  rules: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'input', 'change', 'blur']);

// 生成唯一ID
const inputId = computed(() => `input-${uuidv4().slice(0, 8)}`);

// 輸入值和錯誤信息
const inputValue = ref(props.modelValue);
const errorMessage = ref('');

// 處理輸入事件
const handleInput = (value) => {
  emit('update:modelValue', value);
  emit('input', value);
  validateInput(value);
};

// 處理變更事件
const handleChange = (value) => {
  emit('change', value);
};

// 處理失焦事件
const handleBlur = (event) => {
  emit('blur', event);
  validateInput(inputValue.value);
};

// 表單驗證
const validateInput = (value) => {
  errorMessage.value = '';
  
  if (!props.rules.length) return true;
  
  for (const rule of props.rules) {
    if (rule.required && !value) {
      errorMessage.value = rule.message || '此欄位為必填';
      return false;
    }
    
    if (rule.min && value.length < rule.min) {
      errorMessage.value = rule.message || `最小長度為 ${rule.min}`;
      return false;
    }
    
    if (rule.max && value.length > rule.max) {
      errorMessage.value = rule.message || `最大長度為 ${rule.max}`;
      return false;
    }
    
    if (rule.pattern && !rule.pattern.test(value)) {
      errorMessage.value = rule.message || '格式不正確';
      return false;
    }
    
    if (rule.validator && typeof rule.validator === 'function') {
      const result = rule.validator(value);
      if (result !== true) {
        errorMessage.value = result || '驗證失敗';
        return false;
      }
    }
  }
  
  return true;
};

// 監聽 props.modelValue 的變化
watch(() => props.modelValue, (newVal) => {
  inputValue.value = newVal;
});
&lt;/script&gt;

&lt;style lang="scss" scoped&gt;
.base-input {
  @apply mb-4;
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  .error-message {
    @apply text-red-600 text-xs mt-1;
  }
}
&lt;/style&gt;</code></pre>
          <h4>Props</h4>
          <ul>
            <li>modelValue: 綁定值</li>
            <li>label: 標籤文本</li>
            <li>rules: 驗證規則</li>
          </ul>
          <p>使用示例：</p>
          <pre><code class="language-html">&lt;BaseInput
  v-model="username"
  label="用戶名"
  :rules="[
    { required: true, message: '請輸入用戶名' },
    { min: 3, message: '用戶名至少3個字符' }
  ]"
/&gt;</code></pre>
        </div>

        <div class="component-item">
          <h3>DataTable</h3>
          <p>數據表格組件，支援排序、篩選和分頁。</p>
          <pre><code class="language-javascript">// 表格列配置
const columns = [
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    sortable: true
  },
  {
    prop: 'name',
    label: '名稱',
    sortable: true,
    searchable: true
  },
  {
    prop: 'status',
    label: '狀態',
    formatter: (row) => {
      return row.status === 1 ? '啟用' : '禁用';
    },
    filters: [
      { text: '啟用', value: 1 },
      { text: '禁用', value: 0 }
    ]
  },
  {
    prop: 'createdAt',
    label: '創建時間',
    sortable: true,
    formatter: (row) => {
      return new Date(row.createdAt).toLocaleString();
    }
  }
];</code></pre>
        </div>
      </div>

      <h2>佈局組件</h2>
      <p>系統提供了多種佈局組件，適用於不同的頁面結構需求。</p>
      <div class="component-item">
        <h3>PageLayout</h3>
        <p>標準頁面佈局組件，包含頭部、內容區和底部。</p>
        <pre><code class="language-vue">&lt;template&gt;
  &lt;div class="page-layout"&gt;
    &lt;div v-if="showHeader" class="page-header"&gt;
      &lt;div class="header-title"&gt;
        &lt;h1&gt;{{ title }}&lt;/h1&gt;
        &lt;p v-if="description" class="description"&gt;{{ description }}&lt;/p&gt;
      &lt;/div&gt;
      &lt;div class="header-actions"&gt;
        &lt;slot name="actions"&gt;&lt;/slot&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="page-content"&gt;
      &lt;slot&gt;&lt;/slot&gt;
    &lt;/div&gt;
    
    &lt;div v-if="showFooter" class="page-footer"&gt;
      &lt;slot name="footer"&gt;&lt;/slot&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: false
  }
});
&lt;/script&gt;</code></pre>
      </div>

      <h2>進階技巧</h2>
      <div class="component-item">
        <h3>動態組件載入</h3>
        <p>可以根據條件動態載入不同的組件：</p>
        <pre><code class="language-javascript">import { defineAsyncComponent } from 'vue';

// 異步加載組件
const AsyncComponent = defineAsyncComponent(() => 
  import('@/components/HeavyComponent.vue')
);

// 根據條件選擇不同組件
const DynamicComponent = computed(() => {
  switch(componentType.value) {
    case 'table':
      return DataTable;
    case 'form':
      return DataForm;
    case 'chart':
      return DataChart;
    default:
      return null;
  }
});</code></pre>
      </div>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.components-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  h4 {
    @apply text-lg font-medium mb-2 mt-4;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .component-list {
    @apply space-y-8;

    .component-item {
      @apply mb-8;

      pre {
        @apply my-4;
      }

      ul {
        @apply pl-6 mb-4;

        li {
          @apply mb-1 text-gray-600 dark:text-gray-400;
        }
      }
    }
  }
}
</style>
