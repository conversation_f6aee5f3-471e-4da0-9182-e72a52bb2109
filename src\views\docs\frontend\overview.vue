<template>
  <DocTemplate
    title="前端系統概述"
    description="介紹 IYM 系統的前端架構和核心功能"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="6"
    :next-page="{ path: '/docs/frontend/components', title: '組件說明' }">
    <div class="frontend-overview-content">
      <h2>系統架構</h2>
      <p>
        IYM 前端系統採用 Vue 3 + TypeScript
        技術棧，使用現代化的前端開發工具和最佳實踐，確保代碼的可維護性和性能。
      </p>

      <h2>核心功能</h2>
      <div class="feature-list">
        <div class="feature-item">
          <h3>用戶界面</h3>
          <ul>
            <li>響應式設計</li>
            <li>深色模式支持</li>
            <li>多語言支持</li>
            <li>主題定制</li>
          </ul>
        </div>

        <div class="feature-item">
          <h3>數據可視化</h3>
          <ul>
            <li>圖表展示</li>
            <li>實時數據更新</li>
            <li>交互式分析</li>
            <li>自定義儀表板</li>
          </ul>
        </div>

        <div class="feature-item">
          <h3>系統功能</h3>
          <ul>
            <li>用戶認證</li>
            <li>權限管理</li>
            <li>數據導出</li>
            <li>系統設置</li>
          </ul>
        </div>
      </div>

      <h2>技術棧</h2>
      <div class="tech-stack">
        <div class="tech-category">
          <h3>核心框架</h3>
          <ul>
            <li>Vue 3</li>
            <li>TypeScript</li>
            <li>Vite</li>
          </ul>
        </div>

        <div class="tech-category">
          <h3>UI 框架</h3>
          <ul>
            <li>Element Plus</li>
            <li>TailwindCSS</li>
            <li>Vue Router</li>
          </ul>
        </div>

        <div class="tech-category">
          <h3>狀態管理</h3>
          <ul>
            <li>Pinia</li>
            <li>Vuex</li>
            <li>Composition API</li>
          </ul>
        </div>
      </div>

      <h2>項目結構</h2>
      <div class="project-structure">
        <pre><code>frontend/
├── src/
│   ├── assets/         # 靜態資源
│   ├── components/     # 通用組件
│   ├── views/          # 頁面組件
│   ├── router/         # 路由配置
│   ├── stores/         # 狀態管理
│   ├── utils/          # 工具函數
│   ├── styles/         # 全局樣式
│   ├── types/          # TypeScript 類型定義
│   └── App.vue         # 根組件
├── public/             # 公共資源
├── package.json        # 項目配置
└── vite.config.ts      # Vite 配置</code></pre>
      </div>

      <h2>開發規範</h2>
      <div class="development-rules">
        <h3>代碼規範</h3>
        <ul>
          <li>使用 TypeScript 進行類型檢查</li>
          <li>遵循 Vue 3 組合式 API 風格</li>
          <li>使用 ESLint 和 Prettier 進行代碼格式化</li>
          <li>編寫單元測試</li>
        </ul>

        <h3>命名規範</h3>
        <pre><code>// 組件命名
components/
  ├── BaseButton.vue
  ├── UserProfile.vue
  └── AnalysisChart.vue

// 變量命名
const userData = ref({})
const isLoading = ref(false)
const handleSubmit = () => {}</code></pre>
      </div>

      <h2>性能優化</h2>
      <ul>
        <li>使用路由懶加載</li>
        <li>組件按需加載</li>
        <li>圖片資源優化</li>
        <li>使用緩存策略</li>
        <li>代碼分割</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.frontend-overview-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .feature-list {
    @apply space-y-8;

    .feature-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      ul {
        @apply pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  .tech-stack {
    @apply grid grid-cols-1 md:grid-cols-3 gap-8;

    .tech-category {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      ul {
        @apply pl-6;

        li {
          @apply text-gray-600 dark:text-gray-400 mb-2;
        }
      }
    }
  }

  .project-structure {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  .development-rules {
    @apply mb-8;

    ul {
      @apply pl-6 mb-4;

      li {
        @apply text-gray-600 dark:text-gray-400 mb-2;
      }
    }

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
