<template>
  <DocTemplate
    title="狀態管理"
    description="詳細介紹 IYM 系統的狀態管理方案和最佳實踐"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="8"
    :prev-page="{ path: '/docs/frontend/components', title: '組件說明' }">
    <div class="state-content">
      <h2>狀態管理概述</h2>
      <p>
        IYM 系統使用 Pinia 作為狀態管理工具，結合 Vue 3 的 Composition
        API，提供了一個靈活且高效的狀態管理方案。
      </p>

      <h2>核心概念</h2>
      <div class="concept-list">
        <div class="concept-item">
          <h3>Store</h3>
          <p>Store 是狀態管理的核心單元，包含狀態、getters 和 actions。</p>
          <pre><code>// store/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    token: null
  }),
  getters: {
    isLoggedIn: (state) => !!state.token
  },
  actions: {
    async login(credentials) {
      // 登錄邏輯
    },
    logout() {
      // 登出邏輯
    }
  }
})</code></pre>
        </div>

        <div class="concept-item">
          <h3>State</h3>
          <p>State 是存儲在 Store 中的響應式數據。</p>
          <pre><code>// 在組件中使用
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const user = computed(() => userStore.user)</code></pre>
        </div>

        <div class="concept-item">
          <h3>Actions</h3>
          <p>Actions 是用於修改狀態的方法，可以包含異步操作。</p>
          <pre><code>// 在組件中使用
const handleLogin = async () => {
  try {
    await userStore.login(credentials)
    router.push('/dashboard')
  } catch (error) {
    // 錯誤處理
  }
}</code></pre>
        </div>
      </div>

      <h2>Store 組織</h2>
      <div class="store-organization">
        <h3>模塊化設計</h3>
        <pre><code>stores/
├── user.ts        # 用戶相關狀態
├── analysis.ts    # 分析相關狀態
├── settings.ts    # 系統設置狀態
└── index.ts       # Store 入口文件</code></pre>

        <h3>Store 註冊</h3>
        <pre><code>// main.ts
import { createPinia } from 'pinia'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.mount('#app')</code></pre>
      </div>

      <h2>狀態持久化</h2>
      <div class="persistence">
        <h3>本地存儲</h3>
        <pre><code>// store/plugins/persistence.ts
export const persistencePlugin = ({ store }) => {
  // 從 localStorage 恢復狀態
  const savedState = localStorage.getItem(store.$id)
  if (savedState) {
    store.$patch(JSON.parse(savedState))
  }

  // 監聽狀態變化
  store.$subscribe((mutation, state) => {
    localStorage.setItem(store.$id, JSON.stringify(state))
  })
}</code></pre>
      </div>

      <h2>最佳實踐</h2>
      <div class="best-practices">
        <h3>狀態設計原則</h3>
        <ul>
          <li>保持狀態最小化</li>
          <li>避免重複狀態</li>
          <li>使用 TypeScript 類型定義</li>
          <li>合理使用 getters</li>
        </ul>

        <h3>性能優化</h3>
        <ul>
          <li>使用 computed 屬性</li>
          <li>避免不必要的狀態更新</li>
          <li>合理使用 store 拆分</li>
          <li>注意內存洩漏</li>
        </ul>

        <h3>代碼組織</h3>
        <ul>
          <li>遵循單一職責原則</li>
          <li>保持 actions 的純粹性</li>
          <li>使用命名空間避免衝突</li>
          <li>編寫完整的文檔</li>
        </ul>
      </div>

      <h2>常見問題</h2>
      <ul>
        <li>如何處理複雜的狀態依賴？</li>
        <li>如何優化大型應用的狀態管理？</li>
        <li>如何處理跨模塊的狀態共享？</li>
        <li>如何進行狀態的調試和測試？</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
import { inject } from "vue";

// 檢查是否在 DocLayout 中
// const inDocLayout = inject("inDocLayout", false);
</script>

<style lang="scss" scoped>
.state-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  .concept-list {
    @apply space-y-8;

    .concept-item {
      @apply p-6 bg-gray-50 dark:bg-gray-800 rounded-lg;

      h3 {
        @apply text-dark-mode dark:text-light-mode mb-4;
      }

      pre {
        @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
      }

      code {
        @apply font-mono text-sm;
      }
    }
  }

  .store-organization {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  .persistence {
    @apply mb-8;

    pre {
      @apply bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4 overflow-x-auto;
    }

    code {
      @apply font-mono text-sm;
    }
  }

  .best-practices {
    @apply mb-8;

    h3 {
      @apply text-dark-mode dark:text-light-mode mb-4;
    }

    ul {
      @apply pl-6 mb-4;

      li {
        @apply text-gray-600 dark:text-gray-400 mb-2;
      }
    }
  }

  ul {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }
}
</style>
