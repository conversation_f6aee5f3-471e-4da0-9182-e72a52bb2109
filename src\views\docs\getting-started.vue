<template>
  <DocTemplate
    title="快速開始"
    description="本指南將幫助您快速上手 IYM 系統的開發和使用"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="5"
    :prev-page="{ path: '/docs/overview', title: '系統概述' }"
    :next-page="{ path: '/docs/development-guide', title: '開發指南' }"
    edit-link="https://github.com/your-repo/docs/edit/main/getting-started.md">
    <div class="getting-started-content">
      <h2>環境要求</h2>
      <ul>
        <li>Node.js 16.x 或更高版本</li>
        <li>npm 8.x 或更高版本</li>
        <li>Git</li>
        <li>現代瀏覽器（Chrome、Firefox、Safari 或 Edge）</li>
      </ul>

      <h2>安裝步驟</h2>
      <ol>
        <li>
          <p>克隆代碼庫：</p>
          <pre><code>git clone https://github.com/your-repo/iym.git</code></pre>
        </li>
        <li>
          <p>安裝依賴：</p>
          <pre><code>cd iym
npm install</code></pre>
        </li>
        <li>
          <p>啟動開發服務器：</p>
          <pre><code>npm run dev</code></pre>
        </li>
      </ol>

      <h2>基本配置</h2>
      <p>在開始使用之前，您需要進行一些基本配置：</p>
      <ol>
        <li>配置環境變量（複製 .env.example 到 .env）</li>
        <li>設置數據庫連接</li>
        <li>配置 API 端點</li>
        <li>設置用戶權限</li>
      </ol>

      <h2>常見問題</h2>
      <div class="faq">
        <h3>Q: 如何重置管理員密碼？</h3>
        <p>A: 可以通過數據庫直接修改或使用命令行工具重置。</p>

        <h3>Q: 系統支援哪些數據庫？</h3>
        <p>A: 目前支援 MySQL、PostgreSQL 和 SQLite。</p>

        <h3>Q: 如何添加新的功能模塊？</h3>
        <p>A: 請參考開發指南中的模塊開發章節。</p>
      </div>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.getting-started-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  h3 {
    @apply text-xl font-semibold mb-2 mt-6;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  ul,
  ol {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }

  ul {
    @apply list-disc;
  }

  ol {
    @apply list-decimal;
  }

  pre {
    @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 overflow-x-auto;
  }

  code {
    @apply font-mono text-sm;
  }

  .faq {
    @apply space-y-6;

    h3 {
      @apply text-primary dark:text-primary-dark;
    }
  }
}
</style>
