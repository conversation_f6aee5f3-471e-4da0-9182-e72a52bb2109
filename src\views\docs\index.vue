<template>
  <div class="docs-home">
    <h1 class="text-2xl font-bold mb-6">IYM 開發者文檔</h1>

    <div class="mb-8">
      <p class="text-gray-600 dark:text-gray-300 mb-4">
        歡迎使用 IYM 開發者文檔。這裡提供了完整的系統說明、API 文檔和開發指南。
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 數據分析 -->
      <div class="doc-card">
        <div class="card-header">
          <el-icon class="text-blue-500"><DataAnalysis /></el-icon>
          <h2 class="text-lg font-medium">數據分析</h2>
        </div>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          了解 IYM 系統的數據分析功能、處理流程和模型說明。
        </p>
        <router-link
          to="/docs/analysis/overview"
          class="text-blue-500 hover:text-blue-600 dark:hover:text-blue-400">
          查看文檔 →
        </router-link>
      </div>

      <!-- 後端系統 -->
      <div class="doc-card">
        <div class="card-header">
          <el-icon class="text-green-500"><Monitor /></el-icon>
          <h2 class="text-lg font-medium">後端系統</h2>
        </div>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          探索後端架構、API 設計和數據庫結構。
        </p>
        <router-link
          to="/docs/backend/overview"
          class="text-blue-500 hover:text-blue-600 dark:hover:text-blue-400">
          查看文檔 →
        </router-link>
      </div>

      <!-- 前端開發 -->
      <div class="doc-card">
        <div class="card-header">
          <el-icon class="text-purple-500"><Grid /></el-icon>
          <h2 class="text-lg font-medium">前端開發</h2>
        </div>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          瀏覽前端架構、組件庫和開發指南。
        </p>
        <router-link
          to="/docs/frontend/overview"
          class="text-blue-500 hover:text-blue-600 dark:hover:text-blue-400">
          查看文檔 →
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { DataAnalysis, Monitor, Grid } from "@element-plus/icons-vue";
</script>

<style lang="scss" scoped>
.docs-home {
  @apply max-w-7xl mx-auto;

  .doc-card {
    @apply bg-white dark:bg-gray-800 rounded-lg p-6 border dark:border-gray-700;

    .card-header {
      @apply flex items-center space-x-3 mb-4;

      .el-icon {
        @apply text-2xl;
      }
    }
  }
}
</style>
