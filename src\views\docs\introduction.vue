<template>
  <DocTemplate
    title="介紹"
    description="歡迎使用 IYM 開發文檔系統，這裡提供了完整的開發指南和技術文檔"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="3"
    :next-page="{ path: '/docs/overview', title: '系統概述' }"
    edit-link="https://github.com/your-repo/docs/edit/main/introduction.md">
    <div class="introduction-content">
      <h2>關於 IYM</h2>
      <p>
        IYM (Intelligent Yield Management)
        是一個智能化的產量管理系統，旨在幫助企業優化生產流程、提高效率並降低成本。
      </p>

      <h2>文檔系統特點</h2>
      <ul>
        <li>清晰的文檔結構和導航</li>
        <li>響應式設計，支援多種設備</li>
        <li>深色模式支援</li>
        <li>全文搜索功能</li>
        <li>即時預覽和編輯</li>
      </ul>

      <h2>如何使用本系統</h2>
      <p>本系統提供了完整的開發文檔和使用指南，您可以：</p>
      <ol>
        <li>通過左側導航欄瀏覽文檔結構</li>
        <li>使用頂部搜索框快速查找內容</li>
        <li>通過右側目錄快速定位章節</li>
        <li>使用深色模式切換按鈕調整閱讀體驗</li>
      </ol>

      <h2>貢獻指南</h2>
      <p>我們歡迎您為文檔系統做出貢獻。如果您發現任何問題或有改進建議，請：</p>
      <ul>
        <li>在 GitHub 上提交 Issue</li>
        <li>直接編輯文檔並提交 Pull Request</li>
        <li>通過郵件聯繫文檔維護團隊</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.introduction-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  ul,
  ol {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }

  ul {
    @apply list-disc;
  }

  ol {
    @apply list-decimal;
  }
}
</style>
