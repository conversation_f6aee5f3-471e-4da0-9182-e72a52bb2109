<template>
  <DocTemplate
    title="系統概述"
    description="本文檔介紹系統的整體架構、主要功能和使用方法"
    author="系統管理員"
    :last-updated="new Date()"
    :reading-time="5"
    :prev-page="{ path: '/docs/introduction', title: '介紹' }"
    :next-page="{ path: '/docs/getting-started', title: '快速開始' }"
    edit-link="https://github.com/your-repo/docs/edit/main/overview.md">
    <div class="overview-content">
      <h2>系統架構</h2>
      <p>本系統採用現代化的前端技術棧，主要包括：</p>
      <ul>
        <li>Vue 3 - 漸進式 JavaScript 框架</li>
        <li>Element Plus - 基於 Vue 3 的組件庫</li>
        <li>Tailwind CSS - 原子化 CSS 框架</li>
        <li>Vue Router - 官方路由管理器</li>
        <li>Pinia - 新一代狀態管理工具</li>
      </ul>

      <h2>主要功能</h2>
      <p>系統提供以下核心功能：</p>
      <ul>
        <li>文檔管理 - 支援多種格式的文檔瀏覽和編輯</li>
        <li>流程設計 - 可視化的工作流程設計工具</li>
        <li>數據分析 - 強大的數據處理和可視化能力</li>
        <li>系統配置 - 靈活的系統參數配置</li>
      </ul>

      <h2>使用指南</h2>
      <p>要開始使用本系統，請按照以下步驟操作：</p>
      <ol>
        <li>登錄系統 - 使用您的帳號密碼登錄</li>
        <li>瀏覽文檔 - 閱讀相關文檔了解系統功能</li>
        <li>開始使用 - 根據您的需求使用相應功能</li>
        <li>獲取幫助 - 遇到問題時查閱文檔或聯繫支援</li>
      </ol>

      <h2>最佳實踐</h2>
      <p>為了更好地使用本系統，我們建議：</p>
      <ul>
        <li>定期備份重要數據</li>
        <li>遵循文檔規範進行操作</li>
        <li>及時更新系統到最新版本</li>
        <li>參與社區討論分享經驗</li>
      </ul>
    </div>
  </DocTemplate>
</template>

<script setup>
import DocTemplate from "@/views/docs/components/DocTemplate.vue";
</script>

<style lang="scss" scoped>
.overview-content {
  h2 {
    @apply text-2xl font-bold mb-4 mt-8;
  }

  p {
    @apply mb-4 text-gray-600 dark:text-gray-400;
  }

  ul,
  ol {
    @apply mb-6 pl-6;

    li {
      @apply mb-2 text-gray-600 dark:text-gray-400;
    }
  }

  ul {
    @apply list-disc;
  }

  ol {
    @apply list-decimal;
  }
}
</style>
