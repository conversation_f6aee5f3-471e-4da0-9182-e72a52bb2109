<template>
  <div class="document-list">
    <!-- 文件列表表格 -->
    <el-table
      v-loading="loading"
      :data="documents"
      class="custom-table"
      style="width: 100%">
      <el-table-column
        type="index"
        label="序號"
        width="80" />
      <!-- <el-table-column
        prop="id"
        label="文檔ID"
        width="120" /> -->
      <el-table-column
        prop="name"
        label="文檔名稱"
        min-width="200" />
      <el-table-column
        prop="docType"
        label="類型"
        width="120">
        <template #default="{ row }">
          <el-tag :type="getDocTypeTag(row.docType)">
            {{ getDocTypeLabel(row.docType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="project.name"
        label="所屬專案"
        width="180"
        v-if="showProjectColumn" />

      <el-table-column
        label="上傳者"
        width="200">
        <template #default="{ row }">
          <UserAvatar
            v-if="row.creator"
            :user="row.creator"
            :size="24"
            :show-name="true"
            shape="circle"
            class="mr-1" />
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        label="上傳時間"
        width="180">
        <template #default="{ row }">
          {{ formatTimestamp(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="240"
        fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="isImage(row.docType)"
            link
            @click="handlePreview(row)">
            預覽
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleDownload(row)">
            下載
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete(row)">
            刪除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @update:current-page="currentPage = $event"
        @update:page-size="pageSize = $event" />
    </div>

    <!-- 預覽對話框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文檔預覽"
      class="preview-dialog"
      width="800px"
      destroy-on-close>
      <div class="preview-content">
        <img
          v-if="previewDocument && isImage(previewDocument.docType)"
          :src="previewDocument.url"
          style="max-width: 100%; max-height: 600px" />
        <div
          v-else
          class="text-gray-600 dark:text-dark-mode">
          此文檔類型不支援預覽
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { Search, Download, Delete } from "@element-plus/icons-vue";
import { deleteDocument } from "@/api/modules/flowDocument";
import UserAvatar from "@/components/UserAvatar.vue";
import { formatTimestamp } from "@/utils/dateUtils";

const props = defineProps({
  documents: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showProjectColumn: {
    type: Boolean,
    default: true,
  },
  showDeleteButton: {
    type: Boolean,
    default: true,
  },
  projectId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["refresh", "size-change", "current-change"]);

// 狀態
const total = computed(() => props.documents.length);
const currentPage = ref(1);
const pageSize = ref(10);

// 預覽相關
const previewDialogVisible = ref(false);
const previewDocument = ref(null);

// 工具方法
const getDocTypeLabel = (type) => {
  const types = {
    report: "報告",
    image: "圖片",
    attachment: "附件",
  };
  return types[type] || type;
};

const getDocTypeTag = (type) => {
  const types = {
    report: "success",
    image: "warning",
    attachment: "info",
  };
  return types[type] || "";
};

const isImage = (type) => type.includes("image");

// 處理方法
const handlePreview = (row) => {
  previewDocument.value = row;
  previewDialogVisible.value = true;
};

const handleDownload = (row) => {
  window.open(row.url, "_blank");
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("確定要刪除此文檔嗎？", "警告", {
      type: "warning",
    });

    await deleteDocument(row.id);
    ElMessage.success("刪除成功");
    emit("refresh");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("刪除失敗");
    }
  }
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  emit("size-change", val);
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  emit("current-change", val);
};
</script>

<style scoped>
.document-list {
  width: 100%;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 深色模式表格樣式 */
html.dark :deep(.custom-table) {
  --el-table-header-bg-color: #334155 !important;
  --el-table-header-text-color: #e2e8f0 !important;
  --el-table-row-hover-bg-color: #1e293b !important;
  --el-table-border-color: #334155 !important;
  --el-table-bg-color: #1e293b !important;
  --el-table-tr-bg-color: #1e293b !important;
  --el-table-expanded-cell-bg-color: #1e293b !important;
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
}

html.dark :deep(.custom-table th),
html.dark :deep(.custom-table tr),
html.dark :deep(.custom-table td) {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  border-bottom-color: #334155 !important;
}

html.dark :deep(.custom-table--border),
html.dark :deep(.custom-table--border th),
html.dark :deep(.custom-table--border td) {
  border-color: #334155 !important;
}

html.dark :deep(.custom-table__row:hover > td) {
  background-color: #334155 !important;
}

/* 深色模式分頁器樣式 */
html.dark :deep(.el-pagination) {
  --el-pagination-bg-color: #1e293b !important;
  --el-pagination-text-color: #e2e8f0 !important;
  --el-pagination-button-color: #e2e8f0 !important;
  --el-pagination-button-bg-color: #334155 !important;
  --el-pagination-button-disabled-color: #64748b !important;
  --el-pagination-button-disabled-bg-color: #1e293b !important;
  --el-pagination-hover-color: #0ea5e9 !important;
}

html.dark :deep(.el-pagination .el-select .el-input .el-input__wrapper) {
  background-color: #334155 !important;
  color: #e2e8f0 !important;
  border-color: #475569 !important;
}

html.dark :deep(.el-pagination .el-select .el-input .el-input__inner) {
  color: #e2e8f0 !important;
}

/* 深色模式對話框樣式 */
html.dark :deep(.preview-dialog .el-dialog) {
  --el-dialog-bg-color: #1e293b !important;
  --el-dialog-text-color: #e2e8f0 !important;
  --el-dialog-border-color: #334155 !important;
}

html.dark :deep(.preview-dialog .el-dialog__title) {
  color: #e2e8f0 !important;
}

html.dark :deep(.preview-dialog .el-dialog__header) {
  border-bottom-color: #334155 !important;
}

html.dark :deep(.preview-dialog .el-dialog__footer) {
  border-top-color: #334155 !important;
}
</style>
