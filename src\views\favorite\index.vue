<template>
  <div>
    <el-tabs
      v-model="activeType"
      class="favorite-tabs">
      <el-tab-pane
        v-for="type in favoriteTypes"
        :key="type.value"
        :label="type.label"
        :name="type.value">
        <div v-loading="loading">
          <template v-if="getCategoryFavorites(type.value).length > 0">
            <div class="favorite-list">
              <el-card
                v-for="item in getCategoryFavorites(type.value)"
                :key="`${type.value}-${item.resourceId}`"
                class="favorite-item-card"
                shadow="hover">
                <div class="favorite-item-content">
                  <div class="favorite-item-header">
                    <h3
                      class="favorite-title"
                      @click="navigateTo(item.path)">
                      {{ getFavoriteName(item) }}
                    </h3>
                    <Delete
                      :size="18"
                      class="favorite-item-delete"
                      @click.stop="removeFavorite(item)" />
                  </div>
                  <p class="favorite-description">
                    {{ getFavoriteDescription(item) || "暫無描述" }}
                  </p>
                  <div class="favorite-meta">
                    <span class="favorite-date">{{
                      formatDate(item.createdAt)
                    }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </template>
          <el-empty
            v-else
            :description="`暫無關注的${type.label}`" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { useFavoriteStore } from "@/stores/favorite";
import { Delete } from "lucide-vue-next";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import dayjs from "dayjs";

const router = useRouter();
const favoriteStore = useFavoriteStore();
const { favoriteTypes, getCategoryFavorites } = favoriteStore;

const activeType = ref(favoriteTypes[0]?.value || "project");
const loading = ref(false);

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format("YYYY/MM/DD HH:mm");
};

// 獲取關注項目名稱
const getFavoriteName = (item) => {
  const detail = favoriteStore.getFavoriteDetail(item.type, item.resourceId);
  return detail?.name || item.resourceId;
};

// 獲取關注項目描述
const getFavoriteDescription = (item) => {
  const detail = favoriteStore.getFavoriteDetail(item.type, item.resourceId);
  return detail?.description || "";
};

// 獲取工具提示
const getFavoriteTooltip = (item) => {
  const detail = favoriteStore.getFavoriteDetail(item.type, item.resourceId);
  if (!detail) return "";
  return `${detail.name}\n${detail.description || ""}`;
};

// 移除關注
const removeFavorite = async (item) => {
  try {
    await favoriteStore.removeFromFavorite(item.type, item.resourceId);
    ElMessage.success("已移除關注");
  } catch (error) {
    ElMessage.error("移除關注失敗");
  }
};

// 導航到關注項目
const navigateTo = (path) => {
  router.push(path);
};

// 初始化時獲取關注詳情
const fetchFavoriteDetailsWithRetry = async (retryCount = 0) => {
  const favorites = favoriteStore.favorites;
  console.log(`第 ${retryCount + 1} 次嘗試獲取關注列表:`, favorites);

  if (favorites?.length > 0) {
    await favoriteStore.fetchFavoriteDetails(favorites);
    return true;
  }

  if (retryCount < 2) {
    // 最多重試兩次（總共執行三次）
    await new Promise((resolve) => setTimeout(resolve, 500));
    return fetchFavoriteDetailsWithRetry(retryCount + 1);
  }

  return false;
};

onMounted(async () => {
  loading.value = true;
  try {
    const success = await fetchFavoriteDetailsWithRetry();
    if (!success) {
      console.log("三次嘗試後仍未獲取到關注列表");
    }
  } catch (error) {
    console.error("獲取關注詳情失敗:", error);
    ElMessage.error("獲取關注詳情失敗");
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.favorite-tabs {
  @apply p-4;
}

.favorite-list {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.favorite-item-card {
  @apply transition-all duration-300;
}

.favorite-item-card:hover {
  @apply transform -translate-y-1;
}

.favorite-item-content {
  @apply flex flex-col gap-2;
}

.favorite-item-header {
  @apply flex items-center justify-between;
}

.favorite-title {
  @apply text-lg font-medium cursor-pointer hover:text-blue-500 flex-1 truncate;
}

.favorite-description {
  @apply text-sm text-gray-500 dark:text-gray-400 line-clamp-2;
}

.favorite-meta {
  @apply text-xs text-gray-400 dark:text-gray-500;
}

.favorite-date {
  @apply inline-flex items-center;
}

.favorite-actions {
  @apply flex items-center gap-2;
}

.favorite-item-delete {
  @apply text-gray-500 text-red-400 hover:text-red-500 mr-1 flex-shrink-0 transition-all duration-200 hover:scale-110;
}
</style>
