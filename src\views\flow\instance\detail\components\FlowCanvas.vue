<!-- 流程圖畫布除了檔案節點外，其他節點不可拖動 -->
<template>
  <div
    class="h-full bg-light-mode dark:bg-dark-mode rounded-lg shadow-lg overflow-hidden flex"
    @dragover.prevent="handleFileDragOver"
    @dragleave.prevent="handleFileDragLeave"
    @drop.prevent="handleFileDrop"
    @paste="handlePaste"
    :class="{ 'is-dragover': isFileDragOver, 'css-fullscreen': isFullscreen }"
    ref="flowCanvasRef">
    <div
      class="flex-1"
      id="flowCanvasDiv">
      <div
        v-admin
        class="w-[300px] h-auto rounded-md dark:bg-[#222] text-xs p-2 flex flex-col items-start bg-gray-100 shadow-lg absolute top-20 left-4 z-30 opacity-90"
        style="display: none">
        <!-- TODO 流程實例畫布的「測試用面板」，暫時直接遮蔽，之後可增加env參數控制顯示 -->
        <!-- 列舉 flowCompletionState 的 key,value-->
        <h2 class="text-sm font-bold text-green-400">測試用面板:</h2>
        <span
          class="flex-1"
          v-for="key in Object.keys(flowCompletionState)"
          :key="key">
          {{ key }}: {{ flowCompletionState[key] }}
        </span>
      </div>
      <VueFlow
        v-model="elements"
        class="h-full"
        :default-zoom="1.5"
        :min-zoom="0.2"
        :max-zoom="30"
        :node-types="nodeTypes"
        :edge-types="edgeTypes"
        :delete-key-code="null"
        :apply-default="true"
        :auto-connect="false"
        :select-nodes-on-drag="false"
        :snap-to-grid="true"
        :snap-grid="[20, 20]"
        :connection-mode="ConnectionMode.Loose"
        :elevate-edges-on-select="true"
        :fit-view-on-init="false"
        :prevent-scrolling="true"
        :enable-pan-over-edges="true"
        :nodes-draggable="true"
        :edges-focusable="userStore.isAdmin"
        :edges-selectable="userStore.isAdmin"
        @nodeClick="handleNodeClick"
        @connect="handleConnect"
        @paneClick="handlePaneClick"
        @nodeDragStart="handleNodeDragStart"
        @nodeDrag="handleNodeDrag"
        @nodeDragStop="handleNodeDragStop"
        @edgeClick="handleEdgeClick"
        @edgesChange="handleEdgesChange">
        <Background
          pattern="dots"
          :gap="20"
          :size="1" />

        <Controls />
        <MiniMap
          :pannable="true"
          :zoomable="true" />
        <Panel
          position="top-right"
          class="bg-light-mode dark:bg-dark-mode dark:border dark:border-gray-700 p-2 rounded shadow-md"
          :class="{
            'w-[1000px]': !isPanelCollapsed,
            'w-auto': isPanelCollapsed,
          }">
          <div
            v-if="!isPanelCollapsed"
            class="flex flex-wrap gap-2">
            <!-- 管理員提示信息 -->
            <div
              v-if="userStore.isAdmin && !isFullscreen"
              class="text-xs text-purple-500 font-medium mb-2 w-full">
              <component
                :is="Info"
                :size="16"
                :stroke-width="1.5"
                class="inline-block mr-1" />
              管理員模式：您可以拖動所有節點、選中並刪除連接線
            </div>

            <!-- 儲存異動按鈕 (只有在管理員且有未保存的更改時才顯示) -->
            <el-tooltip
              content="儲存所有節點位置與連接線變更（管理員專用）"
              placement="top"
              effect="light"
              v-if="
                userStore.isAdmin &&
                nodeChangeState.hasUnsavedChanges &&
                !nodeChangeState.needsImmediateSync
              ">
              <el-button
                size="small"
                type="warning"
                class="save-changes-btn"
                @click="handleSaveChanges">
                <component
                  :is="Save"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                儲存異動
              </el-button>
            </el-tooltip>

            <!-- 模擬慢速上傳開關 (僅開發環境顯示) -->
            <el-tooltip
              v-if="isDevelopment"
              :content="
                simulateSlowUpload ? '關閉模擬慢速上傳' : '開啟模擬慢速上傳'
              "
              placement="top"
              effect="light">
              <el-button
                size="small"
                :type="simulateSlowUpload ? 'success' : 'info'"
                @click="toggleSimulateSlowUpload">
                <component
                  :is="Upload"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                {{ simulateSlowUpload ? "慢速" : "正常" }}
              </el-button>
            </el-tooltip>
            <el-tooltip
              content="添加便利貼"
              placement="top"
              effect="light">
              <el-button
                size="small"
                class="!bg-yellow-100 dark:!bg-yellow-400 dark:!text-black"
                @click="() => handleAddNode('sticky')">
                <component
                  :is="StickyNoteIcon"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                便利貼
              </el-button>
            </el-tooltip>

            <el-tooltip
              :content="
                isFullscreen
                  ? `退出全屏 (${ctrlOrCmd}${shiftSymbol}F)`
                  : `全屏 (${ctrlOrCmd}${shiftSymbol}F)`
              "
              placement="top"
              effect="light">
              <el-button
                size="small"
                type="primary"
                plain
                @click="handleToggleFullscreen">
                <component
                  :is="isFullscreen ? Minimize2 : Maximize2"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                {{ isFullscreen ? "退出全屏" : "全屏" }}
              </el-button>
            </el-tooltip>
            <el-tooltip
              content="查看工作流 JSON 數據"
              placement="top"
              effect="light">
              <el-button
                size="small"
                @click="showJsonDrawer = true">
                <component
                  :is="FileJson"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                查看JSON
              </el-button>
            </el-tooltip>
            <el-tooltip
              content="適應工作區"
              placement="top"
              effect="light">
              <el-button
                size="small"
                @click="fitView">
                <component
                  :is="Maximize"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                適應工作區
              </el-button>
            </el-tooltip>
            <!-- 添加重置按鈕 -->
            <el-tooltip
              content="重置所有節點數據"
              placement="top"
              effect="light">
              <el-button
                size="small"
                type="danger"
                plain
                @click="handleResetAllNodes">
                <component
                  :is="RefreshCw"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                重置數據
              </el-button>
            </el-tooltip>

            <el-tooltip
              content="同步本地數據到服務端"
              placement="top"
              effect="light">
              <el-button
                size="small"
                type="success"
                @click="handleSyncCacheToServer">
                <component
                  :is="DatabaseZap"
                  :size="16"
                  :stroke-width="1.5"
                  class="mr-1" />
                同步到服務端
              </el-button>
            </el-tooltip>
          </div>

          <div class="absolute top-0 right-0 flex items-center justify-between">
            <!-- 切換面板狀態的按鈕 -->
            <el-tooltip
              :content="isPanelCollapsed ? '展開面板' : '收起面板'"
              placement="left"
              effect="light">
              <el-button
                size="small"
                type="success"
                @click="handleTogglePanel">
                <component
                  :is="isPanelCollapsed ? ChevronLeft : ChevronRight"
                  :size="16"
                  :stroke-width="1.5" />
              </el-button>
            </el-tooltip>
          </div>
        </Panel>

        <!-- JSON 查看抽屜 -->
        <el-drawer
          v-model="showJsonDrawer"
          title="工作流 JSON 數據"
          direction="rtl"
          size="50%"
          class="json-drawer">
          <template #header>
            <div
              class="flex items-center justify-between w-full pr-4 dark:text-dark-mode">
              <span>工作流 JSON 數據</span>
              <div class="flex items-center space-x-2">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleCopyJson">
                  <component
                    :is="Copy"
                    :size="16"
                    :stroke-width="1.5"
                    class="mr-1" />
                  複製
                </el-button>
              </div>
            </div>
          </template>
          <div class="p-4 dark:bg-gray-900">
            <JsonViewer
              :jsonData="elements"
              :expand-depth="2"
              :expand-on-click="true" />
          </div>
        </el-drawer>
      </VueFlow>
    </div>
  </div>
</template>

<script setup>
/**
 * Vue Flow 事件處理改進說明
 * -----------------------
 * 1. 移除了 @nodesChange 事件監聽和相關的 handleNodesChange 函數
 * 2. 簡化了事件處理流程，使交互邏輯更清晰：
 *    - handleNodeClick: 只處理節點選擇，增加點擊鎖定機制
 *    - handleNodeDragStart: 只設置拖動狀態
 *    - handleNodeDrag: 維持拖動狀態
 *    - handleNodeDragStop: 處理拖動結束後的位置更新
 * 3. 優化 interactionState，只保留必要的狀態追蹤
 *
 * 這些改進減少了重複處理，使代碼更簡潔，交互邏輯更清晰，
 * 同時減少了不必要的 API 請求。
 */

//#region import
import { provide, markRaw } from "vue";
import { VueFlow, useVueFlow, Panel, ConnectionMode } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import { MiniMap } from "@vue-flow/minimap";
import { Controls } from "@vue-flow/controls";
import StickyNote from "@/components/flow-nodes/base/StickyNote.vue";
import FileNode from "@/components/flow-nodes/base/FileNode.vue";
import { useDebounceFn } from "@vueuse/core"; // 引入防抖函數
import {
  ref,
  watch,
  computed,
  onMounted,
  nextTick,
  onBeforeUnmount,
  onUnmounted,
  reactive,
} from "vue"; // 確保導入 ref

import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
import "@vue-flow/minimap/dist/style.css";

// 引入 API
import {
  updateFlowInstance,
  resetInstance,
  getFlowInstanceById,
} from "@/api/modules/flow";

// 引入 composable
import { useWorkflowManager } from "@/composables/useWorkflowManager";
import { useFlowNodeComponents } from "@/composables/useFlowNodeComponents";
import { useFileNode } from "@/composables/flow/useFileNode";
import { globalEventBus, NodeEventType } from "@/utils/eventBus";
import { useThemeStore } from "@/stores/theme";
import { useFlowReset } from "@/composables/useFlowReset";

const themeStore = useThemeStore();
const isDark = computed(() => themeStore.isDark);
provide("isDark", isDark.value);
// for test
//import FlowTaskList from "./FlowTaskList.vue";

import JsonViewer from "@/components/JsonViewer.vue";

import {
  Maximize2,
  Minimize2,
  Copy,
  StickyNote as StickyNoteIcon,
  Upload,
  FileJson,
  Maximize,
  Image as ImageIcon,
  Info,
  RefreshCw,
  DatabaseZap,
  Save, // 添加 Save 圖標
  ChevronLeft, // 添加收起面板的圖標
  ChevronRight, // 添加展開面板的圖標
} from "lucide-vue-next";

import { ElMessage, ElMessageBox } from "element-plus"; // 引入 ElMessageBox
import { useFlowCache } from "@/composables/useFlowCache";

import { useUserStore } from "@/stores/user";
//#endregion

const userStore = useUserStore();

// 添加面板摺疊狀態
const isPanelCollapsed = ref(false);

// 處理面板折疊/展開
const handleTogglePanel = () => {
  isPanelCollapsed.value = !isPanelCollapsed.value;
  console.log("wjats");
  // 儲存偏好設定到本地存儲，下次打開時保持相同狀態
  localStorage.setItem("flowPanelCollapsed", isPanelCollapsed.value);
};

// 在初始化時讀取用戶偏好設定
onMounted(() => {
  const savedPanelState = localStorage.getItem("flowPanelCollapsed");
  if (savedPanelState !== null) {
    isPanelCollapsed.value = savedPanelState === "true";
  }

  // 其餘onMounted代碼保持不變...
});

const props = defineProps({
  // 工作流實例
  flowInstance: {
    type: Object,
    required: true,
  },
});

// 開發環境標誌
const isDevelopment = process.env.NODE_ENV === "development";

// 不再一次性載入所有組件，改為根據當前流程需要的節點類型按需載入
const { flowNodeComponents, loadFlowNodeComponents, loadComponent } =
  useFlowNodeComponents();

// 創建一個存儲已載入組件的對象, 最基本的是便利貼及檔案節點
const nodeTypes = {
  sticky: markRaw(StickyNote),
  file: markRaw(FileNode),
};

// 載入節點元件
function loadNodeTypes() {
  // 根據當前流程實例中的節點類型載入所需的組件
  if (props.flowInstance && props.flowInstance.nodes) {
    const nodeTypesToLoad = new Set();

    // 收集需要載入的節點類型
    props.flowInstance.nodes.forEach((node) => {
      if (node.type && node.type !== "sticky" && node.type !== "file") {
        nodeTypesToLoad.add(node.type);
      }
    });

    const nodeTypesList = Array.from(nodeTypesToLoad);

    // 檢查是否有組件需要載入
    if (nodeTypesList.length === 0) {
      // console.log("沒有自定義節點需要載入，只使用基本節點類型");
      return;
    }

    // 確保流程組件已載入
    if (
      !flowNodeComponents.value ||
      Object.keys(flowNodeComponents.value).length === 0
    ) {
      // console.log("flowNodeComponents未初始化，嘗試再次載入流程組件");
      loadFlowNodeComponents();
    }

    // 確認已載入的組件列表
    const availableComponents = Object.keys(flowNodeComponents.value);
    // console.log(`已載入的組件數量: ${availableComponents.length}`);

    // 處理已載入的組件
    Object.entries(flowNodeComponents.value).forEach(([key, value]) => {
      const componentName = key
        .replace("/src/components/flow-nodes/business/", "")
        .replace(".vue", "");

      // 檢查是否需要此組件
      if (nodeTypesToLoad.has(componentName)) {
        // console.log(`正在註冊節點類型: ${componentName}`);

        try {
          // 檢查組件值是否有效
          if (!value) {
            // console.error(`組件 ${componentName} 的值為空或未定義`);
            return;
          }

          // 使用markRaw包裝原始組件
          const componentValue = value.default || value;
          nodeTypes[componentName] = markRaw(componentValue);
          // console.log(`成功註冊節點類型: ${componentName}`);
        } catch (error) {
          // console.error(`註冊節點類型 ${componentName} 時出錯:`, error);
        }
      }
    });

    // 檢查是否所有需要的節點類型都已成功加載
    const loadedTypes = Object.keys(nodeTypes).filter(
      (type) => type !== "sticky" && type !== "file"
    );
    const missingTypes = nodeTypesList.filter(
      (type) => !loadedTypes.includes(type)
    );

    if (missingTypes.length > 0) {
      // console.log(
      //   `有 ${missingTypes.length} 個節點類型未能成功載入:`,
      //   missingTypes
      // );
    } else {
      // console.log(
      //   `FlowCanvas 所有節點類型已成功載入: ${loadedTypes.join(", ")}`
      // );
    }
  } else {
    console.error("無法載入節點類型: 流程實例或節點資料不存在");
  }
}

// 註冊自定義邊線類型(!TODO: 這沒有作用!?)
const edgeTypes = {
  button: "default",
  custom: "default", // MEMO: 應該沒用到
};

// 設置默認的連接線選項
const defaultEdgeOptions = {
  //type: "button",
  animated: true,
  label: "",
  markerEnd: {
    type: "arrowclosed",
    color: "#e5e5e5",
  },
  updatable: userStore.isAdmin,
  deletable: userStore.isAdmin,
  style: {
    strokeWidth: 4,
  },
};

// #region 模擬慢速上傳控制
// 模擬慢速上傳控制
const simulateSlowUpload = ref(
  localStorage.getItem("simulateSlowUpload") !== "false"
);

// 切換模擬慢速上傳
const toggleSimulateSlowUpload = () => {
  simulateSlowUpload.value = !simulateSlowUpload.value;
  localStorage.setItem("simulateSlowUpload", simulateSlowUpload.value);
  ElMessage.info(`模擬慢速上傳已${simulateSlowUpload.value ? "開啟" : "關閉"}`);
};
// #endregion

// 手動從 elements 中刪除節點, 需要管理員權限
const removeNodeFromElements = (nodeId) => {
  console.log("手動刪除節點:", nodeId);
  // 獲取要刪除的節點
  const nodeToDelete = elements.value.find((el) => el.id === nodeId);

  // 檢查是否為檔案節點，如果是則需要特殊處理
  const isFileNode = nodeToDelete && nodeToDelete.type === "file";

  // 過濾掉要刪除的節點
  elements.value = elements.value.filter((el) => el.id !== nodeId);

  // 使用防抖函數更新流程實例狀態，如果是檔案節點則加上特殊標記
  if (isFileNode && nodeToDelete.data?.fileId) {
    console.log("檔案節點:", nodeToDelete);
    // 特殊處理檔案節點刪除，通知後端同時刪除 flowDocument
    const updateData = {
      nodeId,
      _deleteFileNode: true,
      fileId: nodeToDelete.data.fileId,
      // 重要：同時更新 nodes 陣列，確保節點真的從流程實例中被移除
      nodes: elements.value.filter((el) => !el.source), // 過濾出所有節點（排除 edges）
      edges: elements.value.filter((el) => el.source), // 過濾出所有 edges
    };

    // 直接調用 API 而不使用防抖，確保文件記錄被刪除
    updateFlowInstance(props.flowInstance.id, updateData)
      .then(() => {
        ElMessage.success(`檔案節點 ${nodeId} 及其文件記錄已移除`);
      })
      .catch((error) => {
        console.error("刪除檔案節點及文件記錄失敗:", error);
        ElMessage.error(`刪除失敗: ${error.message || "未知錯誤"}`);
      });
  } else {
    // 非檔案節點使用一般的防抖更新方式
    debouncedUpdateFlowInstanceState();
    ElMessage.success(`節點 ${nodeId} 已從畫布移除`);
  }
};

const { project, fitView, nodes, addNodes } = useVueFlow({
  defaultEdgeOptions,
  edgesUpdatable: true,
  edgesDraggable: true,
  // edgesFocusable: false,
  // selectNodesOnDrag: false,
  // elevateEdgesOnSelect: false,
});
const vueFlowInstance = ref(null);
const selectedNode = ref(null);

// 定義 elements 變數, 用於保存節點和連線
const elements = ref([]);

// 使用工作流管理器
const workflowManager = useWorkflowManager();

// 定義 showJsonDrawer 變數
const showJsonDrawer = ref(false);

// 保存拖動開始時的節點位置
const dragStartPosition = ref(null);

// 是否正在初始化元素（用於防止初始化時觸發不必要的更新）
const isInitializingElements = ref(false);

// 添加一個變量來追蹤是否有未保存的更改
const hasUnsavedChanges = ref(false);

// 檢查是否為檔案或便利貼節點變更
const isFileOrStickyChange = ref(false);

// 統一的節點變更處理狀態
const nodeChangeState = ref({
  hasUnsavedChanges: false, // 是否有未保存的一般節點變更
  needsImmediateSync: false, // 是否需要立即同步的變更(檔案/便利貼)
  isProcessing: false, // 是否正在處理變更
  lastSyncTime: null, // 上次同步時間
});

// 流程完成檢測狀態
const flowCompletionState = ref({
  totalRequiredNodes: 0, // 需要完成的節點總數
  completedNodes: 0, // 已完成的節點數量
  isCompleted: false, // 流程是否已完成
  lastCheckTime: null, // 上次檢查時間
  nodeCompletionMap: new Map(), // 節點完成狀態映射
  isSyncing: false, // 正在進行同步操作
  syncDebounceTimer: null, // 防抖計時器
  lastSyncAttempt: null, // 最後一次同步嘗試時間
});

// 添加交互狀態跟踪
// 簡化後的 interactionState 只保留必要的交互狀態追蹤
// - isDragging: 節點是否正在被拖動
// - processingNodeId: 當前正在處理的節點ID
// - clickLock: 點擊鎖定機制，防止快速連續點擊
// - clickLockTimer: 點擊鎖定計時器
const interactionState = ref({
  isDragging: false,
  processingNodeId: null,
  clickLock: false,
  clickLockTimer: null,
});

// 添加防抖計時器
const nodeChangeDebounceTimer = ref(null);
// 判斷節點是否需要立即同步
const needsImmediateSync = (nodeType) => {
  return nodeType === "file" || nodeType === "sticky";
};

// 統一的節點變更處理函數
const handleNodeChange = async (nodeId, changeType, data = {}) => {
  // 檢查節點類型
  const node = elements.value.find((el) => el.id === nodeId);
  if (!node) return false;

  const requiresImmediateSync = needsImmediateSync(node.type);

  // 更新本地狀態
  updateLocalNodeState(nodeId, changeType, data);

  // 標記狀態
  if (requiresImmediateSync) {
    nodeChangeState.value.needsImmediateSync = true;
    return await syncNodeToServer(nodeId);
  } else if (userStore.isAdmin) {
    nodeChangeState.value.hasUnsavedChanges = true;
    return true;
  }

  return false;
};

// 更新本地節點狀態
const updateLocalNodeState = (nodeId, changeType, data) => {
  const nodeIndex = elements.value.findIndex((el) => el.id === nodeId);
  if (nodeIndex === -1) return;

  const node = elements.value[nodeIndex];

  //TODO: 一定要判斷嗎?
  switch (changeType) {
    case "position":
      if (data.position) {
        node.position = data.position;
      }
      break;
    case "data":
      if (data.nodeData) {
        node.data = { ...node.data, ...data.nodeData };
      }
      break;
    case "size":
      if (data.size) {
        if (!node.style) node.style = {};
        node.style.width = `${data.size.width}px`;
        node.style.height = `${data.size.height}px`;
        if (!node.data) node.data = {};
        node.data.width = data.size.width;
        node.data.height = data.size.height;
      }
      break;
  }

  // 更新節點內部結構，確保連接線位置正確
  setTimeout(() => {
    if (vueFlowInstance.value) {
      vueFlowInstance.value.updateNodeInternals([nodeId]);
    }
  }, 100);
};

// 將節點同步到服務器 //TODO: WHERE USE?
const syncNodeToServer = async (nodeId) => {
  if (nodeChangeState.value.isProcessing) return false;

  try {
    nodeChangeState.value.isProcessing = true;

    const node = elements.value.find((el) => el.id === nodeId);
    if (!node) return false;

    // 準備更新數據
    const updateData = {
      nodes: elements.value.filter((el) => !el.source),
      edges: elements.value.filter((el) => el.source),
      _batchUpdate: true,
    };

    const response = await updateFlowInstance(
      props.flowInstance.id,
      updateData
    );

    // 更新成功，重置狀態
    nodeChangeState.value.needsImmediateSync = false;
    nodeChangeState.value.lastSyncTime = Date.now();

    return true;
  } catch (error) {
    console.error("同步節點到服務器失敗:", error);
    ElMessage.error(`節點同步失敗: ${error.message || "未知錯誤"}`);
    return false;
  } finally {
    nodeChangeState.value.isProcessing = false;
  }
};

// 同步所有變更到服務器
const syncAllChangesToServer = async () => {
  console.log("syncAllChangesToServer", nodeChangeState.value.isProcessing);

  if (nodeChangeState.value.isProcessing) return false;

  try {
    nodeChangeState.value.isProcessing = true;

    // 準備更新數據
    const updateData = {
      nodes: elements.value.filter((el) => !el.source),
      edges: elements.value.filter((el) => el.source),
      _batchUpdate: true,
    };

    const response = await updateFlowInstance(
      props.flowInstance.id,
      updateData
    );

    // 更新成功，重置狀態
    nodeChangeState.value.hasUnsavedChanges = false;
    nodeChangeState.value.needsImmediateSync = false;
    nodeChangeState.value.lastSyncTime = Date.now();

    // ElMessage.success("所有變更已同步到服務器");
    return true;
  } catch (error) {
    console.error("同步所有變更失敗:", error);
    ElMessage.error(`同步失敗: ${error.message || "未知錯誤"}`);
    return false;
  } finally {
    nodeChangeState.value.isProcessing = false;
  }
};

// 使用防抖函數處理一般節點變更
const debouncedSyncAllChanges = useDebounceFn(syncAllChangesToServer, 500);

// 處理節點點擊事件 - 只處理節點選擇
const handleNodeClick = (event) => {
  // 拖動後不要觸發點擊（這個必須保留）
  if (interactionState.value.isDragging) return;

  const { node } = event;

  // 權限判斷（這也很重要）
  if (!userStore.isAdmin && node.type !== "file" && node.type !== "sticky") {
    return;
  }

  // 設置選中節點
  selectedNode.value = node;
};

// 處理節點拖拽開始事件，需要管理員權限
const handleNodeDragStart = (event) => {
  // 檢查事件對象是否有效
  if (!event || !event.node || !event.node.position) {
    console.warn("拖拽開始事件或節點位置資訊無效", event);
    return;
  }

  // 設置拖動狀態
  interactionState.value.isDragging = true;
  interactionState.value.processingNodeId = event.node.id;

  // 記錄拖拽開始位置和時間
  dragStartPosition.value = {
    nodeId: event.node.id,
    position: {
      x: event.node.position.x,
      y: event.node.position.y,
    },
    startTime: Date.now(),
  };

  console.log(
    `節點 ${event.node.id} 開始拖動，初始位置:`,
    dragStartPosition.value.position
  );
};

// 處理節點拖拽結束事件，需要管理員權限
const handleNodeDragStop = async (event) => {
  // 檢查事件對象是否有效
  if (!event || !event.node) {
    console.warn("拖拽事件或節點資訊無效", event);
    return;
  }

  const nodeId = event.node.id;
  const currentPosition = event.node.position;

  console.log(`節點 ${nodeId} 拖拽結束，當前位置:`, currentPosition);

  // 重置拖動狀態
  interactionState.value.isDragging = false;

  // 設定一個短暫延遲，避免拖動後立即處理點擊事件
  setTimeout(() => {
    interactionState.value.processingNodeId = null;
  }, 100);

  // 進行節點變更處理
  await handleNodeChange(nodeId, "position", { position: currentPosition });
};

// 更新流程實例狀態的共用函數
const updateFlowInstanceState = async () => {
  try {
    if (isInitializingElements.value) {
      // console.log("FlowCanvas", "正在初始化元素，跳過更新流程實例狀態");
      return;
    }

    // 檢查是否有實際變更，通過比較當前元素與原始元素
    // 執行淺比較，只檢查基本屬性變化，忽略視覺上的微小調整
    if (!hasSignificantChanges()) {
      // console.log("FlowCanvas", "沒有顯著變更，跳過更新流程實例狀態");
      return;
    }

    // 準備要更新的數據
    const updatedNodes = elements.value
      .filter((el) => !el.source)
      .map((node) => {
        // 如果是並行節點且已完成，保留其狀態
        if (node.data?._isParallelNode && node.data?.status === "completed") {
          return {
            ...node,
            data: {
              ...node.data,
              _preserveState: true, // 標記需要保留狀態
            },
          };
        }
        return node;
      });

    const updatedEdges = elements.value.filter((el) => el.source);

    // 調用 API 更新流程實例
    const updateData = {
      nodes: updatedNodes,
      edges: updatedEdges,
      _batchUpdate: true, // 標記為批量更新
      _preserveParallelStates: true, // 標記需要保留並行節點狀態
    };

    const response = await updateFlowInstance(
      props.flowInstance.id,
      updateData
    );

    // 更新成功後，確保本地狀態與服務器同步
    if (response?.nodes) {
      elements.value = [...response.nodes, ...(response.edges || [])];
    }
  } catch (error) {
    console.error("FlowCanvas", "更新流程實例失敗", error);
    ElMessage.warning("節點變更無法保存：" + (error.message || "未知錯誤"));
  }
};

// 檢查是否有顯著變更的函數
const hasSignificantChanges = () => {
  // 如果沒有原始數據進行比較，則假設有變更
  if (
    !props.flowInstance ||
    !props.flowInstance.nodes ||
    !props.flowInstance.edges
  ) {
    return true;
  }

  // 檢查節點數量是否變化
  const originalNodeCount = props.flowInstance.nodes.length;
  const currentNodeCount = elements.value.filter((el) => !el.source).length;

  if (originalNodeCount !== currentNodeCount) {
    // console.log(
    //   "FlowCanvas",
    //   `節點數量變化: ${originalNodeCount} -> ${currentNodeCount}`
    // );
    return true;
  }

  // 檢查邊數量是否變化
  const originalEdgeCount = props.flowInstance.edges.length;
  const currentEdgeCount = elements.value.filter((el) => el.source).length;

  if (originalEdgeCount !== currentEdgeCount) {
    // console.log(
    //   "FlowCanvas",
    //   `連接線數量變化: ${originalEdgeCount} -> ${currentEdgeCount}`
    // );
    return true;
  }

  // 檢查節點內容是否有顯著變化
  const currentNodes = elements.value.filter((el) => !el.source);
  let hasNodeChanges = false;

  for (const currentNode of currentNodes) {
    const originalNode = props.flowInstance.nodes.find(
      (n) => n.id === currentNode.id
    );
    if (!originalNode) {
      // 找不到對應的原始節點，說明是新增的
      // console.log("FlowCanvas", `發現新增節點: ${currentNode.id}`);
      return true;
    }

    // 檢查節點類型是否變化
    if (originalNode.type !== currentNode.type) {
      // console.log(
      //   "FlowCanvas",
      //   `節點 ${currentNode.id} 類型變化: ${originalNode.type} -> ${currentNode.type}`
      // );
      return true;
    }

    // 檢查節點數據是否有顯著變化
    if (
      JSON.stringify(originalNode.data) !== JSON.stringify(currentNode.data)
    ) {
      // console.log("FlowCanvas", `節點 ${currentNode.id} 數據有顯著變化`);
      return true;
    }

    // 檢查節點位置是否有顯著變化 (超過10像素的變化才算顯著)
    const positionChanged =
      Math.abs(originalNode.position.x - currentNode.position.x) > 10 ||
      Math.abs(originalNode.position.y - currentNode.position.y) > 10;

    if (positionChanged) {
      // console.log("FlowCanvas", `節點 ${currentNode.id} 位置有顯著變化`);
      hasNodeChanges = true;
    }
  }

  // 檢查邊是否有變化
  const currentEdges = elements.value.filter((el) => el.source);
  for (const currentEdge of currentEdges) {
    const originalEdge = props.flowInstance.edges.find(
      (e) => e.id === currentEdge.id
    );
    if (!originalEdge) {
      // 找不到對應的原始邊，說明是新增的
      // console.log("FlowCanvas", `發現新增連接線: ${currentEdge.id}`);
      return true;
    }

    // 檢查連接關係是否變化
    if (
      originalEdge.source !== currentEdge.source ||
      originalEdge.target !== currentEdge.target
    ) {
      // console.log("FlowCanvas", `連接線 ${currentEdge.id} 連接關係變化`);
      return true;
    }
  }

  return hasNodeChanges;
};

// 創建防抖版本的 updateFlowInstanceState 函數，延遲 500 毫秒
const debouncedUpdateFlowInstanceState = useDebounceFn(
  updateFlowInstanceState,
  500 // 減少延遲時間，從 1000 毫秒改為 500 毫秒
);

// 將節點保存為數據更新而非結構更新
const saveNodeAsDataUpdate = async (nodeId, nodeData, skipMessage = false) => {
  try {
    // console.log("FlowCanvas", `開始保存節點 ${nodeId} 的數據更新`);

    // 準備要更新的數據
    const updateData = {
      nodeId,
      nodeData: {
        ...nodeData,
        // 添加額外的數據，確保 API 將其視為數據更新
        _dataUpdate: true,
        position: nodeData.position, // 確保位置信息被包含
        type: nodeData.type, // 確保類型信息被包含
        id: nodeId, // 確保 ID 被包含
      },
    };
    // 調用 API 更新節點數據
    // console.log(
    //   "FlowCanvas",
    //   "準備調用 updateFlowInstance API 更新節點數據",
    //   updateData
    // );

    const response = await updateFlowInstance(
      props.flowInstance.id,
      updateData
    );

    // console.log("FlowCanvas", "API 響應:", response);

    if (!skipMessage) {
      ElMessage.success("節點數據已更新");
    }

    // console.log("FlowCanvas", "節點數據已更新", nodeId);
    return true;
  } catch (error) {
    console.error("FlowCanvas", "更新節點數據失敗", error);
    ElMessage.error("更新節點數據失敗：" + (error.message || "未知錯誤"));
    return false;
  }
};

// 創建防抖版本的 saveNodeAsDataUpdate 函數，延遲 500 毫秒
const debouncedSaveNodeAsDataUpdate = useDebounceFn(
  (nodeId, nodeData, skipMessage = false) =>
    saveNodeAsDataUpdate(nodeId, nodeData, skipMessage),
  500 // 減少延遲時間，從 1000 毫秒改為 500 毫秒
);

// 處理畫布點擊事件
const handlePaneClick = () => {
  // 清除選中狀態
  if (selectedNode.value) {
    selectedNode.value = null;
  }

  // 不觸發後端更新
  // 這裡不需要做其他處理，因為畫布點擊不需要保存
};

// 處理連接事件 只允許管理員連接
const handleConnect = (connection) => {
  if (!userStore.isAdmin) {
    return;
  }
  // 生成唯一ID
  const edgeId = `edge-${Date.now()}`;

  // 創建新的邊緣
  const newEdge = {
    id: edgeId,
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle,
  };

  // 添加到元素列表
  elements.value.push(newEdge);

  // 標記有未保存的更改，而不是立即觸發更新
  hasUnsavedChanges.value = true;
};

// 複製 JSON 到剪貼簿
const handleCopyJson = () => {
  try {
    const jsonString = JSON.stringify(elements.value, null, 2);
    navigator.clipboard.writeText(jsonString);
    ElMessage.success("已複製到剪貼簿");
  } catch (error) {
    console.error("FlowCanvas", "複製失敗:", error);
    ElMessage.error("複製失敗");
  }
};

// 修改適應工作區功能
const handleFitView = () => {
  setTimeout(() => {
    vueFlowInstance.value?.fitView({ padding: 0.2 });
  }, 100);
};

// 處理自定義 nodeDataChange 事件
const handleNodeDataChangeEvent = (event) => {
  try {
    // console.log("FlowCanvas", "接收到自定義 nodeDataChange 事件", event);
    // console.log("FlowCanvas", "事件詳情:", event.detail);

    if (!event || !event.detail) {
      console.error("FlowCanvas", "nodeDataChange 事件無效，缺少 detail 屬性");
      return;
    }

    if (!event.detail.id) {
      console.error("FlowCanvas", "nodeDataChange 事件無效，缺少節點 ID");
      return;
    }

    if (!event.detail.data) {
      console.error("FlowCanvas", "nodeDataChange 事件無效，缺少節點數據");
      return;
    }

    // console.log("FlowCanvas", `準備處理節點 ${event.detail.id} 的數據變更`);
    handleNodeDataChange(event.detail);
  } catch (error) {
    console.error("FlowCanvas", "處理 nodeDataChange 事件時發生錯誤:", error);
  }
};

// 保存定時器引用
const connectionUpdateInterval = ref(null);

// 視窗大小變化監聽器
const handleWindowResize = useDebounceFn(() => {
  if (vueFlowInstance.value && elements.value.length > 0) {
    const nodeIds = elements.value
      .filter((el) => !el.source)
      .map((node) => node.id);

    vueFlowInstance.value.updateNodeInternals(nodeIds);
    // console.log("FlowCanvas", "視窗大小變化後更新所有節點的連接線位置");

    // 適應視窗大小
    setTimeout(() => {
      handleFitView();
    }, 100);
  }
}, 200);

// 初始化
onMounted(async () => {
  console.log("FlowCanvas", "初始化流程畫布, id:", props.flowInstance.id);

  // 獲取 VueFlow 實例
  vueFlowInstance.value = useVueFlow();

  // 全局監聽節點大小變更事件
  globalEventBus.on(NodeEventType.SIZE_CHANGE, handleNodeSizeChange);

  // 監聽文件節點刪除事件
  globalEventBus.on("filenode:delete", (nodeId) => {
    console.log("globalEventBus監聽收到文件節點刪除事件:", nodeId);
    removeNodeFromElements(nodeId);
  });

  // 先載入所有流程組件，確保核心組件可用
  await loadFlowNodeComponents();

  // 然後加載節點類型
  loadNodeTypes();

  // 初始化工作流管理器
  // 確保 workflowManager 已經正確初始化
  if (typeof workflowManager.setupNodeStateListeners === "function") {
    // console.log("FlowCanvas", "設置工作流管理器節點狀態監聽器");
    workflowManager.setupNodeStateListeners();
  } else {
    console.error(
      "FlowCanvas",
      "workflowManager.setupNodeStateListeners 不是一個函數，可能需要更新 useWorkflowManager.js"
    );
  }

  // 監聽節點狀態變更事件
  // console.log("FlowCanvas", "添加節點狀態變更事件監聽器");
  globalEventBus.on(NodeEventType.STATE_CHANGE, handleNodeStateChange);

  // 監聽自定義 nodeDataChange 事件
  window.addEventListener("nodeDataChange", handleNodeDataChangeEvent);
  // console.log("FlowCanvas", "添加自定義 nodeDataChange 事件監聽器");

  // 添加鍵盤事件監聽器，捕捉 delete 鍵
  window.addEventListener("keydown", handleKeyDown);

  // 添加視窗大小變化監聽器
  window.addEventListener("resize", handleWindowResize);
  //console.log("FlowCanvas", "添加視窗大小變化監聽器");

  // 初始化元素
  initializeElements();

  // 適應視圖
  setTimeout(() => {
    vueFlowInstance.value?.fitView({ padding: 0.2 });
    //console.log("FlowCanvas", "已適應視圖大小");
  }, 100);

  // 使用 MutationObserver 監聽節點內容變化
  // 這比定時器更好，因為它只在內容實際變化時才觸發更新
  nextTick(() => {
    const flowCanvasEl = document.getElementById("flowCanvasDiv");
    if (flowCanvasEl) {
      // 創建一個防抖更新函數
      let updateDebounceTimer = null;
      let pendingUpdate = false;

      const debouncedUpdateEdges = () => {
        // 如果已經有一個更新在等待中，則不再添加新的定時器
        if (pendingUpdate) return;

        pendingUpdate = true;

        // 清除之前的定時器
        if (updateDebounceTimer) {
          clearTimeout(updateDebounceTimer);
        }

        // 設置新的定時器，延遲執行更新
        updateDebounceTimer = setTimeout(() => {
          if (vueFlowInstance.value) {
            // 更新前設置標記，避免觸發後端保存
            isInitializingElements.value = true;

            // 更新所有節點內部結構
            const nodeIds = elements.value
              .filter((el) => !el.source)
              .map((node) => node.id);

            if (nodeIds.length > 0) {
              vueFlowInstance.value.updateNodeInternals(nodeIds);
              // console.log(
              //   "FlowCanvas",
              //   `已更新 ${nodeIds.length} 個節點的連接線位置`
              // );
            }

            // 更新後重置標記
            setTimeout(() => {
              isInitializingElements.value = false;
              pendingUpdate = false;
            }, 100);
          } else {
            pendingUpdate = false;
          }
        }, 500); // 500毫秒防抖
      };

      const mutationObserver = new MutationObserver((mutations) => {
        // 如果當前正在初始化，不觸發更新
        if (isInitializingElements.value) {
          return;
        }

        // 檢查是否有節點內容變化
        const relevantMutation = mutations.some((mutation) => {
          try {
            // 確保 mutation.target 是 Element 類型
            if (!(mutation.target instanceof Element)) {
              return false;
            }

            // 檢查變化是否發生在節點內部
            const nodeEl = mutation.target.closest?.(".vue-flow__node");
            if (!nodeEl) {
              // 如果當前目標不在節點內，檢查其父元素
              const parentElement = mutation.target.parentElement;
              if (!parentElement || !(parentElement instanceof Element)) {
                return false;
              }
              const parentNodeEl = parentElement.closest?.(".vue-flow__node");
              if (!parentNodeEl) {
                return false;
              }
            }

            // 如果是屬性變化，只有當變化的是尺寸或位置相關屬性時才處理
            if (mutation.type === "attributes") {
              const relevantAttrs = ["style", "width", "height", "class"];
              return relevantAttrs.includes(mutation.attributeName);
            }

            // 如果是子節點變化或文字內容變化
            return (
              mutation.type === "childList" || mutation.type === "characterData"
            );
          } catch (error) {
            console.error("處理節點變化時發生錯誤:", error);
            return false;
          }
        });

        if (relevantMutation) {
          // 使用防抖函數更新連接線，避免短時間內多次更新
          debouncedUpdateEdges();
        }
      });

      // 觀察整個流程圖容器的子樹變化，但使用更精細的配置
      mutationObserver.observe(flowCanvasEl, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["style", "class", "width", "height"], // 只監聽這些屬性
        characterData: true,
      });

      // 保存觀察器引用以便後續清除
      connectionUpdateInterval.value = mutationObserver;

      // console.log("FlowCanvas", "已設置節點內容變化觀察器(優化版)");
    }
  });

  // 添加beforeunload事件監聽器，當用戶嘗試離開頁面且有未保存的更改時顯示提示
  window.addEventListener("beforeunload", handleBeforeUnload);

  // 添加節點滾動相關的事件監聽
  setupNodeScrollEventListeners();
});

onBeforeUnmount(() => {
  // 移除事件監聽器
  globalEventBus.off(NodeEventType.STATE_CHANGE, handleNodeStateChange);
  globalEventBus.off(NodeEventType.SIZE_CHANGE, handleNodeSizeChange);
  globalEventBus.off("filenode:delete", removeNodeFromElements);

  // 移除自定義 nodeDataChange 事件監聽器
  window.removeEventListener("nodeDataChange", handleNodeDataChangeEvent);

  // 移除鍵盤事件監聽器
  window.removeEventListener("keydown", handleKeyDown);

  // 移除視窗大小變化監聽器
  window.removeEventListener("resize", handleWindowResize);

  // 清除 MutationObserver
  if (connectionUpdateInterval.value) {
    if (typeof connectionUpdateInterval.value.disconnect === "function") {
      // 如果是 MutationObserver
      connectionUpdateInterval.value.disconnect();
    } else if (typeof connectionUpdateInterval.value === "number") {
      // 如果是定時器（向後兼容）
      clearInterval(connectionUpdateInterval.value);
    }
    connectionUpdateInterval.value = null;
  }

  // 清除防抖計時器
  if (nodeChangeDebounceTimer.value) {
    clearTimeout(nodeChangeDebounceTimer.value);
  }

  // 移除節點滾動相關的事件監聽
  cleanupNodeScrollEventListeners();

  // 移除beforeunload事件監聽器
  window.removeEventListener("beforeunload", handleBeforeUnload);

  console.log("FlowCanvas", "準備卸載流程畫布，執行清理任務");

  // 清理節點滾動相關事件監聽器
  cleanupNodeScrollEventListeners();

  // 清理其他資源 TODO: remove(看起來沒用且會引發導航錯誤)
  // if (flowInstanceHistoryInterval.value) {
  //   clearInterval(flowInstanceHistoryInterval.value);
  // }
});

// 處理頁面離開前的提示
const handleBeforeUnload = (event) => {
  if (hasUnsavedChanges.value) {
    const message = "您有未保存的更改，確定要離開嗎？";
    event.returnValue = message;
    return message;
  }
};

// 清理
onUnmounted(() => {
  // onBeforeUnmount函數已經處理了這些清理操作
});

//TODO 整到 composable? - 拖放相關(檔案拖放上傳)
const isFileDragOver = ref(false);
// 添加一個標誌變數，用於防止重複處理拖放事件
const isProcessingDrop = ref(false);

// 使用 useFileNode 中的功能
const { isFileTypeAllowed } = useFileNode();

// 處理檔案拖放
const handleFileDragOver = (event) => {
  // 阻止事件冒泡，確保事件不會被重複處理
  event.stopPropagation();

  isFileDragOver.value = true;
  // 只接受檔案拖放
  if (event.dataTransfer.types.includes("Files")) {
    event.dataTransfer.dropEffect = "copy";
  }
};

// 處理檔案拖放離開
const handleFileDragLeave = (event) => {
  // 阻止事件冒泡，確保事件不會被重複處理
  event.stopPropagation();

  isFileDragOver.value = false;
};

// 處理檔案拖放
const handleFileDrop = async (event) => {
  // 阻止事件冒泡，確保事件不會被重複處理
  event.stopPropagation();

  // 如果已經在處理拖放事件，則直接返回
  if (nodeChangeState.value.isProcessing) {
    console.log("已經在處理拖放事件，跳過重複處理");
    return;
  }

  // 設置標誌，表示正在處理拖放事件
  nodeChangeState.value.isProcessing = true;
  nodeChangeState.value.needsImmediateSync = true;
  isFileDragOver.value = false;

  // 獲取滑鼠在畫布上的位置
  const bounds = event.currentTarget.getBoundingClientRect();
  const position = project({
    x: event.clientX - bounds.left,
    y: event.clientY - bounds.top,
  });

  try {
    // 獲取拖放的檔案
    const files = Array.from(event.dataTransfer.files);

    // 檢查檔案類型
    const invalidFiles = files.filter((file) => !isFileTypeAllowed(file));
    if (invalidFiles.length > 0) {
      ElMessage.error(
        `不支援的檔案類型：${invalidFiles.map((f) => f.name).join(", ")}`
      );
      nodeChangeState.value.isProcessing = false;
      return;
    }

    // 處理每個檔案
    for (const file of files) {
      try {
        // 創建臨時節點 ID
        const nodeId = `file-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        // 創建臨時節點數據
        const tempNodeData = {
          id: nodeId,
          type: "file",
          position,
          draggable: true,
          data: {
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            uploadProgress: 0,
          },
        };

        // 添加臨時節點到畫布
        addNodes([tempNodeData]);

        // 確保節點被添加到 elements 陣列中
        if (!elements.value.some((el) => el.id === nodeId)) {
          elements.value = [...elements.value, tempNodeData];
        }

        // 監聽上傳進度
        const { uploadProgress, uploadFile } = useFileNode();

        // 創建監視器
        const unwatch = watch(uploadProgress, (newProgress) => {
          const node = nodes.value.find((n) => n.id === nodeId);
          if (node) {
            node.data = { ...node.data, uploadProgress: newProgress };
          }
        });

        // 獲取專案編號和範本名稱
        const projectNumber = props.flowInstance.project?.projectNumber;
        const templateName = props.flowInstance.template?.name;

        // 上傳檔案
        const result = await uploadFile(
          file,
          props.flowInstance.projectId,
          props.flowInstance.id,
          projectNumber,
          templateName
        );

        // 停止監視上傳進度
        unwatch();

        // 更新節點資訊
        const node = nodes.value.find((n) => n.id === nodeId);
        if (node) {
          node.data = {
            ...node.data,
            fileId: result.data.id,
            fileUrl: result.data.url,
            fileName: result.data.name,
            uploadProgress: 100,
          };
        }

        // 同步到服務器 - 先同步單個節點
        // await syncNodeToServer(nodeId);

        nodeChangeState.value.isProcessing = false;
        // 然後更新整個工作流程，確保所有節點位置和關係都被保存
        await syncAllChangesToServer();

        ElMessage.success(`檔案 ${result.data.name} 已上傳並添加到畫布`);
      } catch (error) {
        console.error("檔案處理失敗", error);
        ElMessage.error(
          `檔案 ${file.name} 上傳失敗：${error.message || "未知錯誤"}`
        );
      }
    }
  } catch (error) {
    console.error("檔案處理失敗", error);
    ElMessage.error(`檔案處理失敗：${error.message || "未知錯誤"}`);
  } finally {
    // 重置標誌
    nodeChangeState.value.isProcessing = false;
    nodeChangeState.value.needsImmediateSync = false;
  }
};

// 全屏狀態(使用 CSS 模擬全屏，而不是瀏覽器的全屏 API)
const flowCanvasRef = ref(null);
const isFullscreen = ref(false);

// 處理全屏切換並適應視窗大小
const handleToggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  // 進入全屏後自動適應視窗大小
  if (isFullscreen.value) {
    setTimeout(() => {
      handleFitView();
    }, 300);
  }

  // 全屏切換後確保對話框正確顯示
  nextTick(() => {
    // 強制將對話框移至 body 元素下
    const dialogContainers = document.querySelectorAll(
      ".custom-dialog-container"
    );
    dialogContainers.forEach((container) => {
      if (!document.body.contains(container)) {
        document.body.appendChild(container);
      }
      container.style.zIndex = "999999";
    });
  });
};

// 判斷是否為 Mac 平台
const isMac = navigator.platform.toUpperCase().indexOf("MAC") >= 0;
const ctrlOrCmd = isMac ? "⌘" : "Ctrl+";
const shiftSymbol = isMac ? "⇧" : "Shift+";

// 添加處理節點尺寸變化的方法
const handleNodeSizeChange = (payload) => {
  // console.log(
  //   "FlowCanvas",
  //   "handleNodeSizeChange",
  //   "接收到節點尺寸變更事件",
  //   payload
  // );
  const { id, height } = payload;

  // 找到對應的節點
  const nodes = elements.value.filter((el) => !el.source); // 過濾出所有節點（不包含邊）
  const nodeIndex = nodes.findIndex((node) => node.id === id);
  if (nodeIndex === -1) return;

  // 更新節點尺寸
  const updatedNode = {
    ...nodes[nodeIndex],
    style: {
      ...nodes[nodeIndex].style,
      height: `${height}px`,
    },
  };

  // 更新節點列表
  const elementIndex = elements.value.findIndex((el) => el.id === id);
  if (elementIndex !== -1) {
    elements.value.splice(elementIndex, 1, updatedNode);
  }

  // 標記有未保存的更改
  hasUnsavedChanges.value = true;

  // 立即更新節點內部結構以重新計算連接線位置
  if (vueFlowInstance.value) {
    vueFlowInstance.value.updateNodeInternals([id]);
    // console.log(`節點 ${id} 尺寸變更後已立即更新內部結構和連接線位置`);
  }

  // 為確保連接線完全更新，再添加一次延遲的更新
  setTimeout(() => {
    if (vueFlowInstance.value) {
      vueFlowInstance.value.updateNodeInternals([id]);
      // console.log(
      //   `節點 ${id} 尺寸變更後已再次更新內部結構和連接線位置（延遲）`
      // );
    }
  }, 200);
};

// 初始化流程完成檢測
const initFlowCompletionCheck = () => {
  // 重置狀態
  flowCompletionState.value = {
    totalRequiredNodes: 0,
    completedNodes: 0,
    isCompleted: false,
    lastCheckTime: Date.now(),
    nodeCompletionMap: new Map(),
    isSyncing: false,
    syncDebounceTimer: null,
    lastSyncAttempt: null,
  };

  // 計算需要完成的節點數量（排除檔案和便利貼節點）
  const requiredNodes = elements.value.filter((el) => {
    return !el.source && !needsImmediateSync(el.type || "");
  });

  flowCompletionState.value.totalRequiredNodes = requiredNodes.length;

  console.log(
    `[流程完成檢測] 總共需要完成 ${flowCompletionState.value.totalRequiredNodes} 個節點`
  );

  // 初始化已完成節點
  let initialCompletedCount = 0;
  requiredNodes.forEach((node) => {
    // 檢查節點是否已完成
    const isCompleted = node.data?.status === "completed";
    flowCompletionState.value.nodeCompletionMap.set(node.id, isCompleted);
    if (isCompleted) {
      initialCompletedCount++;
    }
  });

  flowCompletionState.value.completedNodes = initialCompletedCount;

  // 檢查是否已完成
  checkFlowCompletion();

  console.log(`[流程完成檢測] 初始已完成 ${initialCompletedCount} 個節點`);
};

// 檢查流程是否已全部完成
const checkFlowCompletion = () => {
  const { completedNodes, totalRequiredNodes, isCompleted } =
    flowCompletionState.value;

  // 避免重複觸發
  if (isCompleted) return;

  // 檢查是否全部完成
  if (completedNodes >= totalRequiredNodes && totalRequiredNodes > 0) {
    console.log(
      `[流程完成檢測] 流程已完全完成! ${completedNodes}/${totalRequiredNodes}`
    );
    flowCompletionState.value.isCompleted = true;
    flowCompletionState.value.lastCheckTime = Date.now();

    // 自動同步到服務器
    handleAutoSyncOnCompletion();
  } else {
    console.log(
      `[流程完成檢測] 流程未完成 ${completedNodes}/${totalRequiredNodes}`
    );
  }
};

// 當流程完成時自動同步
const handleAutoSyncOnCompletion = async () => {
  // 如果已經在進行同步操作，則直接返回
  if (flowCompletionState.value.isSyncing) {
    console.log("[流程完成檢測] 已有同步操作在進行中，忽略此次同步請求");
    return;
  }

  // 檢查最後同步嘗試時間
  if (flowCompletionState.value.lastSyncAttempt) {
    const timeSinceLastAttempt =
      Date.now() - flowCompletionState.value.lastSyncAttempt;
    if (timeSinceLastAttempt < 30000) {
      // 30秒內不重複嘗試同步
      console.log(
        `[流程完成檢測] 距離上次同步嘗試只有 ${Math.round(
          timeSinceLastAttempt / 1000
        )} 秒，不重複嘗試`
      );
      return;
    }
  }

  // 防止頻繁同步，檢查最後同步時間
  if (
    nodeChangeState.value.lastSyncTime &&
    Date.now() - nodeChangeState.value.lastSyncTime < 30000
  ) {
    // 增加到30秒
    console.log("[流程完成檢測] 最近剛同步過，跳過此次同步");
    return;
  }

  // 清除之前的防抖計時器
  if (flowCompletionState.value.syncDebounceTimer) {
    clearTimeout(flowCompletionState.value.syncDebounceTimer);
  }

  // 使用防抖模式避免短時間內重複同步
  flowCompletionState.value.lastSyncAttempt = Date.now();
  flowCompletionState.value.syncDebounceTimer = setTimeout(async () => {
    try {
      // 標記同步開始
      flowCompletionState.value.isSyncing = true;

      // 使用靜默模式同步
      await handleSyncCacheToServer(true); // 傳入參數表示靜默模式

      console.log("[流程完成檢測] 自動同步成功完成");
    } catch (error) {
      console.error("[流程完成檢測] 自動同步失敗", error);
    } finally {
      // 確保重置同步狀態
      flowCompletionState.value.isSyncing = false;
    }
  }, 2000); // 2秒防抖延遲
};

// 添加靜默模式參數
const handleSyncCacheToServer = async (silent = false) => {
  // 檢查是否已在處理中
  if (nodeChangeState.value.isProcessing) {
    console.log("[同步] 已有同步操作在進行中，忽略此次同步請求");
    return false;
  }

  try {
    // 標記正在處理
    nodeChangeState.value.isProcessing = true;

    // 只在非靜默模式下顯示載入中訊息
    let loadingMessage = null;
    if (!silent) {
      loadingMessage = ElMessage({
        message: "正在同步數據到服務端...",
        type: "info",
        duration: 0,
      });
    }

    // 取得 cache 的數據
    const cacheData = await useFlowCache(props.flowInstance.id).readCache();

    const nodes = cacheData.nodes;
    const nodeData = [];
    Object.entries(nodes).forEach(([key, value]) => {
      nodeData.push({
        ...value,
        id: key,
      });
    });

    // 準備要同步的數據
    const syncData = {
      context: {
        globalVariables: cacheData.context.globalVariables,
        sharedData: cacheData.context.sharedData,
        statistics: cacheData.context.statistics,
      },
      nodeData: nodeData.map(({ error, status, ...rest }) => rest), // 為了對應資料庫的數據格式

      // delete nodeData object array 裡的 result
      nodeStates: nodeData.map(({ result, ...rest }) => rest), // 為了對應資料庫的數據格式
      logs: cacheData.context.executionHistory,
    };

    // 調用 API 更新流程實例
    const response = await updateFlowInstance(props.flowInstance.id, syncData);

    // 更新成功後，確保本地狀態與服務器同步
    if (response?.nodes) {
      elements.value = [...response.nodes, ...(response.edges || [])];
      // 同時更新 context、nodeStates 和 logs
      if (response.context) {
        props.flowInstance.context = response.context;
      }
      if (response.nodeStates) {
        props.flowInstance.nodeStates = response.nodeStates;
      }
      if (response.logs) {
        props.flowInstance.logs = response.logs;
      }
    }

    // 更新上次同步時間
    nodeChangeState.value.lastSyncTime = Date.now();

    // 關閉載入中訊息
    if (loadingMessage) {
      loadingMessage.close();
    }

    // 只在非靜默模式下顯示成功消息
    if (!silent) {
      ElMessage({
        message: "數據已成功同步到服務端",
        grouping: true,
        type: "success",
      });
    }

    return true;
  } catch (error) {
    console.error("同步數據到服務端失敗", error);

    // 只在非靜默模式下顯示錯誤消息
    if (!silent) {
      ElMessage.error("同步數據到服務端失敗");
    }

    return false;
  } finally {
    // 重置處理中標誌
    nodeChangeState.value.isProcessing = false;
  }
};

// 處理節點狀態變更事件
const handleNodeStateChange = async (payload) => {
  const { nodeId, status, result, error } = payload;

  // 更新節點視覺狀態
  const node = elements.value.find((el) => el.id === nodeId && !el.source);
  if (node) {
    // 移除所有狀態相關的類
    node.class = (node.class || "").replace(/\bflow-node--\w+\b/g, "");

    // 根據狀態添加對應的類
    if (status === "completed") {
      node.class = (node.class || "") + " flow-node--completed";
      // 注意：不在這裡調用 workflowManager.handleNodeCompleted
      // 因為 useWorkflowManager 的事件監聽器已經會處理這個事件
      // 避免重複調用導致的問題

      // 檢查是否為需要計算的節點（不是文件或便利貼）
      if (!needsImmediateSync(node.type || "")) {
        // 更新節點完成狀態
        const wasCompletedBefore =
          flowCompletionState.value.nodeCompletionMap.get(nodeId);

        // 只有當節點之前未完成時才增加計數
        if (!wasCompletedBefore) {
          flowCompletionState.value.nodeCompletionMap.set(nodeId, true);
          flowCompletionState.value.completedNodes += 1;

          // 檢查流程是否已完成
          checkFlowCompletion();
        }
      }
    } else if (status === "running") {
      node.class = (node.class || "") + " flow-node--running";
    } else if (status === "error") {
      node.class = (node.class || "") + " flow-node--error";
    }

    // 處理節點狀態變更
    handleNodeChange(nodeId, "status", { status });
  }
};

// 處理節點拖動過程中的事件
const handleNodeDrag = (event) => {
  // 保持拖動狀態
  interactionState.value.isDragging = true;

  // 只記錄日誌用於調試，無需其他操作
  if (event?.node?.id === interactionState.value.processingNodeId) {
    // 避免日誌過多，不輸出具體拖動位置
  }
};

// 處理連接線點擊事件
const handleEdgeClick = (event) => {
  if (!userStore.isAdmin) return;

  // console.log("連接線點擊", event);

  if (event && event.edge) {
    // console.log("FlowCanvas", `連接線 ${event.edge.id} 被點擊`, event.edge);
  }
};

// 處理連接線變更事件
const handleEdgesChange = (changes) => {
  if (!userStore.isAdmin) return;

  // console.log("FlowCanvas", "連接線變更", changes);

  for (const change of changes) {
    // console.log("FlowCanvas", "連接線變更類型:", change.type);

    // 處理連接線刪除
    if (change.type === "remove") {
      const edgeIndex = elements.value.findIndex((el) => el.id === change.id);
      if (edgeIndex !== -1) {
        // 移除連接線
        elements.value.splice(edgeIndex, 1);
        // 標記有未保存的更改，而不是立即觸發更新
        hasUnsavedChanges.value = true;
      }
    }
    // 處理連接線選擇狀態變更
    else if (change.type === "select") {
      // 不需要更新後端
      // console.log("FlowCanvas", "連接線選擇狀態變更，不需要更新後端");
    }
  }
};

// 更新節點內部結構並重新計算連接線
const updateNodeInternals = (nodeIds) => {
  if (!vueFlowInstance.value) {
    // console.log("FlowCanvas", "vueFlowInstance 不可用，無法更新節點內部結構");
    return;
  }

  // 如果沒有提供節點ID，則更新所有節點
  if (!nodeIds) {
    const allNodeIds = elements.value
      .filter((el) => !el.source)
      .map((node) => node.id);

    vueFlowInstance.value.updateNodeInternals(allNodeIds);
    console.log("FlowCanvas", `已更新所有節點(${allNodeIds.length}個)內部結構`);
    return;
  }

  // 更新指定節點
  const idsToUpdate = Array.isArray(nodeIds) ? nodeIds : [nodeIds];
  vueFlowInstance.value.updateNodeInternals(idsToUpdate);
  console.log("FlowCanvas", `已更新節點 ${idsToUpdate.join(", ")} 內部結構`);
};

// 添加重置函數
const handleResetAllNodes = async () => {
  try {
    const { handleResetAllNodes: resetNodes } = useFlowReset();

    const result = await resetNodes({
      flowInstanceId: props.flowInstance.id,
      elements: elements.value,
      flowInstanceRef: props.flowInstance,
    });

    // 檢查用戶是否取消了重置操作
    if (!result.success) {
      if (result.reason === "user_cancel") {
        console.log("用戶取消了重置操作");
        return;
      }
      console.error("重置失敗:", result.error);
      return;
    }

    // 更新元素列表
    elements.value = result.updatedElements;

    // 等待 Vue 的響應式更新完成
    await nextTick();

    // 強制更新所有節點的內部結構，確保連接線和顯示狀態正確
    if (vueFlowInstance.value) {
      // 獲取所有節點 ID
      const nodeIds = result.updatedElements
        .filter((el) => !el.source) // 過濾出節點（非邊）
        .map((node) => node.id);

      if (nodeIds.length > 0) {
        // 更新節點內部結構
        vueFlowInstance.value.updateNodeInternals(nodeIds);
        console.log(`重置後已更新 ${nodeIds.length} 個節點的內部結構`);
      }
    }

    // 適應視圖，確保顯示完整
    setTimeout(() => {
      handleFitView();
    }, 100);

    // 添加額外的延遲更新，確保所有組件都已正確渲染
    setTimeout(() => {
      if (vueFlowInstance.value) {
        const nodeIds = result.updatedElements
          .filter((el) => !el.source)
          .map((node) => node.id);

        if (nodeIds.length > 0) {
          vueFlowInstance.value.updateNodeInternals(nodeIds);
          console.log("重置後第二次更新節點內部結構（確保完整更新）");
        }
      }
    }, 500);
  } catch (error) {
    console.error("重置和執行過程中發生錯誤:", error);
    ElMessage.error(`操作失敗: ${error.message || error}`);
  }
};

// 儲存異動按鈕的處理函數
const handleSaveChanges = () => {
  if (!nodeChangeState.value.hasUnsavedChanges) {
    ElMessage.info("沒有需要儲存的更改");
    return;
  }

  // 使用非靜默模式同步
  handleSyncCacheToServer(false);
};

// 先於 script setup 最上方註冊全域事件監聽，確保任何時候都能收到
const handleAutoSync = async (event) => {
  console.log(
    "[FlowCanvas] 收到 flow:autoSyncCacheToServer 事件",
    event.detail
  );

  // 確保只同步當前流程
  if (
    event.detail.instanceId &&
    props?.flowInstance?.id !== event.detail.instanceId
  ) {
    console.log("[FlowCanvas] 非當前流程，忽略同步請求");
    return;
  }

  // 檢查是否已在同步中
  if (
    flowCompletionState.value.isSyncing ||
    nodeChangeState.value.isProcessing
  ) {
    console.log("[FlowCanvas] 已有同步操作在進行中，忽略此次同步請求");
    return;
  }

  // 檢查最後同步時間
  if (nodeChangeState.value.lastSyncTime) {
    const timeSinceLastSync = Date.now() - nodeChangeState.value.lastSyncTime;
    if (timeSinceLastSync < 15000) {
      // 15秒內不重複同步
      console.log(
        `[FlowCanvas] 距離上次同步只有 ${Math.round(
          timeSinceLastSync / 1000
        )} 秒，忽略此次同步請求`
      );
      return;
    }
  }

  // 確保 handleSyncCacheToServer 存在
  try {
    console.log(
      "[FlowCanvas] 開始同步當前流程到服務端",
      props?.flowInstance?.id
    );

    // 使用靜默模式同步
    await handleSyncCacheToServer(true);
    // ElMessage.success("流程結束，數據已自動同步到服務端");
  } catch (error) {
    console.error("[FlowCanvas] 同步失敗", error);
  }
};

// 確保在組件卸載時移除事件監聽
// 立即註冊事件監聽
window.addEventListener("flow:autoSyncCacheToServer", handleAutoSync);

// 組件卸載時移除事件監聽
onUnmounted(() => {
  console.log("[FlowCanvas] 移除事件監聽器");
  window.removeEventListener("flow:autoSyncCacheToServer", handleAutoSync);
});

// 處理節點數據變更事件
const handleNodeDataChange = (event) => {
  try {
    const { id, data } = event;

    if (!id || !data) {
      console.error("節點數據變更事件無效，缺少必要參數", event);
      return;
    }

    // 進行節點變更處理
    handleNodeChange(id, "data", { nodeData: data });
  } catch (error) {
    console.error("處理節點數據變更事件時發生錯誤", error);
  }
};

const handleKeyDown = (event) => {
  if (
    event.key === "Delete" ||
    (event.key === "Backspace" && selectedNode.value)
  ) {
    ElMessageBox.confirm("確定要刪除選中的節點嗎？", "刪除確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        removeNodeFromElements(selectedNode.value.id);
      })
      .catch(() => {
        ElMessage.info("已取消刪除");
      });
  }
};
// 處理鍵盤事件
const handleKeyDown22 = (event) => {
  // 檢查是否按下 Delete 或 Backspace 鍵
  if (event.key === "Delete" || event.key === "Backspace") {
    // 檢查是否有選中的節點
    const selectedNodes = elements.value.filter(
      (el) => el.selected && !el.source
    );

    // 檢查是否有選中的連接線（管理員模式下）
    const selectedEdges = userStore.isAdmin
      ? elements.value.filter((el) => el.selected && el.source)
      : [];

    // 計算可刪除的節點和連接線總數
    const deletableItemsCount =
      selectedNodes.filter(
        (node) =>
          node.type === "file" || node.type === "sticky" || userStore.isAdmin
      ).length + selectedEdges.length;

    if (deletableItemsCount > 0) {
      // 顯示確認對話框
      ElMessageBox.confirm(
        `確定要刪除選中的 ${deletableItemsCount} 個項目嗎？`,
        "刪除確認",
        {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          // 用戶確認刪除
          // 首先刪除節點
          const deletableNodes = selectedNodes.filter(
            (node) =>
              node.type === "file" ||
              node.type === "sticky" ||
              userStore.isAdmin
          );

          // 處理每個要刪除的節點
          deletableNodes.forEach((node) => {
            removeNodeFromElements(node.id);
          });

          // 然後刪除連接線
          selectedEdges.forEach((edge) => {
            elements.value = elements.value.filter((el) => el.id !== edge.id);
          });

          // 如果有刪除連接線，更新流程實例
          if (selectedEdges.length > 0) {
            debouncedUpdateFlowInstanceState();
            // console.log(
            //   `已刪除 ${selectedEdges.length} 條連接線，已觸發防抖更新`
            // );
          }

          ElMessage.success(`已刪除 ${deletableItemsCount} 個項目`);
        })
        .catch(() => {
          // 用戶取消刪除
          ElMessage({
            message: "已取消刪除",
            type: "info",
          });
        });
    } else if (selectedNodes.length > 0) {
      // 如果有選中的節點但都不可刪除
      ElMessage.warning("選中的節點類型不允許刪除");
    }
  }
};

// 處理節點變更
// handleNodesChange 函數已被移除，改為直接處理特定事件 (nodeClick, nodeDragStop)

// 在此主要用於新增便利貼(!TODO: 先保留)
const handleAddNode = (type) => {
  console.error("只是要注意，這邊還是用 type.label", type);
  const id = `node_${Date.now()}`;
  const newNode = {
    id,
    type: type === "sticky" ? "sticky" : "",
    data:
      type === "sticky"
        ? {
            content: "",
            color: "#fef3c7",
          }
        : {
            type: type.type,
            name: `新的${type.label}-${id}`,
            content: `新的${type.label}-${id}`,
            status: "IDLE",
            config: { ...type.defaultConfig },
          },
    position: project({ x: 100, y: 100 }),
    zIndex: type === "sticky" ? 90 : 10,
    // 設置節點是否可拖動 - 便利貼、文件節點或管理員可拖動所有節點
    draggable: type === "file" || type === "sticky" || userStore.isAdmin,
  };
  ``;
  // 添加新節點

  // 再更新畫布
  elements.value = [...elements.value, newNode];

  // 添加節點後標記有未保存的更改
  hasUnsavedChanges.value = true;
};

// 處理粘貼事件
const handlePaste = async (event) => {
  // 設置處理標誌
  nodeChangeState.value.isProcessing = true;
  // 設置標誌，表示正在處理貼上事件
  isFileOrStickyChange.value = true; // 添加這行

  // 獲取滑鼠在畫布上的位置（使用畫布中心點）
  const bounds = flowCanvasRef.value.getBoundingClientRect();
  const position = project({
    x: bounds.width / 2,
    y: bounds.height / 2,
  });

  // 檢查剪貼板數據
  const clipboardData = event.clipboardData;
  if (!clipboardData) {
    isFileOrStickyChange.value = false; // 添加這行
    return;
  }

  try {
    // 處理圖片粘貼
    if (clipboardData.items) {
      for (const item of clipboardData.items) {
        // 處理圖片
        if (item.type.indexOf("image") === 0) {
          const blob = item.getAsFile();
          if (blob) {
            try {
              // 創建臨時節點 ID
              const nodeId = `file-${Date.now()}-${Math.random()
                .toString(36)
                .substr(2, 9)}`;

              // 創建臨時節點數據
              const tempNodeData = {
                id: nodeId,
                type: "file",
                position,
                draggable: true,
                data: {
                  fileName: `clipboard-image-${new Date()
                    .toISOString()
                    .slice(0, 19)
                    .replace(/:/g, "-")}.png`,
                  fileType: blob.type,
                  fileSize: blob.size,
                  uploadProgress: 0,
                },
              };

              // 添加臨時節點到畫布
              addNodes([tempNodeData]);

              // 確保節點被添加到 elements 陣列中
              if (!elements.value.some((el) => el.id === nodeId)) {
                elements.value = [...elements.value, tempNodeData];
              }

              // 創建一個監聽器，用於更新節點的上傳進度
              const updateNodeProgress = (progress) => {
                const node = nodes.value.find((n) => n.id === nodeId);
                if (node) {
                  node.data = { ...node.data, uploadProgress: progress };
                }
              };

              // 監聽 uploadProgress 的變化
              const { uploadProgress, uploadFile } = useFileNode();

              // 創建一個監視器，用於監視上傳進度的變化
              const unwatch = watch(uploadProgress, (newProgress) => {
                updateNodeProgress(newProgress);
              });

              // 獲取專案編號和範本名稱
              const projectNumber = props.flowInstance.project?.projectNumber;
              const templateName = props.flowInstance.template?.name;

              // 上傳檔案
              const result = await uploadFile(
                blob,
                props.flowInstance.projectId,
                props.flowInstance.id,
                projectNumber,
                templateName
              );

              // 停止監視上傳進度
              unwatch();

              // 更新節點資訊
              const node = nodes.value.find((n) => n.id === nodeId);
              if (node) {
                node.data = {
                  ...node.data,
                  fileId: result.data.id,
                  fileUrl: result.data.url,
                  fileName: result.data.name,
                  uploadProgress: 100,
                };
              }

              // 使用防抖函數更新流程實例狀態
              debouncedUpdateFlowInstanceState();
              // console.log(`圖片已粘貼，已觸發防抖更新`);
              ElMessage.success(`圖片已粘貼並添加到畫布`);
              return;
            } catch (error) {
              console.error("圖片處理失敗", error);
              ElMessage.error(`圖片粘貼失敗：${error.message || "未知錯誤"}`);
            }
            return;
          }
        }
      }
    }

    // 處理文本粘貼（檢查是否為YouTube鏈接）
    const text = clipboardData.getData("text");
    if (text) {
      // 檢查是否為YouTube鏈接
      const youtubeRegex =
        /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
      const match = text.match(youtubeRegex);

      if (match && match[1]) {
        const videoId = match[1];
        const id = `youtube-${Date.now()}`;

        // 創建YouTube節點
        const youtubeNode = {
          id,
          type: "sticky", // 使用便利貼節點類型
          position,
          draggable: true,
          data: {
            content: `<div style="position:relative;width:100%;height:100%.5px;z-index:1;">
              <iframe src="https://www.youtube.com/embed/${videoId}" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen style="position:relative;z-index:1;"></iframe>
            </div>`,
            color: "#dedede", // 灰色背景
            isYoutubeEmbed: true,
            youtubeVideoId: videoId,
          },
        };

        // 添加節點到畫布
        elements.value = [...elements.value, youtubeNode];

        // 使用防抖函數將節點數據保存為數據更新
        debouncedSaveNodeAsDataUpdate(id, youtubeNode, true);

        // 更新整個工作流程
        await syncAllChangesToServer();

        // console.log(`YouTube影片已添加，已觸發工作流程更新`);
        ElMessage.success("YouTube影片已添加到畫布");
        return;
      }

      // 如果不是YouTube鏈接，創建普通便利貼
      const id = `note-${Date.now()}`;
      const noteNode = {
        id,
        type: "sticky",
        position,
        draggable: true,
        data: {
          content: text,
          color: "#fef3c7", // 黃色背景
        },
      };

      // 添加節點到畫布
      elements.value = [...elements.value, noteNode];

      // 使用防抖函數將節點數據保存為數據更新
      debouncedSaveNodeAsDataUpdate(id, noteNode, true);

      // 更新整個工作流程
      await syncAllChangesToServer();

      console.log(`文本已添加，已觸發工作流程更新`);
      ElMessage.success("文本已添加到畫布");
    }
  } catch (error) {
    console.error("粘貼處理失敗", error);
    ElMessage.error(`粘貼處理失敗：${error.message || "未知錯誤"}`);
  } finally {
    // 重置標誌
    nodeChangeState.value.isProcessing = false;
    isFileOrStickyChange.value = false;
    // 確保不會觸發 hasUnsavedChanges
    hasUnsavedChanges.value = false;
  }
};

// 初始化元素
const initializeElements = () => {
  // 設置初始化標誌
  isInitializingElements.value = true;

  // 初始化模擬慢速上傳設置
  if (isDevelopment && localStorage.getItem("simulateSlowUpload") === null) {
    localStorage.setItem("simulateSlowUpload", "true");
  }

  // 初始化 elements，從 flowInstance 中獲取節點和邊緣數據
  if (
    props.flowInstance &&
    props.flowInstance.nodes &&
    props.flowInstance.edges
  ) {
    // 處理節點，設置 FileNode, StickyNode 可拖動，管理員可拖動所有節點，其他節點不可拖動
    const processedNodes = props.flowInstance.nodes.map((node) => {
      // 確保節點數據中包含必要的屬性
      const nodeData = {
        ...node.data,
        // 確保 name 屬性存在
        name: node.data?.name || node.data?.label || "未命名節點",
        // 確保 nodeDefinitionId 屬性存在
        nodeDefinitionId:
          node.data?.nodeDefinitionId || node.data?.definitionId || "",
      };

      // 如果是管理員，所有節點都可拖動
      if (userStore.isAdmin) {
        return {
          ...node,
          data: nodeData,
          draggable: true,
          // 確保節點有正確的樣式
          style: {
            ...(node.style || {}),
            cursor: "move",
          },
        };
      }

      // 非管理員模式下，只有文件和便利貼節點可拖動
      if (node.type === "file" || node.type === "sticky") {
        return {
          ...node,
          data: nodeData,
          draggable: true,
          // 確保節點有正確的樣式
          style: {
            ...(node.style || {}),
            cursor: "move",
          },
        };
      }

      // 其他節點設置為不可拖動（非管理員模式）
      return {
        ...node,
        data: nodeData,
        draggable: userStore.isAdmin, // 如果是管理員依然可拖動
        // 確保節點有正確的樣式
        style: {
          ...(node.style || {}),
          cursor: userStore.isAdmin ? "move" : "default",
        },
      };
    });

    // 處理邊，確保所有邊都不可更新和刪除
    const processedEdges = props.flowInstance.edges.map((edge) => {
      return {
        ...edge,
        deletable: userStore.isAdmin,
        updatable: userStore.isAdmin,
        selectable: userStore.isAdmin,
        focusable: userStore.isAdmin,
        // 確保連接線在管理員模式下有正確的樣式和交互性
        style: {
          ...(edge.style || {}),
          cursor: userStore.isAdmin ? "pointer" : "default",
          pointerEvents: userStore.isAdmin ? "all" : "none",
        },
      };
    });

    // 保存初始元素的深拷貝，用於後續比較是否有實際變更
    const initialElements = JSON.stringify([
      ...processedNodes,
      ...processedEdges,
    ]);

    elements.value = [...processedNodes, ...processedEdges];

    // 適應視窗大小
    handleFitView();

    // 在初始化完成後，添加一個延遲的連接線位置更新
    setTimeout(() => {
      if (vueFlowInstance.value) {
        const nodeIds = processedNodes.map((node) => node.id);
        vueFlowInstance.value.updateNodeInternals(nodeIds);
      }
    }, 500);

    // 添加第二次更新，處理可能的異步加載問題
    setTimeout(() => {
      if (vueFlowInstance.value) {
        const nodeIds = processedNodes.map((node) => node.id);
        vueFlowInstance.value.updateNodeInternals(nodeIds);

        // 再次適應視窗大小
        handleFitView();
      }
    }, 1000);

    console.log("FlowCanvas", "流程實例元素已初始化");
    console.log(
      "FlowCanvas",
      `已載入 ${processedNodes.length} 個節點和 ${processedEdges.length} 條連線`
    );

    // 初始化完成後，重置標誌，但添加檢查避免不必要的API請求
    setTimeout(() => {
      // 檢查元素是否有實際變化，只有在有變化時才需要解除初始化鎖定
      const currentElements = JSON.stringify(elements.value);
      const hasRealChanges = initialElements !== currentElements;

      if (hasRealChanges) {
        // console.log("FlowCanvas", "初始化過程中元素有實際變化，解除初始化鎖定");
      } else {
        // console.log(
        //   "FlowCanvas",
        //   "初始化過程中元素無實際變化，解除初始化鎖定但禁止自動更新"
        // );
        // 在無變化情況下重置標誌，但額外添加一個一次性標記來阻止後續自動更新
        isInitializingElements.value = false;

        // 為了安全起見，再添加一個短暫的延時來阻止可能的後續更新
        setTimeout(() => {
          isInitializingElements.value = true;
          setTimeout(() => {
            isInitializingElements.value = false;
            // console.log("FlowCanvas", "安全期結束，恢復正常操作");

            // 在安全期結束後初始化流程完成檢測
            initFlowCompletionCheck();
          }, 500);
        }, 100);

        return;
      }

      isInitializingElements.value = false;
      // console.log("FlowCanvas", "元素初始化完成，已重置初始化標誌");

      // 初始化流程完成檢測
      initFlowCompletionCheck();

      // 初始化節點滾動指示器
      nextTick(() => {
        initializeNodeScrollIndicators();
      });
    }, 2500);
  } else {
    // 即使數據不完整也需要重置標誌
    setTimeout(() => {
      isInitializingElements.value = false;
      console.error(
        "FlowCanvas",
        "元素初始化完成（數據不完整），已重置初始化標誌"
      );
    }, 2500);
  }

  // 設置畫布為可聚焦元素，以便接收粘貼事件
  if (flowCanvasRef.value) {
    flowCanvasRef.value.tabIndex = 0;
  }
};

// 處理節點大小變更事件
const handleNodeResize = (event) => {
  try {
    // 檢查事件對象是否有效
    if (!event || !event.id) {
      console.error("FlowCanvas", "調整大小事件無效", event);
      return;
    }

    // 獲取節點 ID
    const nodeId = event.id;
    console.log("FlowCanvas", `節點 ${nodeId} 大小調整事件`, event);

    // 獲取寬度和高度
    const width = event.width || (event.params && event.params.width);
    const height = event.height || (event.params && event.params.height);

    if (!width || !height) {
      console.error("FlowCanvas", "無法獲取節點新的寬度和高度", event);
      return;
    }

    console.log("FlowCanvas", `節點 ${nodeId} 新尺寸: ${width} x ${height}`);

    // 找到要調整大小的節點
    const node = elements.value.find((el) => el.id === nodeId);
    if (!node) {
      console.error("FlowCanvas", "找不到要調整大小的節點", nodeId);
      return;
    }

    // 更新節點的樣式和數據
    if (!node.style) node.style = {};
    node.style.width = `${width}px`;
    node.style.height = `${height}px`;

    // 更新節點數據中的寬度和高度
    if (!node.data) node.data = {};
    node.data.width = width;
    node.data.height = height;

    // 立即更新 VueFlow 中的節點
    if (vueFlowInstance.value) {
      vueFlowInstance.value.updateNode({
        id: nodeId,
        style: node.style,
        data: node.data,
      });
      console.log(`已通知 VueFlow 更新節點 ${nodeId}`);
    } else {
      console.log(`vueFlowInstance 不可用，無法更新節點 ${nodeId}`);
    }

    // 保存節點數據更新
    if (node.type === "file" || node.type === "sticky") {
      // 立即保存節點數據更新，不使用防抖
      saveNodeAsDataUpdate(nodeId, node, true);
      console.log(`節點 ${nodeId} 大小已變更，使用數據更新方式儲存`);

      // 同時也更新結構，確保重新整理後能看到變更
      updateFlowInstanceState();
      console.log(
        `節點 ${nodeId} 大小已變更，同時也使用結構更新方式儲存，確保重新整理後能看到變更`
      );
    } else {
      updateFlowInstanceState();
      console.log(`節點 ${nodeId} 大小已變更，使用結構更新方式儲存`);
    }

    console.log(`節點 ${nodeId} 大小調整完成`);
  } catch (error) {
    console.error("FlowCanvas", "處理節點大小調整事件時發生錯誤", error);
  }
};

// 添加節點滾動控制的狀態管理
const isMouseOverNode = ref(false); // 記錄滑鼠是否在節點上
const currentHoverNodeId = ref(null); // 當前懸停的節點ID
const currentHoverNodeElement = ref(null); // 當前懸停的節點DOM元素

// 添加新的函數處理節點上的滾動功能
const setupNodeScrollEventListeners = () => {
  console.log("FlowCanvas", "設置節點滾動事件監聽器");

  // 獲取VueFlow容器元素
  const vueFlowContainer = document.querySelector(".vue-flow");
  if (!vueFlowContainer) return;

  // 清除可能已存在的事件監聽器
  if (nodeScrollState.wheelListener) {
    vueFlowContainer.removeEventListener(
      "wheel",
      nodeScrollState.wheelListener,
      {
        passive: false,
      }
    );
  }

  // 為VueFlow容器添加全局事件委託
  vueFlowContainer.addEventListener(
    "mouseover",
    (event) => {
      const nodeElement = event.target.closest(".vue-flow__node");
      if (nodeElement) {
        handleNodeMouseOver(event);
      }
    },
    true
  );

  vueFlowContainer.addEventListener(
    "mouseout",
    (event) => {
      const nodeElement = event.target.closest(".vue-flow__node");
      if (nodeElement) {
        handleNodeMouseOut(event);
      }
    },
    true
  );

  // 添加滾輪事件監聽
  vueFlowContainer.addEventListener("wheel", handleNodeWheel, {
    passive: false,
    capture: true, // 使用捕獲階段，確保先於其他事件處理程序執行
  });

  // 保存到狀態變數中，以便後續清理
  nodeScrollState.wheelListener = handleNodeWheel;
  nodeScrollState.mouseEnterListener = handleNodeMouseOver;
  nodeScrollState.mouseLeaveListener = handleNodeMouseOut;
  nodeScrollState.container = vueFlowContainer;

  // 初始化節點內容監視器
  nodeScrollState.contentObserver = setupNodeContentChangeObserver();
};

// 添加清理節點滾動事件監聽器的函數
const cleanupNodeScrollEventListeners = () => {
  console.log("FlowCanvas", "清理節點滾動事件監聽器");

  // 檢查是否有已儲存的容器元素和事件監聽器
  if (nodeScrollState.container) {
    // 移除事件監聽器
    if (nodeScrollState.wheelListener) {
      nodeScrollState.container.removeEventListener(
        "wheel",
        nodeScrollState.wheelListener,
        { passive: false, capture: true }
      );
    }

    if (nodeScrollState.mouseEnterListener) {
      nodeScrollState.container.removeEventListener(
        "mouseover",
        nodeScrollState.mouseEnterListener,
        true
      );
    }

    if (nodeScrollState.mouseLeaveListener) {
      nodeScrollState.container.removeEventListener(
        "mouseout",
        nodeScrollState.mouseLeaveListener,
        true
      );
    }

    // 清理所有滾動指示器
    const indicators = document.querySelectorAll(".scrollable-indicator");
    indicators.forEach((indicator) => {
      indicator.remove();
    });

    // 清理幫助提示
    const helpTip = document.querySelector(".node-scroll-help-tip");
    if (helpTip) {
      helpTip.remove();
    }

    // 清理內容觀察器
    if (nodeScrollState.contentObserver) {
      nodeScrollState.contentObserver.disconnect();
      nodeScrollState.contentObserver = null;
    }
  }

  // 重置狀態
  nodeScrollState.wheelListener = null;
  nodeScrollState.mouseEnterListener = null;
  nodeScrollState.mouseLeaveListener = null;
  nodeScrollState.container = null;
};

// 優化 handleNodeWheel 函數
const handleNodeWheel = (event) => {
  // 嘗試找到目標節點元素
  const targetNodeElement = event.target.closest(".vue-flow__node");

  // 如果不是在節點上或沒有當前懸停節點，使用默認行為
  if (!targetNodeElement || !isMouseOverNode.value) {
    return;
  }

  // 檢查事件目標是否在連接點(handle)上
  const isOnHandle =
    event.target.classList.contains("vue-flow__handle") ||
    event.target.closest(".vue-flow__handle");
  if (isOnHandle) {
    return; // 如果在連接點上，使用默認行為
  }

  // 獲取節點內容元素 (一般是節點內的可滾動容器)
  let nodeContentElement =
    targetNodeElement.querySelector(".node-content") ||
    targetNodeElement.querySelector(".scrollable-content");

  // 如果找不到特定的滾動容器，則檢查節點本身是否有滾動內容
  if (!nodeContentElement) {
    // 檢查節點是否具有可滾動性（內容超出視窗）
    if (targetNodeElement.scrollHeight > targetNodeElement.clientHeight) {
      nodeContentElement = targetNodeElement;
    }
  }

  // 如果沒有可滾動內容，或者內容不能滾動，則使用默認行為
  if (
    !nodeContentElement ||
    nodeContentElement.scrollHeight <= nodeContentElement.clientHeight
  ) {
    return;
  }

  // 無論捲軸在頂部還是底部，都先阻止事件傳播和默認行為
  // 這樣能確保滾輪事件不會傳遞給畫布
  event.stopPropagation();
  event.preventDefault();

  // 計算是否可以繼續滾動
  const atTop = nodeContentElement.scrollTop <= 0;
  const atBottom =
    nodeContentElement.scrollTop + nodeContentElement.clientHeight >=
    nodeContentElement.scrollHeight - 2; // 減2是為了處理小數點誤差

  // 判斷滾動方向
  const isScrollingUp = event.deltaY < 0;
  const isScrollingDown = event.deltaY > 0;

  // 只有在內容可以按照滾動方向滾動時才執行滾動
  if ((isScrollingUp && !atTop) || (isScrollingDown && !atBottom)) {
    // 執行滾動
    nodeContentElement.scrollTop += event.deltaY;
  }

  // 無論是否執行滾動，都更新指示器
  updateScrollIndicator(nodeContentElement);
};

// 處理滑鼠進入節點事件 - 優化版本
const handleNodeMouseOver = (event) => {
  // 檢查目標是否是節點或節點內的元素
  const nodeElement = event.target.closest(".vue-flow__node");
  if (!nodeElement) return;

  // 檢查是否在連接點上
  const isOnHandle =
    event.target.classList.contains("vue-flow__handle") ||
    event.target.closest(".vue-flow__handle");
  if (isOnHandle) return;

  // 設置狀態
  isMouseOverNode.value = true;
  currentHoverNodeId.value = nodeElement.getAttribute("data-id");
  currentHoverNodeElement.value = nodeElement;

  // 添加視覺指示類別
  nodeElement.classList.add("is-scroll-active");

  // 檢查節點內是否有可滾動內容
  const contentElement =
    nodeElement.querySelector(".node-content") ||
    nodeElement.querySelector(".scrollable-content") ||
    nodeElement;

  // 檢查內容是否可滾動
  if (contentElement.scrollHeight > contentElement.clientHeight) {
    // 添加視覺指示，表明節點現在可滾動
    nodeElement.style.cursor = "default";

    // 添加滾動指示器（如果尚未存在）
    addScrollIndicator(nodeElement, contentElement);

    // 顯示滾動提示
    const helpTip = document.querySelector(".node-scroll-help-tip");
    if (helpTip) {
      helpTip.style.opacity = "1";
      setTimeout(() => {
        helpTip.style.opacity = "0";
      }, 3000);
    }
  }
};

// 添加滾動指示器實現函數
const addScrollIndicator = (nodeElement, contentElement) => {
  // 檢查是否已有滾動指示器
  let indicator = nodeElement.querySelector(".scrollable-indicator");

  // 如果已存在，直接返回
  if (indicator) return;

  // 創建滾動指示器
  indicator = document.createElement("div");
  indicator.className = "scrollable-indicator";
  indicator.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M7 13l5 5 5-5"></path>
      <path d="M7 6l5 5 5-5"></path>
    </svg>
  `;

  // 設置樣式
  Object.assign(indicator.style, {
    position: "absolute",
    right: "4px",
    bottom: "4px",
    width: "16px",
    height: "16px",
    borderRadius: "50%",
    background: "rgba(0, 0, 0, 0.1)",
    color: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    pointerEvents: "none",
    zIndex: "100",
    opacity: "0",
    transition: "opacity 0.3s ease",
  });

  // 添加到節點
  nodeElement.appendChild(indicator);

  // 設置滾動事件監聽器來更新指示器顯示
  contentElement.addEventListener("scroll", () => {
    // 如果內容可以繼續滾動，顯示指示器
    const canScrollDown =
      contentElement.scrollTop + contentElement.clientHeight <
      contentElement.scrollHeight;
    const canScrollUp = contentElement.scrollTop > 0;

    if (canScrollDown || canScrollUp) {
      indicator.style.opacity = "1";

      // 顯示適當的滾動方向
      if (canScrollDown && canScrollUp) {
        // 可上可下
        indicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M7 13l5 5 5-5"></path>
            <path d="M7 6l5 5 5-5"></path>
          </svg>
        `;
      } else if (canScrollDown) {
        // 只能往下滾動
        indicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M7 13l5 5 5-5"></path>
          </svg>
        `;
      } else {
        // 只能往上滾動
        indicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M7 6l5 5 5-5"></path>
          </svg>
        `;
      }
    } else {
      // 內容無法滾動，隱藏指示器
      indicator.style.opacity = "0";
    }
  });

  // 節點滑鼠懸停時顯示指示器
  nodeElement.addEventListener("mouseenter", () => {
    if (isNodeScrollable(nodeElement)) {
      indicator.style.opacity = "1";
    }
  });

  nodeElement.addEventListener("mouseleave", () => {
    indicator.style.opacity = "0";
  });
};

// 添加初始化節點滾動指示器的函數
const initializeNodeScrollIndicators = () => {
  // 獲取所有節點
  const nodeElements = document.querySelectorAll(".vue-flow__node");

  // 為每個節點檢查並添加滾動指示器
  nodeElements.forEach((nodeElement) => {
    // 獲取節點內容元素
    const contentElement =
      nodeElement.querySelector(".node-content") ||
      nodeElement.querySelector(".scrollable-content");

    // 如果找到內容元素並且內容可滾動
    if (
      contentElement &&
      contentElement.scrollHeight > contentElement.clientHeight
    ) {
      // 為節點添加滾動指示器
      addScrollIndicator(nodeElement, contentElement);
    } else if (nodeElement.scrollHeight > nodeElement.clientHeight) {
      // 如果節點本身可滾動
      addScrollIndicator(nodeElement, nodeElement);
    }
  });

  // 設置一個輔助元素作為提示
  const helpElement = document.querySelector(".vue-flow");
  if (helpElement) {
    // 先檢查是否已存在提示元素
    let helpTip = document.querySelector(".node-scroll-help-tip");

    // 如果不存在，則創建新的提示元素
    if (!helpTip) {
      helpTip = document.createElement("div");
      helpTip.className = "node-scroll-help-tip";
      helpTip.textContent = "滑鼠滾輪可在節點內容滾動";
      helpTip.style.cssText = `
        position: absolute;
        bottom: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1000;
      `;
      helpElement.appendChild(helpTip);
    }

    // 清除舊的事件監聽器，避免重複綁定
    const oldListener = helpElement._scrollHelpListener;
    if (oldListener) {
      document.removeEventListener("mouseover", oldListener);
    }

    // 創建並保存新的事件監聽器
    const newListener = (event) => {
      const node = event.target.closest(".vue-flow__node");
      if (node && isNodeScrollable(node)) {
        helpTip.style.opacity = "1";
        // 3秒後自動隱藏
        setTimeout(() => {
          helpTip.style.opacity = "0";
        }, 3000);
      }
    };

    helpElement._scrollHelpListener = newListener;
    document.addEventListener("mouseover", newListener);
  }
};

// 處理滑鼠離開節點事件
const handleNodeMouseOut = (event) => {
  // 檢查是否真的離開了節點 (避免因為進入子元素而誤觸發)
  const nodeElement = event.target.closest(".vue-flow__node");
  const toElement = event.relatedTarget;

  if (nodeElement && (!toElement || !nodeElement.contains(toElement))) {
    // 重置狀態
    isMouseOverNode.value = false;
    currentHoverNodeId.value = null;
    currentHoverNodeElement.value = null;

    // 移除視覺指示類別
    nodeElement.classList.remove("is-scroll-active");

    // 重置視覺指示
    if (nodeElement.style.cursor === "default") {
      nodeElement.style.cursor = "";
    }

    // 可選：隱藏滾動指示器
    const indicator = nodeElement.querySelector(".scrollable-indicator");
    if (indicator) {
      indicator.style.opacity = "0";
    }
  }
};

// 添加節點變更監聽器，確保在節點內容變化時更新滾動指示器
const setupNodeContentChangeObserver = () => {
  // 創建一個 MutationObserver 來監視節點內容變化
  const observer = new MutationObserver((mutations) => {
    // 收集涉及的節點
    const affectedNodes = new Set();

    mutations.forEach((mutation) => {
      // 檢查變化是否在節點內
      // 確保 mutation.target 是一個 DOM 元素並且有 closest 方法
      if (mutation.target && typeof mutation.target.closest === "function") {
        const nodeElement = mutation.target.closest(".vue-flow__node");
        if (nodeElement) {
          affectedNodes.add(nodeElement);
        }
      }
    });

    // 處理每個受影響的節點
    affectedNodes.forEach((nodeElement) => {
      // 檢查節點是否可滾動
      if (isNodeScrollable(nodeElement)) {
        // 更新或添加滾動指示器
        const contentElement =
          nodeElement.querySelector(".node-content") ||
          nodeElement.querySelector(".scrollable-content") ||
          nodeElement;

        addScrollIndicator(nodeElement, contentElement);
      } else {
        // 如果節點不可滾動，則移除滾動指示器
        const indicator = nodeElement.querySelector(".scrollable-indicator");
        if (indicator) {
          indicator.remove();
        }
      }
    });
  });

  // 開始觀察整個流程畫布
  const flowCanvas = document.getElementById("flowCanvasDiv");
  if (flowCanvas) {
    observer.observe(flowCanvas, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
      attributeFilter: ["style", "class"],
    });

    // 保存觀察器引用以便後續清理
    return observer;
  }

  return null;
};

// 添加節點滾動狀態對象來管理相關狀態和引用
const nodeScrollState = reactive({
  contentObserver: null, // 用於監聽節點內容變化的觀察器
});

// 添加更新滾動指示器的函數
const updateScrollIndicator = (contentElement) => {
  if (!currentHoverNodeElement.value) return;

  // 獲取滾動指示器
  const indicator = currentHoverNodeElement.value.querySelector(
    ".scrollable-indicator"
  );
  if (!indicator) return;

  // 檢查內容是否可滾動
  const canScrollDown =
    contentElement.scrollTop + contentElement.clientHeight <
    contentElement.scrollHeight - 1;
  const canScrollUp = contentElement.scrollTop > 0;

  // 更新滾動指示器
  if (canScrollDown || canScrollUp) {
    indicator.style.opacity = "1";

    // 更新指示器圖標
    if (canScrollDown && canScrollUp) {
      // 可上可下
      indicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M7 13l5 5 5-5"></path>
          <path d="M7 6l5 5 5-5"></path>
        </svg>
      `;
    } else if (canScrollDown) {
      // 只能往下滾動
      indicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M7 13l5 5 5-5"></path>
        </svg>
      `;
    } else {
      // 只能往上滾動
      indicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M7 6l5 5 5-5"></path>
        </svg>
      `;
    }
  } else {
    // 內容不可滾動或已完全顯示
    indicator.style.opacity = "0";
  }
};

// 檢查節點是否可滾動的函數
const isNodeScrollable = (nodeElement) => {
  // 首先檢查是否有特定的可滾動內容元素
  const contentElement =
    nodeElement.querySelector(".node-content") ||
    nodeElement.querySelector(".scrollable-content");

  // 如果找到特定的內容元素，檢查是否可滾動
  if (contentElement) {
    return contentElement.scrollHeight > contentElement.clientHeight;
  }

  // 如果沒有找到特定的內容元素，檢查節點本身是否可滾動
  return nodeElement.scrollHeight > nodeElement.clientHeight;
};
</script>

<style scoped>
@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }
}

/* 全屏狀態樣式 */
:fullscreen {
  background-color: white;
  padding: 0;
  overflow: hidden;
}

:fullscreen .vue-flow {
  width: 100%;
  height: 100%;
}

:fullscreen .vue-flow__panel {
  z-index: 10;
}

:fullscreen .vue-flow__panel.top-right {
  top: 10px;
  right: 10px;
}

:fullscreen .vue-flow__controls {
  bottom: 40px;
}

:fullscreen .vue-flow__minimap {
  bottom: 40px;
  right: 10px;
}

/* 全屏模式下按鈕懸停效果增強 */
:fullscreen .vue-flow__panel button:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* CSS 模擬全屏樣式 */
.css-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9000 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  background-color: white !important;
}

.css-fullscreen .vue-flow {
  width: 100% !important;
  height: 100% !important;
}

.css-fullscreen .vue-flow__panel {
  z-index: 9010 !important;
}

.css-fullscreen .vue-flow__panel.top-right {
  top: 10px !important;
  right: 10px !important;
}

.css-fullscreen .vue-flow__controls {
  bottom: 40px !important;
}

.css-fullscreen .vue-flow__minimap {
  bottom: 40px !important;
  right: 10px !important;
}

/* CSS 全屏模式下按鈕懸停效果增強 */
.css-fullscreen .vue-flow__panel button:hover {
  transform: scale(1.05) !important;
  transition: transform 0.2s ease !important;
}

.vue-flow__minimap {
  background-color: #202020;
  border: 1px solid #e5e7eb;
  opacity: 0.8;
}
.vue-flow__minimap:hover {
  opacity: 1;
  /* background-color: yellow; */
  transition: all 0.05s ease;
  border: 1px solid rgb(156, 156, 156);
}

/* 管理員模式下連接線的懸停效果 */
:deep(.vue-flow__edge:hover .vue-flow__edge-path) {
  stroke: #0004ff !important; /* 淺紫色 */
  stroke-width: 8px !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.vue-flow__edge:hover .vue-flow__edge-path) marker {
  fill: #6366f1 !important;
  transition: all 0.2s ease;
}

/* JSON Drawer 深色模式樣式 */
:deep(.dark) .json-drawer .el-drawer__header,
html.dark :deep(.json-drawer .el-drawer__header) {
  background-color: #1e293b;
  color: #e2e8f0;
  border-bottom: 1px solid #334155;
}

:deep(.dark) .json-drawer .el-drawer__body,
html.dark :deep(.json-drawer .el-drawer__body) {
  background-color: #0f172a;
  color: #e2e8f0;
}

:deep(.dark) .json-drawer .el-drawer__close-btn,
html.dark :deep(.json-drawer .el-drawer__close-btn) {
  color: #94a3b8;
}

:deep(.dark) .json-drawer .el-drawer__close-btn:hover,
html.dark :deep(.json-drawer .el-drawer__close-btn:hover) {
  color: #e2e8f0;
}

/* 儲存異動按鈕的閃爍效果 */
.save-changes-btn {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(237, 137, 54, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(237, 137, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(237, 137, 54, 0);
  }
}

/* 節點內容滾動相關樣式 */
:deep(.vue-flow__node:hover) {
  z-index: 10 !important; /* 確保懸停節點顯示在最前面 */
}

/* 提供視覺反饋指示節點中的內容可滾動 */
:deep(.vue-flow__node .node-content),
:deep(.vue-flow__node .scrollable-content) {
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

/* 自定義滾動條樣式 */
:deep(.vue-flow__node .node-content::-webkit-scrollbar),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar) {
  width: 4px;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-track),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-thumb),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-thumb) {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
}

/* 懸停時更明顯的滾動條 */
:deep(.vue-flow__node:hover .node-content::-webkit-scrollbar-thumb),
:deep(.vue-flow__node:hover .scrollable-content::-webkit-scrollbar-thumb) {
  background-color: rgba(155, 155, 155, 0.8);
}

/* 可滾動指示 - 當節點內容可滾動時添加微妙視覺提示 */
:deep(.vue-flow__node .scrollable-indicator) {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(100, 100, 100, 0.4);
  opacity: 0;
  transition: opacity 0.2s ease;
}

:deep(.vue-flow__node:hover .scrollable-indicator) {
  opacity: 1;
}

.vue-flow-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 節點內容滾動相關樣式 */
:deep(.vue-flow__node:hover) {
  z-index: 10 !important; /* 確保懸停節點顯示在最前面 */
}

/* 提供視覺反饋指示節點中的內容可滾動 */
:deep(.vue-flow__node .node-content),
:deep(.vue-flow__node .scrollable-content) {
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

/* 自定義滾動條樣式 */
:deep(.vue-flow__node .node-content::-webkit-scrollbar),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar) {
  width: 4px;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-track),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-thumb),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-thumb) {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 4px;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-thumb:hover),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-thumb:hover) {
  background-color: rgba(100, 100, 100, 0.8);
}

/* 滾動指示器樣式 */
:deep(.scrollable-indicator) {
  position: absolute;
  right: 4px;
  bottom: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 100;
  opacity: 0;
  transition: opacity 0.3s ease;
}

:deep(.vue-flow__node:hover .scrollable-indicator) {
  opacity: 1;
}

/* 修復拖曳連接點的樣式，確保連接點始終可點擊 */
:deep(.vue-flow__handle) {
  z-index: 20;
  cursor: crosshair !important;
}

/* 在 <style scoped> 區塊添加節點滾動相關樣式，確保覆蓋所有可能的情況 */

/* 節點滾動相關樣式 */
:deep(.vue-flow__node) {
  transition: box-shadow 0.3s ease, transform 0.1s ease;
}

:deep(.vue-flow__node:hover) {
  z-index: 10 !important; /* 確保懸停節點顯示在最前面 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 當滑鼠在節點上時的視覺指示 */
:deep(.vue-flow__node.is-scroll-active) {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* 節點內容滾動相關樣式 */
:deep(.vue-flow__node .node-content),
:deep(.vue-flow__node .scrollable-content) {
  max-height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  transition: scrollbar-color 0.3s ease;
}

/* 控制節點內容滾動時的效果 */
:deep(.vue-flow__node:hover .node-content),
:deep(.vue-flow__node:hover .scrollable-content) {
  scrollbar-color: rgba(100, 100, 100, 0.7) transparent;
}

/* 自定義滾動條樣式 */
:deep(.vue-flow__node .node-content::-webkit-scrollbar),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar) {
  width: 4px;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-track),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.vue-flow__node .node-content::-webkit-scrollbar-thumb),
:deep(.vue-flow__node .scrollable-content::-webkit-scrollbar-thumb) {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

:deep(.vue-flow__node:hover .node-content::-webkit-scrollbar-thumb),
:deep(.vue-flow__node:hover .scrollable-content::-webkit-scrollbar-thumb) {
  background-color: rgba(100, 100, 100, 0.7);
}

/* 滾動指示器樣式 */
:deep(.scrollable-indicator) {
  position: absolute;
  right: 4px;
  bottom: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  opacity: 0;
  transition: opacity 0.3s ease, background 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

:deep(.vue-flow__node:hover .scrollable-indicator) {
  opacity: 0.8;
}

:deep(.scrollable-indicator:hover) {
  background: rgba(0, 0, 0, 0.7);
}

/* 節點滾動提示樣式 */
.node-scroll-help-tip {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 1000;
}
</style>
