<template>
  <div class="workflow-container">
    <!-- 工作流程畫布區域 -->
    <div class="workflow-canvas">
      <FlowCanvas
        :flowInstance="formattedFlowInstance"
        :key="flowCanvasKey" />
    </div>
  </div>
</template>

<script setup>
import FlowCanvas from "@/views/flow/instance/detail/components/FlowCanvas.vue";
import { useFlowStore } from "@/stores/flowStore";
import { useRoute } from "vue-router";

const props = defineProps({
  flowInstance: {
    type: Object,
    required: true,
  },
});

// 生成唯一的key，用於強制更新FlowCanvas組件
const flowCanvasKey = ref(Date.now());

// 預處理 flowInstance，避免不必要的屬性變更觸發更新
const formattedFlowInstance = computed(() => {
  if (!props.flowInstance) return null;

  // 只提取必要的屬性，避免額外的屬性觸發不必要的更新
  return {
    id: props.flowInstance.id,
    nodes: props.flowInstance.nodes,
    edges: props.flowInstance.edges,
    projectId: props.flowInstance.projectId,
    project: {
      projectNumber: props.flowInstance.project?.projectNumber,
      name: props.flowInstance.project?.name,
    },
    template: {
      id: props.flowInstance.template?.id,
      name: props.flowInstance.template?.name,
    },
    context: props.flowInstance.context || {},
  };
});
</script>

<style scoped>
.workflow-container {
  position: relative;
  height: calc(100vh - 110px);
  overflow: hidden;
}

.workflow-canvas {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
