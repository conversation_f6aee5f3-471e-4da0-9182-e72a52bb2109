<template>
  <Teleport
    to="#header-actions"
    defer>
    <!-- 返回按鈕 -->
    <el-button
      plain
      type="default"
      @click="handleBack"
      class="mr-2 dark:border-gray-700 dark:text-dark-mode dark:hover:bg-gray-700">
      <ArrowLeft
        class="mr-1"
        :size="16" />
      返回
    </el-button>
    <!-- <KeycapGroup>
      <Keycap>⌘</Keycap>
      <Keycap>K</Keycap>
    </KeycapGroup> -->

    <!-- 收藏按鈕 -->
    <div class="inline-flex items-center mx-2 cursor-pointer" @click="toggleFavorite">
      <el-tooltip
        :content="isFavorited ? '取消收藏' : '加入收藏'"
        placement="top">
        <Star
          :size="20"
          :class="[
            isFavorited
              ? 'text-yellow-400 dark:text-yellow-400 fill-yellow-400'
              : 'text-gray-400 dark:text-gray-400',
            'cursor-pointer hover:text-yellow-500 dark:hover:text-yellow-500',
          ]" />
      </el-tooltip>
    </div>

    <el-button
      plain
      link
      type="primary"
      class="dark:bg-transparent">
      <el-segmented
        v-model="viewFlowMode"
        :options="viewModeoptions"
        @change="handleModeChange"
        block>
        <template #default="scope">
          <div class="flex align-center justify-center">
            <component
              :is="scope.item.icon"
              class="!w-3 !h-3 mr-1 pt-1" />

            {{ scope.item.label }}
          </div>
        </template>
      </el-segmented>
    </el-button>

    <!-- 報表按鈕 -->
    <el-button
      plain
      type="primary"
      @click="navigateToReport"
      class="ml-2">
      <FileBarChart
        class="mr-1"
        :size="16" />
      報表
    </el-button>
  </Teleport>
</template>

<script setup>
import {
  GitBranch,
  List,
  ArrowLeft,
  FileBarChart,
  Star,
} from "lucide-vue-next";
import { useFlowStore } from "@/stores/flowStore";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import Keycap from "@/components/Keycap.vue";
import KeycapGroup from "@/components/KeycapGroup.vue";
import { useFavoriteStore } from "@/stores/favorite";
import { useUserStore } from "@/stores/user";

const props = defineProps({
  // showHeaderContent: {
  //   type: Boolean,
  //   default: true,
  // },
  modelValue: {
    type: String,
    default: "flow",
  },
});

const emit = defineEmits(["update:modelValue"]);

const flowStore = useFlowStore();
const router = useRouter();
const route = useRoute();
const favoriteStore = useFavoriteStore();
const userStore = useUserStore();

const viewModeoptions = [
  { label: "流程", value: "flow", icon: GitBranch },
  { label: "列表", value: "list", icon: List },
];

const viewFlowMode = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 檢查是否已關注
const isFavorited = computed(() => {
  const instanceId = flowStore.currentInstance?.id || route.params.id;
  return instanceId ? favoriteStore.isFavorited("flow", instanceId) : false;
});

// 切換關注狀態
const toggleFavorite = async () => {
  const instanceId = flowStore.currentInstance?.id || route.params.id;

  if (!instanceId) {
    ElMessage.warning("無法找到流程實例 ID");
    return;
  }

  if (!userStore.user?.id) {
    ElMessage.warning("請先登入");
    return;
  }

  try {
    if (isFavorited.value) {
      await favoriteStore.removeFromFavorite("flow", instanceId);
    } else {
      await favoriteStore.addToFavorite({
        type: "flow",
        resourceId: instanceId,
        name: flowStore.currentInstance?.template?.name || `流程 ${instanceId}`,
        path: `/flow-instances/${instanceId}`,
        createdBy: userStore.user.id,
      });
    }
  } catch (error) {
    console.error("切換關注狀態失敗:", error);
  }
};

// 處理模式變化
const handleModeChange = (val) => {
  console.log("模式變化", val);
  emit("update:modelValue", val);
};

// 處理返回
const handleBack = () => {
  // 檢查是否從專案詳情頁進入
  if (flowStore.fromProject && flowStore.projectId) {
    // 返回專案詳情頁
    router.push(`/projects/${flowStore.projectId}`);
  } else {
    // 返回流程實例列表
    router.push("/flow-instances");
  }
};

// 導航到報表頁面
const navigateToReport = () => {
  // 使用當前流程實例 ID 導航到報表詳情頁面
  const instanceId = flowStore.currentInstance?.id || route.params.id;
  if (instanceId) {
    router.push(`/report/${instanceId}`);
  } else {
    ElMessage.warning("無法找到流程實例 ID");
  }
};
</script>
