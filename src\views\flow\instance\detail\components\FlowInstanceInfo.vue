<template>
  <el-card class="mb-4 dark:bg-dark-mode dark:border-gray-700">
    <div class="flex justify-between items-center">
      <div class="flex-1">
        <h2 class="text-xl font-bold mb-2 dark:text-dark-mode">
          <template v-if="flowInstance?.template">
            {{ flowInstance.template.name }}
          </template>
          <template v-else>
            <span class="text-gray-500">範本已刪除</span>
          </template>
          <el-tag
            :type="getStatusTagType(flowInstance?.status)"
            class="ml-2">
            {{ getStatusLabel(flowInstance?.status) }}
          </el-tag>
        </h2>
        <div class="text-gray-500 dark:text-gray-400">
          <p>專案名稱：{{ flowInstance?.project?.name }}</p>
          <p>建立者：{{ flowInstance?.creator?.username }}</p>
          <p>建立時間：{{ formatTimestamp(flowInstance?.createdAt) }}</p>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <el-button
          type="primary"
          @click="handleViewFlow">
          <Workflow class="mr-1" />
          流程
        </el-button>
        <el-button
          v-if="flowInstance?.status === 'draft'"
          type="primary"
          @click="handleStart"
          :loading="loading">
          <Play class="mr-1" />
          啟動
        </el-button>
        <!-- 已移除停止按鈕，因為工作流設計為每個節點必須執行完成 -->
        <el-button
          v-if="flowInstance?.status === 'draft'"
          type="danger"
          @click="handleDelete"
          :loading="loading">
          <Trash2 class="mr-1" />
          刪除
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { formatTimestamp } from "@/utils/dateUtils";
import { Workflow, Play, StopCircle, Trash2 } from "lucide-vue-next";

const props = defineProps({
  flowInstance: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  statusOptions: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["view-flow", "start", "stop", "delete"]);

// 獲取狀態標籤類型
const getStatusTagType = (status) => {
  const option = props.statusOptions.find((opt) => opt.value === status);
  return option ? option.tagType : "info";
};

// 獲取狀態標籤文字
const getStatusLabel = (status) => {
  const option = props.statusOptions.find((opt) => opt.value === status);
  return option ? option.label : status;
};

// 處理查看流程
const handleViewFlow = () => {
  emit("view-flow");
};

// 處理啟動流程
const handleStart = () => {
  emit("start");
};

// 處理停止流程
const handleStop = () => {
  emit("stop");
};

// 處理刪除流程
const handleDelete = () => {
  emit("delete");
};
</script>
