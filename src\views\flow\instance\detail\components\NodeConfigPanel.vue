<template>
  <div
    v-if="selectedNode"
    class="node-config-panel dark:bg-dark-mode dark:border dark:border-gray-700">
    <div class="panel-header dark:border-gray-700">
      <h3 class="text-lg font-semibold dark:text-dark-mode">節點配置</h3>
      <button
        @click="$emit('close')"
        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
        <X :size="16" />
      </button>
    </div>

    <div class="panel-content">
      <div class="form-group">
        <label class="dark:text-dark-mode">節點類型</label>
        <select
          v-model="nodeData.type"
          class="form-select dark:bg-gray-700 dark:border-gray-600 dark:text-dark-mode">
          <option
            v-for="type in nodeTypes"
            :key="type.type"
            :value="type.type">
            {{ type.label }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="dark:text-dark-mode">節點名稱</label>
        <input
          v-model="nodeData.name"
          type="text"
          class="form-input dark:bg-gray-700 dark:border-gray-600 dark:text-dark-mode" />
      </div>

      <div class="form-group">
        <label class="dark:text-dark-mode">描述</label>
        <textarea
          v-model="nodeData.content"
          class="form-textarea dark:bg-gray-700 dark:border-gray-600 dark:text-dark-mode"></textarea>
      </div>

      <div
        class="form-group"
        v-if="nodeData.type === 'input'">
        <label class="dark:text-dark-mode">數據來源</label>
        <div class="flex items-center space-x-2">
          <button
            class="btn-secondary dark:bg-gray-700 dark:text-dark-mode dark:hover:bg-gray-600"
            @click="handleFileUpload">
            <Upload
              :size="16"
              class="mr-2" />
            上傳文件
          </button>
          <span
            v-if="nodeData.file"
            class="text-sm text-gray-600 dark:text-gray-400">
            {{ nodeData.file.name }}
          </span>
        </div>
      </div>

      <div
        class="form-group"
        v-if="nodeData.type === 'analysis'">
        <label class="dark:text-dark-mode">分析方法</label>
        <select
          v-model="nodeData.analysisMethod"
          class="form-select dark:bg-gray-700 dark:border-gray-600 dark:text-dark-mode">
          <option value="correlation">相關性分析</option>
          <option value="regression">回歸分析</option>
          <option value="classification">分類分析</option>
          <option value="clustering">聚類分析</option>
        </select>
      </div>

      <div
        class="form-group"
        v-if="nodeData.type === 'visualization'">
        <label class="dark:text-dark-mode">圖表類型</label>
        <select
          v-model="nodeData.chartType"
          class="form-select dark:bg-gray-700 dark:border-gray-600 dark:text-dark-mode">
          <option value="line">折線圖</option>
          <option value="bar">柱狀圖</option>
          <option value="scatter">散點圖</option>
          <option value="pie">餅圖</option>
        </select>
      </div>
    </div>

    <div class="panel-footer dark:border-gray-700">
      <button
        class="btn-primary dark:bg-blue-600 dark:hover:bg-blue-700"
        @click="handleSave">
        保存
      </button>
      <button
        class="btn-secondary dark:bg-gray-700 dark:text-dark-mode dark:hover:bg-gray-600"
        @click="$emit('close')">
        取消
      </button>
    </div>
  </div>
</template>

<script setup>
import { getFlowNodeDefinitions } from "@/api";

const props = defineProps({
  selectedNode: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(["update:node", "close"]);

const nodeData = ref({ ...props.selectedNode?.data });
const nodeTypes = ref([]);

// 在組件掛載時獲取節點類型定義
onMounted(async () => {
  try {
    const response = await getFlowNodeDefinitions();
    nodeTypes.value = Object.values(response.data);
  } catch (error) {
    console.error("獲取節點類型定義失敗：", error);
    ElMessage.error("獲取節點類型定義失敗");
  }
});

const handleSave = () => {
  emit("update:node", {
    ...props.selectedNode,
    data: nodeData.value,
  });
  emit("close");
};

const handleFileUpload = () => {
  // TODO: 實現文件上傳邏輯
};
</script>

<style scoped>
.node-config-panel {
  @apply bg-white rounded-lg shadow-lg p-4 w-80 flex flex-col;
}

.panel-header {
  @apply flex justify-between items-center mb-4 pb-2 border-b;
}

.panel-content {
  @apply flex-1 overflow-y-auto;
}

.panel-footer {
  @apply flex justify-end space-x-2 mt-4 pt-4 border-t;
}

.form-group {
  @apply mb-4;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500;
}

.form-textarea {
  @apply h-24 resize-none;
}

.btn-primary {
  @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors;
}

.btn-secondary {
  @apply px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors;
}

/* 深色模式樣式覆蓋 */
.dark .form-group label {
  @apply text-gray-300;
}

.dark .form-input,
.dark .form-select,
.dark .form-textarea {
  @apply bg-gray-700 border-gray-600 text-gray-300 focus:border-blue-400 focus:ring-blue-400;
}
</style>
