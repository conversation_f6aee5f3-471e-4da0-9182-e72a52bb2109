<template>
  <div>
    <!-- 頂部操作區域 -->
    <FlowInstanceHeader v-model="viewFlowMode" />

    <!-- 詳情視圖 -->
    <div
      v-if="viewFlowMode === 'list'"
      class="flow-instance-detail">
      <!-- 頂部資訊卡片 -->
      <FlowInstanceInfo
        :flow-instance="flowInstance"
        :loading="loading"
        :status-options="statusOptions"
        @view-flow="viewFlowMode = 'flow'"
        @start="handleStart"
        @stop="handleStop"
        @delete="handleDelete" />

      <!-- 內容區域 -->
      <FlowInstanceTabs :flow-instance="flowInstance" />
    </div>

    <!-- 流程圖視圖 -->
    <div v-if="viewFlowMode === 'flow'">
      <FlowInstanceDiagram
        v-if="flowInstance"
        :flowInstance="flowInstance" />
    </div>
  </div>
</template>

<script setup>
import {
  getFlowInstanceById,
  updateFlowInstance,
  deleteFlowInstance,
} from "@/api/modules/flow";
import { useFlowStore } from "@/stores/flowStore";
import { useFlowCache } from "@/composables/useFlowCache";

import FlowInstanceHeader from "./components/FlowInstanceHeader.vue";
import FlowInstanceInfo from "./components/FlowInstanceInfo.vue";
import FlowInstanceTabs from "./components/FlowInstanceTabs.vue";
import FlowInstanceDiagram from "./components/FlowInstanceDiagram.vue";

const flowStore = useFlowStore();
const viewFlowMode = ref("flow"); // 默認為流程模式

// 從 IndexDB 恢復 context 數據
const restoreContextFromCache = async (instanceId) => {
  try {
    console.log("restoreContextFromCache", "嘗試從 IndexDB 恢復 context 數據");
    const flowCache = useFlowCache(instanceId);
    console.log("restoreContextFromCache.flowCache", flowCache);
    const cachedContext = await flowCache.getNodeCache("context");
    console.log("restoreContextFromCache.cachedContext", cachedContext);

    if (cachedContext?.result && flowStore.currentInstance) {
      console.log(
        "restoreContextFromCache",
        "從 IndexDB 恢復的 context:",
        cachedContext.result
      );

      // 合併 API 載入的數據和緩存的 context
      const updatedInstance = {
        ...flowStore.currentInstance,
        context: {
          ...flowStore.currentInstance.context,
          ...cachedContext.result,
        },
      };

      flowStore.setCurrentInstance(updatedInstance);
      console.log("restoreContextFromCache", "context 數據恢復完成");
    } else {
      console.log("restoreContextFromCache", "IndexDB 中沒有找到 context 緩存");
    }
  } catch (error) {
    console.error("restoreContextFromCache", "恢復 context 數據失敗:", error);
  }
};

// 監聽視圖模式變化，確保麵包屑不會重置
watch(viewFlowMode, (newMode) => {
  // console.log("視圖模式變化", newMode, "flowInstance", flowInstance.value);
  // 如果已經載入了流程實例數據，則重新設置麵包屑
  if (flowInstance.value) {
    updateBreadcrumb(false); // 傳入 false 表示不需要重新載入專案資訊
  }
});

// 更新麵包屑的輔助函數
const updateBreadcrumb = (shouldLoadProject = true) => {
  // 使用 setBreadcrumbInstance 設置麵包屑使用的流程實例
  if (flowInstance.value) {
    // 檢查是否從專案詳情頁進入
    const fromProject = route.query && route.query.from === "project";

    // 設置麵包屑使用的流程實例
    // 如果不需要重新載入專案資訊，則傳入 noLoadProject 參數
    flowStore.setBreadcrumbInstance(flowInstance.value, {
      fromProject,
      noLoadProject: !shouldLoadProject,
    });
  }
};

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const flowInstance = ref(null);

// 狀態選項
const statusOptions = [
  { value: "draft", label: "草稿", tagType: "info" },
  { value: "running", label: "執行中", tagType: "warning" },
  { value: "completed", label: "已完成", tagType: "success" },
  { value: "failed", label: "失敗", tagType: "danger" },
];

// 載入實例數據
const loadFlowInstance = async () => {
  try {
    loading.value = true;
    // console.log("載入實例數據", route.params.id);
    const response = await getFlowInstanceById(route.params.id);
    flowInstance.value = response.data;
    console.log("載入實例數據成功", flowInstance.value);

    // 設置 flowStore 的 currentInstance
    flowStore.setCurrentInstance(flowInstance.value);

    // 嘗試從 IndexDB 恢復 context 數據
    await restoreContextFromCache(flowInstance.value.id);

    // 設置麵包屑
    if (flowInstance.value) {
      // 確保無論當前視圖模式是什麼，都正確設置麵包屑
      updateBreadcrumb();
    }
  } catch (error) {
    console.error("載入數據失敗:", error);
    ElMessage.error("載入數據失敗");
  } finally {
    loading.value = false;
  }
};

// 啟動流程實例
const handleStart = async () => {
  try {
    await ElMessageBox.confirm("確定要啟動該流程實例嗎？", "提示", {
      type: "warning",
    });

    loading.value = true;
    await updateFlowInstance(flowInstance.value.id, { status: "running" });
    ElMessage.success("啟動成功");
    loadFlowInstance();
  } catch (error) {
    if (error !== "cancel") {
      console.error("啟動失敗:", error);
      ElMessage.error("啟動失敗");
    }
  } finally {
    loading.value = false;
  }
};

// 已移除停止功能，因為我們的工作流設計是每個節點必須執行完成

// 刪除流程實例
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      "確定要刪除該流程實例嗎？此操作不可恢復！",
      "警告",
      {
        type: "error",
      }
    );

    loading.value = true;
    await deleteFlowInstance(flowInstance.value.id);
    ElMessage.success("刪除成功");
    router.push("/flow-instances");
  } catch (error) {
    if (error !== "cancel") {
      console.error("刪除失敗:", error);
      ElMessage.error("刪除失敗");
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadFlowInstance().then(() => {
    // 確保在數據載入完成後，無論當前視圖模式是什麼，都正確設置麵包屑
    if (flowInstance.value) {
      updateBreadcrumb();
    }
  });
});
</script>

<style scoped>
.flow-instance-detail {
  padding: 20px;
  @apply dark:bg-dark-mode;
}

.flow-container {
  height: 600px;
  @apply dark:bg-dark-mode;
}
</style>
