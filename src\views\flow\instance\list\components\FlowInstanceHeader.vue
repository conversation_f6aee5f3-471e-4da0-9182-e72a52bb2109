<template>
  <Teleport
    to="#header-actions"
    defer>
    <el-select
      v-model="queryParams.projectId"
      class="!w-64"
      placeholder="篩選專案"
      clearable
      :fit-input-width="true"
      @change="handleSearch">
      <el-option
        v-for="project in projects"
        :key="project.id"
        :label="project.name"
        :value="project.id" />
    </el-select>
    <el-select
      v-model="queryParams.status"
      style="width: 102px"
      placeholder="篩選狀態"
      clearable
      @change="handleSearch">
      <el-option
        v-for="status in statusOptions"
        :key="status.value"
        :label="status.label"
        :value="status.value">
        <el-tag
          :type="status.tagType"
          size="small"
          >{{ status.label }}</el-tag
        >
      </el-option>
    </el-select>
    <div class="flex items-center space-x-4">
      <!-- 顯示詳細節點切換 -->
      <el-switch
        v-model="showDetailedNodesValue"
        active-text="詳細節點"
        @change="$emit('show-detailed-nodes-change', showDetailedNodesValue)" />

      <!-- 只顯示關注的流程 -->
      <el-switch
        v-model="showFavoriteOnly"
        active-text="篩選關注"
        @change="$emit('show-favorite-only-change', showFavoriteOnly)" />
    </div>
    <el-button
      type="info"
      plain
      @click="handleRefresh"
      :loading="loading"
      title="重新整理">
      <RotateCw
        class="mr-1"
        :size="14" />
      重整
    </el-button>
    <el-button
      type="primary"
      @click="handleCreate">
      <Plus class="mr-1" /> 新建流程實例
    </el-button>
    <el-button
      type="danger"
      :disabled="selectedInstances.length === 0"
      @click="handleBatchDelete"
      :title="
        isAdmin
          ? '管理員可以強制刪除任何狀態的流程實例'
          : '只能刪除草稿或失敗狀態的流程實例'
      ">
      <Trash2 class="mr-1" /> 批次刪除 ({{ selectedInstances.length }})
    </el-button>
  </Teleport>
</template>

<script setup>
//import { RotateCw, Plus, Trash2 } from "lucide-vue-next";

const props = defineProps({
  queryParams: {
    type: Object,
    required: true,
  },
  projects: {
    type: Array,
    required: true,
  },
  statusOptions: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  selectedInstances: {
    type: Array,
    default: () => [],
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  showDetailedNodes: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "search",
  "refresh",
  "create",
  "batch-delete",
  "show-detailed-nodes-change",
  "show-favorite-only-change",
]);

const showFavoriteOnly = ref(false);

// 處理顯示詳細節點變更
const handleShowDetailedNodesChange = (value) => {
  emit("show-detailed-nodes-change", value);
};

// 搜索
const handleSearch = () => {
  emit("search");
};

// 刷新
const handleRefresh = () => {
  emit("refresh");
};

// 創建流程實例
const handleCreate = () => {
  emit("create");
};

// 批次刪除
const handleBatchDelete = () => {
  emit("batch-delete");
};
</script>
