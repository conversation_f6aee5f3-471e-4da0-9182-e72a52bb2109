<template>
  <div>
    <!-- <div class="mb-4 flex items-center">
      <el-switch
        v-model="showDetailedNodes"
        active-text="顯示詳細節點"
        @change="handleShowDetailedNodesChange" />
    </div> -->
    <el-table
      :data="loading ? [] : instances"
      v-loading="false"
      @selection-change="handleSelectionChange"
      row-key="id">
      <el-table-column
        type="selection"
        width="55"
        :selectable="
          (row) => isAdmin || row.status === 'draft' || row.status === 'failed'
        " />
      <el-table-column
        type="index"
        label="序號"
        width="80" />
      <el-table-column
        prop="project.name"
        label="專案名稱"
        min-width="100">
        <template #default="{ row }">
          <template v-if="loading">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </template>
          <template v-else>
            {{ row.project.name }}
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="template.name"
        label="模板名稱"
        min-width="100">
        <template #default="{ row }">
          <template v-if="loading">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </template>
          <template v-else-if="row.template">
            {{ row.template.name }}
          </template>
          <template v-else>
            <span class="text-gray-500">範本已刪除</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showDetailedNodes"
        prop="nodes"
        label="節點"
        min-width="60"
        align="center">
        <template #default="{ row }">
          <template v-if="loading">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </template>
          <template v-else>
            <div v-if="showDetailedNodes">
              <el-popover
                placement="right"
                :width="400"
                trigger="hover">
                <template #reference>
                  <div class="cursor-pointer text-blue-500">
                    {{ Array.isArray(row.nodes) ? row.nodes.length : 0 }} 個節點
                    (懸停查看詳細)
                  </div>
                </template>
                <div class="max-h-[500px] overflow-auto">
                  <VueJsonPretty
                    :data="row.nodes"
                    :deep="3"
                    :show-double-quotes="true"
                    :show-length="true"
                    :show-line="true"
                    :collapsed-level="2" />
                </div>
              </el-popover>
            </div>
            <div v-else>
              <el-tag
                size="small"
                type="info">
                {{ Array.isArray(row.nodes) ? row.nodes.length : 0 }} 個節點
              </el-tag>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="狀態"
        width="150"
        align="center">
        <template #default="{ row }">
          <template v-if="loading">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </template>
          <template v-else>
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="建立者"
        width="200"
        align="center">
        <template #default="{ row }">
          <template v-if="loading">
            <div class="flex items-center justify-center">
              <el-skeleton-item
                variant="circle"
                style="width: 24px; height: 24px" />
              <el-skeleton-item
                variant="text"
                style="width: 60px; margin-left: 8px" />
            </div>
          </template>
          <template v-else>
            <div class="flex items-center justify-center">
              <el-avatar
                :size="24"
                :src="`http://localhost:3001/uploads/avatars/${row.creator.avatar}`">
                {{ row.creator.username.charAt(0) }}
              </el-avatar>
              <span class="ml-2">{{ row.creator.username }}</span>
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        label="建立時間"
        width="180">
        <template #default="{ row }">
          <template v-if="loading">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </template>
          <template v-else>
            {{ formatTimestamp(row.createdAt) }}
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="300"
        fixed="right">
        <template #default="{ row }">
          <template v-if="loading">
            <div class="flex space-x-2">
              <el-skeleton-item
                variant="button"
                style="width: 60px; height: 32px" />
              <el-skeleton-item
                variant="button"
                style="width: 60px; height: 32px" />
              <el-skeleton-item
                variant="button"
                style="width: 60px; height: 32px" />
            </div>
          </template>
          <template v-else>
            <div class="flex items-center space-x-2">
              <el-button
                v-if="row.status === 'draft'"
                type="primary"
                @click="handleStart(row)"
                :loading="row.loading">
                <Play
                  class="mr-1"
                  :size="14" />
                啟動
              </el-button>
              <el-button
                v-if="row.status === 'running'"
                type="danger"
                @click="handleStop(row)"
                :loading="row.loading">
                <StopCircle
                  class="mr-1"
                  :size="14" />
                停止
              </el-button>
              <!-- 關注按鈕 -->
              <Star
                :size="20"
                :class="[
                  isFlowFavorited(row)
                    ? 'text-yellow-400 dark:text-yellow-400 fill-yellow-400'
                    : 'text-gray-400 dark:text-gray-400',
                  'cursor-pointer hover:text-yellow-500 dark:hover:text-yellow-500',
                ]"
                @click.stop="toggleFavorite(row)" />
              <el-button
                v-if="row.status !== 'deleted'"
                type="primary"
                size="small"
                @click="handleView(row)">
                <Eye
                  class="mr-1"
                  :size="14" />
                查看
              </el-button>
              <el-button
                v-if="row.status !== 'deleted'"
                type="danger"
                size="small"
                @click="handleDelete(row)"
                :loading="row.loading">
                <Trash2
                  class="mr-1"
                  :size="14" />
                刪除
              </el-button>
              <el-button
                v-if="row.status === 'deleted'"
                type="success"
                size="small"
                @click="handleRestore(row)"
                :loading="row.loading">
                <RefreshCw
                  class="mr-1"
                  :size="14" />
                恢復
              </el-button>
            </div>
          </template>
        </template>
      </el-table-column>

      <!-- Skeleton 行 -->
      <template #empty>
        <div v-if="loading">
          <div
            v-for="i in 5"
            :key="i"
            class="skeleton-row">
            <div class="flex items-center w-full py-4 px-2">
              <el-skeleton-item
                variant="circle"
                style="width: 20px; height: 20px; margin-right: 12px" />
              <el-skeleton-item
                variant="text"
                style="width: 80px; margin-right: 12px" />
              <el-skeleton-item
                variant="text"
                style="width: 100px; margin-right: 12px" />
              <el-skeleton-item
                variant="text"
                style="width: 100px; margin-right: 12px" />
              <el-skeleton-item
                variant="text"
                style="width: 80px; margin-right: 12px" />
              <el-skeleton-item
                variant="p"
                style="width: 120px; margin-right: 12px" />
              <el-skeleton-item
                variant="text"
                style="width: 120px; margin-right: 12px" />
              <div class="flex space-x-2 ml-auto">
                <el-skeleton-item
                  variant="button"
                  style="width: 60px; height: 32px" />
                <el-skeleton-item
                  variant="button"
                  style="width: 60px; height: 32px" />
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          description="暫無數據" />
      </template>
    </el-table>
  </div>
</template>

<script setup>
import { formatTimestamp } from "@/utils/dateUtils";
import {
  Play,
  StopCircle,
  Eye,
  Trash2,
  Star,
  RefreshCw,
} from "lucide-vue-next";
import VueJsonPretty from "vue-json-pretty";
import "vue-json-pretty/lib/styles.css";
import { watch } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();

const props = defineProps({
  instances: {
    type: Array,
    required: true,
  },
  statusOptions: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  showDetailedNodes: {
    type: Boolean,
    default: false,
  },
  favoriteStore: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  "selection-change",
  "start",
  "stop",
  "view",
  "delete",
  "restore",
  "show-detailed-nodes-change",
]);

// 監視資料變化，用於調試
watch(
  () => props.instances,
  (newVal) => {
    console.log("實例資料更新:", newVal);
    if (newVal && newVal.length > 0) {
      console.log("第一個實例的節點:", newVal[0].nodes);
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.showDetailedNodes,
  (newVal) => {
    console.log("顯示詳細節點狀態變更:", newVal);
  },
  { immediate: true }
);

// 處理顯示詳細節點變更
const handleShowDetailedNodesChange = (value) => {
  emit("show-detailed-nodes-change", value);
};

// 獲取狀態標籤類型
const getStatusTagType = (status) => {
  const option = props.statusOptions.find((opt) => opt.value === status);
  return option ? option.tagType : "info";
};

// 獲取狀態標籤文字
const getStatusLabel = (status) => {
  const option = props.statusOptions.find((opt) => opt.value === status);
  return option ? option.label : status;
};

// 獲取節點狀態標籤類型
const getNodeStatusTagType = (status) => {
  switch (status) {
    case "completed":
      return "success";
    case "running":
      return "primary";
    case "failed":
      return "danger";
    case "pending":
      return "warning";
    default:
      return "info";
  }
};

// 處理選擇變更
const handleSelectionChange = (selection) => {
  emit("selection-change", selection);
};

// 啟動流程實例
const handleStart = (row) => {
  emit("start", row);
};

// 停止流程實例
const handleStop = (row) => {
  emit("stop", row);
};

// 查看流程實例
const handleView = (row) => {
  emit("view", row);
};

// 刪除流程實例
const handleDelete = (row) => {
  emit("delete", row);
};

// 恢復已刪除的流程實例
const handleRestore = (row) => {
  emit("restore", row);
};

// 判斷流程是否已關注
const isFlowFavorited = (flow) => {
  return props.favoriteStore.isFavorited("flow", flow.id);
};

// 切換關注狀態
const toggleFavorite = async (flow) => {
  if (!userStore.user?.id) {
    ElMessage.warning("請先登入");
    return;
  }

  if (isFlowFavorited(flow)) {
    await props.favoriteStore.removeFromFavorite("flow", flow.id);
  } else {
    await props.favoriteStore.addToFavorite({
      type: "flow",
      resourceId: flow.id,
      name: flow.template?.name || flow.id,
      path: `/flow-instances/${flow.id}`,
      createdBy: userStore.user.id,
    });
  }
};
</script>

<style scoped>
.el-tag {
  width: 100%;
  text-align: center;
}

.skeleton-row {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.skeleton-row:last-child {
  border-bottom: none;
}
</style>
