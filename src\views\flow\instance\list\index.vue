<template>
  <div class="p-0">
    <!-- 頂部搜索和操作區域 -->
    <FlowInstanceHeader
      :query-params="queryParams"
      :projects="projects"
      :status-options="statusOptions"
      :loading="loading"
      :selected-instances="selectedInstances"
      :is-admin="userStore.isAdmin"
      :show-detailed-nodes="showDetailedNodes"
      @search="handleSearch"
      @refresh="handleRefresh"
      @create="handleCreate"
      @batch-delete="handleBatchDelete"
      @show-detailed-nodes-change="handleShowDetailedNodesChange"
      @show-favorite-only-change="handleShowFavoriteOnlyChange" />

    <!-- 實例表格 -->
    <FlowInstanceTable
      :instances="filteredInstances"
      :status-options="statusOptions"
      :loading="loading"
      :is-admin="userStore.isAdmin"
      :show-detailed-nodes="showDetailedNodes"
      :favorite-store="favoriteStore"
      @selection-change="handleSelectionChange"
      @start="handleStart"
      @stop="handleStop"
      @view="handleView"
      @delete="handleDelete"
      @restore="handleRestore" />

    <!-- 創建流程實例對話框 -->
    <FlowInstanceCreateDialog
      v-model:visible="dialogVisible"
      :projects="projects"
      :templates="templates"
      :submitting="submitting"
      @submit="handleSubmit"
      @cancel="dialogVisible = false" />
  </div>
</template>

<script setup>
import {
  getFlowInstances,
  createFlowInstance,
  updateFlowInstance,
  deleteFlowInstance,
  restoreFlowInstance,
  getFlowTemplates,
} from "@/api/modules/flow";
import { getAllProjects } from "@/api/modules/project";
import { useUserStore } from "@/stores/user";
import { useFavoriteStore } from "@/stores/favorite";
import FlowInstanceHeader from "./components/FlowInstanceHeader.vue";
import FlowInstanceTable from "./components/FlowInstanceTable.vue";
import FlowInstanceCreateDialog from "./components/FlowInstanceCreateDialog.vue";

const router = useRouter();
const userStore = useUserStore();
const favoriteStore = useFavoriteStore();
const loading = ref(false);
const instances = ref([]);
const projects = ref([]);
const templates = ref([]);
const dialogVisible = ref(false);
const submitting = ref(false);
const selectedInstances = ref([]);
const showDetailedNodes = ref(false);
const showFavoriteOnly = ref(false);

// 查詢參數
const queryParams = ref({
  projectId: "",
  status: "",
  simplified: "true", // 默認使用簡化模式
});

// 表單數據
const formRef = ref(null);
const form = ref({
  projectId: "",
  templateId: "",
});

// 表單驗證規則
const rules = {
  projectId: [{ required: true, message: "請選擇專案", trigger: "change" }],
  templateId: [
    { required: true, message: "請選擇流程模板", trigger: "change" },
  ],
};

// 狀態選項
const statusOptions = [
  { value: "draft", label: "草稿", tagType: "danger" },
  { value: "active", label: "執行中", tagType: "warning" },
  { value: "completed", label: "已完成", tagType: "success" },
  { value: "cancelled", label: "已取消", tagType: "info" },
  { value: "deleted", label: "已刪除", tagType: "info" },
];

// 處理選擇變更
const handleSelectionChange = (selection) => {
  selectedInstances.value = selection;
};

// 處理批次刪除
const handleBatchDelete = async () => {
  if (selectedInstances.value.length === 0) return;

  // 如果是管理員，可以強制刪除任何狀態的實例
  const canForceDelete = userStore.isAdmin;

  // 過濾出可刪除的實例（草稿或失敗狀態，或管理員強制刪除）
  const deletableInstances = canForceDelete
    ? selectedInstances.value
    : selectedInstances.value.filter(
        (instance) =>
          instance.status === "draft" || instance.status === "failed"
      );

  // 檢查是否有不可刪除的實例
  if (deletableInstances.length < selectedInstances.value.length) {
    const nonDeletableCount =
      selectedInstances.value.length - deletableInstances.length;
    ElMessage.warning(
      `已選中 ${nonDeletableCount} 個非草稿或失敗狀態的實例，這些實例無法刪除`
    );

    if (deletableInstances.length === 0) {
      return;
    }
  }

  try {
    const confirmMessage =
      canForceDelete &&
      deletableInstances.some(
        (instance) => !["draft", "failed"].includes(instance.status)
      )
        ? `您正在以管理員身份強制刪除 ${deletableInstances.length} 個流程實例，其中包含非草稿或失敗狀態的實例。此操作不可逆且可能影響系統運行。`
        : `確定要刪除選中的 ${deletableInstances.length} 個流程實例嗎？此操作不可逆。`;

    await ElMessageBox.confirm(confirmMessage, "批次刪除確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "warning",
    });

    loading.value = true;

    console.log("批次刪除流程實例:", {
      總數: selectedInstances.value.length,
      可刪除數: deletableInstances.length,
      isAdmin: userStore.isAdmin,
      canForceDelete,
    });

    const deletePromises = deletableInstances.map((instance) => {
      // 確定是否需要強制刪除
      const needForceDelete =
        canForceDelete && !["draft", "failed"].includes(instance.status);
      console.log(`刪除實例 ${instance.id}:`, {
        status: instance.status,
        needForceDelete,
      });
      return deleteFlowInstance(instance.id, needForceDelete);
    });

    const results = await Promise.all(deletePromises);
    console.log("批次刪除結果:", results);
    ElMessage.success(`成功刪除 ${deletableInstances.length} 個流程實例`);
    selectedInstances.value = [];
    loadData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("批次刪除流程實例失敗", error);
      ElMessage.error(`批次刪除流程實例失敗: ${error.message || error}`);
    }
  } finally {
    loading.value = false;
  }
};

// 載入數據
const loadData = async () => {
  try {
    console.log("loadData", queryParams.value);
    loading.value = true;
    const [instancesRes, projectsRes, templatesRes] = await Promise.all([
      getFlowInstances(queryParams.value),
      getAllProjects(),
      getFlowTemplates(),
    ]);
    console.log(instancesRes);
    instances.value = instancesRes.data;
    projects.value = projectsRes.data;
    templates.value = templatesRes.data;
  } catch (error) {
    console.error("載入數據失敗:", error);
    ElMessage.error("載入數據失敗");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  loadData();
};

// 刷新
const handleRefresh = () => {
  loadData();
};

// 創建流程實例
const handleCreate = () => {
  form.value = {
    projectId: "",
    templateId: "",
  };
  dialogVisible.value = true;
};

// 提交表單
const handleSubmit = async (formData) => {
  try {
    submitting.value = true;
    await createFlowInstance(formData);
    ElMessage.success("創建成功");
    dialogVisible.value = false;
    loadData();
  } catch (error) {
    console.error("創建失敗:", error);
    ElMessage.error("創建失敗");
  } finally {
    submitting.value = false;
  }
};

// 啟動流程實例
const handleStart = async (row) => {
  try {
    row.loading = true;
    // 改用 updateFlowInstance 將狀態更新為 running
    await updateFlowInstance(row.id, { status: "running" });
    ElMessage.success("啟動成功");
    loadData();
  } catch (error) {
    console.error("啟動失敗:", error);
    ElMessage.error("啟動失敗");
  } finally {
    row.loading = false;
  }
};

// 已移除停止功能，因為我們的工作流設計是每個節點必須執行完成

// 查看流程實例
const handleView = (row) => {
  router.push(`/flow-instances/${row.id}`);
};

// 刪除流程實例
const handleDelete = async (row) => {
  try {
    // 檢查是否為管理員以及實例狀態
    const canForceDelete = userStore.isAdmin;
    const isDeletableStatus = row.status === "draft" || row.status === "failed";

    // 如果不是可刪除狀態且不是管理員，則顯示錯誤訊息
    if (!isDeletableStatus && !canForceDelete) {
      ElMessage.warning("只有草稿和失敗狀態的流程實例可以刪除");
      return;
    }

    // 根據情況顯示不同的確認訊息
    const confirmMessage =
      !isDeletableStatus && canForceDelete
        ? "您正在以管理員身份強制刪除非草稿或失敗狀態的流程實例。此操作不可逆且可能影響系統運行，確定要繼續嗎？"
        : "確定要刪除該流程實例嗎？";

    await ElMessageBox.confirm(confirmMessage, "提示", {
      type: "warning",
    });

    row.loading = true;

    // 確定是否需要強制刪除
    const needForceDelete = canForceDelete && !isDeletableStatus;
    console.log("刪除流程實例:", {
      id: row.id,
      status: row.status,
      isAdmin: userStore.isAdmin,
      canForceDelete,
      isDeletableStatus,
      needForceDelete,
    });

    const result = await deleteFlowInstance(row.id, needForceDelete);
    console.log("刪除結果:", result);
    ElMessage.success("刪除成功");
    loadData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("刪除失敗:", error);
      console.error("刪除失敗詳情:", error.response?.data || error);
      ElMessage.error(
        `刪除失敗: ${error.response?.data?.message || error.message || error}`
      );
    }
  } finally {
    row.loading = false;
  }
};

// 恢復已刪除的流程實例
const handleRestore = async (row) => {
  if (!row) return;
  row.loading = true;
  try {
    await ElMessageBox.confirm("確定要恢復該流程實例嗎？", "恢復確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "info",
    });

    console.log("恢復流程實例:", {
      id: row.id,
      status: row.status,
    });

    const result = await restoreFlowInstance(row.id);
    console.log("恢復結果:", result);
    ElMessage.success("恢復成功");
    loadData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("恢復失敗:", error);
      ElMessage.error(
        `恢復失敗: ${error.response?.data?.message || error.message || error}`
      );
    }
  } finally {
    row.loading = false;
  }
};

// 處理顯示詳細節點變更
const handleShowDetailedNodesChange = (value) => {
  showDetailedNodes.value = value;
  queryParams.value.simplified = value ? "false" : "true";
  loadData();
};

// 使用 favorite store
// 過濾後的流程實例
const filteredInstances = computed(() => {
  let filtered = instances.value;

  if (showFavoriteOnly.value) {
    filtered = filtered.filter((instance) =>
      favoriteStore.isFavorited("flow", instance.id)
    );
  }

  return filtered;
});

// 處理只顯示關注切換
const handleShowFavoriteOnlyChange = (value) => {
  showFavoriteOnly.value = value;
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.flow-instance-list {
  padding: 20px;
}

.el-tag {
  width: 100%;
  text-align: center;
}
</style>
