<template>
  <el-dialog
    :title="isEdit ? '編輯節點定義' : '新增節點定義'"
    v-model="dialogVisible"
    top="5vh"
    draggable
    width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="mt-4">
      <el-form-item
        label="組件路徑"
        prop="componentPath">
        <div class="flex items-center space-x-1">
          <div
            class="px-1 bg-gray-100 dark:bg-zinc-800 dark:text-gray-400 rounded-sm text-gray-500 whitespace-nowrap">
            @/components/flow-nodes/
          </div>

          <el-select
            v-model="formData.componentPath"
            class="!w-[100px]"
            placeholder="請選擇組件路徑"
            filterable>
            <el-option
              label="base(管理員檢測用，勿選!)"
              value="base" />
            <el-option
              label="business"
              value="business" />
          </el-select>

          <el-autocomplete
            v-model="formData.componentName"
            :fetch-suggestions="queryComponentSearch"
            placeholder="組件名稱，例如：TopDefectsNode"
            clearable
            style="width: 340px"
            @select="handleComponentNameSelect">
            <template #default="{ item }">
              <div class="flex flex-col">
                <span>{{ item.value }}</span>
                <span class="text-xs text-gray-500">{{ item.path }}</span>
              </div>
            </template>
          </el-autocomplete>
        </div>
        <div
          v-if="fullComponentPath"
          class="text-white bg-blue-700 mt-1 p-1 rounded-md flex items-center justify-between w-full">
          <div>
            完整組件路徑:
            {{ fullComponentPath }}
          </div>
          <div class="flex gap-2">
            <el-button
              type="info"
              plain
              size="small"
              @click="handleGetComponentProps"
              :loading="loadingProps">
              獲取組件屬性
            </el-button>
            <el-button
              type="primary"
              plain
              size="small"
              @click="handlePreview">
              預覽節點
            </el-button>
          </div>
        </div>
        <div class="form-item-tip w-full mt-2">
          <el-alert
            type="warning"
            show-icon
            :closable="false"
            class="mt-0">
            組件的相對路徑，系統會自動添加 @/components/flow-nodes/ 前綴
            <br />※※系統裡要有對應的 Vue 組件※※</el-alert
          >
        </div>
      </el-form-item>

      <!-- 組件屬性顯示區域 -->
      <div
        v-if="componentProps && Object.keys(componentProps).length > 0"
        class="mb-4">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item name="props">
            <template #title>
              <div class="flex items-center">
                <span class="text-base font-medium">組件屬性</span>
                <el-tag
                  size="small"
                  type="info"
                  class="ml-2"
                  >{{ propsTableData.length }} 個屬性</el-tag
                >
              </div>
            </template>
            <el-table
              :data="propsTableData"
              border
              style="width: 100%"
              size="small">
              <el-table-column
                prop="name"
                label="屬性名稱"
                width="180" />
              <el-table-column
                prop="type"
                label="類型"
                width="120"
                style="margin-top: 4px" />
              <el-table-column
                prop="required"
                label="必填"
                width="80">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.required ? 'danger' : 'info'"
                    size="small">
                    {{ scope.row.required ? "是" : "否" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="default"
                label="預設值"
                width="120" />
              <el-table-column
                prop="description"
                label="描述" />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-form-item
        label="名稱"
        prop="name">
        <el-input
          v-model="formData.name"
          placeholder="請輸入名稱" />
        <div class="form-item-tip mt-1">
          <el-tag
            size="small"
            :type="propsLoaded ? 'success' : 'info'">
            {{
              propsLoaded ? "自動從組件的 title 屬性獲取" : "自動從組件名稱生成"
            }}
          </el-tag>
          <el-tag
            size="small"
            type="danger"
            >**有必要時再手動修改**</el-tag
          >
        </div>
      </el-form-item>
      <el-form-item
        label="分類"
        prop="category">
        <el-select
          v-model="formData.category"
          placeholder="請選擇分類"
          style="width: 100%">
          <el-option
            label="資料輸入"
            value="data-input" />
          <el-option
            label="資料處理"
            value="data-process" />
          <el-option
            label="資料輸出"
            value="data-output" />
        </el-select>
        <div class="form-item-tip w-full mt-2">
          <el-alert
            type="info"
            show-icon
            :closable="false"
            class="mt-0">
            系統會根據組件名稱自動選擇分類：
            <br />- 含有「input」的組件 → 資料輸入 <br />- 含有「process」的組件
            → 資料處理 <br />- 其他組件 → 資料輸出
          </el-alert>
        </div>
      </el-form-item>
      <el-form-item
        label="描述"
        prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="請輸入描述" />
        <div class="form-item-tip mt-1">
          <el-tag
            size="small"
            :type="propsLoaded ? 'success' : 'info'">
            {{
              propsLoaded
                ? "自動從組件的 description 屬性獲取"
                : "自動從組件屬性獲取"
            }}
          </el-tag>
          <el-tag
            size="small"
            type="danger"
            >**有必要時再手動修改**</el-tag
          >
        </div>
      </el-form-item>
      <el-form-item prop="helpContent">
        <template #label>
          <span style="line-height: 32px; margin-top: 4px">詳細說明</span>
        </template>
        <div class="mb-2 w-full">
          <el-tabs v-model="helpContentTab">
            <el-tab-pane
              label="編輯"
              name="edit">
              <div
                class="markdown-toolbar flex items-center space-x-2 mb-2 p-1 border-b">
                <el-tooltip
                  content="標題"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('header')">
                    <span class="font-bold">H</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="粗體"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('bold')">
                    <span class="font-bold">B</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="斜體"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('italic')">
                    <span class="italic">I</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="程式碼區塊"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('code')">
                    <span class="font-mono">{}</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="無序列表"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('list')">
                    <span>• •</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="有序列表"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('orderedList')">
                    <span>1. 2.</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="引用"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('quote')">
                    <span>"</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="連結"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('link')">
                    <span>🔗</span>
                  </el-button>
                </el-tooltip>
              </div>
              <div class="edit-textarea-container h-[261px]">
                <el-input
                  v-model="formData.helpContent"
                  type="textarea"
                  :rows="10"
                  class="markdown-editor h-full"
                  style="height: 100%"
                  :autosize="false"
                  resize="none"
                  ref="helpContentTextarea"
                  placeholder="請輸入詳細說明內容（支援 Markdown 格式）" />
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="預覽"
              name="preview">
              <div
                class="markdown-preview p-3 border rounded h-[300px] overflow-auto">
                <div v-html="renderedHelpContent"></div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="form-item-tip mt-1">
          <el-tag
            size="small"
            type="info">
            詳細的節點使用說明，將顯示在節點編輯面板的幫助區域
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item
        label="圖示"
        prop="icon">
        <IconPicker
          :modelValue="formData.icon"
          @update:modelValue="formData.icon = $event" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          >確定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import IconPicker from "@/components/IconPicker.vue";
import { useFlowNodeComponents } from "@/composables/useFlowNodeComponents";
import { renderMarkdown } from "@/utils/markdown";
import { nextTick } from "vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    required: true,
  },
  rules: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue", "submit", "preview"]);

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const formRef = ref(null);
const componentProps = ref(null);
const loadingProps = ref(false);
const propsLoaded = ref(false);
const activeCollapse = ref(["props"]); // 預設展開組件屬性
const helpContentTab = ref("edit"); // 詳細說明標籤頁
const helpContentTextarea = ref(null); // 詳細說明文本區域的引用

// 使用 Flow Components composable
const {
  flowNodeComponents,
  getComponentName,
  loadFlowNodeComponents,
  getComponentProps,
} = useFlowNodeComponents();

// 將 components 轉換為下拉選單選項
const componentOptions = computed(() => {
  if (
    !flowNodeComponents.value ||
    Object.keys(flowNodeComponents.value).length === 0
  ) {
    return [];
  }

  return Object.entries(flowNodeComponents.value).map(([path]) => {
    // 移除前面的 '@' 符號
    const cleanPath = path.startsWith(".") ? path.slice(2) : path;

    return {
      value: path,
      label: getComponentName(path),
      path: cleanPath,
    };
  });
});

// 將組件屬性轉換為表格數據
const propsTableData = computed(() => {
  if (!componentProps.value) return [];

  return Object.entries(componentProps.value).map(([name, config]) => ({
    name,
    type: config.type || "Any",
    required: config.required,
    default:
      config.default !== undefined ? JSON.stringify(config.default) : "-",
    description: config.description || "-",
  }));
});

// 計算屬性
const fullComponentPath = computed(() => {
  if (!props.formData.componentPath || !props.formData.componentName) return "";
  // 確保組件名稱包含 .vue 副檔名
  const componentName = props.formData.componentName.endsWith(".vue")
    ? props.formData.componentName
    : `${props.formData.componentName}.vue`;
  return `@/components/flow-nodes/${props.formData.componentPath}/${componentName}`;
});

// 渲染 Markdown 內容
const renderedHelpContent = computed(() => {
  return renderMarkdown(props.formData.helpContent || "");
});

// 監聽表單數據變化
watch(
  () => props.formData,
  (newVal) => {
    Object.assign(props.formData, newVal);
  },
  { deep: true }
);

// 根據組件名稱自動選擇分類和填入名稱
const autoSelectCategory = (componentName) => {
  if (!componentName) return;

  const name = componentName.toLowerCase();

  // 自動選擇分類
  if (name.includes("input")) {
    props.formData.category = "data-input";
  } else if (name.includes("process")) {
    props.formData.category = "data-process";
  } else {
    props.formData.category = "data-output";
  }

  // 如果還沒有從組件屬性中獲取名稱和描述，則使用組件名稱自動填入
  if (!propsLoaded.value) {
    // 自動填入名稱，去掉「input」或「process」等關鍵字
    let displayName = componentName
      .replace(/input/i, "")
      .replace(/process/i, "")
      .replace(/node/i, "")
      .trim();

    // 確保首字母大寫
    displayName = displayName.charAt(0).toUpperCase() + displayName.slice(1);

    // 如果名稱為空（例如組件名稱只有 "InputNode"），則使用原始組件名稱
    if (!displayName) {
      displayName = componentName;
    }

    // 只有在名稱字段為空時才自動填入，避免覆蓋用戶已輸入的內容
    if (!props.formData.name) {
      props.formData.name = displayName;
    }
  }
};

// 監聽組件名稱變化，自動選擇分類
watch(
  () => props.formData.componentName,
  (newVal) => {
    if (newVal) {
      autoSelectCategory(newVal);
      // 清空之前的組件屬性
      componentProps.value = null;
      propsLoaded.value = false;
    }
  }
);

// 處理組件名稱選擇
const handleComponentNameSelect = (item) => {
  props.formData.componentName = item.value;
  autoSelectCategory(item.value);
};

// 獲取組件屬性
const handleGetComponentProps = async () => {
  if (!props.formData.componentPath || !props.formData.componentName) {
    ElMessage.warning("請先填寫完整的組件路徑和名稱");
    return;
  }

  loadingProps.value = true;
  try {
    componentProps.value = await getComponentProps(
      props.formData.componentPath,
      props.formData.componentName
    );

    if (Object.keys(componentProps.value).length === 0) {
      ElMessage.info("該組件沒有定義任何屬性");
    } else {
      // 從組件屬性中獲取 title 和 description
      const titleProp = componentProps.value.title || componentProps.value.name;
      const descriptionProp = componentProps.value.description;

      // 如果組件有 title/name 屬性，則使用它作為表單的名稱
      if (titleProp) {
        props.formData.name = titleProp.default || props.formData.name;
      }

      // 如果組件有 description 屬性，則使用它作為表單的描述
      if (descriptionProp) {
        props.formData.description =
          descriptionProp.default || props.formData.description;
      }

      // 標記為已從組件屬性中獲取數據
      propsLoaded.value = true;
      ElMessage.success("已自動填充組件的名稱和描述");
    }
  } catch (error) {
    ElMessage.error(`獲取組件屬性失敗: ${error.message}`);
    componentProps.value = null;
  } finally {
    loadingProps.value = false;
  }
};

// 處理組件搜尋
const queryComponentSearch = (queryString, cb) => {
  const query = queryString.toLowerCase();
  const results = componentOptions.value
    .filter((option) => {
      // 如果有選擇 componentPath，則只顯示對應路徑下的組件
      if (props.formData.componentPath) {
        return (
          option.path.includes(`/${props.formData.componentPath}/`) &&
          option.label.toLowerCase().includes(query)
        );
      }
      // 否則顯示所有符合搜尋條件的組件
      return option.label.toLowerCase().includes(query);
    })
    .map((option) => ({
      value: option.label.replace(".vue", ""),
      path: option.path,
      fullPath: option.value,
    }));

  cb(results);
};

// 在當前位置插入 Markdown 語法
const insertMarkdown = (type) => {
  console.log("執行 insertMarkdown:", type);

  // 直接使用更可靠的選擇器，確保能找到正確的 textarea
  const textarea = document.querySelector(".markdown-editor textarea");

  if (!textarea) {
    console.error("找不到文本區域");
    return;
  }
  console.log("找到文本區域:", textarea);

  const { selectionStart, selectionEnd } = textarea;
  const selectedText =
    props.formData.helpContent?.substring(selectionStart, selectionEnd) || "";

  let insertion = "";
  switch (type) {
    case "header":
      insertion = `## ${selectedText || "標題"}`;
      break;
    case "bold":
      insertion = `**${selectedText || "粗體文字"}**`;
      break;
    case "italic":
      insertion = `*${selectedText || "斜體文字"}*`;
      break;
    case "code":
      insertion = selectedText
        ? "```\n" + selectedText + "\n```"
        : "```\n程式碼區塊\n```";
      break;
    case "list":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `- ${line}`)
            .join("\n")
        : "- 項目1\n- 項目2\n- 項目3";
      break;
    case "orderedList":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line, index) => `${index + 1}. ${line}`)
            .join("\n")
        : "1. 項目1\n2. 項目2\n3. 項目3";
      break;
    case "quote":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `> ${line}`)
            .join("\n")
        : "> 引用文字";
      break;
    case "link":
      insertion = `[${selectedText || "連結標題"}](https://example.com)`;
      break;
  }

  // 更新文本內容
  props.formData.helpContent =
    (props.formData.helpContent?.substring(0, selectionStart) || "") +
    insertion +
    (props.formData.helpContent?.substring(selectionEnd) || "");

  // 聚焦並將游標定位到適當位置
  nextTick(() => {
    try {
      textarea.focus();
      const newPosition = selectionStart + insertion.length;
      textarea.setSelectionRange(newPosition, newPosition);
      console.log("游標已設置到:", newPosition);
    } catch (error) {
      console.error("設置游標位置失敗:", error);
    }
  });
};

// 處理預覽
const handlePreview = () => {
  emit("preview");
};

// 處理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      emit("submit", props.formData, valid);
    }
  });
};

// 在組件掛載時載入組件
onMounted(() => {
  loadFlowNodeComponents();
});
</script>

<style scoped>
.form-item-tip {
  font-size: 12px;
  color: #909399;
}

/* 編輯區域樣式 */
.edit-textarea-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.edit-textarea-container :deep(.el-textarea) {
  height: 100%;
}

.edit-textarea-container :deep(.el-textarea__inner) {
  height: 100% !important;
  max-height: 300px;
  overflow-y: auto;
}

/* 確保 markdown-editor 類容易被選中 */
.markdown-editor {
  width: 100%;
}

.markdown-editor :deep(textarea) {
  font-family: monospace;
}

/* Markdown 預覽樣式 */
.markdown-preview :deep(h1) {
  font-size: 1.8em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h2) {
  font-size: 1.5em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h3) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h4) {
  font-size: 1.1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h5),
.markdown-preview :deep(h6) {
  font-size: 1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(p) {
  margin: 0.5em 0;
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
  list-style-position: outside;
}

.markdown-preview :deep(ol) {
  list-style-type: decimal;
}

.markdown-preview :deep(ol) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(ul) {
  list-style-type: disc;
}

.markdown-preview :deep(ul) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(pre) {
  margin: 0.5em 0;
  padding: 0.5em;
  border-radius: 4px;
  background-color: #f6f8fa;
}

.markdown-preview :deep(code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  background-color: #f6f8fa;
  padding: 0.2em 0.4em;
  border-radius: 3px;
}

.markdown-preview :deep(blockquote) {
  margin: 0.5em 0;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-preview :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.markdown-preview :deep(a):hover {
  text-decoration: underline;
}

.markdown-preview :deep(table) {
  border-collapse: collapse;
  margin: 0.5em 0;
  width: 100%;
}

.markdown-preview :deep(th),
.markdown-preview :deep(td) {
  border: 1px solid #ddd;
  padding: 0.5em;
}

.markdown-preview :deep(th) {
  background-color: #f6f8fa;
  font-weight: bold;
}
</style>
