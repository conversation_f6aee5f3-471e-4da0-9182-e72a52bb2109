<template>
  <el-dialog
    v-model="dialogVisible"
    title="節點預覽"
    width="1200px"
    top="5vh"
    :fullscreen="false"
    :draggable="true"
    @opened="handleFitView"
    destroy-on-close
    :close-on-click-modal="false">
    <div
      class="w-full h-[800px] bg-gray-50 dark:bg-dark-mode rounded-lg overflow-hidden flex items-center justify-center node-preview-container">
      <VueFlow
        v-if="dialogVisible"
        :modelValue="nodes"
        @update:modelValue="handleNodesUpdate"
        :default-viewport="{ x: 0, y: 0, zoom: 1 }"
        :min-zoom="0.5"
        :max-zoom="2"
        :pannable="false"
        :zoomable="true"
        :selectable="false"
        :deletable="false"
        :draggable="false"
        :node-types="{
          custom: currentComponent,
        }"
        class="w-full h-full flex items-center justify-center"
        :fit-view="true"
        :center="true"
        :auto-connect="false"
        :snap-to-grid="true"
        :snap-grid="[20, 20]">
        <Background :gap="20" />
        <Controls />
      </VueFlow>
    </div>
  </el-dialog>
</template>

<script setup>
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { Controls } from "@vue-flow/controls";
import { Background } from "@vue-flow/background";
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
const { fitView } = useVueFlow();
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  nodes: {
    type: Array,
    default: () => [],
  },
  currentComponent: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue"]);

// 對話框可見性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 處理節點更新
const handleNodesUpdate = (newNodes) => {
  console.log("節點更新:", newNodes);
  // 這裡只是預覽，不需要更新 props.nodes
};

const handleFitView = () => {
  console.log("onMounted");
  setTimeout(() => {
    fitView({ padding: 0.1 });
  }, 300);
};

onMounted(() => {
  handleFitView();
});
</script>

<style scoped>
/* 移除固定背景色設置，改用全局CSS中的暗黑模式樣式 */
</style>
