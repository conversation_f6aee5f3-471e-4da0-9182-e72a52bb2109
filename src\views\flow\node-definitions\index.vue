<!-- 節點定義管理頁面 -->

<template>
  <div class="p-0">
    <Teleport
      to="#header-actions"
      defer>
      <el-button
        type="info"
        plain
        @click="handleRefresh"
        :loading="loading"
        title="重新整理">
        <RotateCw
          class="mr-1"
          :size="14" />
        重整
      </el-button>

      <el-button
        type="primary"
        @click="handleCreate">
        <Plus
          class="mr-1"
          :size="14" />
        新增節點定義
      </el-button>
    </Teleport>

    <!-- 節點定義表格 -->
    <NodeDefinitionTable
      v-loading="loading"
      :nodeDefinitions="nodeDefinitions"
      @edit="handleEdit"
      @delete="handleDelete"
      @preview="handlePreview" />

    <!-- 編輯對話框 -->
    <NodeDefinitionEditDialog
      v-model="dialogVisible"
      :is-edit="isEdit"
      :form-data="form"
      :rules="rules"
      @submit="handleSubmit"
      @preview="() => handlePreview(form)" />

    <!-- 節點預覽對話框 -->
    <NodeDefinitionPreviewDialog
      v-model="previewDialogVisible"
      :nodes="nodes"
      :current-component="currentComponent" />
  </div>
</template>

<script setup>
import {
  getFlowNodeDefinitions,
  createFlowNodeDefinition,
  updateFlowNodeDefinition,
  deleteFlowNodeDefinition,
} from "@/api/modules/flow";
import { useFlowNodeComponents } from "@/composables/useFlowNodeComponents";
import NodeDefinitionTable from "./components/NodeDefinitionTable.vue";
import NodeDefinitionEditDialog from "./components/NodeDefinitionEditDialog.vue";
import NodeDefinitionPreviewDialog from "./components/NodeDefinitionPreviewDialog.vue";

// 數據
const loading = ref(false);
const nodeDefinitions = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const previewDialogVisible = ref(false);

// 使用 Flow Components composable (僅用於預覽功能)
const { previewComponent, currentComponent, nodes, closePreview } =
  useFlowNodeComponents();

// 表單數據
const form = ref({
  name: "",
  category: "",
  description: "",
  helpContent: "",
  icon: "",
  componentName: "",
  componentPath: "",
  config: "{}",
  uiConfig: "{}",
  handles: "{}",
});

// 表單驗證規則
const rules = {
  name: [
    { required: true, message: "請輸入名稱", trigger: "blur" },
    { min: 2, max: 50, message: "長度在 2 到 50 個字符之間", trigger: "blur" },
  ],
  category: [{ required: true, message: "請選擇分類", trigger: "change" }],
  description: [{ required: true, message: "請輸入描述", trigger: "blur" }],
  componentPath: [
    { required: true, message: "請選擇組件路徑", trigger: "change" },
  ],
  componentName: [
    { required: true, message: "請輸入組件名稱", trigger: "blur" },
  ],
  icon: [{ required: true, message: "請選擇圖示", trigger: "change" }],
};

// 統一的預覽處理函數
const handlePreview = async (nodeData) => {
  if (!nodeData.componentPath || !nodeData.componentName) {
    ElMessage.warning("請先填寫完整的組件路徑和名稱");
    return;
  }

  try {
    await previewComponent({
      componentPath: nodeData.componentPath,
      componentName: nodeData.componentName,
      nodeData,
    });
    previewDialogVisible.value = true;
  } catch (error) {
    console.error("預覽組件失敗：", error);
  }
};

// 監聽預覽對話框的開關
watch(previewDialogVisible, (visible) => {
  if (!visible) {
    closePreview();
  }
});

// 獲取節點定義列表
const handleRefresh = async () => {
  loading.value = true;
  try {
    const response = await getFlowNodeDefinitions();
    nodeDefinitions.value = response.data.sort((a, b) =>
      a.name.localeCompare(b.name, "zh-TW")
    );
  } catch (error) {
    ElMessage.error("獲取數據失敗");
  } finally {
    loading.value = false;
  }
};

// 重置表單
const resetForm = () => {
  form.value = {
    name: "",
    category: "",
    description: "",
    helpContent: "",
    icon: "",
    componentName: "",
    componentPath: "",
    config: "{}",
    uiConfig: "{}",
    handles: "{}",
  };
};

// 新增節點定義
const handleCreate = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 編輯節點定義
const handleEdit = (row) => {
  isEdit.value = true;
  form.value = {
    ...row,
    config:
      typeof row.config === "object"
        ? JSON.stringify(row.config, null, 2)
        : row.config,
    uiConfig:
      typeof row.uiConfig === "object"
        ? JSON.stringify(row.uiConfig, null, 2)
        : row.uiConfig,
    handles:
      typeof row.handles === "object"
        ? JSON.stringify(row.handles, null, 2)
        : row.handles,
  };
  dialogVisible.value = true;
};

// 刪除節點定義
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm("確定要刪除這個節點定義嗎？", "提示", {
      type: "warning",
    });
    await deleteFlowNodeDefinition(row.id);
    ElMessage.success("刪除成功");
    handleRefresh();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("刪除失敗");
    }
  }
};

// 提交表單
const handleSubmit = async (formData, valid) => {
  if (!valid) return;

  try {
    const submitData = {
      ...formData,
      config: JSON.parse(formData.config),
      uiConfig: JSON.parse(formData.uiConfig),
      handles: JSON.parse(formData.handles),
    };

    if (isEdit.value) {
      await updateFlowNodeDefinition(formData.id, submitData);
      ElMessage.success("更新成功");
    } else {
      await createFlowNodeDefinition(submitData);
      ElMessage.success("創建成功");
    }
    dialogVisible.value = false;
    handleRefresh();
  } catch (error) {
    ElMessage.error(isEdit.value ? "更新失敗" : "創建失敗");
  }
};

// 生命週期
onMounted(() => {
  handleRefresh();
});
</script>

<style></style>
