<template>
  <div
    class="bg-white dark:bg-dark-mode px-0 flex items-center justify-between">
    <!-- 右側按鈕組 -->

    <div class="flex items-center space-x-1">
      <!-- 預覽縮圖按鈕,放著備用-->
      <el-tooltip
        content="預覽縮圖(這個畫出來不好看!!!!)"
        placement="bottom"
        effect="light">
        <el-button
          type="info"
          @click="$emit('preview-thumbnail')">
          <LayoutPanelTop :size="16" />
        </el-button>
      </el-tooltip>
      <!-- 添加展開/折合所有節點按鈕 -->
      <el-tooltip
        :content="allNodesCompact ? '展開所有節點' : '折合所有節點'"
        placement="bottom"
        effect="light">
        <el-button @click="toggleAllNodesCompactMode">
          <component
            :is="allNodesCompact ? ChevronDown : ChevronUp"
            :size="16" />
        </el-button>
      </el-tooltip>
      <el-tooltip
        content="重置畫布"
        placement="bottom">
        <el-button @click="$emit('reset-canvas')">
          <RefreshCcw :size="16" />
        </el-button>
      </el-tooltip>
      <!-- 儲存按鈕 -->
      <el-tooltip
        content="儲存模板"
        placement="bottom"
        effect="light">
        <el-button
          :class="{ 'text-blue-500 dark:text-blue-400': hasUnsavedChanges }"
          @click="$emit('save-template')">
          <Save
            class="text-gray-500 dark:text-gray-400"
            :class="{ 'text-blue-500 dark:text-blue-400': hasUnsavedChanges }"
            :size="16" />
        </el-button>
      </el-tooltip>

      <!-- 顯示JSON輸出按鈕 -->
      <el-tooltip
        content="檢視JSON"
        placement="bottom">
        <el-button @click="$emit('show-json')">
          <Code :size="14" />
        </el-button>
      </el-tooltip>

      <!-- 啟用/停用按鈕 -->
      <el-tooltip
        v-if="flowTemplate?.status === 'inactive'"
        content="啟用模板"
        placement="bottom"
        effect="light">
        <el-button
          type="success"
          @click="$emit('activate-template')">
          <CheckCircle
            :size="16"
            class="mr-1" />
          啟用
        </el-button>
      </el-tooltip>
      <el-tooltip
        v-if="flowTemplate?.status === 'active'"
        content="停用模板"
        placement="bottom"
        effect="light">
        <el-button
          type="warning"
          @click="$emit('deactivate-template')">
          <XCircle
            :size="16"
            class="mr-1" />
          停用
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import {
  LayoutPanelTop,
  RefreshCcw,
  Save,
  Code,
  CheckCircle,
  XCircle,
  ChevronUp,
  ChevronDown,
} from "lucide-vue-next";
import { useVueFlow } from "@vue-flow/core";

const props = defineProps({
  layoutDirection: {
    type: String,
    default: "LR",
  },
  layoutDirections: {
    type: Array,
    default: () => [],
  },
  hasUnsavedChanges: {
    type: Boolean,
    default: false,
  },
  flowTemplate: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  "reset-canvas",
  "change-direction",
  "preview-thumbnail",
  "save-template",
  "show-json",
  "activate-template",
  "deactivate-template",
  "toggle-all-nodes-compact",
]);

const { nodes } = useVueFlow();
const currentDirection = ref(props.layoutDirection);
const allNodesCompact = ref(false);

// 檢查所有節點的折合狀態
const checkAllNodesCompactState = () => {
  // 如果沒有節點，預設為未折合
  if (!nodes.value || nodes.value.length === 0) {
    allNodesCompact.value = false;
    return;
  }

  // 檢查是否所有節點都處於折合狀態
  const allCompact = nodes.value.every((node) => {
    // 檢查節點上是否有 compactMode 屬性或 data.compactMode
    return node.data?.compactMode === true;
  });

  allNodesCompact.value = allCompact;
};

// 切換所有節點的折合狀態
const toggleAllNodesCompactMode = () => {
  // 切換狀態
  const newCompactState = !allNodesCompact.value;
  allNodesCompact.value = newCompactState;

  // 發送事件到父組件處理所有節點的狀態變更
  emit("toggle-all-nodes-compact", newCompactState);
};

watch(
  () => props.layoutDirection,
  (newVal) => {
    currentDirection.value = newVal;
  }
);

// 監聽節點變化以更新折合狀態
watch(
  () => nodes.value,
  () => {
    checkAllNodesCompactState();
  },
  { deep: true }
);

onMounted(() => {
  // 初始化時檢查節點折合狀態
  checkAllNodesCompactState();
});
</script>
