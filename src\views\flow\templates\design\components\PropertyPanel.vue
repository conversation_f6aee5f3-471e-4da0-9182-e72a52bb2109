<template>
  <div
    class="border-l border-gray-200 dark:border-gray-700 h-full flex flex-col bg-light-mode dark:bg-dark-mode transition-all duration-300"
    :class="[isCollapsed ? 'w-12' : 'w-96']">
    <div
      class="p-2.5 border-b border-gray-200 dark:border-gray-700 bg-slate-50 dark:bg-dark-mode flex items-center space-x-1">
      <div class="flex items-center">
        <el-tooltip
          :content="isCollapsed ? '展開面板' : '收合面板'"
          placement="left">
          <div
            class="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
            @click="$emit('toggle-panel')">
            <component
              :is="isCollapsed ? 'PanelRightOpen' : 'PanelRightClose'"
              class="text-gray-500 dark:text-dark-mode"
              :size="16" />
          </div>
        </el-tooltip>
      </div>
      <h3
        v-show="!isCollapsed"
        class="text-sm font-medium text-gray-700 dark:text-dark-mode">
        基本屬性
      </h3>
    </div>

    <div class="flex-1 overflow-y-auto dark:text-dark-mode">
      <div
        v-show="!isCollapsed"
        class="p-2 space-y-4">
        <!-- 基本屬性 -->
        <div v-loading="!flowTemplate" />

        <el-form
          v-if="flowTemplate"
          ref="formRef"
          :model="flowTemplate"
          :rules="formRules"
          label-width="80px"
          label-position="right"
          class="dark:text-dark-mode">
          <!-- // 如何判斷範本名稱是否已存在-->
          <el-form-item
            label="範本類型"
            prop="type">
            <el-select
              v-model="flowTemplate.type"
              placeholder="請選擇範本類型"
              :fit-input-width="true">
              <el-option
                label="business"
                value="business" />
            </el-select>
            <el-tag type="info"
              >先暫時固定 business (未來可能會有不同的流程類型)</el-tag
            >
          </el-form-item>

          <el-form-item
            label="範本名稱"
            prop="name">
            <el-input v-model="flowTemplate.name" />
          </el-form-item>
          <el-form-item
            label="範本描述"
            prop="description">
            <el-input
              v-model="flowTemplate.description"
              type="textarea"
              :rows="5" />
          </el-form-item>
          <el-form-item
            label="狀態"
            prop="status">
            <el-radio-group v-model="flowTemplate.status">
              <el-radio-button
                label="active"
                value="active"
                >啟用</el-radio-button
              >
              <el-radio-button
                label="inactive"
                value="inactive"
                >停用</el-radio-button
              >
              <el-radio-button
                label="deleted"
                value="deleted"
                >已刪除</el-radio-button
              >
            </el-radio-group>
          </el-form-item>

          <!-- <el-form-item label="元數據">
            <el-input
              v-model="flowTemplate.metadata"
              type="textarea"
              :rows="5" />
            <el-tag type="info"> 暫時沒使用，備著 </el-tag>
          </el-form-item> -->

          <el-form-item
            label="縮圖"
            prop="thumbnail">
            <div class="flex flex-col space-y-2">
              <el-upload
                class="thumbnail-uploader"
                action=""
                :auto-upload="false"
                :show-file-list="false"
                :before-upload="handleBeforeUpload"
                :on-change="handleUploadChange">
                <template v-if="flowTemplate.thumbnail">
                  <div class="relative thumbnail-preview-container">
                    <img
                      :src="flowTemplate.thumbnail"
                      class="thumbnail-preview" />
                    <div class="thumbnail-actions">
                      <el-button
                        type="danger"
                        size="small"
                        @click.stop="removeThumbnail"
                        class="thumbnail-remove-btn">
                        <Trash2 :size="16" />
                      </el-button>
                    </div>
                  </div>
                </template>
                <el-button
                  v-else
                  type="primary"
                  size="small">
                  <i class="el-icon-upload mr-1"></i>
                  上傳縮圖
                </el-button>
              </el-upload>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                建議尺寸: 800x600 像素，格式: JPG/PNG
              </div>
              <div class="mt-2 flex items-center space-x-2">
                <el-button
                  type="info"
                  size="small"
                  @click="activatePasteMode">
                  <Clipboard
                    :size="16"
                    class="mr-1" />
                  從剪貼簿貼上
                </el-button>
                <div
                  v-if="isPasteModeActive"
                  class="text-xs text-green-500">
                  <span class="animate-pulse">請按 Ctrl+V 貼上圖片</span>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <div class="flex justify-center">
          <el-button
            type="primary"
            class="w-1/2"
            :loading="loading"
            @click="handleSave"
            >儲存</el-button
          >
        </div>
      </div>
      <div
        v-show="isCollapsed"
        class="p-4 text-sm writing-vertical-lr text-gray-700 dark:text-dark-mode">
        基本屬性
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { Trash2, Clipboard } from "lucide-vue-next";

import { ElMessage } from "element-plus";
import { compressImage, validateImage } from "@/utils/imageUtils";

const props = defineProps({
  flowTemplate: {
    type: Object,
    required: true,
  },
  formRules: {
    type: Object,
    required: true,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["toggle-panel", "save"]);
const formRef = ref(null);

const isPasteModeActive = ref(true);

const activatePasteMode = () => {
  isPasteModeActive.value = true;
  ElMessage.info("已啟用貼上模式，請按 Ctrl+V 貼上圖片");
  // 5秒後自動關閉貼上模式
  setTimeout(() => {
    if (isPasteModeActive.value) {
      isPasteModeActive.value = false;
    }
  }, 55000);
};

const handlePaste = async (event) => {
  // 只有在貼上模式啟用時才處理
  if (!isPasteModeActive.value) return;

  const items = event.clipboardData?.items;
  if (!items) return;

  let imageItem = null;
  for (let i = 0; i < items.length; i++) {
    if (items[i].type.indexOf("image") !== -1) {
      imageItem = items[i];
      break;
    }
  }

  if (imageItem) {
    const file = imageItem.getAsFile();

    try {
      // 檢查文件是否合法
      const validation = validateImage(file);
      if (!validation.valid) {
        ElMessage.error(validation.message);
        return;
      }

      // 壓縮圖片
      const result = await compressImage(file, {
        maxWidth: 800,
        maxHeight: 600,
        quality: 0.7,
      });

      // 設置縮圖
      props.flowTemplate.thumbnail = result.dataUrl;

      // 顯示成功訊息
      ElMessage.success(
        `圖片已從剪貼簿貼上並壓縮: ${result.info.originalSize}KB → ${result.info.compressedSize}KB`
      );

      // 關閉貼上模式
      //isPasteModeActive.value = false;
    } catch (error) {
      console.error("貼上圖片處理失敗", error);
      ElMessage.error("圖片處理失敗，請重試");
    }
  } else {
    ElMessage.warning("剪貼簿中沒有圖片");
  }
};

// 掛載和卸載全局貼上事件監聽器
onMounted(() => {
  document.addEventListener("paste", handlePaste);
});

onUnmounted(() => {
  document.removeEventListener("paste", handlePaste);
});

const handleBeforeUpload = (file) => {
  // 使用 imageUtils 中的驗證函數
  const validation = validateImage(file);
  if (!validation.valid) {
    ElMessage.error(validation.message);
    return false;
  }
  return true;
};

const handleUploadChange = async (file) => {
  if (file && file.raw) {
    try {
      // 使用 imageUtils 中的壓縮函數
      const result = await compressImage(file.raw, {
        maxWidth: 800,
        maxHeight: 600,
        quality: 0.7,
      });

      // 設置縮圖
      props.flowTemplate.thumbnail = result.dataUrl;

      // 顯示壓縮資訊
      ElMessage.success(
        `圖片已壓縮: ${result.info.originalSize}KB → ${result.info.compressedSize}KB`
      );
    } catch (error) {
      console.error("圖片壓縮失敗", error);
      ElMessage.error("圖片處理失敗，請重試");
    }
  }
};

// 移除縮圖
const removeThumbnail = (e) => {
  e.stopPropagation(); // 阻止事件冒泡，避免觸發上傳
  props.flowTemplate.thumbnail = null;
  // ElMessage.success("縮圖已移除");
};

const handleSave = () => {
  if (!formRef.value) return;

  formRef.value.validate((valid) => {
    if (valid) {
      emit("save", props.flowTemplate);
    } else {
      ElMessage.warning("請檢查表單內容是否正確");
    }
  });
};
</script>

<style scoped>
.writing-vertical-lr {
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.thumbnail-uploader {
  display: flex;
  align-items: center;
}

.thumbnail-preview-container {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 120px;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #eee;
  transition: all 0.3s;
}

.thumbnail-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.thumbnail-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.thumbnail-preview-container:hover .thumbnail-actions {
  opacity: 1;
}

.thumbnail-remove-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #ff0000;
}

.dark .thumbnail-preview-container {
  border-color: #333;
}

.dark .thumbnail-remove-btn {
  background-color: rgba(30, 30, 30, 0.9);
  color: #f56c6c;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
