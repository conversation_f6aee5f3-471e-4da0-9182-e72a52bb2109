<template>
  <div
    class="bg-light-mode dark:bg-dark-mode border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300"
    :class="[isCollapsed ? 'w-12' : 'w-64']">
    <!-- 工具欄標題 -->
    <div
      class="p-2.5 bg-slate-50 dark:bg-dark-mode border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <h3
        v-show="!isCollapsed"
        class="text-md font-medium text-gray-900 dark:text-gray-100">
        節點類型
      </h3>
      <div class="flex items-center">
        <el-tooltip
          :content="isCollapsed ? '展開面板' : '收合面板'"
          placement="right">
          <div
            class="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
            @click="$emit('toggle-panel')">
            <component
              :is="isCollapsed ? 'PanelLeftOpen' : 'PanelLeftClose'"
              class="text-gray-500 dark:text-dark-mode"
              :size="16" />
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 搜尋框 -->
    <div
      v-show="!isCollapsed"
      class="p-3 border-b border-gray-200 dark:border-gray-700">
      <el-input
        v-model="searchQuery"
        placeholder="搜尋節點..."
        size="small"
        clearable
        class="w-full">
        <template #prefix>
          <Search
            class="text-gray-400"
            :size="14" />
        </template>
      </el-input>
    </div>

    <!-- 節點類型列表 -->
    <div
      v-show="!isCollapsed"
      class="flex-1 overflow-y-auto p-4">
      <div class="space-y-4">
        <!-- 資料輸入節點 -->
        <div
          v-if="filteredInputNodes.length > 0"
          class="space-y-2">
          <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
            資料輸入
            <span class="text-gray-400">({{ filteredInputNodes.length }})</span>
          </div>
          <div
            v-for="node in filteredInputNodes"
            :key="node.type"
            class="p-2 truncate bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800 cursor-move hover:shadow-md transition-shadow"
            :class="{ 'opacity-50 cursor-not-allowed': node.disabled }"
            draggable="true"
            @dragstart="!node.disabled && $emit('drag-start', $event, node)"
            :title="node.disabled ? '此節點已停用' : node.description || node.name">
            <div class="flex items-center space-x-2">
              <component
                :is="node.icon"
                class="text-blue-500 dark:text-blue-400"
                :size="16" />
              <span class="text-xs text-gray-700 dark:text-dark-mode">{{
                node.name
              }}</span>
            </div>
          </div>
        </div>

        <!-- 資料處理節點 -->
        <div
          v-if="filteredProcessNodes.length > 0"
          class="space-y-2">
          <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
            資料處理
            <span class="text-gray-400">({{ filteredProcessNodes.length }})</span>
          </div>
          <div
            v-for="node in filteredProcessNodes"
            :key="node.type"
            class="p-2 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-100 dark:border-green-800 cursor-move hover:shadow-md transition-shadow"
            :class="{ 'opacity-50 cursor-not-allowed': node.disabled }"
            draggable="true"
            @dragstart="!node.disabled && $emit('drag-start', $event, node)"
            :title="node.disabled ? '此節點已停用' : node.description || node.name">
            <div class="flex items-center space-x-2">
              <component
                :is="node.icon"
                class="text-green-500 dark:text-green-400"
                :size="16" />
              <span class="text-xs text-gray-700 dark:text-dark-mode">{{
                node.name
              }}</span>
            </div>
          </div>
        </div>

        <!-- 沒有找到結果時的提示 -->
        <div
          v-if="searchQuery && filteredInputNodes.length === 0 && filteredProcessNodes.length === 0"
          class="text-center py-8 text-gray-500 dark:text-gray-400">
          <div class="flex flex-col items-center space-y-2">
            <SearchX :size="32" />
            <p class="text-sm">沒有找到符合條件的節點</p>
            <p class="text-xs">嘗試使用其他關鍵字搜尋</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 收合時的圖示列表 -->
    <div
      v-show="isCollapsed"
      class="flex-1 overflow-y-auto py-4">
      <div class="space-y-4">
        <!-- 資料輸入節點 -->
        <div class="space-y-2">
          <div
            v-for="node in inputNodes"
            :key="node.type"
            class="px-2">
            <el-tooltip
              :content="node.name"
              effect="light"
              placement="right">
              <div
                class="p-2 rounded-lg cursor-move hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                :class="{ 'opacity-50 cursor-not-allowed': node.disabled }"
                draggable="true"
                @dragstart="
                  !node.disabled && $emit('drag-start', $event, node)
                ">
                <component
                  :is="node.icon"
                  class="text-blue-500 dark:text-blue-400"
                  :size="16" />
              </div>
            </el-tooltip>
          </div>
        </div>

        <!-- 資料處理節點 -->
        <div class="space-y-2">
          <div
            v-for="node in processNodes"
            :key="node.type"
            class="px-2">
            <el-tooltip
              :content="node.name"
              effect="light"
              placement="right">
              <div
                class="p-2 rounded-lg cursor-move hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
                :class="{ 'opacity-50 cursor-not-allowed': node.disabled }"
                draggable="true"
                @dragstart="
                  !node.disabled && $emit('drag-start', $event, node)
                ">
                <component
                  :is="node.icon"
                  class="text-purple-500 dark:text-purple-400"
                  :size="16" />
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { Search, SearchX, PanelLeftOpen, PanelLeftClose } from "lucide-vue-next";

const props = defineProps({
  inputNodes: {
    type: Array,
    required: true,
  },
  processNodes: {
    type: Array,
    required: true,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["toggle-panel", "drag-start"]);

// 搜尋功能
const searchQuery = ref("");

// 篩選輸入節點
const filteredInputNodes = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.inputNodes;
  }
  
  const query = searchQuery.value.toLowerCase().trim();
  return props.inputNodes.filter(node => {
    // 搜尋節點名稱、描述或組件名稱
    const searchableText = [
      node.name,
      node.description,
      node.componentName
    ].filter(Boolean).join(' ').toLowerCase();
    
    return searchableText.includes(query);
  });
});

// 篩選處理節點
const filteredProcessNodes = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.processNodes;
  }
  
  const query = searchQuery.value.toLowerCase().trim();
  return props.processNodes.filter(node => {
    // 搜尋節點名稱、描述或組件名稱
    const searchableText = [
      node.name,
      node.description,
      node.componentName
    ].filter(Boolean).join(' ').toLowerCase();
    
    return searchableText.includes(query);
  });
});
</script>
