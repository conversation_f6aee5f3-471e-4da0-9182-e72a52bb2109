<template>
  <div class="h-full flex">
    <!-- 左側工具欄 -->
    <ToolPanel
      :input-nodes="inputNodes"
      :process-nodes="processNodes"
      :is-collapsed="isLeftPanelCollapsed"
      @toggle-panel="handleLeftPanelToggle"
      @drag-start="handleDragStart" />

    <!-- 右側工作區 -->
    <div class="flex-1 flex flex-col">
      <!-- 工具列 -->
      <Teleport
        to="#header-actions"
        defer>
        <FlowToolbar
          :flow-template="flowTemplate"
          :layout-direction="layoutSettings.direction"
          :layout-directions="layoutDirections"
          :has-unsaved-changes="hasUnsavedChanges"
          @reset-canvas="handleReset"
          @preview-thumbnail="handlePreviewThumbnail"
          @save-template="handleSave"
          @show-json="handleShowJson"
          @layout="handleLayout"
          @layout-direction-change="handleLayoutDirectionChange"
          @activate-template="handleActivateTemplate"
          @deactivate-template="handleDeactivateTemplate"
          @toggle-all-nodes-compact="handleToggleAllNodesCompact" />
      </Teleport>

      <!-- JSON 輸出抽屜 -->
      <el-drawer
        v-model="jsonDrawerVisible"
        title="工作流程 JSON"
        direction="rtl"
        size="40%"
        class="json-drawer">
        <template #header>
          <div
            class="flex items-center justify-between w-full pr-4 dark:text-dark-mode">
            <span>工作流程 JSON</span>
            <div class="flex items-center space-x-3">
              <span class="text-sm text-gray-600 dark:text-gray-300">檢視</span>
              <el-switch
                v-model="jsonEditMode"
                size="default"
                :active-text="'編輯'"
                :inactive-text="'檢視'"
                inline-prompt
                :style="{
                  '--el-switch-on-color': '#409eff',
                  '--el-switch-off-color': isDark ? '#4b5563' : '#dcdfe6',
                  '--el-switch-border-color': isDark ? '#6b7280' : '#dcdfe6'
                }"
                @change="handleJsonModeChange" />
              <span class="text-sm text-gray-600 dark:text-gray-300">編輯</span>
            </div>
          </div>
        </template>

        <div class="p-4 dark:bg-gray-900 h-full flex flex-col">
          <!-- 檢視模式 -->
          <div v-if="!jsonEditMode" class="flex-1">
            <JsonViewer
              :jsonData="elements"
              :expand-depth="2"
              :expand-on-click="true" />
          </div>
          
          <!-- 編輯模式 -->
          <div v-else class="flex-1 flex flex-col space-y-4">
            <div class="flex-1">
              <el-input
                v-model="editableJsonText"
                type="textarea"
                :rows="25"
                placeholder="請貼上或編輯流程模板的JSON資料..."
                class="font-mono text-sm"
                resize="none"
                @keydown="handleJsonTextareaKeydown"
                @paste="handleJsonTextareaPaste" />
            </div>
            
            <div class="flex justify-between items-center pt-4 border-t dark:border-gray-700">
              <div class="text-sm text-gray-500 dark:text-gray-400">
                提示：可以使用 Ctrl+A 全選，Ctrl+V 貼上
              </div>
              <div class="flex space-x-2">
                <el-button @click="resetJsonText">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
                <el-button @click="switchToViewMode">
                  取消
                </el-button>
                <el-button type="primary" @click="handleApplyJsonChanges">
                  <el-icon><Check /></el-icon>
                  套用變更
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-drawer>

      <!-- 畫布區域 -->
      <div class="flex-1 bg-gray-50 dark:bg-dark-mode relative">
        <!-- Vue Flow 畫布 -->
        <VueFlow
          v-model="elements"
          class="h-full vue-flow-container"
          :default-zoom="1.5"
          :min-zoom="0.02"
          :max-zoom="30"
          :node-types="nodeTypes"
          :edge-types="edgeTypes"
          :default-edge-options="defaultEdgeOptions"
          :auto-connect="false"
          :edges-updatable="true"
          :edges-draggable="true"
          :edges-focusable="true"
          :edges-selectable="true"
          :select-nodes-on-drag="false"
          :connect-on-click="false"
          :snap-to-grid="snapToGrid"
          :snap-grid="[20, 20]"
          :connection-mode="ConnectionMode.Loose"
          :delete-key-code="['Backspace', 'Delete']"
          :elevate-edges-on-select="true"
          :fit-view-on-init="false"
          :prevent-scrolling="true"
          :enable-pan-over-edges="true"
          :enable-edge-updates="true"
          :update-edge-on-drag="true"
          :enable-connection-on-drag="true"
          :enable-strict-connect="false"
          :enable-edge-hover="true"
          :enable-edge-markers="true"
          :enable-edge-labels="true"
          :enable-edge-buttons="true"
          :enable-edge-update-on-drag="true"
          :enable-edge-update-on-mode-change="true"
          :enable-edge-update-on-handle-change="true"
          @nodeClick="onNodeClick"
          @connect="onConnect"
          @paneClick="onPaneClick"
          @edgeClick="onEdgeClick"
          @edgeUpdate="onEdgeUpdate"
          @edgeUpdateStart="onEdgeUpdateStart"
          @edgeUpdateEnd="onEdgeUpdateEnd"
          @nodeDragStart="onNodeDragStart"
          @nodeDragStop="onNodeDragStop"
          @nodesChange="onNodesChange"
          @edgesChange="onEdgesChange"
          @dragover="handleDragOver"
          @drop="handleDrop"
          @nodes-initialized="() => {}">
          <Background
            pattern="lines"
            :gap="20"
            :size="1" />

          <Controls />
          <MiniMap :pannable="true" />
          <Panel
            position="top-right"
            class="!bg-transparent !border-0">
            <div
              class="bg-light-mode dark:bg-dark-mode dark:border dark:border-gray-700 p-2 rounded shadow-lg">
              <el-switch
                v-model="snapToGrid"
                active-text="網格對齊" />
            </div>
          </Panel>
        </VueFlow>
      </div>
    </div>

    <!-- 右側屬性面板 -->
    <PropertyPanel
      :flow-template="flowTemplate"
      :form-rules="formRules"
      :is-collapsed="isRightPanelCollapsed"
      :loading="savingProperty"
      @toggle-panel="handleRightPanelToggle"
      @save="handleSaveProperty" />
  </div>
</template>

<script setup>
import { VueFlow, Panel, useVueFlow, ConnectionMode } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";
import { MiniMap } from "@vue-flow/minimap";
import EdgeWithButton from "@/components/flow-nodes/base/EdgeWithButton.vue";
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
import "@vue-flow/minimap/dist/style.css";
import JsonViewer from "@/components/JsonViewer.vue";
//import "vue-json-viewer/style.css";

// 導入子元件
import ToolPanel from "./components/ToolPanel.vue";
import PropertyPanel from "./components/PropertyPanel.vue";
import FlowToolbar from "./components/FlowToolbar.vue";

// 導入 composables
import { useFlowTemplate } from "@/composables/flow/useFlowTemplate";
import { useFlowLayout } from "@/composables/flow/useFlowLayout";
import { useFlowNodes } from "@/composables/flow/useFlowNodes";
import { useFlowEdges } from "@/composables/flow/useFlowEdges";
import { useFlowNodeComponents } from "@/composables/useFlowNodeComponents";
import { useScreenshot } from "@/composables/useScreenshot"; //TODO: 待測，想擷取後上傳存檔當做封面
import { updateFlowTemplate } from "@/api/modules/flow";
import { ElMessage, ElMessageBox } from "element-plus";
import { View, Edit, Refresh, Check } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";
import { useThemeMode } from "@/composables/useThemeMode";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { isDark } = useThemeMode();

// 使用 composables
const {
  flowTemplate,
  hasUnsavedChanges,
  formRules,
  loadTemplate,
  saveTemplate,
  handleBeforeUnload,
  setupRouteGuard,
} = useFlowTemplate();

const {
  layoutSettings,
  layoutDirections,
  layoutGraph,
  isLeftPanelCollapsed,
  isRightPanelCollapsed,
  loadPanelStates,
  toggleLeftPanel,
  toggleRightPanel,
} = useFlowLayout();

const {
  inputNodes,
  processNodes,
  selectedNode,
  loadNodeDefinitions,
  handleDragStart,
  handleDrop: nodeHandleDrop,
  handleDragOver,
  handleDeleteNode,
  onNodeClick,
  onPaneClick,
  onNodeDragStart: nodeOnDragStart,
  onNodeDragStop: nodeOnDragStop,
  onNodesChange: nodeOnNodesChange,
} = useFlowNodes();

const {
  defaultEdgeOptions,
  edgeTypes: flowEdgeTypes,
  onConnect: edgeOnConnect,
  onEdgeClick,
  onEdgeUpdate: edgeOnEdgeUpdate,
  onEdgeUpdateStart,
  onEdgeUpdateEnd,
  onEdgesChange: edgeOnEdgesChange,
} = useFlowEdges();

// Vue Flow 相關狀態
const elements = ref([]);
const snapToGrid = ref(true);
const jsonDrawerVisible = ref(false);
const jsonEditMode = ref(false);
const editableJsonText = ref('');

// 註冊自定義節點類型
const { flowNodeComponents, loadFlowNodeComponents } = useFlowNodeComponents();
const nodeTypes = ref({});

// 載入節點元件
const loadNodeComponents = async () => {
  await loadFlowNodeComponents();

  Object.entries(flowNodeComponents.value).forEach(([key, value]) => {
    const componentName = key
      .replace("/src/components/flow-nodes/business/", "")
      .replace(".vue", "");
    nodeTypes.value[componentName] = value.default || value;
  });
};

// 註冊自定義邊線類型
const edgeTypes = {
  button: EdgeWithButton,
};

// Vue Flow 相關函數
const { project, fitView, nodes, edges, setEdges, setNodes, vueFlowRef } =
  useVueFlow({
    defaultEdgeOptions,
    edgesUpdatable: true,
    edgesDraggable: true,
    edgesFocusable: true,
    selectNodesOnDrag: false,
    elevateEdgesOnSelect: true,
  });

// 截圖功能
const { capture } = useScreenshot();

// 處理左側面板摺疊
const handleLeftPanelToggle = () => {
  toggleLeftPanel(() => fitView({ padding: 0.2 }));
};

// 處理右側面板摺疊
const handleRightPanelToggle = () => {
  toggleRightPanel(() => fitView({ padding: 0.2 }));
};

// 處理拖放
const handleDrop = (event) => {
  nodeHandleDrop(event, project, elements, snapToGrid.value);
};

// 處理重置畫布
const handleReset = () => {
  setNodes([]);
  setEdges([]);
};

// 處理 JSON 顯示
const handleShowJson = () => {
  jsonDrawerVisible.value = true;
  jsonEditMode.value = false; // 預設為檢視模式
};

// 切換到檢視模式
const switchToViewMode = () => {
  jsonEditMode.value = false;
};

// 切換到編輯模式
const switchToEditMode = () => {
  jsonEditMode.value = true;
  // 將目前的 elements 轉換為格式化的 JSON 字串
  editableJsonText.value = JSON.stringify(elements.value, null, 2);
};

// 處理 JSON 模式切換 (for Switch component)
const handleJsonModeChange = (value) => {
  if (value) {
    // 切換到編輯模式
    editableJsonText.value = JSON.stringify(elements.value, null, 2);
  }
  // 檢視模式不需要特別處理
};

// 重置 JSON 文字
const resetJsonText = () => {
  editableJsonText.value = JSON.stringify(elements.value, null, 2);
};

// 套用 JSON 變更
const handleApplyJsonChanges = async () => {
  try {
    if (!editableJsonText.value.trim()) {
      ElMessage.warning('請輸入JSON資料');
      return;
    }

    // 解析JSON
    const parsedData = JSON.parse(editableJsonText.value);
    
    // 驗證JSON格式
    if (!Array.isArray(parsedData)) {
      throw new Error('JSON格式錯誤：應為陣列格式');
    }

    // 確認是否要覆蓋現有內容
    if (elements.value.length > 0) {
      await ElMessageBox.confirm(
        '套用變更將覆蓋目前的流程設計，確定要繼續嗎？',
        '確認套用',
        {
          confirmButtonText: '確定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
    }

    // 載入到畫布
    elements.value = parsedData;
    
    // 自動調整畫布視角
    setTimeout(() => {
      handleFitView();
    }, 100);

    // 標記為有未保存的變更
    hasUnsavedChanges.value = true;

    // 切換回檢視模式
    jsonEditMode.value = false;

    ElMessage.success('JSON套用成功');
    
  } catch (error) {
    console.error('套用JSON失敗:', error);
    if (error.name === 'SyntaxError') {
      ElMessage.error('JSON格式錯誤，請檢查語法');
    } else {
      ElMessage.error(`套用失敗：${error.message}`);
    }
  }
};

// JSON textarea 的鍵盤事件處理 - 阻止事件冒泡避免與其他貼上功能衝突
const handleJsonTextareaKeydown = (event) => {
  // 在 JSON 編輯區域內，完全阻止鍵盤快捷鍵事件傳播
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'v': // 貼上
        // 對於貼上操作，我們需要完全阻止事件傳播到全域處理器
        event.stopPropagation();
        event.stopImmediatePropagation();
        // 不阻止預設行為，讓 textarea 正常處理貼上
        // console.log('JSON編輯區域：阻止貼上事件冒泡');
        break;
      case 'c': // 複製
      case 'x': // 剪下
      case 'a': // 全選
      case 'z': // 復原
      case 'y': // 重做
        // 其他操作也阻止冒泡但不阻止預設行為
        event.stopPropagation();
        event.stopImmediatePropagation();
        break;
    }
  }
};

// JSON textarea 專門的貼上事件處理
const handleJsonTextareaPaste = (event) => {
  // 阻止事件冒泡到全域貼上處理器
  event.stopPropagation();
  event.stopImmediatePropagation();
  // console.log('JSON編輯區域：專門處理貼上事件，已阻止冒泡');
  // 允許 textarea 的原生貼上行為繼續
};

// 處理布局方向變更
const handleLayoutDirectionChange = (direction) => {
  layoutSettings.value.direction = direction;
  handleLayout();
};

// 處理重新布局
const handleLayout = () => {
  layoutGraph(nodes, setNodes, fitView, layoutSettings.value.direction);
};

// 處理儲存
const handleSave = async () => {
  await saveTemplate(route.params.id, elements);
};

// 處理縮圖預覽
const handlePreviewThumbnail = async () => {
  if (!vueFlowRef.value) {
    console.warn("VueFlow element not found");
    return;
  }
  const data = await capture(vueFlowRef.value, { shouldDownload: true });
  flowTemplate.value.thumbnail = data;
};

// 處理 fitView
const handleFitView = () => {
  setTimeout(() => {
    fitView({ padding: 0.2 });
  }, 400);
};

// 處理所有節點展開/折合
const handleToggleAllNodesCompact = (compactState) => {
  console.log("handleToggleAllNodesCompact", compactState);

  // 使用自定義事件通知所有節點進行折合狀態切換
  const customEvent = new CustomEvent("flow:toggleNodesCompact", {
    detail: {
      compactState,
    },
  });

  // 分發自定義事件
  window.dispatchEvent(customEvent);

  // 延遲調整視圖，讓節點有時間完成轉換
  setTimeout(() => {
    fitView({ padding: 0.2 });
  }, 300);

  // 標記為有未保存的變更
  hasUnsavedChanges.value = true;
};

// 節點拖動開始事件
const onNodeDragStart = (event) => {
  nodeOnDragStart(event, () => (hasUnsavedChanges.value = true));
};

// 節點拖動結束事件
const onNodeDragStop = (event) => {
  nodeOnDragStop(event, () => (hasUnsavedChanges.value = true));
};

// 節點變化事件
const onNodesChange = (changes) => {
  nodeOnNodesChange(changes, () => (hasUnsavedChanges.value = true));
};

// 連接事件
const onConnect = (params) => {
  edgeOnConnect(params, elements);
};

// 邊線更新事件
const onEdgeUpdate = (oldEdge, newConnection) => {
  edgeOnEdgeUpdate(oldEdge, newConnection, elements);
};

// 邊線變化事件
const onEdgesChange = (changes) => {
  edgeOnEdgesChange(changes, () => (hasUnsavedChanges.value = true));
};

// 處理節點數據更新
const handleNodeDataUpdated = () => {
  // 標記為有未保存的更改
  hasUnsavedChanges.value = true;
};

// 處理啟用模板
const handleActivateTemplate = async () => {
  try {
    await ElMessageBox.confirm("確定要啟用此模板嗎？", "啟用確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "info",
    });

    // 更新模板狀態為啟用
    const updatedTemplate = {
      ...flowTemplate.value,
      status: "active",
      updatedBy: userStore.user?.id,
    };

    await updateFlowTemplate(route.params.id, updatedTemplate);
    flowTemplate.value.status = "active";
    ElMessage.success("模板已啟用");
  } catch (error) {
    if (error !== "cancel") {
      console.error("啟用模板失敗:", error);
      ElMessage.error("啟用模板失敗");
    }
  }
};

// 處理停用模板
const handleDeactivateTemplate = async () => {
  try {
    await ElMessageBox.confirm("確定要停用此模板嗎？", "停用確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // 更新模板狀態為停用
    const updatedTemplate = {
      ...flowTemplate.value,
      status: "inactive",
      updatedBy: userStore.user?.id,
    };

    await updateFlowTemplate(route.params.id, updatedTemplate);
    flowTemplate.value.status = "inactive";
    ElMessage.success("模板已停用");
  } catch (error) {
    if (error !== "cancel") {
      console.error("停用模板失敗:", error);
      ElMessage.error("停用模板失敗");
    }
  }
};

// 新增狀態
const savingProperty = ref(false);

// 處理屬性面板儲存
const handleSaveProperty = async (formData) => {
  try {
    savingProperty.value = true;

    // 更新範本資料
    const updatedTemplate = {
      ...flowTemplate.value,
      thumbnail: formData.thumbnail,
      updatedBy: userStore.user?.id,
    };

    console.log("handleSaveProperty", updatedTemplate);

    // 呼叫 API 更新範本
    await updateFlowTemplate(route.params.id, updatedTemplate);

    // 同時保存流程設計元素，比照 handleSave
    await saveTemplate(route.params.id, elements);

    ElMessage.success("基本屬性與流程設計已儲存");

    // 標記未保存的變更
    hasUnsavedChanges.value = false;
  } catch (error) {
    console.error("儲存屬性失敗:", error);
    ElMessage.error("儲存屬性失敗");
  } finally {
    savingProperty.value = false;
  }
};

onMounted(async () => {
  // 載入面板狀態
  loadPanelStates();

  // 載入節點定義和元件
  await loadNodeDefinitions();
  await loadNodeComponents();

  // 載入範本資料
  await loadTemplate(route.params.id, elements, handleFitView);

  // 添加瀏覽器原生的離開提示
  window.addEventListener("beforeunload", handleBeforeUnload);

  // 添加路由守衛
  const removeRouteGuard = setupRouteGuard(router);

  onUnmounted(() => {
    // 移除瀏覽器原生的離開提示
    window.removeEventListener("beforeunload", handleBeforeUnload);

    // 移除路由守衛
    removeRouteGuard();
  });
});
</script>

<style>
/* VueFlow深色模式樣式 */
.dark .vue-flow__edge-path {
  stroke: #94a3b8 !important;
}

.dark .vue-flow__controls {
  background-color: #1e293b !important;
  border-color: #334155 !important;
}

.dark .vue-flow__controls-button {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #e2e8f0 !important;
}

.dark .vue-flow__controls-button:hover {
  background-color: #475569 !important;
}

.dark .vue-flow__minimap {
  background-color: #1e293b !important;
  border: 1px solid #334155 !important;
}
</style>
