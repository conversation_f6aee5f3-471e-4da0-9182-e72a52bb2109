<template>
  <div>
    <!-- 批次操作工具列 -->
    <div
      v-if="selectedTemplates.length > 0"
      class="batch-actions mb-4 p-2 bg-red-50 dark:bg-red-900/30 rounded flex items-center justify-between">
      <div class="flex items-center">
        <span class="text-red-600 dark:text-red-400 mr-2"
          >已選擇 {{ selectedTemplates.length }} 個範本</span
        >

        <!-- 添加全選/取消全選按鈕 -->
        <el-button
          type="text"
          size="small"
          class="ml-2 text-gray-600 dark:text-gray-400"
          @click="toggleSelectAll">
          {{ isAllSelected ? "取消全選" : "全選" }}
        </el-button>
      </div>
      <el-button
        type="danger"
        size="small"
        @click="handleBatchDelete">
        <Trash2
          :size="14"
          class="mr-1" />
        批次刪除
      </el-button>
    </div>
    <div
      v-if="templates.length === 0"
      class="flex justify-center items-center">
      <el-empty description="暫無資料" />
    </div>

    <div
      class="p-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-6 gap-2">
      <el-card
        v-for="template in templates"
        :key="template.id"
        class="template-card bg-white dark:bg-dark-mode rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-dark-mode hover:shadow-md transition-shadow"
        :class="{
          'template-selected': isSelected(template),
          'template-deleted': template.status === 'deleted',
        }"
        shadow="hover">
        <!-- 添加已刪除水印 -->
        <div
          v-if="template.status === 'deleted'"
          class="deleted-watermark">
          已刪除
        </div>

        <!-- 縮圖區域 -->
        <div class="template-thumbnail h-40 overflow-hidden relative">
          <img
            v-if="template.thumbnail"
            :src="template.thumbnail"
            class="w-full h-full object-cover"
            alt="模板縮圖" />
          <div
            v-else
            class="w-full h-full bg-gray-100 dark:bg-[#262626] rounded-md flex items-center justify-center">
            <FileImage
              :size="32"
              class="text-gray-400" />
          </div>

          <!-- 狀態標籤 -->
          <div class="absolute top-2 right-2">
            <el-tag
              :type="getStatusType(template.status)"
              size="small"
              effect="dark">
              {{ getStatusLabel(template.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 內容區域 -->
        <div class="p-4">
          <div class="flex items-start justify-between mb-2">
            <div class="flex items-center">
              <!-- 添加複選框 -->
              <el-checkbox
                v-if="selectable"
                v-model="selectedTemplates"
                :label="template.id"
                @change="handleSelectionChange"
                class="mr-2" />
              <h3
                class="text-lg font-medium truncate"
                :title="template.name">
                🚀
                {{ template.name }}
              </h3>
            </div>
          </div>

          <div
            class="h-16 overflow-hidden text-sm text-gray-600 dark:text-gray-400 mb-2">
            <p class="line-clamp-3">{{ template.description || "無描述" }}</p>
          </div>

          <div
            class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-500 mt-4">
            <div>
              <span>{{ formatDate(template.createdAt) }} 建立</span>
            </div>
            <div>
              <span v-if="template.updatedAt"
                >{{ formatDate(template.updatedAt) }} 更新</span
              >
            </div>
          </div>

          <div class="flex justify-between mt-0">
            <div>
              <el-button
                type="primary"
                size="small"
                plain
                :disabled="template.status === 'deleted'"
                @click="$emit('design', template)">
                <Pencil
                  :size="14"
                  class="mr-1" />
                設計
              </el-button>
              <el-button
                type="info"
                size="small"
                plain
                :disabled="template.status === 'deleted'"
                @click="$emit('edit', template)">
                <Edit
                  :size="14"
                  class="mr-1" />
                編輯
              </el-button>
            </div>
            <div>
              <el-dropdown trigger="click">
                <el-button
                  type="text"
                  class="p-1">
                  <MoreVertical :size="16" />
                </el-button>

                <template #dropdown>
                  <el-dropdown-menu>
                    <template v-if="template.status === 'deleted'">
                      <el-dropdown-item
                        @click="
                          $emit('change-status', {
                            template,
                            status: 'inactive',
                          })
                        ">
                        <RefreshCw
                          :size="14"
                          class="mr-1 text-green-500" />
                        <span class="text-green-500">還原為停用</span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        divided
                        @click="$emit('delete', template)"
                        v-if="hasDeletePermission">
                        <Trash2
                          :size="14"
                          class="mr-1 text-red-500" />
                        <span class="text-red-500">永久刪除</span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item @click="$emit('edit', template)">
                        <Edit
                          :size="14"
                          class="mr-1" />
                        編輯
                      </el-dropdown-item>
                      <el-dropdown-item @click="$emit('clone', template)">
                        <Copy
                          :size="14"
                          class="mr-1" />
                        複製
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="template.status === 'inactive'"
                        @click="
                          $emit('change-status', { template, status: 'active' })
                        ">
                        <CheckCircle
                          :size="14"
                          class="mr-1 text-green-500" />
                        <span class="text-green-500">啟用</span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="template.status === 'active'"
                        @click="
                          $emit('change-status', {
                            template,
                            status: 'inactive',
                          })
                        ">
                        <XCircle
                          :size="14"
                          class="mr-1 text-yellow-500" />
                        <span class="text-yellow-500">停用</span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        divided
                        @click="
                          $emit('change-status', {
                            template,
                            status: 'deleted',
                          })
                        "
                        v-if="
                          hasDeletePermission && template.status !== 'deleted'
                        ">
                        <Trash2
                          :size="14"
                          class="mr-1 text-red-500" />
                        <span class="text-red-500">刪除</span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  Pencil,
  Edit,
  MoreVertical,
  CheckCircle,
  XCircle,
  Trash2,
  Copy,
  RefreshCw,
  FileImage,
} from "lucide-vue-next";

import { formatTimestamp } from "@/utils/dateUtils";
import { renderMarkdown } from "@/utils/markdown.js";
import UserAvatar from "@/components/UserAvatar.vue";
import { ElMessageBox } from "element-plus";

const props = defineProps({
  templates: {
    type: Array,
    required: true,
  },
  selectable: {
    type: Boolean,
    default: false,
  },
  hasDeletePermission: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  "edit",
  "clone",
  "delete",
  "change-status",
  "selection-change",
]);

const selectedTemplates = ref([]);

const handleSelectionChange = () => {
  emit("selection-change", selectedTemplates.value);
};

const isSelected = (template) => {
  return selectedTemplates.value.includes(template.id);
};

const getStatusType = (status) => {
  switch (status) {
    case "active":
      return "success";
    case "inactive":
      return "warning";
    case "deleted":
      return "danger";
    default:
      return "info";
  }
};

const getStatusLabel = (status) => {
  switch (status) {
    case "active":
      return "啟用";
    case "inactive":
      return "停用";
    case "deleted":
      return "已刪除";
    default:
      return status;
  }
};

// 處理批次刪除
const handleBatchDelete = async () => {
  if (selectedTemplates.value.length === 0) return;

  // 檢查是否有已處於刪除狀態的範本
  const deletedTemplates = selectedTemplates.value.filter(
    (template) => template.status === "deleted"
  );
  const notDeletedTemplates = selectedTemplates.value.filter(
    (template) => template.status !== "deleted"
  );

  try {
    if (deletedTemplates.length > 0 && notDeletedTemplates.length > 0) {
      // 混合情況：有些是已刪除狀態，有些不是
      await ElMessageBox.confirm(
        `選中的 ${selectedTemplates.value.length} 個範本中，有 ${deletedTemplates.length} 個已處於刪除狀態，將被永久刪除；其餘 ${notDeletedTemplates.length} 個將被標記為已刪除。確定繼續嗎？`,
        "批次刪除確認",
        {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    } else if (deletedTemplates.length > 0) {
      // 全部都是已刪除狀態
      await ElMessageBox.confirm(
        `確定要永久刪除選中的 ${deletedTemplates.length} 個範本嗎？此操作不可恢復！`,
        "批次永久刪除確認",
        {
          confirmButtonText: "永久刪除",
          cancelButtonText: "取消",
          type: "danger",
        }
      );
    } else {
      // 全部都不是已刪除狀態
      await ElMessageBox.confirm(
        `確定要將選中的 ${notDeletedTemplates.length} 個範本標記為已刪除嗎？`,
        "批次刪除確認",
        {
          confirmButtonText: "標記為已刪除",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    }

    // 將選擇的範本傳送給父元件進行刪除
    emit("delete", selectedTemplates.value);

    // 清空選擇
    selectedTemplates.value = [];
  } catch (error) {
    // 用戶取消操作，不做任何處理
  }
};

// 格式化日期
const formatDate = (timestamp) => {
  return formatTimestamp(timestamp, "YYYY-MM-DD");
};

// 渲染描述
const renderDescription = (description) => {
  if (!description) return "";
  return renderMarkdown(description);
};

// 添加全選/取消全選功能
const toggleSelectAll = () => {
  if (selectedTemplates.value.length === props.templates.length) {
    // 取消全選
    selectedTemplates.value = [];
    // 更新每個範本的 selected 屬性
    props.templates.forEach((template) => {
      template.selected = false;
    });
  } else {
    // 全選
    selectedTemplates.value = [...props.templates];
    // 更新每個範本的 selected 屬性
    props.templates.forEach((template) => {
      template.selected = true;
    });
  }
};

// 判斷是否全選
const isAllSelected = computed(() => {
  return selectedTemplates.value.length === props.templates.length;
});
</script>

<style scoped>
.template-card {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.template-selected {
  border: 2px solid var(--el-color-primary) !important;
}

.template-deleted {
  opacity: 0.6;
}

.deleted-watermark {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: bold;
  color: rgba(255, 0, 0, 0.5);
  transform: rotate(-30deg);
  pointer-events: none;
  z-index: 10;
}

.dark .deleted-watermark {
  color: rgba(255, 0, 0, 0.7);
}

.template-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.template-card :deep(.markdown-content) {
  font-size: 0.875rem;
}

.template-card :deep(.markdown-content ol),
.template-card :deep(.markdown-content ul) {
  list-style-position: outside;
  padding-left: 1.2em;
  margin: 0.2em 0;
}

.template-card :deep(.markdown-content ol) {
  list-style-type: decimal;
}

.template-card :deep(.markdown-content ul) {
  list-style-type: disc;
}

.template-card :deep(.markdown-content li) {
  margin-left: 0.5em;
}

.template-card :deep(.markdown-content h1),
.template-card :deep(.markdown-content h2),
.template-card :deep(.markdown-content h3),
.template-card :deep(.markdown-content h4),
.template-card :deep(.markdown-content h5),
.template-card :deep(.markdown-content h6) {
  margin: 0;
  padding: 0;
  font-size: 0.875rem;
  font-weight: bold;
  line-height: 1.2;
}

.template-card :deep(.markdown-content p) {
  margin: 0;
}

.template-card :deep(.markdown-content pre) {
  margin: 0;
  padding: 0.25rem;
  font-size: 0.8rem;
}

.template-card :deep(.markdown-content blockquote) {
  margin: 0;
  padding: 0 0.5rem;
  border-left: 2px solid #ddd;
}

.template-card :deep(.markdown-content *) {
  max-width: 100%;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.batch-actions {
  transition: all 0.3s ease;
  border: 1px solid #f56c6c;
}

.dark .batch-actions {
  border-color: #b91c1c;
}
</style>
