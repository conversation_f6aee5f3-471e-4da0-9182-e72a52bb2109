<template>
  <el-dialog
    :title="dialogTitle"
    :modelValue="visible"
    @update:modelValue="$emit('update:visible', $event)"
    :close-on-click-modal="false"
    width="50%"
    top="5vh"
    draggable
    destroy-on-close>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
      @submit.prevent>
      <el-form-item
        label="名稱"
        prop="name">
        <el-input
          v-model="formData.name"
          placeholder="請輸入流程模板名稱" />
      </el-form-item>

      <el-form-item
        label="類型"
        prop="type">
        <el-select
          v-model="formData.type"
          placeholder="請選擇類型"
          class="w-full">
          <el-option
            label="業務流程"
            value="business" />
          <!-- <el-option
            label="系統流程"
            value="system" /> -->
        </el-select>
      </el-form-item>

      <!-- TODO: markdown 抽離 -->
      <el-form-item
        label="描述"
        prop="description">
        <div class="mb-2 w-full">
          <el-tabs v-model="descriptionTab">
            <el-tab-pane
              label="編輯"
              name="edit">
              <div
                class="markdown-toolbar flex items-center space-x-2 mb-2 p-1 border-b border-light-mode dark:border-dark-mode">
                <el-tooltip
                  content="標題"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('header')">
                    <span class="font-bold">H</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="粗體"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('bold')">
                    <span class="font-bold">B</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="斜體"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('italic')">
                    <span class="italic">I</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="程式碼區塊"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('code')">
                    <span class="font-mono">{}</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="無序列表"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('list')">
                    <span>• •</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="有序列表"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('orderedList')">
                    <span>1. 2.</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="引用"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('quote')">
                    <span>"</span>
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="連結"
                  placement="top">
                  <el-button
                    size="small"
                    @click="insertMarkdown('link')">
                    <span>🔗</span>
                  </el-button>
                </el-tooltip>
              </div>
              <!-- TODO: Focus 時的 border 顏色 -->
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="5"
                maxlength="1000"
                class="border !border-light-mode dark:!border-dark-mode"
                show-word-limit
                placeholder="請輸入流程模板描述（支援 Markdown 語法）" />
            </el-tab-pane>
            <el-tab-pane
              label="預覽"
              name="preview">
              <div
                class="markdown-preview p-3 border rounded min-h-[120px] max-h-[300px] overflow-auto bg-light-mode dark:bg-dark-mode border-light-mode dark:border-dark-mode">
                <div v-html="renderedMarkdown"></div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>

      <el-form-item
        label="版本"
        prop="version">
        <el-input
          v-model="formData.version"
          placeholder="請輸入版本號，例如：1.0.0" />
      </el-form-item>

      <el-form-item
        label="狀態"
        prop="status">
        <el-select
          v-model="formData.status"
          placeholder="請選擇狀態"
          class="w-full">
          <el-option
            label="啟用"
            value="active" />
          <el-option
            label="停用"
            value="inactive" />
        </el-select>
        <el-tag
          class="mt-1"
          type="warning"
          >預設為停用，待設計好啟用後會顯示在流程選擇的列表</el-tag
        >
      </el-form-item>

      <!-- FIXME: 用不到，暫保留 -->
      <!-- <el-collapse
        v-model="activeNames"
        class="mb-4">
        <el-collapse-item
          title="進階設定(用不到，暫保留)"
          name="advanced">
          <el-form-item
            label="節點"
            prop="nodes">
            <el-input
              v-model="formData.nodes"
              type="textarea"
              :rows="5"
              placeholder="請輸入節點 JSON 數據" />
          </el-form-item>

          <el-form-item
            label="連線"
            prop="edges">
            <el-input
              v-model="formData.edges"
              type="textarea"
              :rows="5"
              placeholder="請輸入連線 JSON 數據" />
          </el-form-item>

          <el-form-item
            label="元數據"
            prop="metadata">
            <el-input
              v-model="formData.metadata"
              type="textarea"
              :rows="5"
              placeholder="請輸入元數據 JSON 數據" />
          </el-form-item>
        </el-collapse-item>
      </el-collapse> -->
    </el-form>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading">
          {{ submitButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { renderMarkdown } from "@/utils/markdown.js";

const props = defineProps({
  // 是否顯示對話框
  visible: {
    type: Boolean,
    default: false,
  },
  // 是否為編輯模式
  isEdit: {
    type: Boolean,
    default: false,
  },
  // 是否為複製模式
  isCopy: {
    type: Boolean,
    default: false,
  },
  // 表單數據
  form: {
    type: Object,
    required: true,
  },
  // 表單規則
  rules: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

// 表單引用
const formRef = ref(null);
// 加載狀態
const loading = ref(false);
// 展開的項目
const activeNames = ref([]);
// 表單數據
const formData = reactive({ ...props.form });
// 描述標籤頁
const descriptionTab = ref("edit");

// 在當前位置插入 Markdown 語法
const insertMarkdown = (type) => {
  const textarea = document.querySelector('textarea[maxlength="1000"]');
  if (!textarea) return;

  const { selectionStart, selectionEnd } = textarea;
  const selectedText = formData.description.substring(
    selectionStart,
    selectionEnd
  );

  let insertion = "";
  switch (type) {
    case "header":
      insertion = `## ${selectedText || "標題"}`;
      break;
    case "bold":
      insertion = `**${selectedText || "粗體文字"}**`;
      break;
    case "italic":
      insertion = `*${selectedText || "斜體文字"}*`;
      break;
    case "code":
      insertion = selectedText
        ? "```\n" + selectedText + "\n```"
        : "```\n程式碼區塊\n```";
      break;
    case "list":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `- ${line}`)
            .join("\n")
        : "- 項目1\n- 項目2\n- 項目3";
      break;
    case "orderedList":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line, index) => `${index + 1}. ${line}`)
            .join("\n")
        : "1. 項目1\n2. 項目2\n3. 項目3";
      break;
    case "quote":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `> ${line}`)
            .join("\n")
        : "> 引用文字";
      break;
    case "link":
      insertion = `[${selectedText || "連結標題"}](https://example.com)`;
      break;
  }

  formData.description =
    formData.description.substring(0, selectionStart) +
    insertion +
    formData.description.substring(selectionEnd);

  // 聚焦並將游標定位到適當位置
  setTimeout(() => {
    textarea.focus();
    const newPosition = selectionStart + insertion.length;
    textarea.setSelectionRange(newPosition, newPosition);
  }, 0);
};

// 渲染 Markdown
const renderedMarkdown = computed(() => {
  return renderMarkdown(formData.description || "");
});

// 監聽表單數據變化
watch(
  () => props.form,
  (newVal) => {
    Object.assign(formData, newVal);
  },
  { deep: true }
);

// 處理取消
const handleCancel = () => {
  emit("cancel");
};

// 處理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    loading.value = true;
    await formRef.value.validate();
    console.log("表單數據", formData);
    emit("submit", { ...formData });
  } catch (error) {
    console.error("表單驗證失敗", error);
  } finally {
    loading.value = false;
  }
};

// 對話框標題
const dialogTitle = computed(() => {
  if (props.isCopy) {
    return "複製流程模板";
  }
  return props.isEdit ? "編輯流程模板" : "新增流程模板";
});

// 提交按鈕文字
const submitButtonText = computed(() => {
  if (props.isCopy) {
    return "複製";
  }
  return props.isEdit ? "更新" : "新增";
});
</script>

<style scoped>
.markdown-preview {
  background-color: #fafafa;
}

.markdown-preview :deep(.markdown-content) {
  font-size: 14px;
}

.markdown-preview :deep(h1) {
  font-size: 1.8em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h2) {
  font-size: 1.5em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h3) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h4) {
  font-size: 1.1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h5),
.markdown-preview :deep(h6) {
  font-size: 1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(pre) {
  margin: 0.5em 0;
  padding: 0.5em;
  border-radius: 4px;
}

.markdown-preview :deep(code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
}

.markdown-preview :deep(p) {
  margin: 0.5em 0;
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
  list-style-position: outside;
}

.markdown-preview :deep(ol) {
  list-style-type: decimal;
}

.markdown-preview :deep(ol) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(ul) {
  list-style-type: disc;
}

.markdown-preview :deep(ul) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(table) {
  border-collapse: collapse;
  margin: 0.5em 0;
}

.markdown-preview :deep(th),
.markdown-preview :deep(td) {
  border: 1px solid #ddd;
  padding: 0.4em 0.6em;
}

.markdown-preview :deep(blockquote) {
  margin: 0.5em 0;
  padding: 0.2em 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.markdown-preview :deep(hr) {
  margin: 0.5em 0;
  border: none;
  border-top: 1px solid #ddd;
}

.markdown-preview :deep(img) {
  max-width: 100%;
}
</style>
