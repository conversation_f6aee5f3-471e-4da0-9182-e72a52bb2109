<template>
  <div
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
    <el-skeleton
      v-for="i in count"
      :key="i"
      style="width: 100%"
      animated>
      <template #template>
        <div
          class="border rounded-md p-4 h-[300px] dark:border-gray-700 dark:bg-dark-mode">
          <div class="flex justify-between items-center mb-4">
            <el-skeleton-item
              variant="text"
              style="width: 60%" />
            <el-skeleton-item
              variant="button"
              style="width: 20%" />
          </div>
          <el-skeleton-item
            variant="p"
            style="width: 100%; height: 80px" />
          <div class="flex gap-2 my-4">
            <el-skeleton-item
              variant="button"
              style="width: 20%" />
            <el-skeleton-item
              variant="button"
              style="width: 30%" />
          </div>
          <div class="space-y-2 mb-4">
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
            <el-skeleton-item
              variant="text"
              style="width: 100%" />
          </div>
          <div class="flex justify-between mt-4">
            <div class="flex gap-2">
              <el-skeleton-item
                variant="button"
                style="width: 60px" />
              <el-skeleton-item
                variant="button"
                style="width: 60px" />
            </div>
            <el-skeleton-item
              variant="button"
              style="width: 40px" />
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script setup>
defineProps({
  count: {
    type: Number,
    default: 4,
  },
});
</script>

<style scoped>
:deep(.el-skeleton__item) {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
}

html.dark :deep(.el-skeleton__item) {
  background: linear-gradient(90deg, #2d3748 25%, #1a202c 37%, #2d3748 63%);
  background-size: 400% 100%;
}
</style>
