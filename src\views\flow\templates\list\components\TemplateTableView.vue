<template>
  <div class="template-table-view">
    <!-- 添加批次操作工具列 -->
    <div
      v-if="selectedTemplates.length > 0"
      class="batch-actions mb-4 p-2 bg-red-50 dark:bg-red-900/30 rounded flex items-center justify-between">
      <div class="flex items-center">
        <span class="text-red-600 dark:text-red-400 mr-2"
          >已選擇 {{ selectedTemplates.length }} 個範本</span
        >

        <!-- 添加全選/取消全選按鈕 -->
        <el-button
          type="text"
          size="small"
          class="ml-2 text-gray-600 dark:text-gray-400"
          @click="toggleSelectAll">
          {{ isAllSelected ? "取消全選" : "全選" }}
        </el-button>
      </div>
      <el-button
        type="danger"
        size="small"
        @click="handleBatchDelete">
        <Trash2
          :size="14"
          class="mr-1" />
        批次刪除
      </el-button>
    </div>

    <el-table
      ref="tableRef"
      :data="templates"
      style="width: 100%"
      stripe
      highlight-current-row
      @selection-change="handleSelectionChange">
      <!-- 添加複選框列 -->
      <el-table-column
        type="selection"
        width="55" />

      <el-table-column
        type="index"
        width="50"
        align="right" />

      <el-table-column
        label="名稱"
        prop="name"
        min-width="180">
        <template #default="{ row }">
          <div class="flex items-center">
            <!-- 縮圖預覽 -->
            <div class="thumbnail-preview mr-2">
              <img
                v-if="row.thumbnail"
                :src="row.thumbnail"
                class="w-10 h-10 object-cover rounded"
                alt="模板縮圖" />
              <div
                v-else
                class="w-10 h-10 bg-gray-100 dark:bg-gray-800 flex items-center justify-center rounded">
                <FileImage
                  :size="16"
                  class="text-gray-400" />
              </div>
            </div>

            <div>{{ row.name }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="description"
        label="描述"
        show-overflow-tooltip />

      <!-- <el-table-column
        prop="type"
        label="類型"
        width="120">
        <template #default="{ row }">
          <el-tag
            size="small"
            effect="plain">
            {{ row.type === "business" ? "業務流程" : "系統流程" }}
          </el-tag>
        </template>
      </el-table-column> -->

      <!-- <el-table-column
        prop="version"
        label="版本"
        width="100">
        <template #default="{ row }">
          <el-tag
            size="small"
            effect="plain"
            >v{{ row.version }}</el-tag
          >
        </template>
      </el-table-column> -->

      <el-table-column
        prop="status"
        label="狀態"
        width="100"
        sortable>
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            size="small">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="createdAt"
        label="建立時間"
        width="180"
        sortable>
        <template #default="{ row }">
          <div class="text-xs">
            <div>{{ formatTimestamp(row.createdAt) }}</div>
            <div class="text-gray-500">建立者: {{ row.creator.username }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="updatedAt"
        label="更新時間"
        width="180"
        sortable>
        <template #default="{ row }">
          <div class="text-xs">
            <div>{{ formatTimestamp(row.updatedAt) }}</div>
            <div class="text-gray-500">更新者: {{ row.updater.username }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        fixed="right">
        <template #default="{ row }">
          <div class="flex space-x-2">
            <!-- 編輯按鈕 -->
            <el-button
              v-if="row.status !== 'deleted'"
              type="primary"
              size="small"
              @click="$emit('edit', row)"
              plain>
              <Edit
                :size="14"
                class="mr-1" />
              編輯
            </el-button>

            <!-- 設計按鈕 -->
            <el-button
              v-if="row.status !== 'deleted'"
              type="success"
              size="small"
              @click="$emit('design', row)"
              plain>
              <ExternalLink
                :size="14"
                class="mr-1" />
              設計
            </el-button>

            <!-- 複製按鈕 -->
            <el-button
              v-if="row.status !== 'deleted'"
              type="info"
              size="small"
              @click="$emit('clone', row)"
              plain>
              <Copy
                :size="14"
                class="mr-1" />
              複製
            </el-button>

            <!-- 啟用/停用按鈕 -->
            <el-button
              v-if="row.status === 'inactive'"
              type="success"
              size="small"
              @click="
                $emit('change-status', { template: row, status: 'active' })
              "
              plain>
              <CheckCircle
                :size="14"
                class="mr-1" />
              啟用
            </el-button>

            <el-button
              v-if="row.status === 'active'"
              type="warning"
              size="small"
              @click="
                $emit('change-status', { template: row, status: 'inactive' })
              "
              plain>
              <XCircle
                :size="14"
                class="mr-1" />
              停用
            </el-button>

            <!-- 刪除/還原按鈕 -->
            <el-button
              v-if="row.status === 'deleted'"
              type="success"
              size="small"
              @click="
                $emit('change-status', { template: row, status: 'inactive' })
              "
              plain>
              <RefreshCw
                :size="14"
                class="mr-1" />
              還原
            </el-button>

            <el-button
              v-if="row.status !== 'deleted' && hasDeletePermission"
              type="danger"
              size="small"
              @click="
                $emit('change-status', { template: row, status: 'deleted' })
              "
              plain>
              <Trash2
                :size="14"
                class="mr-1" />
              刪除
            </el-button>

            <el-button
              v-if="row.status === 'deleted' && hasDeletePermission"
              type="danger"
              size="small"
              @click="$emit('delete', row)"
              plain>
              <Trash2
                :size="14"
                class="mr-1" />
              永久刪除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from "vue";
import {
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  RefreshCw,
  CheckCircle,
  XCircle,
  FileImage,
} from "lucide-vue-next";
import { formatTimestamp } from "@/utils/dateUtils";
import { ElMessageBox } from "element-plus";

const props = defineProps({
  templates: {
    type: Array,
    required: true,
    default: () => [],
  },
  hasDeletePermission: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  "edit",
  "clone",
  "delete",
  "design",
  "change-status",
]);

// 已選擇的範本
const selectedTemplates = ref([]);

// 使用 ref 引用表格
const tableRef = ref(null);

// 處理選擇變更
const handleSelectionChange = (selection) => {
  selectedTemplates.value = selection;
};

// 處理批次刪除
const handleBatchDelete = async () => {
  if (selectedTemplates.value.length === 0) return;

  // 檢查是否有已處於刪除狀態的範本
  const deletedTemplates = selectedTemplates.value.filter(
    (template) => template.status === "deleted"
  );
  const notDeletedTemplates = selectedTemplates.value.filter(
    (template) => template.status !== "deleted"
  );

  try {
    if (deletedTemplates.length > 0 && notDeletedTemplates.length > 0) {
      // 混合情況：有些是已刪除狀態，有些不是
      await ElMessageBox.confirm(
        `選中的 ${selectedTemplates.value.length} 個範本中，有 ${deletedTemplates.length} 個已處於刪除狀態，將被永久刪除；其餘 ${notDeletedTemplates.length} 個將被標記為已刪除。確定繼續嗎？`,
        "批次刪除確認",
        {
          confirmButtonText: "確定",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    } else if (deletedTemplates.length > 0) {
      // 全部都是已刪除狀態
      await ElMessageBox.confirm(
        `確定要永久刪除選中的 ${deletedTemplates.length} 個範本嗎？此操作不可恢復！`,
        "批次永久刪除確認",
        {
          confirmButtonText: "永久刪除",
          cancelButtonText: "取消",
          type: "danger",
        }
      );
    } else {
      // 全部都不是已刪除狀態
      await ElMessageBox.confirm(
        `確定要將選中的 ${notDeletedTemplates.length} 個範本標記為已刪除嗎？`,
        "批次刪除確認",
        {
          confirmButtonText: "標記為已刪除",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    }

    // 將選擇的範本傳送給父元件進行刪除
    emit("batch-delete", selectedTemplates.value);

    // 清空選擇
    selectedTemplates.value = [];

    // 使用表格 ref 清除選擇
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value.clearSelection();
      }
    });
  } catch (error) {
    // 用戶取消操作，不做任何處理
  }
};

// 獲取狀態標籤類型
const getStatusType = (status) => {
  switch (status) {
    case "active":
      return "success";
    case "inactive":
      return "warning";
    case "deleted":
      return "danger";
    default:
      return "info";
  }
};

// 獲取狀態標籤文字
const getStatusLabel = (status) => {
  switch (status) {
    case "active":
      return "啟用";
    case "inactive":
      return "停用";
    case "deleted":
      return "已刪除";
    default:
      return status;
  }
};

// 添加全選/取消全選功能
const isAllSelected = computed(() => {
  return (
    selectedTemplates.value.length === props.templates.length &&
    props.templates.length > 0
  );
});

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 取消全選
    selectedTemplates.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
  } else {
    // 全選
    selectedTemplates.value = [...props.templates];
    if (tableRef.value) {
      tableRef.value.toggleAllSelection();
    }
  }
};
</script>

<style scoped>
.batch-actions {
  transition: all 0.3s ease;
  border: 1px solid #f56c6c;
}

.dark .batch-actions {
  border-color: #b91c1c;
}

.thumbnail-preview img {
  object-fit: cover;
}
</style>
