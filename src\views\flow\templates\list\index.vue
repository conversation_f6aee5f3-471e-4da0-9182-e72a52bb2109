<template>
  <div class="p-0">
    <Teleport
      to="#header-actions"
      defer>
      <div class="flex items-center space-x-2">
        <el-radio-group v-model="viewMode">
          <el-radio-button
            label="卡片"
            value="card" />
          <el-radio-button
            label="列表"
            value="list" />
        </el-radio-group>
        <el-select
          v-model="filters.status"
          placeholder="選擇狀態"
          clearable
          class="!w-32">
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value">
            <el-tag
              :type="getStatusType(status.value)"
              size="small">
              {{ status.label }}
            </el-tag>
          </el-option>
        </el-select>

        <el-input
          v-model="filters.search"
          placeholder="搜尋流程模板"
          class="!w-60"
          clearable>
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-button
          type="info"
          plain
          @click="handleRefresh"
          :loading="loading"
          title="重新整理">
          <RotateCw
            class="mr-1"
            :size="16" />
          重整
        </el-button>

        <el-button
          type="primary"
          @click="handleAddTemplate">
          <Plus
            class="mr-1"
            :size="16" />
          新增流程模板
        </el-button>
      </div>
    </Teleport>

    <!-- 範本列表容器 -->
    <template v-if="loading">
      <TemplateListSkeleton :count="8" />
    </template>
    <template v-else>
      <TemplateCardView
        v-show="viewMode === 'card'"
        :templates="filteredTemplates"
        @edit="handleEditTemplate"
        @clone="handleCopyTemplate"
        @delete="handleDelete"
        @design="handleDesignTemplate"
        @change-status="handleChangeStatus" />

      <TemplateTableView
        v-show="viewMode === 'list'"
        :templates="filteredTemplates"
        @edit="handleEditTemplate"
        @change-status="handleChangeStatus"
        @delete="handleDelete"
        @design="handleDesignTemplate"
        @clone="handleCopyTemplate" />
    </template>

    <!-- 編輯對話框 -->
    <TemplateEditDialog
      :visible="dialogVisible"
      @update:visible="dialogVisible = $event"
      :is-edit="isEdit"
      :is-copy="isCopy"
      :form="form"
      :rules="rules"
      @submit="handleSubmit"
      @cancel="dialogVisible = false" />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated, onDeactivated, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, RotateCw, Plus, FileImage } from "lucide-vue-next";
import {
  getFlowTemplates,
  createFlowTemplate,
  updateFlowTemplate,
  deleteFlowTemplate,
} from "@/api/modules/flow";
import { useUserStore } from "@/stores/user";

// 導入子組件
import TemplateCardView from "./components/TemplateCardView.vue";
import TemplateTableView from "./components/TemplateTableView.vue";
import TemplateEditDialog from "./components/TemplateEditDialog.vue";
import TemplateListSkeleton from "./components/TemplateListSkeleton.vue";

// 數據
const loading = ref(false);
const templates = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const isCopy = ref(false);
const userStore = useUserStore();
const router = useRouter();

const viewMode = ref("card");

// 篩選器狀態
const filters = ref({
  category: "",
  status: "",
  search: "",
});

// 狀態選項
const statusOptions = [
  { label: "啟用", value: "active" },
  { label: "停用", value: "inactive" },
  { label: "已刪除", value: "deleted" },
];

// 表單數據
const form = ref({
  name: "",
  type: "business",
  description: "",
  version: "1.0.0",
  status: "inactive",
  nodes: "[]",
  edges: "[]",
  metadata: "{}",
});

// 表單驗證規則
const rules = {
  name: [
    { required: true, message: "請輸入名稱", trigger: "blur" },
    { min: 2, max: 50, message: "長度在 2 到 50 個字符之間", trigger: "blur" },
  ],
  type: [{ required: true, message: "請選擇類型", trigger: "change" }],
  description: [
    { required: true, message: "請輸入描述", trigger: "blur" },
    { max: 200, message: "長度不能超過 200 個字符", trigger: "blur" },
  ],
  version: [
    { required: true, message: "請輸入版本號", trigger: "blur" },
    {
      pattern: /^\d+\.\d+\.\d+$/,
      message: "版本號格式為：x.y.z",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "請選擇狀態", trigger: "change" }],
  nodes: [
    {
      validator: (rule, value, callback) => {
        try {
          JSON.parse(value);
          callback();
        } catch (error) {
          callback(new Error("請輸入有效的 JSON 格式"));
        }
      },
      trigger: "blur",
    },
  ],
  edges: [
    {
      validator: (rule, value, callback) => {
        try {
          JSON.parse(value);
          callback();
        } catch (error) {
          callback(new Error("請輸入有效的 JSON 格式"));
        }
      },
      trigger: "blur",
    },
  ],
  metadata: [
    {
      validator: (rule, value, callback) => {
        try {
          if (value) {
            JSON.parse(value);
          }
          callback();
        } catch (error) {
          callback(new Error("請輸入有效的 JSON 格式"));
        }
      },
      trigger: "blur",
    },
  ],
};

// 根據篩選條件過濾範本
const filteredTemplates = computed(() => {
  return templates.value.filter((template) => {
    const categoryMatch =
      !filters.value.category ||
      template.templateCategory === filters.value.category;
    const statusMatch =
      !filters.value.status || template.status === filters.value.status;
    const searchMatch =
      !filters.value.search ||
      template.name
        .toLowerCase()
        .includes(filters.value.search.toLowerCase()) ||
      template.description
        .toLowerCase()
        .includes(filters.value.search.toLowerCase());

    return categoryMatch && statusMatch && searchMatch;
  });
});

// 獲取狀態標籤類型
const getStatusType = (status) => {
  const types = {
    active: "success",
    inactive: "warning",
    deleted: "danger",
  };
  return types[status] || "info";
};

// 處理刷新(取得所有範本)
const handleRefresh = async () => {
  loading.value = true;
  try {
    const response = await getFlowTemplates();
    // 根據更新時間排序
    templates.value = response.data.sort(
      (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
    );
    console.log("templates size:", templates.value.length);
  } catch (error) {
    ElMessage.error("獲取數據失敗");
  } finally {
    loading.value = false;
  }
};

// 處理重置表單
const handleResetForm = () => {
  form.value = {
    name: "",
    type: "business",
    description: "",
    version: "1.0.0",
    status: "inactive",
    nodes: "[]",
    edges: "[]",
    metadata: "{}",
  };
};

// 處理新增範本
const handleAddTemplate = () => {
  isEdit.value = false;
  isCopy.value = false;
  handleResetForm();
  dialogVisible.value = true;
};

// 處理編輯範本
const handleEditTemplate = (template) => {
  isEdit.value = true;
  isCopy.value = false;
  form.value = {
    ...template,
    nodes:
      typeof template.nodes === "object"
        ? JSON.stringify(template.nodes, null, 2)
        : template.nodes,
    edges:
      typeof template.edges === "object"
        ? JSON.stringify(template.edges, null, 2)
        : template.edges,
    metadata:
      typeof template.metadata === "object"
        ? JSON.stringify(template.metadata, null, 2)
        : template.metadata || "{}",
  };
  dialogVisible.value = true;
};

// 處理複製範本
const handleCopyTemplate = (template) => {
  isEdit.value = false;
  isCopy.value = true;

  // 創建一個新的表單數據，並移除ID，修改名稱
  const formData = {
    ...template,
    id: undefined, // 移除ID
    name: `${template.name} (複製)`,
    version: "1.0.0",
    status: "inactive", // 新複製的範本默認為停用而非草稿
    nodes: template.nodes || [],
    edges: template.edges || [],
    metadata: template.metadata || "{}",
  };

  form.value = formData;
  dialogVisible.value = true;
};

// 處理設計範本
const handleDesignTemplate = (template) => {
  router.push(`/flow-templates/${template.id}/design`);
};

// 處理狀態切換
const handleChangeStatus = async ({ template, status }) => {
  try {
    let confirmMessage = "";
    let successMessage = "";

    if (status === "active") {
      confirmMessage = "確定要將此範本標記為啟用嗎？";
      successMessage = "範本已啟用";
    } else if (status === "inactive") {
      if (template.status === "deleted") {
        confirmMessage = "確定要還原此範本從刪除狀態轉為停用狀態嗎？";
        successMessage = "範本已還原為停用狀態";
      } else {
        confirmMessage = "確定要將此範本標記為停用嗎？";
        successMessage = "範本已停用";
      }
    } else {
      confirmMessage = "確定要變更狀態嗎？";
      successMessage = "狀態已更新";
    }

    await ElMessageBox.confirm(confirmMessage, "狀態變更確認", {
      confirmButtonText: "確定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await updateFlowTemplate(template.id, { ...template, status });
    ElMessage.success(successMessage);
    handleRefresh();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("狀態更新失敗");
    }
  }
};

// 處理刪除(for table)
const handleDelete = async (template) => {
  try {
    // 如果範本已經是刪除狀態，詢問是否要永久刪除
    if (template.status === "deleted") {
      await ElMessageBox.confirm(
        "此範本已處於刪除狀態，確定要永久刪除嗎？此操作不可恢復！",
        "永久刪除確認",
        {
          confirmButtonText: "永久刪除",
          cancelButtonText: "取消",
          type: "danger",
        }
      );

      // 執行實際的永久刪除操作
      await deleteFlowTemplate(template.id, true);
      ElMessage.success("永久刪除成功");
    } else {
      // 如果不是刪除狀態，詢問是否要標記為刪除
      await ElMessageBox.confirm("確定要將此範本標記為已刪除嗎？", "刪除確認", {
        confirmButtonText: "標記為已刪除",
        cancelButtonText: "取消",
        type: "warning",
      });

      // 將範本標記為已刪除
      await updateFlowTemplate(template.id, { ...template, status: "deleted" });
      ElMessage.success("範本已標記為刪除");
    }

    handleRefresh();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("刪除操作失敗");
    }
  }
};

// 處理提交(for dialog)
const handleSubmit = async (formData) => {
  try {
    const submitData = {
      ...formData,
      // nodes: JSON.parse(formData.nodes),
      // edges: JSON.parse(formData.edges),
      // metadata: formData.metadata ? JSON.parse(formData.metadata) : null,
      createdBy: userStore.user.id,
      updatedBy: userStore.user.id,
    };

    let templateId;

    if (isEdit.value) {
      templateId = formData.id;
      await updateFlowTemplate(formData.id, submitData);
      ElMessage.success("更新成功");
    } else {
      // 新增或複製
      const response = await createFlowTemplate(submitData);
      templateId = response.data.id;
      ElMessage.success(isCopy.value ? "複製成功" : "創建成功");
    }

    dialogVisible.value = false;
    router.push(`/flow-templates/${templateId}/design`);
    // 如果是複製模式，完成後直接進入設計頁面
    // if (isCopy.value && newTemplateId) {
    //   router.push(`/flow-templates/${newTemplateId}/design`);
    // } else {
    //   handleRefresh();
    // }
  } catch (error) {
    console.error("handleSubmit error", error);
    let errorMsg = "操作失敗";
    if (isEdit.value) {
      errorMsg = "更新失敗";
    } else if (isCopy.value) {
      errorMsg = "複製失敗";
    } else {
      errorMsg = "創建失敗";
    }
    ElMessage.error(errorMsg);
  }
};

// 生命週期
onMounted(() => {
  handleRefresh();
});
</script>
