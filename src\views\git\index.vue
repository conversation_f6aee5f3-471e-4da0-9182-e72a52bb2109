<template>
  <div>
    <Teleport
      to="#header-actions"
      defer>
      <div class="header-filters">
        <el-select
          v-model="selectedProject"
          placeholder="選擇專案"
          style="width: 200px"
          @change="handleProjectChange">
          <el-option
            v-for="project in projects"
            :key="project.value"
            :label="project.label"
            :value="project.value" />
        </el-select>
        <el-select
          v-model="selectedBranch"
          placeholder="選擇分支"
          style="width: 150px"
          :loading="branchesLoading"
          :disabled="branchesLoading"
          @change="loadCommits">
          <el-option
            v-for="branch in branches"
            :key="branch"
            :label="branch"
            :value="branch" />
        </el-select>
        <el-select
          v-model="timeRange"
          placeholder="時間範圍"
          style="width: 150px"
          @change="loadCommits">
          <el-option
            label="最近一週"
            value="1 week ago" />
          <el-option
            label="最近一個月"
            value="1 month ago" />
          <el-option
            label="最近三個月"
            value="3 months ago" />
          <el-option
            label="最近六個月"
            value="6 months ago" />
          <el-option
            label="最近一年"
            value="1 year ago" />
          <el-option
            label="全部歷史"
            value="10 years ago" />
        </el-select>
        <el-switch
          v-model="compactMode"
          active-text="精簡模式"
          inactive-text="詳細模式"
          inline-prompt />
        <el-button
          type="primary"
          @click="loadCommits"
          >更新</el-button
        >
      </div>
    </Teleport>

    <el-container class="overflow-hidden h-[calc(100vh-120px)]">
      <el-aside width="240px">
        <div class="aside-content">
          <h3 class="font-bold">統計資訊</h3>
          <div class="stats-card text-xs">
            <p>總提交數： {{ commits.length }}</p>
            <p>參與作者： {{ uniqueAuthors.length }}</p>
            <p>最近提交： {{ latestCommitDate }}</p>
          </div>

          <h3 class="font-bold">作者篩選</h3>
          <div class="author-filters">
            <el-checkbox-group v-model="selectedAuthors">
              <el-checkbox
                v-for="author in uniqueAuthors"
                :key="author"
                :label="author"
                class="!border-none"
                border>
                {{ author }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </el-aside>

      <el-main>
        <el-tabs v-model="activeTab">
          <el-tab-pane
            label="提交歷史"
            name="commits">
            <div
              v-if="loading"
              class="loading-container">
              <el-skeleton
                :rows="10"
                animated />
            </div>
            <div
              v-else-if="error"
              class="error-message">
              <el-alert
                :title="error"
                type="error"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
            <div
              v-else-if="filteredCommits.length === 0"
              class="no-data">
              <el-empty description="沒有找到符合條件的提交歷史" />
            </div>
            <div
              v-else
              class="commits-list"
              :class="{ 'compact-mode': compactMode }">
              <el-timeline>
                <el-timeline-item
                  v-for="commit in filteredCommits"
                  :key="
                    commit.hash || `_${commit.date || new Date().toISOString()}`
                  "
                  :timestamp="commit.date || ''"
                  :type="getCommitType(commit)">
                  <el-card
                    v-if="commit.hash"
                    :body-style="compactMode ? { padding: '0' } : {}">
                    <template #header>
                      <div class="commit-header">
                        <h3>{{ commit.subject || "無主題" }}</h3>
                        <el-tag
                          style="margin-left: 5px"
                          size="small"
                          type="info"
                          >{{ shortenHash(commit.hash) }}</el-tag
                        >
                      </div>
                    </template>
                    <div
                      class="commit-content"
                      v-if="!compactMode">
                      <div class="commit-info">
                        <p v-if="commit.author">
                          <strong>作者：</strong> {{ commit.author }} &lt;{{
                            commit.email || ""
                          }}&gt;
                        </p>
                        <p v-if="commit.branch">
                          <strong>分支：</strong>
                          {{ formatBranch(commit.branch) }}
                        </p>
                      </div>
                      <!-- <div
                          v-if="commit.body"
                          class="commit-body">
                          <pre>{{ commit.body }}</pre>
                        </div> -->
                      <div
                        v-if="commit.stats"
                        class="commit-stats mt-2">
                        <div class="stats-info">
                          <el-tag effect="plain"
                            >{{ commit.stats.filesChanged }} 個檔案修改</el-tag
                          >
                          <el-tag
                            type="success"
                            effect="plain"
                            >+{{ commit.stats.insertions }}</el-tag
                          >
                          <el-tag
                            type="danger"
                            effect="plain"
                            >-{{ commit.stats.deletions }}</el-tag
                          >
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>

          <el-tab-pane
            label="視覺化圖表"
            name="visualizations">
            <div class="charts-container">
              <div class="chart-wrapper">
                <h3>每天提交量</h3>
                <div
                  ref="commitsPerDayChart"
                  class="chart"></div>
              </div>
              <div class="chart-wrapper">
                <h3>作者提交佔比</h3>
                <div
                  ref="authorsPieChart"
                  class="chart"></div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import request from "@/api/request";
import * as echarts from "echarts/core";
import { PieChart, BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import { logger } from "@/utils/logger";

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  BarChart,
  CanvasRenderer,
]);

// 專案選項
const projects = ref([
  { label: "前端 (Frontend)", value: "frontend" },
  { label: "後端 (Backend)", value: "backend" },
  { label: "分析 (Analysis)", value: "analysis" },
]);

// 狀態
const commits = ref([]);
const loading = ref(false);
const error = ref("");
const selectedProject = ref("frontend");
const selectedBranch = ref("");
const branches = ref([]);
const branchesLoading = ref(false);
const timeRange = ref("1 month ago");
const selectedAuthors = ref([]);
const activeTab = ref("commits");
const compactMode = ref(
  localStorage.getItem("gitHistoryCompactMode") === "true"
);

// 圖表引用
const commitsPerDayChart = ref(null);
const authorsPieChart = ref(null);

// 計算屬性
const uniqueAuthors = computed(() => {
  return [...new Set(commits.value.map((commit) => commit.author))];
});

const filteredCommits = computed(() => {
  if (selectedAuthors.value.length === 0) {
    return commits.value;
  }
  return commits.value.filter((commit) =>
    selectedAuthors.value.includes(commit.author)
  );
});

const latestCommitDate = computed(() => {
  if (commits.value.length === 0) return "無數據";
  return commits.value[0].date;
});

// 方法
const handleProjectChange = async (projectValue) => {
  selectedBranch.value = "";
  branches.value = [];
  await loadBranches();
  await loadCommits();
};

const loadBranches = async () => {
  branchesLoading.value = true;
  try {
    const response = await request.get("/git/git-branch", {
      params: {
        project: selectedProject.value,
      },
    });
    branches.value = response.data.branches;
    console.log(response.data);

    if (branches.value.length > 0 && !selectedBranch.value) {
      if (branches.value.includes("experiment")) {
        selectedBranch.value = "experiment";
      } else if (branches.value.includes("main")) {
        selectedBranch.value = "main";
      } else if (branches.value.includes("master")) {
        selectedBranch.value = "master";
      } else {
        selectedBranch.value = branches.value[0];
      }
    }
  } catch (err) {
    console.error("獲取分支列表失敗:", err);
    error.value = `獲取分支列表失敗: ${err.message}`;
  } finally {
    branchesLoading.value = false;
  }
};

const loadCommits = async () => {
  if (!selectedProject.value) return;

  loading.value = true;
  error.value = "";
  try {
    const response = await request.get("/git/git-history", {
      params: {
        project: selectedProject.value,
        branch: selectedBranch.value,
        since: timeRange.value,
      },
    });
    console.log(response.data);
    commits.value = response.data.commits.filter(
      (commit) => !!commit.hash && !!commit.subject
    );
    selectedAuthors.value = [];
  } catch (err) {
    console.error("獲取提交歷史失敗:", err);
    error.value = `獲取提交歷史失敗: ${err.message}`;
    commits.value = [];
  } finally {
    loading.value = false;
  }
};

const shortenHash = (hash) => {
  if (!hash) return "無效提交";
  return hash.substring(0, 7);
};

const getCommitType = (commit) => {
  logger.debug("git index.vue", 351, commit);
  if (!commit || !commit.subject) return "info";
  const subject = commit.subject.toLowerCase();
  if (subject.includes("fix") || subject.includes("bug")) return "warning";
  if (subject.includes("feat") || subject.includes("add")) return "success";
  if (subject.includes("docs") || subject.includes("文檔")) return "info";
  return "primary";
};

const formatBranch = (branchInfo) => {
  if (!branchInfo) return "";
  return branchInfo.replace("remotes/origin/", "").replace("~", "@");
};

// 圖表相關方法
const renderCommitsPerDayChart = () => {
  if (!commitsPerDayChart.value) return;

  // 按日期分組提交
  const commitsByDate = {};
  filteredCommits.value.forEach((commit) => {
    const date = commit.date.split(" ")[0];
    if (!commitsByDate[date]) {
      commitsByDate[date] = 0;
    }
    commitsByDate[date]++;
  });

  const dates = Object.keys(commitsByDate).sort();
  const counts = dates.map((date) => commitsByDate[date]);

  const chart = echarts.init(commitsPerDayChart.value);
  chart.setOption({
    title: {
      text: "每日提交量",
    },
    tooltip: {
      trigger: "axis",
      formatter: "{b}: {c} 次提交",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: counts,
        type: "bar",
        name: "提交數",
        itemStyle: {
          color: "#409eff",
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    chart.resize();
  });
};

const renderAuthorsPieChart = () => {
  if (!authorsPieChart.value) return;

  // 按作者分組提交
  const commitsByAuthor = {};
  filteredCommits.value.forEach((commit) => {
    if (!commitsByAuthor[commit.author]) {
      commitsByAuthor[commit.author] = 0;
    }
    commitsByAuthor[commit.author]++;
  });

  const chartData = Object.keys(commitsByAuthor).map((author) => ({
    name: author,
    value: commitsByAuthor[author],
  }));

  const chart = echarts.init(authorsPieChart.value);
  chart.setOption({
    title: {
      text: "各作者提交佔比",
      left: "center",
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} 次提交 ({d}%)",
    },
    legend: {
      orient: "vertical",
      left: "left",
      data: Object.keys(commitsByAuthor),
    },
    series: [
      {
        name: "提交數",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "18",
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: chartData,
      },
    ],
  });

  window.addEventListener("resize", () => {
    chart.resize();
  });
};

// 監聽選項卡變化
watch(activeTab, (newVal) => {
  if (newVal === "visualizations") {
    setTimeout(() => {
      renderCommitsPerDayChart();
      renderAuthorsPieChart();
    }, 100);
  }
});

// 監聽過濾器變化
watch([filteredCommits, activeTab], ([newCommits, tab]) => {
  if (tab === "visualizations" && newCommits.length > 0) {
    setTimeout(() => {
      renderCommitsPerDayChart();
      renderAuthorsPieChart();
    }, 100);
  }
});

// 監聽精簡模式變化，保存到本地存儲
watch(compactMode, (newValue) => {
  localStorage.setItem("gitHistoryCompactMode", newValue);
});

// 生命週期
onMounted(async () => {
  await loadBranches();
  await loadCommits();
});
</script>

<style>
.el-header {
  background-color: #fff;
  border-bottom: 1px solid #dcdfe6;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo h1 {
  font-size: 22px;
  margin: 0;
  color: #409eff;
  font-weight: 600;
}

.header-filters {
  display: flex;
  gap: 16px;
  align-items: center;
}

.el-aside {
  background: #f5f7fa;
  padding: 20px;
  border-right: 1px solid #e6e6e6;
}

/* 暗黑模式下的側邊欄樣式 */
html.dark .el-aside {
  background: #1a1a1a;
  border-right: 1px solid #333;
}

.aside-content h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #606266;
  font-size: 16px;
}

/* 暗黑模式下的標題顏色 */
html.dark .aside-content h3 {
  color: #e0e0e0;
}

.stats-card {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 暗黑模式下的統計卡片樣式 */
html.dark .stats-card {
  background: #2c2c2c;
  color: #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.author-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

/* 暗黑模式下的作者篩選區域 */
html.dark .author-filters {
  color: #e0e0e0;
}

/* 暗黑模式下的復選框文字顏色 */
html.dark .el-checkbox__label {
  color: #e0e0e0;
}

.commits-list {
  padding: 20px 0;
}

/* 精簡模式樣式 */
.commit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commit-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

/* 暗黑模式下的提交標題顏色 */
html.dark .commit-header h3 {
  color: #e5e7eb;
}

.compact-mode .el-timeline-item {
  padding-bottom: 16px; /* 減少項目之間的間距 */
}

.compact-mode .el-timeline-item__node {
  transform: scale(0.8); /* 縮小時間線節點 */
}

.compact-mode .el-card {
  margin-bottom: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 減輕陰影 */
}

.compact-mode .el-card__header {
  padding: 10px 15px; /* 減少標題內邊距 */
}

.commit-info {
  margin-bottom: 10px;
  line-height: 1.5;
}

/* 暗黑模式下的提交信息顏色 */
html.dark .commit-info {
  color: #e0e0e0;
}

.commit-body {
  background: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
}

/* 暗黑模式下的提交內容背景 */
html.dark .commit-body {
  background: #333;
  color: #e0e0e0;
}

.commit-body pre {
  margin: 0;
  font-family: inherit;
}

.stats-info {
  display: flex;
  gap: 10px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.chart-wrapper {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 暗黑模式下的圖表容器樣式 */
html.dark .chart-wrapper {
  background: #2c2c2c;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.chart-wrapper h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
  text-align: center;
}

/* 暗黑模式下的圖表標題顏色 */
html.dark .chart-wrapper h3 {
  color: #e0e0e0;
}

.chart {
  height: 400px;
  width: 100%;
}

.loading-container {
  padding: 30px;
}

.error-message,
.no-data {
  margin: 30px 0;
}
</style>
