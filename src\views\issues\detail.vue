<template>
  <div class="issue-detail-container">
    <el-card
      shadow="never"
      class="issue-detail-card">
      <template #header>
        <div class="issue-header">
          <div class="issue-header-left">
            <h2>{{ issue.title }}</h2>
            <div class="issue-meta">
              <el-tag
                :type="getStatusType(issue.status)"
                effect="plain">
                {{ getStatusText(issue.status) }}
              </el-tag>
              <el-tag
                :type="getPriorityType(issue.priority)"
                effect="plain">
                {{ getPriorityText(issue.priority) }}
              </el-tag>
              <el-tag
                type="info"
                effect="plain">
                {{ getCategoryText(issue.category) }}
              </el-tag>
              <span class="issue-id">#{{ issue.id }}</span>
            </div>
          </div>
          <div class="issue-actions">
            <el-button-group>
              <el-button
                type="primary"
                @click="handleEdit"
                >編輯</el-button
              >
              <el-button
                type="success"
                @click="handleChangeStatus('resolved')"
                v-if="issue.status !== 'resolved' && issue.status !== 'closed'">
                <el-icon><Check /></el-icon>標記為已解決
              </el-button>
              <el-button
                type="info"
                @click="handleChangeStatus('closed')"
                v-if="issue.status !== 'closed'">
                <el-icon><Close /></el-icon>關閉問題
              </el-button>
              <el-button @click="goBack">返回列表</el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <div class="issue-body">
        <div class="issue-info">
          <div class="info-section">
            <h3>問題詳情</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">狀態</span>
                <el-tag :type="getStatusType(issue.status)">
                  {{ getStatusText(issue.status) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">優先級</span>
                <el-tag :type="getPriorityType(issue.priority)">
                  {{ getPriorityText(issue.priority) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">分類</span>
                <el-tag type="info">{{
                  getCategoryText(issue.category)
                }}</el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">創建時間</span>
                <span>{{ formatTimestamp(issue.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">更新時間</span>
                <span>{{ formatTimestamp(issue.updatedAt) }}</span>
              </div>
              <div
                class="info-item"
                v-if="issue.dueDate">
                <span class="info-label">截止日期</span>
                <span>{{ formatTimestamp(issue.dueDate) }}</span>
              </div>
            </div>
          </div>

          <div class="users-section">
            <div class="user-box">
              <h3>創建者</h3>
              <div
                class="user-info"
                v-if="issue.createdBy">
                <el-avatar
                  v-if="issue.createdBy.avatar"
                  :src="issue.createdBy.avatar"
                  :size="40" />
                <el-avatar
                  v-else
                  :size="40"
                  :icon="UserFilled" />
                <div class="user-details">
                  <span class="username">{{ issue.createdBy.username }}</span>
                </div>
              </div>
              <div v-else>未知用戶</div>
            </div>

            <div class="user-box">
              <h3>負責人</h3>
              <div
                v-if="issue.assignedTo"
                class="user-info">
                <el-avatar
                  v-if="issue.assignedTo.avatar"
                  :src="issue.assignedTo.avatar"
                  :size="40" />
                <el-avatar
                  v-else
                  :size="40"
                  :icon="UserFilled" />
                <div class="user-details">
                  <span class="username">{{ issue.assignedTo.username }}</span>
                </div>
              </div>
              <div v-else>
                <el-button
                  type="primary"
                  size="small"
                  @click="showAssignDialog"
                  >分配問題</el-button
                >
              </div>
            </div>
          </div>
        </div>

        <div class="content-section">
          <h3>問題描述</h3>
          <div
            class="markdown-content"
            v-html="renderMarkdown(issue.content)"></div>
        </div>

        <div
          class="screenshot-section"
          v-if="issue.screenshot">
          <h3>截圖</h3>
          <el-image
            :src="issue.screenshot"
            :preview-src-list="[issue.screenshot]"
            fit="contain"
            class="screenshot" />
        </div>

        <div class="comments-section">
          <h3>評論</h3>
          <div
            class="no-comments"
            v-if="!comments.length">
            目前還沒有評論
          </div>
          <div
            class="comment-list"
            v-else>
            <div
              class="comment-item"
              v-for="comment in comments"
              :key="comment.id">
              <div class="comment-header">
                <div class="user-info">
                  <el-avatar
                    v-if="comment.user.avatar"
                    :src="comment.user.avatar"
                    :size="32" />
                  <el-avatar
                    v-else
                    :size="32"
                    :icon="UserFilled" />
                  <span class="username">{{ comment.user.username }}</span>
                </div>
                <span class="comment-time">{{
                  formatTimestamp(comment.createdAt)
                }}</span>
              </div>
              <div
                class="comment-content"
                v-html="renderMarkdown(comment.content)"></div>
            </div>
          </div>

          <div class="add-comment">
            <h4>添加評論</h4>
            <el-form
              :model="commentForm"
              @submit.prevent="submitComment">
              <el-form-item>
                <el-input
                  v-model="commentForm.content"
                  type="textarea"
                  :rows="4"
                  placeholder="輸入評論內容 (支持 Markdown 格式)" />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="submitComment"
                  :loading="submitting">
                  發表評論
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 指派問題對話框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="指派問題"
      width="30%">
      <el-form :model="assignForm">
        <el-form-item label="選擇用戶">
          <el-select
            v-model="assignForm.assignedToId"
            filterable
            placeholder="選擇指派人員">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.id">
              <div class="user-info">
                <el-avatar
                  v-if="user.avatar"
                  :src="user.avatar"
                  :size="24" />
                <el-avatar
                  v-else
                  :size="24"
                  :icon="UserFilled" />
                <span>{{ user.username }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleAssign"
            :loading="submitting">
            確定指派
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 編輯問題對話框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="編輯問題工單"
      width="60%"
      destroy-on-close>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="issue-form">
        <el-form-item
          label="標題"
          prop="title">
          <el-input
            v-model="form.title"
            placeholder="請輸入問題標題" />
        </el-form-item>
        <el-form-item
          label="內容"
          prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="請詳細描述問題內容 (支持 Markdown 格式)" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="狀態"
              prop="status">
              <el-select
                v-model="form.status"
                placeholder="選擇狀態">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="優先級"
              prop="priority">
              <el-select
                v-model="form.priority"
                placeholder="選擇優先級">
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="分類"
              prop="category">
              <el-select
                v-model="form.category"
                placeholder="選擇分類">
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="指派給"
              prop="assignedToId">
              <el-select
                v-model="form.assignedToId"
                filterable
                placeholder="選擇指派人員"
                clearable>
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="user.username"
                  :value="user.id">
                  <div class="user-info">
                    <el-avatar
                      v-if="user.avatar"
                      :src="user.avatar"
                      :size="24" />
                    <el-avatar
                      v-else
                      :size="24"
                      :icon="UserFilled" />
                    <span>{{ user.username }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="截止日期">
          <el-date-picker
            v-model="form.dueDate"
            type="date"
            placeholder="選擇截止日期" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitEditForm"
            :loading="submitting">
            更新
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Check, Close, UserFilled } from "@element-plus/icons-vue";
import { getIssueById, updateIssue } from "@/api/modules/issue";
import { getUsers } from "@/api/modules/user";
import { formatTimestamp } from "@/utils/dateUtils";
import { renderMarkdown } from "@/utils/markdown";

const route = useRoute();
const router = useRouter();
const issueId = route.params.id;

// 數據
const issue = ref({});
const comments = ref([]);
const users = ref([]);
const loading = ref(false);
const submitting = ref(false);
const assignDialogVisible = ref(false);
const editDialogVisible = ref(false);

// 表單引用
const formRef = ref(null);

// 指派表單
const assignForm = reactive({
  assignedToId: "",
});

// 評論表單
const commentForm = reactive({
  content: "",
});

// 編輯表單
const form = reactive({
  title: "",
  content: "",
  status: "",
  priority: "",
  category: "",
  assignedToId: null,
  dueDate: null,
});

// 表單驗證規則
const rules = {
  title: [
    { required: true, message: "請輸入問題標題", trigger: "blur" },
    {
      min: 3,
      max: 100,
      message: "標題長度應在 3 到 100 個字符之間",
      trigger: "blur",
    },
  ],
  content: [{ required: true, message: "請輸入問題內容", trigger: "blur" }],
  status: [{ required: true, message: "請選擇狀態", trigger: "change" }],
  priority: [{ required: true, message: "請選擇優先級", trigger: "change" }],
  category: [{ required: true, message: "請選擇分類", trigger: "change" }],
};

// 選項
const statusOptions = [
  { value: "open", label: "待處理" },
  { value: "in_progress", label: "處理中" },
  { value: "resolved", label: "已解決" },
  { value: "closed", label: "已關閉" },
];

const priorityOptions = [
  { value: "low", label: "低" },
  { value: "medium", label: "中" },
  { value: "high", label: "高" },
  { value: "critical", label: "緊急" },
];

const categoryOptions = [
  { value: "frontend", label: "前端" },
  { value: "backend", label: "後端" },
  { value: "analysis", label: "分析" },
  { value: "design", label: "設計" },
  { value: "infrastructure", label: "基礎設施" },
  { value: "other", label: "其他" },
];

// 獲取狀態文本和顏色
const getStatusText = (status) => {
  const option = statusOptions.find((opt) => opt.value === status);
  return option ? option.label : "未知";
};

const getStatusType = (status) => {
  switch (status) {
    case "open":
      return "info";
    case "in_progress":
      return "warning";
    case "resolved":
      return "success";
    case "closed":
      return "";
    default:
      return "info";
  }
};

// 獲取優先級文本和顏色
const getPriorityText = (priority) => {
  const option = priorityOptions.find((opt) => opt.value === priority);
  return option ? option.label : "未知";
};

const getPriorityType = (priority) => {
  switch (priority) {
    case "low":
      return "info";
    case "medium":
      return "";
    case "high":
      return "warning";
    case "critical":
      return "danger";
    default:
      return "info";
  }
};

// 獲取分類文本
const getCategoryText = (category) => {
  const option = categoryOptions.find((opt) => opt.value === category);
  return option ? option.label : "未知";
};

// 獲取問題詳情
const fetchIssueDetail = async () => {
  loading.value = true;
  try {
    issue.value = await getIssueById(issueId);
    // 初始化編輯表單
    Object.assign(form, {
      title: issue.value.title,
      content: issue.value.content,
      status: issue.value.status,
      priority: issue.value.priority,
      category: issue.value.category,
      assignedToId: issue.value.assignedToId,
      dueDate: issue.value.dueDate ? new Date(issue.value.dueDate) : null,
    });
  } catch (error) {
    console.error("獲取問題詳情失敗:", error);
    ElMessage.error("獲取問題詳情失敗");
    router.push("/issues");
  } finally {
    loading.value = false;
  }
};

// 獲取用戶列表
const fetchUsers = async () => {
  try {
    users.value = await getUsers();
  } catch (error) {
    console.error("獲取用戶列表失敗:", error);
  }
};

// 返回列表
const goBack = () => {
  router.push("/issues");
};

// 顯示指派對話框
const showAssignDialog = () => {
  assignForm.assignedToId = "";
  assignDialogVisible.value = true;
};

// 處理指派問題
const handleAssign = async () => {
  if (!assignForm.assignedToId) {
    ElMessage.warning("請選擇指派人員");
    return;
  }

  submitting.value = true;
  try {
    await updateIssue(issueId, { assignedToId: assignForm.assignedToId });
    ElMessage.success("問題指派成功");
    assignDialogVisible.value = false;
    fetchIssueDetail();
  } catch (error) {
    console.error("問題指派失敗:", error);
    ElMessage.error("問題指派失敗");
  } finally {
    submitting.value = false;
  }
};

// 處理改變狀態
const handleChangeStatus = async (status) => {
  submitting.value = true;
  try {
    await updateIssue(issueId, { status });
    ElMessage.success(`問題已標記為${getStatusText(status)}`);
    fetchIssueDetail();
  } catch (error) {
    console.error("更新問題狀態失敗:", error);
    ElMessage.error("更新問題狀態失敗");
  } finally {
    submitting.value = false;
  }
};

// 處理編輯
const handleEdit = () => {
  editDialogVisible.value = true;
};

// 提交編輯表單
const submitEditForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;
      try {
        await updateIssue(issueId, form);
        ElMessage.success("問題工單更新成功");
        editDialogVisible.value = false;
        fetchIssueDetail();
      } catch (error) {
        console.error("更新問題工單失敗:", error);
        ElMessage.error("更新問題工單失敗");
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 提交評論
const submitComment = async () => {
  if (!commentForm.content.trim()) {
    ElMessage.warning("評論內容不能為空");
    return;
  }

  submitting.value = true;
  try {
    // 模擬提交評論的API調用
    // const response = await addComment(issueId, commentForm.content);
    // 暫時使用模擬數據
    const mockComment = {
      id: String(Date.now()),
      content: commentForm.content,
      createdAt: new Date().toISOString(),
      user: {
        id: "current-user-id",
        username: "當前用戶",
        avatar: null,
      },
    };

    comments.value.unshift(mockComment);
    commentForm.content = "";
    ElMessage.success("評論發表成功");
  } catch (error) {
    console.error("評論發表失敗:", error);
    ElMessage.error("評論發表失敗");
  } finally {
    submitting.value = false;
  }
};

// 生命週期
onMounted(() => {
  fetchIssueDetail();
  fetchUsers();
  // 模擬獲取評論列表
  comments.value = [];
});
</script>

<style scoped>
.issue-detail-container {
  padding: 20px;
}

.issue-detail-card {
  margin-bottom: 20px;
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.issue-header-left {
  flex: 1;
}

.issue-header-left h2 {
  margin: 0 0 10px 0;
  font-size: 1.5em;
}

.issue-meta {
  display: flex;
  gap: 8px;
  align-items: center;
}

.issue-id {
  color: #666;
  font-size: 0.9em;
}

.issue-actions {
  margin-left: 20px;
}

.issue-body {
  margin-top: 20px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h3,
.users-section h3,
.content-section h3,
.screenshot-section h3,
.comments-section h3 {
  margin: 0 0 15px 0;
  font-size: 1.2em;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-label {
  color: #666;
  font-size: 0.9em;
}

.users-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.user-box {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
}

.content-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
}

:deep(.markdown-content) {
  font-size: 1rem;
  line-height: 1.6;
  color: #2c3e50;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin: 1em 0 0.5em;
  line-height: 1.4;
  font-weight: 600;
}

:deep(.markdown-content h1) {
  font-size: 2em;
}
:deep(.markdown-content h2) {
  font-size: 1.5em;
}
:deep(.markdown-content h3) {
  font-size: 1.3em;
}
:deep(.markdown-content h4) {
  font-size: 1.1em;
}
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  font-size: 1em;
}

:deep(.markdown-content p) {
  margin: 1em 0;
}

:deep(.markdown-content pre) {
  margin: 1em 0;
  padding: 1em;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.markdown-content code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: #f6f8fa;
  border-radius: 3px;
}

:deep(.markdown-content ol),
:deep(.markdown-content ul) {
  margin: 1em 0;
  padding-left: 2em;
}

:deep(.markdown-content li) {
  margin: 0.5em 0;
}

:deep(.markdown-content blockquote) {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #42b983;
  background-color: #f8f8f8;
  color: #666;
}

:deep(.markdown-content img) {
  max-width: 100%;
  margin: 1em 0;
}

:deep(.markdown-content table) {
  width: 100%;
  margin: 1em 0;
  border-collapse: collapse;
}

:deep(.markdown-content th),
:deep(.markdown-content td) {
  padding: 0.5em;
  border: 1px solid #ddd;
}

:deep(.markdown-content th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

.screenshot-section {
  margin-bottom: 20px;
}

.screenshot {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.comments-section {
  margin-top: 30px;
}

.comment-list {
  margin-bottom: 20px;
}

.comment-item {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 15px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.comment-time {
  color: #999;
  font-size: 0.9em;
}

.comment-content {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.add-comment {
  margin-top: 20px;
}

.add-comment h4 {
  margin: 0 0 10px 0;
  font-size: 1.1em;
  color: #333;
}

.no-comments {
  text-align: center;
  color: #999;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
}

:deep(.comment-content .markdown-content) {
  font-size: 0.95em;
}

:deep(.comment-content .markdown-content p) {
  margin: 0.5em 0;
}

:deep(.comment-content .markdown-content pre) {
  margin: 0.5em 0;
  padding: 0.5em;
}

:deep(.comment-content .markdown-content blockquote) {
  margin: 0.5em 0;
  padding: 0.25em 0.5em;
}
</style>
