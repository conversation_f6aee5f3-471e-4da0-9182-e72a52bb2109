<template>
  <div class="p-0">
    <Teleport
      to="#header-actions"
      defer>
      <div class="flex items-center space-x-2">
        <span class="text-gray-600 text-xs">
          共
          <span class="font-bold text-purple-500">{{ issues.length }}</span>
          個問題工單
        </span>

        <el-select
          v-model="filters.type"
          clearable
          placeholder="選擇類型"
          class="!w-32">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select
          v-model="filters.status"
          clearable
          placeholder="選擇狀態"
          class="!w-24">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select
          v-model="filters.priority"
          clearable
          placeholder="選擇優先級"
          class="!w-24">
          <el-option
            v-for="item in priorityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select
          v-model="filters.category"
          clearable
          placeholder="選擇分類"
          class="!w-24">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-input
          v-model="filters.search"
          placeholder="搜尋問題"
          clearable
          class="!w-48">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button
          type="info"
          plain
          @click="handleSearch"
          >搜尋</el-button
        >
        <el-button
          type="info"
          plain
          @click="resetFilters"
          >重置</el-button
        >
        <el-button
          type="success"
          @click="handleCreateIssue">
          <el-icon><Plus /></el-icon>新增問題工單
        </el-button>
      </div>
    </Teleport>
    <div class="p-0">
      <el-table
        v-loading="loading"
        :data="issues"
        border
        style="width: 100%"
        @row-click="handleRowClick">
        <el-table-column
          type="index"
          width="60" />
        <el-table-column
          label="分類"
          align="center"
          width="80">
          <template #default="{ row }">
            <el-tag type="info">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="截圖"
          min-width="100"
          align="center">
          <template #default="{ row }">
            <div class="image-list flex flex-wrap gap-2 justify-center">
              <template v-if="row.screenshot">
                <div
                  v-for="(filename, index) in row.screenshot
                    .split(',')
                    .filter(Boolean)"
                  :key="index"
                  class="image-item">
                  <el-image
                    :src="`${uploadBaseUrl}/uploads/issue/${filename}`"
                    fit="cover"
                    :preview-src-list="[]"
                    class="w-20 h-20 object-cover rounded border border-gray-200">
                    <template #error>
                      <div
                        class="image-error flex items-center justify-center w-20 h-20 bg-gray-100 text-gray-400 text-xs">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
              </template>
              <div
                v-else
                class="no-image flex items-center justify-center w-20 h-20 bg-gray-50 text-gray-400 text-xs rounded">
                無圖片
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="標題"
          prop="title"
          min-width="300">
          <template #default="{ row }">
            <h3 class="text-lg font-bold">{{ row.title }}</h3>
            <p />
            <div
              class="markdown-content text-xs text-gray-500"
              v-html="renderMarkdown(row.content)" />
          </template>
        </el-table-column>
        <el-table-column
          label="系統"
          align="center"
          width="150">
          <template #default="{ row }">
            <div class="flex flex-col items-center space-y-2">
              <el-tag :type="getTypeType(row.type)">
                {{ getTypeText(row.type) }}
              </el-tag>
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
              <el-tag :type="getPriorityType(row.priority)">
                {{ getPriorityText(row.priority) }}
              </el-tag>
              <div>
                {{ formatTimestamp(row.createdAt) }}
              </div>
              <UserAvatar
                :user="row.reporter"
                :size="24" />
              <span>{{ row.reporter?.username || "未知用戶" }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- <el-table-column
          label="類型"
          align="center"
          width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeType(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="狀態"
          align="center"
          width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="優先級"
          align="center"
          width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="建立者"
          align="center"
          width="150">
          <template #default="{ row }">
            <div class="user-info">
              <UserAvatar
                :user="row.reporter"
                :size="24" />
              <span>{{ row.reporter?.username || "未知用戶" }}</span>
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="指派給"
          align="center"
          width="150">
          <template #default="{ row }">
            <div
              class="user-info"
              v-if="row.assignedTo">
              <el-avatar
                v-if="row.assignedTo?.avatar"
                :src="row.assignedTo.avatar"
                :size="24" />
              <el-avatar
                v-else
                :size="24"
                :icon="UserFilled" />
              <span>{{ row.assignedTo?.username || "未分配" }}</span>
            </div>
            <span v-else>未分配</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="建立日期"
          align="center"
          width="180">
          <template #default="{ row }">
            {{ formatTimestamp(row.createdAt) }}
          </template>
        </el-table-column> -->
        <el-table-column
          label="操作"
          width="250"
          fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                @click.stop="handleViewIssue(row)"
                >查看</el-button
              >
              <el-button
                size="small"
                type="primary"
                @click.stop="handleEditIssue(row)"
                >編輯</el-button
              >
              <el-button
                size="small"
                type="danger"
                @click.stop="handleDeleteIssue(row)"
                >刪除</el-button
              >
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="mt-0 p-2 flex justify-center">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange" />
      </div>

      <!-- 新增/編輯問題工單對話框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '編輯問題工單' : '新增問題工單'"
        width="40%"
        top="5vh"
        :close-on-click-modal="false"
        draggable="true"
        destroy-on-close>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="issue-form">
          <el-form-item
            label="標題"
            prop="title">
            <el-input
              v-model="form.title"
              placeholder="請輸入問題標題" />
          </el-form-item>
          <el-form-item
            label="內容"
            prop="content">
            <div class="mb-2 w-full">
              <el-tabs v-model="contentTab">
                <el-tab-pane
                  label="編輯"
                  name="edit">
                  <div
                    class="markdown-toolbar flex items-center space-x-2 mb-2 p-1 border-b">
                    <el-tooltip
                      content="標題"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('header')">
                        <span class="font-bold">H</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="粗體"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('bold')">
                        <span class="font-bold">B</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="斜體"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('italic')">
                        <span class="italic">I</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="程式碼區塊"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('code')">
                        <span class="font-mono">{}</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="無序列表"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('list')">
                        <span>• •</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="有序列表"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('orderedList')">
                        <span>1. 2.</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="引用"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('quote')">
                        <span>"</span>
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="連結"
                      placement="top">
                      <el-button
                        size="small"
                        @click="insertMarkdown('link')">
                        <span>🔗</span>
                      </el-button>
                    </el-tooltip>
                  </div>
                  <el-input
                    v-model="form.content"
                    type="textarea"
                    :rows="6"
                    maxlength="1000"
                    show-word-limit
                    placeholder="請輸入問題內容（支援 Markdown 語法）" />
                </el-tab-pane>
                <el-tab-pane
                  label="預覽"
                  name="preview">
                  <div
                    class="markdown-preview p-3 border rounded min-h-[120px] max-h-[300px] overflow-auto">
                    <div v-html="renderedContent"></div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="類型"
                prop="type">
                <el-select
                  v-model="form.type"
                  placeholder="選擇類型">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分類"
                prop="category">
                <el-select
                  v-model="form.category"
                  placeholder="選擇分類">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="狀態"
                prop="status">
                <el-select
                  v-model="form.status"
                  placeholder="選擇狀態">
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="優先級"
                prop="priority">
                <el-select
                  v-model="form.priority"
                  placeholder="選擇優先級">
                  <el-option
                    v-for="item in priorityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="截止日期">
                <el-date-picker
                  v-model="form.dueDate"
                  type="date"
                  placeholder="選擇截止日期" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="截圖"
            prop="screenshot">
            <el-upload
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              multiple
              list-type="picture-card">
              <el-icon><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              @click="submitForm"
              :loading="submitting">
              {{ isEdit ? "更新" : "建立" }}
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 問題詳情抽屜 -->
      <el-drawer
        v-model="drawerVisible"
        title="問題工單詳情"
        size="50%"
        destroy-on-close>
        <template v-if="currentIssue">
          <div class="issue-detail">
            <h2 class="issue-title">{{ currentIssue.title }}</h2>
            <div class="issue-meta text-xs">
              <div class="meta-item">
                <span class="meta-label">狀態:</span>
                <el-tag :type="getStatusType(currentIssue.status)">
                  {{ getStatusText(currentIssue.status) }}
                </el-tag>
              </div>
              <div class="meta-item">
                <span class="meta-label">優先級:</span>
                <el-tag :type="getPriorityType(currentIssue.priority)">
                  {{ getPriorityText(currentIssue.priority) }}
                </el-tag>
              </div>
              <div class="meta-item">
                <span class="meta-label">分類:</span>
                <el-tag type="info">{{
                  getCategoryText(currentIssue.category)
                }}</el-tag>
              </div>
              <div class="meta-item">
                <span class="meta-label">建立日期:</span>
                <span>{{ formatTimestamp(currentIssue.createdAt) }}</span>
              </div>
              <div
                class="meta-item"
                v-if="currentIssue.dueDate">
                <span class="meta-label">截止日期:</span>
                <span>{{ formatTimestamp(currentIssue.dueDate) }}</span>
              </div>
            </div>

            <div class="issue-creators text-xs">
              <div class="creator-info">
                <span class="meta-label">建立者:</span>
                <div class="user-info">
                  <UserAvatar
                    :user="currentIssue.reporter"
                    :size="32" />

                  <span>{{
                    currentIssue.reporter?.username || "未知用戶"
                  }}</span>
                </div>
              </div>
              <!-- <div
                class="creator-info"
                v-if="currentIssue.assignedTo">
                <span class="meta-label">指派給:</span>
                <div class="user-info">
                  <el-avatar
                    v-if="currentIssue.assignedTo?.avatar"
                    :src="currentIssue.assignedTo.avatar"
                    :size="32" />
                  <el-avatar
                    v-else
                    :size="32"
                    :icon="UserFilled" />
                  <span>{{ currentIssue.assignedTo?.username }}</span>
                </div>
              </div> -->
            </div>

            <div
              class="issue-screenshot"
              v-if="currentIssue.screenshot">
              <h3>截圖</h3>
              <div class="image-grid grid grid-cols-2 md:grid-cols-3 gap-4">
                <div
                  v-for="(filename, index) in currentIssue.screenshot
                    .split(',')
                    .filter(Boolean)"
                  :key="index"
                  class="image-item">
                  <el-image
                    :src="`${uploadBaseUrl}/uploads/issue/${filename}`"
                    :preview-src-list="
                      currentIssue.screenshot
                        .split(',')
                        .filter(Boolean)
                        .map((name) => `${uploadBaseUrl}/uploads/issue/${name}`)
                    "
                    style="width: 100%; max-height: 200px; z-index: 1000"
                    fit="contain"
                    class="rounded border border-gray-200" />
                </div>
              </div>
              <div
                v-html="renderMarkdown(currentIssue.content)"
                class="text-sm mt-4"></div>
            </div>
          </div>
        </template>
      </el-drawer>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, UserFilled, Picture } from "@element-plus/icons-vue";
import { formatTimestamp } from "@/utils/dateUtils";
import UserAvatar from "@/components/UserAvatar.vue";
import {
  getIssues,
  getIssueById,
  createIssue as apiCreateIssue,
  updateIssue as apiUpdateIssue,
  deleteIssue as apiDeleteIssue,
  uploadScreenshot,
} from "@/api/modules/issue";
import { getUploadBaseUrl } from "@/utils/url";
import { renderMarkdown } from "@/utils/markdown";

const uploadBaseUrl = getUploadBaseUrl();

// 引用
const formRef = ref(null);
const fileList = ref([]);
const currentId = ref(null);

// 狀態
const dialogVisible = ref(false);
const drawerVisible = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const submitting = ref(false);

// 目前查看的問題
const currentIssue = ref(null);

// 數據
const issues = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 過濾條件
const filters = reactive({
  type: "",
  status: "",
  priority: "",
  category: "",
  search: "",
});

// 表單數據
const form = reactive({
  title: "",
  content: "",
  type: "bug",
  category: "frontend",
  priority: "medium",
  status: "open",
  systemCode: "IYM",
  screenshot: [],
  dueDate: null,
});

// 表單規則
const rules = {
  title: [{ required: true, message: "請輸入標題", trigger: "blur" }],
  content: [{ required: true, message: "請輸入內容", trigger: "blur" }],
  type: [{ required: true, message: "請選擇類型", trigger: "change" }],
  category: [{ required: true, message: "請選擇分類", trigger: "change" }],
  priority: [{ required: true, message: "請選擇優先級", trigger: "change" }],
};

// 選項
const statusOptions = [
  { value: "open", label: "待處理" },
  { value: "in_progress", label: "處理中" },
  { value: "resolved", label: "已解決" },
  { value: "closed", label: "已關閉" },
];

const priorityOptions = [
  { value: "low", label: "低" },
  { value: "medium", label: "中" },
  { value: "high", label: "高" },
  { value: "critical", label: "緊急" },
];

const categoryOptions = [
  { value: "frontend", label: "前端" },
  { value: "backend", label: "後端" },
  { value: "analysis", label: "分析" },
];

const typeOptions = [
  { value: "bug", label: "錯誤" },
  { value: "feature", label: "功能" },
  { value: "task", label: "任務" },
];

// 內容標籤頁
const contentTab = ref("edit");

// 在當前位置插入 Markdown 語法
const insertMarkdown = (type) => {
  const textarea = document.querySelector('textarea[maxlength="1000"]');
  if (!textarea) return;

  const { selectionStart, selectionEnd } = textarea;
  const selectedText = form.content.substring(selectionStart, selectionEnd);

  let insertion = "";
  switch (type) {
    case "header":
      insertion = `## ${selectedText || "標題"}`;
      break;
    case "bold":
      insertion = `**${selectedText || "粗體文字"}**`;
      break;
    case "italic":
      insertion = `*${selectedText || "斜體文字"}*`;
      break;
    case "code":
      insertion = selectedText
        ? "```\n" + selectedText + "\n```"
        : "```\n程式碼區塊\n```";
      break;
    case "list":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `- ${line}`)
            .join("\n")
        : "- 項目1\n- 項目2\n- 項目3";
      break;
    case "orderedList":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line, index) => `${index + 1}. ${line}`)
            .join("\n")
        : "1. 項目1\n2. 項目2\n3. 項目3";
      break;
    case "quote":
      insertion = selectedText
        ? selectedText
            .split("\n")
            .map((line) => `> ${line}`)
            .join("\n")
        : "> 引用文字";
      break;
    case "link":
      insertion = `[${selectedText || "連結標題"}](https://example.com)`;
      break;
  }

  form.content =
    form.content.substring(0, selectionStart) +
    insertion +
    form.content.substring(selectionEnd);

  // 聚焦並將游標定位到適當位置
  setTimeout(() => {
    textarea.focus();
    const newPosition = selectionStart + insertion.length;
    textarea.setSelectionRange(newPosition, newPosition);
  }, 0);
};

// 渲染 Markdown 內容
const renderedContent = computed(() => {
  return renderMarkdown(form.content || "");
});

// 初始化
onMounted(async () => {
  await fetchIssues();
});

// 獲取問題列表
async function fetchIssues() {
  loading.value = true;
  try {
    const response = await getIssues({
      page: currentPage.value,
      size: pageSize.value,
      ...filters,
    });

    console.log("API 回傳結果：", response);

    // 處理回傳結果，根據 API 回傳的格式設置資料
    issues.value = Array.isArray(response) ? response : response.data || [];
    total.value = Array.isArray(response)
      ? response.length
      : response.total || issues.value.length;

    console.log("設置後的 issues：", issues.value);
  } catch (error) {
    console.error("獲取問題列表失敗", error);
    ElMessage.error("獲取問題列表失敗");
  } finally {
    loading.value = false;
  }
}

// 處理建立問題
function handleCreateIssue() {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
}

// 處理編輯問題
function handleEditIssue(row) {
  isEdit.value = true;
  currentId.value = row.id;
  Object.assign(form, {
    title: row.title,
    content: row.content,
    type: row.type,
    category: row.category,
    priority: row.priority,
    status: row.status,
    systemCode: row.systemCode || "IYM",
    dueDate: row.dueDate ? new Date(row.dueDate) : null,
  });

  if (row.screenshot) {
    const screenshots = row.screenshot.split(",").filter(Boolean);
    fileList.value = screenshots.map((filename) => ({
      name: filename,
      url: `${uploadBaseUrl}/uploads/issue/${filename}`,
    }));
    form.screenshot = screenshots;
  } else {
    fileList.value = [];
    form.screenshot = [];
  }

  dialogVisible.value = true;
}

// 處理查看問題
async function handleViewIssue(row) {
  try {
    loading.value = true;
    const response = await getIssueById(row.id);
    currentIssue.value = response.data || response;
    drawerVisible.value = true;
  } catch (error) {
    console.error("獲取問題詳情失敗", error);
    ElMessage.error("獲取問題詳情失敗");
  } finally {
    loading.value = false;
  }
}

// 處理刪除問題
function handleDeleteIssue(row) {
  ElMessageBox.confirm("確定要刪除此問題工單嗎？此操作不可恢復！", "警告", {
    confirmButtonText: "確定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        submitting.value = true;
        await apiDeleteIssue(row.id);
        ElMessage.success("問題工單刪除成功");
        await fetchIssues();
      } catch (error) {
        console.error("刪除問題失敗", error);
        ElMessage.error("刪除問題失敗");
      } finally {
        submitting.value = false;
      }
    })
    .catch(() => {});
}

// 處理搜索
function handleSearch() {
  currentPage.value = 1;
  fetchIssues();
}

// 重置過濾器
function resetFilters() {
  Object.keys(filters).forEach((key) => {
    filters[key] = "";
  });
  currentPage.value = 1;
  fetchIssues();
}

// 處理頁面變化
function handlePageChange(page) {
  currentPage.value = page;
  fetchIssues();
}

// 處理文件變化
async function handleFileChange(file) {
  if (!file.raw) return;

  try {
    // 檢查文件類型
    const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
    if (!allowedTypes.includes(file.raw.type)) {
      ElMessage.error("只支援 JPG、PNG、GIF 格式的圖片");
      const index = fileList.value.indexOf(file);
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }
      return;
    }

    // 檢查文件大小（10MB）
    const maxSize = 10 * 1024 * 1024;
    if (file.raw.size > maxSize) {
      ElMessage.error("檔案大小不能超過 10MB");
      const index = fileList.value.indexOf(file);
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }
      return;
    }

    // 將新文件添加到待上傳列表
    if (!form.screenshot) {
      form.screenshot = [];
    }
    if (typeof form.screenshot === "string") {
      form.screenshot = form.screenshot.split(",").filter(Boolean);
    }
    form.screenshot.push(file.raw);
  } catch (error) {
    console.error("處理文件變化失敗：", error);
    ElMessage.error("處理文件變化失敗");
  }
}

// 處理文件移除
function handleFileRemove(file) {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    if (Array.isArray(form.screenshot)) {
      form.screenshot.splice(index, 1);
    } else if (typeof form.screenshot === "string") {
      const screenshots = form.screenshot.split(",").filter(Boolean);
      screenshots.splice(index, 1);
      form.screenshot = screenshots.join(",");
    }
  }
}

// 提交表單
async function submitForm() {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;
    try {
      let uploadedFiles = [];

      // 處理新上傳的文件
      if (Array.isArray(form.screenshot)) {
        for (const file of form.screenshot) {
          if (file instanceof File) {
            const response = await uploadScreenshot(file);
            uploadedFiles.push(response.data.filename);
          } else {
            uploadedFiles.push(file);
          }
        }
      }

      // 更新表單中的 screenshot 字段為逗號分隔的字符串
      form.screenshot = uploadedFiles.join(",");

      if (isEdit.value) {
        // 更新問題
        if (!currentId.value) {
          ElMessage.error("無法識別要更新的問題");
          return;
        }

        const response = await apiUpdateIssue(currentId.value, form);
        ElMessage.success("問題工單更新成功");

        // 更新本地數據
        const index = issues.value.findIndex(
          (item) => item.id === currentId.value
        );
        if (index !== -1) {
          issues.value[index] = response.data || response;
        }
      } else {
        // 建立問題
        await apiCreateIssue(form);
        ElMessage.success("問題工單創建成功");
      }
      dialogVisible.value = false;
      await fetchIssues();
    } catch (error) {
      console.error("提交表單失敗", error);
      ElMessage.error("提交表單失敗");
    } finally {
      submitting.value = false;
    }
  });
}

// 重置表單
function resetForm() {
  currentId.value = null;
  Object.assign(form, {
    title: "",
    content: "",
    type: "bug",
    category: "frontend",
    priority: "medium",
    status: "open",
    systemCode: "IYM",
    screenshot: [],
    dueDate: null,
  });
  fileList.value = [];
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}

// 處理行點擊
function handleRowClick(row) {
  handleViewIssue(row);
}

// 獲取狀態類型
function getStatusType(status) {
  const types = {
    open: "danger",
    in_progress: "warning",
    resolved: "success",
    closed: "info",
  };
  return types[status] || "info";
}

// 獲取狀態文本
function getStatusText(status) {
  const texts = {
    open: "待處理",
    in_progress: "處理中",
    resolved: "已解決",
    closed: "已關閉",
  };
  return texts[status] || status;
}

// 獲取優先級類型
function getPriorityType(priority) {
  const types = {
    low: "info",
    medium: "warning",
    high: "danger",
    critical: "danger",
  };
  return types[priority] || "info";
}

// 獲取優先級文本
function getPriorityText(priority) {
  const texts = {
    low: "低",
    medium: "中",
    high: "高",
    critical: "緊急",
  };
  return texts[priority] || priority;
}

// 獲取分類文本
function getCategoryText(category) {
  const texts = {
    frontend: "前端",
    backend: "後端",
    analysis: "分析",
  };
  return texts[category] || category;
}

// 獲取類型類型
function getTypeType(type) {
  const types = {
    bug: "danger",
    feature: "primary",
    task: "warning",
  };
  return types[type] || "info";
}
// 獲取類型文本
function getTypeText(type) {
  const texts = {
    bug: "錯誤",
    feature: "功能",
    task: "任務",
  };
  return texts[type] || type;
}
</script>

<style scoped>
.issue-list-container {
  padding: 20px;
}

.issue-list-header {
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.filter-section {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-lighter);
}

.text-right {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.issue-form {
  margin-top: 20px;
}

:deep(.el-card) {
  border-radius: 8px;

  .el-card__body {
    padding: 20px;
  }
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-upload--picture-card) {
  width: 150px;
  height: 150px;
  line-height: 150px;
  border-radius: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-image-viewer__wrapper) {
  z-index: 2050 !important; /* 確保圖片預覽在最上層 */
}

:deep(.el-image__preview) {
  cursor: pointer;
}

:deep(.el-image) {
  position: relative;
  z-index: 1;
}
:deep(.el-drawer__header) {
  margin-bottom: 0px;
}
.issue-detail {
  padding: 5px;

  .issue-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }

  .issue-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 20px;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .meta-label {
        font-weight: 500;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .issue-creators {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;

    .creator-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .issue-content {
    padding: 20px;
    background-color: var(--el-bg-color-page);
    border-radius: 8px;
    margin-bottom: 24px;
    min-height: 100px;

    font-size: 16px;
  }

  .issue-screenshot {
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--el-bg-color-page);
    padding: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

:deep(.markdown-content) {
  font-size: 0.75rem;
  line-height: 1.4;
  color: #666;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin: 0.5em 0;
  font-size: 0.875rem;
  font-weight: bold;
}

:deep(.markdown-content p) {
  margin: 0.5em 0;
}

:deep(.markdown-content ol),
:deep(.markdown-content ul) {
  margin: 0.5em 0;
  padding-left: 1.5em;
  list-style-position: outside;
}

:deep(.markdown-content ol) {
  list-style-type: decimal;
}

:deep(.markdown-content ul) {
  list-style-type: disc;
}

:deep(.markdown-content li) {
  margin: 0.25em 0;
  padding-left: 0.25em;
}

:deep(.markdown-content pre) {
  margin: 0.5em 0;
  padding: 0.5em;
  background-color: #f6f8fa;
  border-radius: 4px;
  font-size: 0.75rem;
}

:deep(.markdown-content code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
  font-size: 0.75rem;
  padding: 0.1em 0.2em;
  background-color: #f6f8fa;
  border-radius: 3px;
}

:deep(.markdown-content blockquote) {
  margin: 0.5em 0;
  padding: 0 0.5em;
  border-left: 2px solid #ddd;
  color: #666;
}

:deep(.markdown-content *:first-child) {
  margin-top: 0;
}

:deep(.markdown-content *:last-child) {
  margin-bottom: 0;
}

.markdown-preview {
  background-color: #fafafa;
}

.markdown-preview :deep(.markdown-content) {
  font-size: 14px;
}

.markdown-preview :deep(h1) {
  font-size: 1.8em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h2) {
  font-size: 1.5em;
  margin: 0.5em 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-preview :deep(h3) {
  font-size: 1.3em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h4) {
  font-size: 1.1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(h5),
.markdown-preview :deep(h6) {
  font-size: 1em;
  margin: 0.5em 0;
}

.markdown-preview :deep(pre) {
  margin: 0.5em 0;
  padding: 0.5em;
  border-radius: 4px;
}

.markdown-preview :deep(code) {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
}

.markdown-preview :deep(p) {
  margin: 0.5em 0;
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
  list-style-position: outside;
}

.markdown-preview :deep(ol) {
  list-style-type: decimal;
}

.markdown-preview :deep(ol) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(ul) {
  list-style-type: disc;
}

.markdown-preview :deep(ul) li {
  margin-left: 1em;
  padding-left: 0.5em;
}

.markdown-preview :deep(table) {
  border-collapse: collapse;
  margin: 0.5em 0;
}

.markdown-preview :deep(th),
.markdown-preview :deep(td) {
  border: 1px solid #ddd;
  padding: 0.4em 0.6em;
}

.markdown-preview :deep(blockquote) {
  margin: 0.5em 0;
  padding: 0.2em 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.markdown-preview :deep(hr) {
  margin: 0.5em 0;
  border: none;
  border-top: 1px solid #ddd;
}

.markdown-preview :deep(img) {
  max-width: 100%;
}

.image-list {
  padding: 4px;
}

.image-item {
  position: relative;
}

.image-item :deep(.el-image) {
  border: 1px solid #eee;
}

:deep(.el-image-viewer__wrapper) {
  z-index: 2100;
}
</style>
