<template>
  <div
    class="p-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-2">
    <!-- 新增專案卡片 -->
    <div
      class="dark:bg-[#262626] rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-[#b2b2b2] cursor-pointer p-6 flex flex-col items-center justify-center min-h-[200px] transition-colors duration-200 border-t-[3px] border-t-gray-300 dark:border-t-gray-600"
      @click="handleCreateProject">
      <Plus
        :size="32"
        class="text-gray-400 dark:text-dark-mode" />
      <span class="mt-2 text-gray-600 dark:text-dark-mode text-sm"
        >新增專案</span
      >
    </div>

    <!-- 專案卡片列表 -->
    <div
      v-for="project in filteredProjects"
      :key="project.id"
      class="bg-light-mode dark:bg-dark-mode border dark:border-dark-mode rounded-lg shadow-md hover:shadow-lg hover:border-[#cecece] dark:hover:border-[#5e5e5e] cursor-pointer"
      @click="handleViewProject(project)">
      <div class="p-3">
        <div class="flex items-start justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              <el-tag
                :type="getStatusType(project.status)"
                size="small">
                {{ getStatusText(project.status) }}
              </el-tag>
              {{ project.name }}
            </h3>
            <p
              class="mt-2 text-sm text-gray-600 dark:text-dark-mode inline-block max-w-[30ch] truncate whitespace-nowrap overflow-hidden">
              {{ project.description }}
            </p>
          </div>
          <div class="flex items-center">
            <!-- 關注按鈕 -->
            <div
              class="mr-2"
              @click.stop="toggleFavorite(project)">
              <Star
                :size="20"
                :class="[
                  isProjectFavorited(project)
                    ? 'text-yellow-400 dark:text-yellow-400 fill-yellow-400'
                    : 'text-gray-400 dark:text-gray-400',
                  'cursor-pointer hover:text-yellow-500 dark:hover:text-yellow-500',
                ]" />
            </div>
            <!-- 更多選項下拉選單 -->
            <div @click.stop>
              <el-dropdown trigger="hover">
                <MoreVertical
                  :size="20"
                  class="text-gray-400 dark:text-dark-mode cursor-pointer hover:text-gray-600 dark:hover:text-gray-100" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleEditProject(project)">
                      <Pencil
                        :size="16"
                        class="mr-1" />
                      編輯
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-admin
                      divided
                      @click="handleDeleteProject(project)"
                      class="!text-red-500">
                      <Trash2
                        :size="16"
                        class="mr-1" />
                      刪除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div class="mt-4 flex justify-between">
          <div
            class="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
            <!-- <UserAvatar
              :user="project.creator"
              :show-name="true" /> -->
            <Workflow
              :size="16"
              class="mr-2" />

            <span v-if="project.instanceNames == '尚未使用流程'">
              <span class="text-gray-300 dark:text-gray-500">尚未使用流程</span>
            </span>

            <el-tooltip
              v-if="project.instanceNames != '尚未使用流程'"
              :content="project.instanceNames"
              effect="light"
              placement="top">
              <div class="text-xs text-blue-500 dark:text-blue-400">
                {{ project.instanceNames.split(",").length }} 個分析流程
              </div>
            </el-tooltip>
          </div>
          <div
            class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-2">
            <Calendar
              :size="20"
              class="mr-2" />
            <span class="text-xs">
              {{
                formatTimestamp(project.updatedAt || project.createdAt)
              }}</span
            >
          </div>
          <!-- 添加專案號碼 -->
          <!-- <div class="flex items-center gap-2 mt-2">
            <div
              v-if="isAdmin && project.systemCode"
              class="text-xs text-blue-500 rounded-sm bg-slate-100 p-1">
              {{ project.systemCode }}
            </div>
            <span
              v-if="project.projectNumber || project.id"
              class="text-xs font-semibold text-gray-500">
              {{ project.projectNumber }}</span
            >
          </div> -->
        </div>

        <div class="mt-2 flex items-center justify-between">
          <UserAvatar
            :user="project.creator"
            :show-name="true" />
          <el-button
            type="primary"
            icon="play-circle"
            @click.stop="handleViewProject(project)">
            開啟專案
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 引入圖標
import { Calendar, User, MoreVertical, Workflow, Star } from "lucide-vue-next";
import { formatTimestamp } from "@/utils/dateUtils";
import UserAvatar from "@/components/UserAvatar.vue";
import { useUserStore } from "@/stores/user";

// 定義 props
const props = defineProps({
  projects: {
    type: Array,
    required: true,
    default: () => [],
  },
  filterStatus: {
    type: String,
    default: "",
  },
  //TODO: TO BE REMOVED, userStore.isAdmin 可直接取代
  isAdmin: {
    type: Boolean,
    default: false,
  },
  favoriteStore: {
    type: Object,
    required: true,
  },
  showFavoriteOnly: {
    type: Boolean,
    default: false,
  },
});

// 用戶存儲
const userStore = useUserStore();

// 定義 emits
const emit = defineEmits([
  "create-project",
  "view-project",
  "edit-project",
  "delete-project",
]);

// 根據狀態和關注篩選專案
const filteredProjects = computed(() => {
  let filtered = props.projects;

  // 先篩選狀態
  if (props.filterStatus) {
    filtered = filtered.filter(
      (project) => project.status === props.filterStatus
    );
  }

  // 如果開啟了只顯示關注，則進一步篩選
  if (props.showFavoriteOnly) {
    filtered = filtered.filter((project) => isProjectFavorited(project));
  }

  return filtered;
});

// 判斷專案是否已關注
const isProjectFavorited = (project) => {
  return props.favoriteStore.isFavorited("project", project.id);
};

// 切換關注狀態
const toggleFavorite = async (project) => {
  if (!userStore.user?.id) {
    ElMessage.warning("請先登入");
    return;
  }

  if (isProjectFavorited(project)) {
    // 如果已關注，則移除關注
    await props.favoriteStore.removeFromFavorite("project", project.id);
  } else {
    // 如果未關注，則添加關注
    await props.favoriteStore.addToFavorite({
      type: "project",
      resourceId: project.id,
      name: project.name,
      path: `/projects/${project.id}`,
      createdBy: userStore.user.id,
    });
  }
};

// 獲取狀態標籤類型
const getStatusType = (status) => {
  const types = {
    draft: "info",
    active: "warning",
    completed: "success",
    cancelled: "danger",
  };
  return types[status] || "info";
};

// 獲取狀態文字
const getStatusText = (status) => {
  const texts = {
    draft: "草稿",
    active: "進行中",
    completed: "已完成",
    cancelled: "已取消",
  };
  return texts[status] || status;
};

// 處理創建專案
const handleCreateProject = () => {
  emit("create-project");
};

// 處理查看專案
const handleViewProject = (project) => {
  emit("view-project", project);
};

// 處理編輯專案
const handleEditProject = (project) => {
  emit("edit-project", project);
};

// 處理刪除專案
const handleDeleteProject = (project) => {
  emit("delete-project", project);
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 專案卡片狀態邊框 */
.bg-white {
  border-top: 3px solid var(--el-color-info);
  transform-origin: center;
  backface-visibility: hidden;
  will-change: transform;
}

/* 草稿狀態 */
.bg-light-mode:has(.el-tag--info) {
  border-top-width: 2px;
  border-top-color: var(--el-color-info);
}

/* 進行中狀態 */
.bg-light-mode:has(.el-tag--warning) {
  border-top-width: 2px;
  border-top-color: var(--el-color-warning);
}

/* 已完成狀態 */
.bg-light-mode:has(.el-tag--success) {
  border-top-width: 2px;
  border-top-color: var(--el-color-success);
}

/* 已取消狀態 */
.bg-light-mode:has(.el-tag--danger) {
  border-top-width: 2px;
  border-top-color: var(--el-color-danger);
}

/* 深色模式下的各種狀態 */
html.dark .bg-light-mode:has(.el-tag--info) {
  border-top-width: 2px;
  border-top-color: var(--el-color-info-light-5);
}

html.dark .bg-light-mode:has(.el-tag--warning) {
  border-top-width: 2px;
  border-top-color: var(--el-color-warning-light-5);
}

html.dark .bg-light-mode:has(.el-tag--success) {
  border-top-width: 2px;
  border-top-color: var(--el-color-success-light-5);
}

html.dark .bg-light-mode:has(.el-tag--danger) {
  border-top-color: var(--el-color-danger-light-5);
}

/* 專案卡片動畫效果 */
.transform {
  transform-origin: center;
  backface-visibility: hidden;
  will-change: transform;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 移除原有的旋轉效果，改用放大效果 */
.hover\:scale-105:hover {
  transform: scale(1.05);
  z-index: 10;
}

/* 確保卡片懸停時顯示在其他卡片上方 */
.bg-white {
  position: relative;
}
</style>
