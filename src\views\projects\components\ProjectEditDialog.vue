<template>
  <el-dialog
    :title="isEdit ? '編輯專案' : '新增專案'"
    v-model="dialogVisible"
    width="500px"
    class="project-edit-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="top">
      <el-form-item
        label="專案名稱"
        prop="name">
        <el-input
          v-model="formData.name"
          placeholder="請輸入專案名稱" />
      </el-form-item>
      <el-form-item
        label="專案描述"
        prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="請輸入專案描述" />
      </el-form-item>
      <el-form-item
        label="專案狀態"
        prop="status">
        <el-select
          v-model="formData.status"
          placeholder="請選擇專案狀態"
          style="width: 100%">
          <!-- <el-option
            label="草稿"
            value="draft" /> -->
          <el-option
            label="進行中"
            value="active" />
          <el-option
            label="已完成"
            value="completed" />
          <el-option
            label="已取消"
            value="cancelled" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit">
          確定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
// 定義 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  project: {
    type: Object,
    default: () => ({
      id: "",
      name: "",
      description: "",
      status: "draft",
    }),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

// 定義 emits
const emit = defineEmits(["update:modelValue", "submit", "cancel"]);

// 表單相關
const formRef = ref(null);
const formData = ref({
  id: "",
  name: "",
  description: "",
  status: "draft",
});

// 表單驗證規則
const rules = {
  name: [{ required: true, message: "請輸入專案名稱", trigger: "blur" }],
  description: [{ required: true, message: "請輸入專案描述", trigger: "blur" }],
  status: [{ required: true, message: "請選擇專案狀態", trigger: "change" }],
};

// 對話框可見性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 初始化表單數據
const initFormData = () => {
  if (props.isEdit && props.project && Object.keys(props.project).length > 0) {
    // 編輯模式，填充表單數據
    formData.value = {
      id: props.project.id || "",
      name: props.project.name || "",
      description: props.project.description || "",
      status: props.project.status || "draft",
    };
  } else {
    // 新增模式，重置表單
    formData.value = {
      id: "",
      name: "",
      description: "",
      status: "draft",
    };
  }
};

// 監聽 project 變化
watch(
  () => props.project,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      formData.value = {
        id: newVal.id || "",
        name: newVal.name || "",
        description: newVal.description || "",
        status: newVal.status || "draft",
      };
    }
  },
  { immediate: true, deep: true }
);

// 監聽 modelValue 變化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 當對話框打開時，初始化表單數據
      initFormData();
    }
  },
  { immediate: true }
);

// 處理取消
const handleCancel = () => {
  emit("update:modelValue", false);
  emit("cancel");
};

// 處理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit("submit", { ...formData.value });
  } catch (error) {
    console.error("表單驗證失敗:", error);
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 深色模式對話框樣式 */
html.dark :deep(.project-edit-dialog .el-dialog) {
  --el-dialog-bg-color: #1e293b !important;
  --el-dialog-text-color: #e2e8f0 !important;
  --el-dialog-border-color: #334155 !important;
}

html.dark :deep(.project-edit-dialog .el-dialog__title) {
  color: #e2e8f0 !important;
}

html.dark :deep(.project-edit-dialog .el-dialog__header) {
  border-bottom-color: #334155 !important;
}

html.dark :deep(.project-edit-dialog .el-dialog__footer) {
  border-top-color: #334155 !important;
}

/* 深色模式表單樣式 */
html.dark :deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

html.dark :deep(.el-input__wrapper) {
  background-color: #334155 !important;
  box-shadow: 0 0 0 1px #475569 inset !important;
}

html.dark :deep(.el-input__inner),
html.dark :deep(.el-textarea__inner) {
  color: #e2e8f0 !important;
  background-color: #334155 !important;
}

html.dark :deep(.el-select .el-input .el-select__caret) {
  color: #e2e8f0 !important;
}

html.dark :deep(.el-select-dropdown) {
  background-color: #1e293b !important;
  border-color: #334155 !important;
}

html.dark :deep(.el-select-dropdown__item) {
  color: #e2e8f0 !important;
}

html.dark :deep(.el-select-dropdown__item.hover),
html.dark :deep(.el-select-dropdown__item:hover) {
  background-color: #334155 !important;
}

html.dark :deep(.el-select-dropdown__item.selected) {
  background-color: #0ea5e9 !important;
  color: #ffffff !important;
}

html.dark :deep(.el-popper__arrow::before) {
  background-color: #1e293b !important;
  border-color: #334155 !important;
}
</style>
