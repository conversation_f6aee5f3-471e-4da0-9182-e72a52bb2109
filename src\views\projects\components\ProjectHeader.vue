<template>
  <Teleport
    to="#header-actions"
    defer>
    <div class="flex items-center space-x-4">
      <!-- 返回按鈕 -->
      <el-button
        plain
        class="flex items-center"
        @click="handleBack">
        <ArrowLeft
          class="mr-1"
          :size="16" />
        返回專案列表
      </el-button>

      <!-- 重整按鈕 -->
      <el-button
        type="default"
        plain
        class="flex items-center"
        :loading="loading"
        @click="handleRefresh">
        <RefreshCw
          class="mr-1"
          :size="16" />
        重整
      </el-button>

      <!-- 新增流程實例按鈕 -->
      <el-button
        type="primary"
        class="flex items-center"
        @click="handleCreateInstance">
        <Plus
          class="mr-1"
          :size="16" />
        新增分析流程
      </el-button>
    </div>
  </Teleport>
</template>

<script setup>
import { ArrowLeft, RefreshCw, Plus } from "lucide-vue-next";

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["back", "refresh", "createInstance"]);

const handleBack = () => {
  emit("back");
};

const handleRefresh = () => {
  emit("refresh");
};

const handleCreateInstance = () => {
  emit("createInstance");
};
</script>

<style scoped>
/* 深色模式按鈕樣式 - 已移至全局 style.css */
</style>
