<template>
  <div
    class="bg-light-mode dark:bg-dark-mode p-2 border-b border-gray-200 dark:border-gray-700">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">
        📌 分析流程列表
      </h3>
      <div class="flex items-center space-x-2">
        <el-select
          v-model="filterStatus"
          placeholder="篩選狀態"
          clearable
          size="small"
          class="!w-24">
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value">
            <el-tag
              :type="status.tagType"
              size="small">
              {{ status.label }}
            </el-tag>
          </el-option>
        </el-select>
      </div>
    </div>

    <el-table
      :data="filteredInstances"
      v-loading="loading"
      style="width: 100%"
      class="custom-table">
      <el-table-column
        type="index"
        label="序號"
        width="80" />
      <el-table-column
        prop="template.name"
        label="模板名稱"
        min-width="120" />
      <el-table-column
        label="狀態"
        width="120"
        align="center">
        <template #default="{ row }">
          <el-tag :type="getInstanceStatusType(row.status)">
            {{ getInstanceStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="建立者"
        width="150"
        align="center">
        <template #default="{ row }">
          <div class="flex items-center justify-start">
            <UserAvatar
              :user="row.creator"
              :size="24"
              :show-name="true"
              shape="circle"
              class="mr-1" />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        label="建立時間"
        width="180">
        <template #default="{ row }">
          {{ formatTimestamp(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updatedAt"
        label="更新時間"
        width="180">
        <template #default="{ row }">
          {{ formatTimestamp(row.updatedAt) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="240"
        fixed="right">
        <template #default="{ row }">
          <div class="flex items-center space-x-2">
            <!-- 關注按鈕 -->
            <el-tooltip
              :content="isFlowFavorited(row) ? '取消關注' : '加入關注'"
              placement="top">
              <Star
                :size="20"
                :class="[
                  isFlowFavorited(row)
                    ? 'text-yellow-400 dark:text-yellow-400 fill-yellow-400'
                    : 'text-gray-400 dark:text-gray-400',
                  'cursor-pointer hover:text-yellow-500 dark:hover:text-yellow-500',
                ]"
                @click.stop="toggleFavorite(row)" />
            </el-tooltip>

            <el-button-group>
              <el-button
                type="primary"
                size="small"
                @click="handleView(row)">
                查看
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)">
                刪除
              </el-button>
            </el-button-group>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div
      v-if="!loading && instances.length === 0"
      class="text-center py-8 text-gray-500 dark:text-gray-400">
      <FileX
        :size="48"
        class="mx-auto mb-4 text-gray-300 dark:text-gray-600" />
      <p>此專案尚未建立分析流程</p>
      <el-button
        type="primary"
        class="mt-4"
        @click="handleCreate">
        新增分析流程
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { formatTimestamp } from "@/utils/dateUtils";
import { useFavoriteStore } from "@/stores/favorite";
import { useUserStore } from "@/stores/user";
import { Star, FileX } from "lucide-vue-next";
import { ElMessage } from "element-plus";

const favoriteStore = useFavoriteStore();
const userStore = useUserStore();

const props = defineProps({
  instances: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  statusOptions: {
    type: Array,
    default: () => [
      { label: "草稿", value: "draft", tagType: "info" },
      { label: "進行中", value: "active", tagType: "warning" },
      { label: "已完成", value: "completed", tagType: "success" },
      { label: "已取消", value: "cancelled", tagType: "danger" },
    ],
  },
});

const emit = defineEmits(["view", "delete", "create"]);

const filterStatus = ref("");

// 過濾後的實例列表
const filteredInstances = computed(() => {
  if (!filterStatus.value) return props.instances;
  return props.instances.filter(
    (instance) => instance.status === filterStatus.value
  );
});

// 獲取實例狀態標籤類型
const getInstanceStatusType = (status) => {
  const types = {
    draft: "info",
    active: "warning",
    completed: "success",
    cancelled: "danger",
  };
  return types[status] || "info";
};

// 獲取實例狀態文字
const getInstanceStatusText = (status) => {
  const texts = {
    draft: "草稿",
    active: "進行中",
    completed: "已完成",
    cancelled: "已取消",
  };
  return texts[status] || status;
};

const handleView = (instance) => {
  emit("view", instance);
};

const handleDelete = (instance) => {
  emit("delete", instance);
};

const handleCreate = () => {
  emit("create");
};

// 檢查流程實例是否已關注
const isFlowFavorited = (instance) => {
  return favoriteStore.isFavorited("flow", instance.id);
};

// 切換流程實例的關注狀態
const toggleFavorite = async (instance) => {
  if (!userStore.user?.id) {
    ElMessage.warning("請先登入");
    return;
  }

  try {
    if (isFlowFavorited(instance)) {
      await favoriteStore.removeFromFavorite("flow", instance.id);
    } else {
      await favoriteStore.addToFavorite({
        type: "flow",
        resourceId: instance.id,
        name: instance.template?.name || `流程 ${instance.id}`,
        path: `/flow-instances/${instance.id}`,
        createdBy: userStore.user.id,
      });
    }
  } catch (error) {
    console.error("切換關注狀態失敗:", error);
    ElMessage.error("操作失敗，請稍後再試");
  }
};
</script>

<style scoped>
/* 深色模式表格樣式 */
html.dark :deep(.custom-table .el-table) {
  --el-table-header-bg-color: #334155 !important;
  --el-table-header-text-color: #e2e8f0 !important;
  --el-table-row-hover-bg-color: #1e293b !important;
  --el-table-border-color: #334155 !important;
  --el-table-bg-color: #1e293b !important;
  --el-table-tr-bg-color: #1e293b !important;
  --el-table-expanded-cell-bg-color: #1e293b !important;
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
}

html.dark :deep(.custom-table .el-table th),
html.dark :deep(.custom-table .el-table tr),
html.dark :deep(.custom-table .el-table td) {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  border-bottom-color: #334155 !important;
}

html.dark :deep(.custom-table .el-table--border),
html.dark :deep(.custom-table .el-table--border th),
html.dark :deep(.custom-table .el-table--border td) {
  border-color: #334155 !important;
}

html.dark :deep(.custom-table .el-table__row:hover > td) {
  background-color: #334155 !important;
}

/* 按鈕樣式已移至全局 style.css */
</style>
