<template>
  <div
    class="bg-light-mode dark:bg-dark-mode p-2 pb-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">
        📍 專案統計資訊
      </h3>
    </div>

    <div
      v-if="loading"
      class="py-4">
      <el-skeleton
        animated
        :rows="3" />
    </div>
    <div
      v-else
      class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div
        class="relative overflow-hidden rounded-2xl bg-gradient-to-b from-blue-200 via-blue-50 to-blue-100 dark:from-blue-900 dark:via-blue-800 dark:to-blue-900 shadow-sm">
        <div class="relative z-0 p-6">
          <div class="flex flex-col">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-mode">
              分析流程總數
            </p>
            <p
              class="mt-2 text-3xl font-semibold text-blue-600 dark:text-blue-300">
              {{ statistics.totalInstances }}
            </p>
          </div>
          <div class="absolute top-4 right-4">
            <div class="rounded-xl bg-blue-500/10 dark:bg-blue-500/20 p-2">
              <GitBranch
                :size="24"
                class="text-blue-500 dark:text-blue-300" />
            </div>
          </div>
        </div>
      </div>
      <div
        class="relative overflow-hidden rounded-2xl bg-gradient-to-b from-green-200 via-green-50 to-green-100 dark:from-green-900 dark:via-green-800 dark:to-green-900 shadow-sm">
        <div class="relative z-0 p-6">
          <div class="flex flex-col">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-mode">
              已完成分析
            </p>
            <p
              class="mt-2 text-3xl font-semibold text-green-600 dark:text-green-300">
              {{ statistics.completedInstances }}
            </p>
          </div>
          <div class="absolute top-4 right-4">
            <div class="rounded-xl bg-green-500/10 dark:bg-green-500/20 p-2">
              <CheckCircle
                :size="24"
                class="text-green-500 dark:text-green-300" />
            </div>
          </div>
        </div>
      </div>
      <div
        class="relative overflow-hidden rounded-2xl bg-gradient-to-b from-yellow-200 via-yellow-50 to-yellow-100 dark:from-yellow-900 dark:via-yellow-800 dark:to-yellow-900 shadow-sm">
        <div class="relative z-0 p-6">
          <div class="flex flex-col">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-mode">
              進行中分析
            </p>
            <p
              class="mt-2 text-3xl font-semibold text-yellow-600 dark:text-yellow-300">
              {{ statistics.activeInstances }}
            </p>
          </div>
          <div class="absolute top-4 right-4">
            <div class="rounded-xl bg-yellow-500/10 dark:bg-yellow-500/20 p-2">
              <Clock
                :size="24"
                class="text-yellow-500 dark:text-yellow-300" />
            </div>
          </div>
        </div>
      </div>
      <div
        class="relative overflow-hidden rounded-2xl bg-gradient-to-b from-purple-200 via-purple-50 to-purple-100 dark:from-purple-900 dark:via-purple-800 dark:to-purple-900 shadow-sm">
        <div class="relative z-0 p-6">
          <div class="flex flex-col">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-mode">
              文檔總數
            </p>
            <p
              class="mt-2 text-3xl font-semibold text-purple-600 dark:text-purple-300">
              {{ statistics.totalDocuments }}
            </p>
          </div>
          <div class="absolute top-4 right-4">
            <div class="rounded-xl bg-purple-500/10 dark:bg-purple-500/20 p-2">
              <FileText
                :size="24"
                class="text-purple-500 dark:text-purple-300" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  statistics: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>

<style scoped>
.bg-gradient-to-br {
  position: relative;
}

.bg-gradient-to-br::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at top right,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  pointer-events: none;
}

.bg-gradient-to-br:hover::before {
  background: radial-gradient(
    circle at top right,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.4) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.3s ease;
}

/* 深色模式下的漸變光暈效果 */
html.dark .bg-gradient-to-br::before {
  background: radial-gradient(
    circle at top right,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

html.dark .bg-gradient-to-br:hover::before {
  background: radial-gradient(
    circle at top right,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 60%,
    rgba(255, 255, 255, 0) 100%
  );
}
</style>
