<template>
  <div class="p-0">
    <el-table
      :data="filteredProjects"
      style="width: 100%"
      class="custom-table"
      v-loading="loading">
      <el-table-column
        type="index"
        label=""
        width="60" />
      <el-table-column
        prop="status"
        width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="專案名稱"
        min-width="180">
        <template #default="{ row }">
          <div class="flex items-center">
            <!-- <el-tag
              :type="getStatusType(row.status)"
              class="mr-2"
              size="small"
              effect="plain">
              {{ getStatusText(row.status) }}
            </el-tag> -->
            <span class="font-medium dark:text-gray-100">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="description"
        label="描述"
        min-width="250">
        <template #default="{ row }">
          <div class="line-clamp-2 dark:text-dark-mode">
            {{ row.description }}
          </div>
        </template>
      </el-table-column>

      <!-- 添加流程實例名稱列 -->
      <el-table-column
        prop="instanceNames"
        label="流程實例"
        min-width="180">
        <template #default="{ row }">
          <div class="flex items-center">
            <Workflow class="w-4 h-4 mr-1 text-gray-400 dark:text-gray-500" />
            <span
              class="line-clamp-1 text-blue-500 dark:text-blue-400"
              :class="{
                'text-gray-400 dark:text-gray-500':
                  row.instanceNames === '尚未使用流程',
              }"
              :title="row.instanceNames">
              {{ row.instanceNames || "尚未使用流程" }}
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="建立時間"
        min-width="150">
        <template #default="{ row }">
          <div class="flex items-center">
            <!-- <Calendar class="w-4 h-4 mr-1 text-gray-400" /> -->
            📅
            <div class="ml-1 dark:text-dark-mode">
              {{ formatDate(row.createdAt) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="建立者"
        min-width="120">
        <template #default="{ row }">
          <UserAvatar
            :user="row.creator"
            :show-name="true" />
          <!-- <div class="flex items-center">
            <User class="w-4 h-4 mr-1 text-gray-400" />
            <span>{{ row.createdBy?.name || "未知" }}</span>
          </div> -->
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="180"
        fixed="right">
        <template #default="{ row }">
          <div class="flex space-x-2">
            <el-button
              type="primary"
              size="small"
              @click="handleViewProject(row)">
              開啟專案
            </el-button>

            <el-button
              v-if="isAdmin"
              type="warning"
              size="small"
              @click="handleEditProject(row)">
              編輯
            </el-button>

            <el-button
              v-if="isAdmin"
              type="danger"
              size="small"
              @click="handleDeleteProject(row)">
              刪除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import UserAvatar from "@/components/UserAvatar.vue";
import { Calendar, User, MoreVertical, Workflow } from "lucide-vue-next";
// 定義 props
const props = defineProps({
  projects: {
    type: Array,
    required: true,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  filterStatus: {
    type: String,
    default: "",
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
});

// 定義 emits
const emit = defineEmits(["view-project", "edit-project", "delete-project"]);

// 根據狀態篩選專案
const filteredProjects = computed(() => {
  if (!props.filterStatus) return props.projects;
  return props.projects.filter(
    (project) => project.status === props.filterStatus
  );
});

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleDateString("zh-TW", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// 獲取狀態標籤類型
const getStatusType = (status) => {
  const types = {
    draft: "info",
    active: "warning",
    completed: "success",
    cancelled: "danger",
  };
  return types[status] || "info";
};

// 獲取狀態文字
const getStatusText = (status) => {
  const texts = {
    draft: "草稿",
    active: "進行中",
    completed: "已完成",
    cancelled: "已取消",
  };
  return texts[status] || status;
};

// 處理查看專案
const handleViewProject = (project) => {
  emit("view-project", project);
};

// 處理編輯專案
const handleEditProject = (project) => {
  emit("edit-project", project);
};

// 處理刪除專案
const handleDeleteProject = (project) => {
  emit("delete-project", project);
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 深色模式表格樣式 */
html.dark :deep(.custom-table) {
  --el-table-header-bg-color: #334155 !important;
  --el-table-header-text-color: #e2e8f0 !important;
  --el-table-row-hover-bg-color: #1e293b !important;
  --el-table-border-color: #334155 !important;
  --el-table-bg-color: #1e293b !important;
  --el-table-tr-bg-color: #1e293b !important;
  --el-table-expanded-cell-bg-color: #1e293b !important;
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
}

html.dark :deep(.custom-table th),
html.dark :deep(.custom-table tr),
html.dark :deep(.custom-table td) {
  background-color: #1e293b !important;
  color: #e2e8f0 !important;
  border-bottom-color: #334155 !important;
}

html.dark :deep(.custom-table--border),
html.dark :deep(.custom-table--border th),
html.dark :deep(.custom-table--border td) {
  border-color: #334155 !important;
}

html.dark :deep(.custom-table__row:hover > td) {
  background-color: #334155 !important;
}

/* 修正深色模式下的表格標題樣式 */
html.dark :deep(.custom-table .cell) {
  color: #e2e8f0 !important;
}

html.dark :deep(.custom-table .el-table__header-wrapper) {
  background-color: #334155 !important;
}

html.dark :deep(.custom-table .el-table__header) {
  background-color: #334155 !important;
}

html.dark :deep(.custom-table th.el-table__cell) {
  background-color: #334155 !important;
}
</style>
