<template>
  <div class="template-cards-container">
    <div
      v-if="loading"
      class="loading-container">
      <el-skeleton
        :rows="3"
        animated />
    </div>
    <div
      v-else-if="templates.length === 0"
      class="no-templates">
      <el-empty description="沒有可用的流程模板" />
    </div>
    <div
      v-else
      class="cards-grid">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        :class="{ selected: modelValue === template.id }"
        @click="selectTemplate(template.id)">
        <!-- 流程縮圖 -->
        <div class="template-thumbnail">
          <svg
            v-if="!template.thumbnail"
            class="default-thumbnail"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M9,13V19H7V13H9M15,13V19H17V13H15M11,13V19H13V13H11Z" />
          </svg>
          <img
            v-else
            :src="template.thumbnail"
            alt="模板縮圖"
            class="template-image" />
        </div>

        <!-- 模板資訊 -->
        <div class="template-info">
          <h3 class="template-name">{{ template.name }}</h3>
          <p class="template-description">
            {{ truncateDescription(template.description) }}
          </p>

          <!-- 狀態標籤 -->
          <!-- <div class="template-status">
            <el-tag
              :type="getTagType(template.status)"
              size="small">
              {{ getStatusText(template.status) }}
            </el-tag>
          </div> -->
        </div>

        <!-- 選中標記 -->
        <div
          v-if="modelValue === template.id"
          class="selected-mark">
          <svg
            class="check-icon"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  templates: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

// 選擇模板
const selectTemplate = (templateId) => {
  emit("update:modelValue", templateId);
};

// 截斷描述文字
const truncateDescription = (description) => {
  if (!description) return "無描述";
  return description.length > 50
    ? description.substring(0, 50) + "..."
    : description;
};

// 獲取狀態標籤類型
const getTagType = (status) => {
  switch (status) {
    case "active":
      return "success";
    case "draft":
      return "info";
    case "archived":
      return "danger";
    default:
      return "info";
  }
};

// 獲取狀態文字
const getStatusText = (status) => {
  switch (status) {
    case "active":
      return "已發布";
    case "draft":
      return "草稿";
    case "archived":
      return "已歸檔";
    default:
      return "未知";
  }
};
</script>

<style scoped>
.template-cards-container {
  width: 100%;
  padding: 20px 0;
}

.loading-container {
  padding: 20px;
}

.no-templates {
  padding: 40px 0;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
}

.template-card {
  position: relative;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: var(--el-bg-color);
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary-light-5);
}

.template-card.selected {
  border: 2px solid var(--el-color-primary);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.template-thumbnail {
  height: 120px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--el-fill-color-lighter);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 12px;
}

.default-thumbnail {
  width: 60px;
  height: 60px;
  color: var(--el-color-info-light-3);
}

.template-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  padding-top: 8px;
}

.template-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 3px 0;
  color: var(--el-text-color-primary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin: 0 0 3px 0;
  height: 28px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-status {
  display: flex;
  justify-content: flex-end;
}

.selected-mark {
  position: absolute;
  background-color: var(--el-bg-color);
  top: -10px;
  right: 10px;
}

.check-icon {
  width: 24px;
  height: 24px;
  color: var(--el-color-primary);
}

/* 暗黑模式適配 */
html.dark .template-card {
  background-color: #1e293b;
  border-color: #334155;
}

html.dark .template-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

html.dark .template-thumbnail {
  background-color: #334155;
}

html.dark .default-thumbnail {
  color: #475569;
}
</style>
