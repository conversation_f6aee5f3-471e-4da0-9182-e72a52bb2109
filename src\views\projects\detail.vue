<template>
  <div class="p-2 bg-light-mode dark:bg-dark-mode">
    <!-- 專案統計資訊 -->
    <ProjectStatistics
      :statistics="statistics"
      :loading="loading" />
    <!-- 使用 ProjectHeader 組件 -->
    <ProjectHeader
      :loading="loading"
      @back="handleBack"
      @refresh="handleRefresh"
      @create-instance="handleCreateInstance" />

    <!-- 專案詳細資訊 -->
    <ProjectInfo
      :project="project"
      :loading="loading"
      :is-admin="isAdmin"
      @edit="handleEditProject"
      @delete="handleDeleteProject" />

    <!-- 流程實例列表 -->
    <ProjectInstanceList
      :instances="instances"
      :loading="instancesLoading"
      @view="handleViewInstance"
      @delete="handleDeleteInstance"
      @create="handleCreateInstance" />

    <!-- 專案文件列表 -->
    <ProjectDocumentList
      :documents="documents"
      :loading="documentsLoading"
      @refresh="fetchDocuments" />

    <!-- 編輯專案對話框 -->
    <ProjectEditDialog
      v-model="dialogVisible"
      :project="project"
      :is-edit="isEdit"
      :loading="submitLoading"
      @submit="handleSubmit"
      @cancel="dialogVisible = false" />

    <!-- 創建流程實例對話框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="新增分析流程"
      width="800px"
      :close-on-click-modal="false">
      <div class="template-dialog-content">
        <p class="mb-4 dark:text-dark-mode">請選擇要建立的分析流程：</p>
        <TemplateSelectCards
          :templates="templates"
          :loading="templatesLoading"
          v-model="selectedTemplateId" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmCreateInstance"
            :disabled="!selectedTemplateId"
            >確定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useUserStore } from "@/stores/user";
import { useProjectStore } from "@/stores/project";
import { useTeleportVisibility } from "@/composables/useTeleportVisibility";
import {
  getProjectById,
  updateProject,
  deleteProject,
  getProjectInstances,
} from "@/api/modules/project";
import {
  createFlowInstance,
  deleteFlowInstance,
  getFlowTemplates,
} from "@/api/modules/flow";
import { getDocumentsByProject } from "@/api/modules/flowDocument";

// 引入組件
import ProjectHeader from "./components/ProjectHeader.vue";
import ProjectInfo from "./components/ProjectInfo.vue";
import ProjectStatistics from "./components/ProjectStatistics.vue";
import ProjectInstanceList from "./components/ProjectInstanceList.vue";
import ProjectDocumentList from "./components/ProjectDocumentList.vue";
import ProjectEditDialog from "./components/ProjectEditDialog.vue";
import TemplateSelectCards from "./components/TemplateSelectCards.vue";

// 路由
const router = useRouter();
const route = useRoute();
const projectId = computed(() => route.params.id);

// 狀態
const loading = ref(false);
const instancesLoading = ref(false);
const documentsLoading = ref(false);
const templatesLoading = ref(false);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const project = ref({});
const instances = ref([]);
const templates = ref([]);
const documents = ref([]);

// 流程模板選擇對話框
const templateDialogVisible = ref(false);
const selectedTemplateId = ref("");

// 用戶狀態
const userStore = useUserStore();
const projectStore = useProjectStore();

// 檢查是否為管理員
const isAdmin = computed(() => {
  const userRole = userStore.user?.role;
  return userRole === "ADMIN" || userRole === "SUPERADMIN";
});

// 統計資訊
const statistics = computed(() => {
  return {
    totalInstances: instances.value.length,
    completedInstances: instances.value.filter((i) => i.status === "completed")
      .length,
    activeInstances: instances.value.filter((i) => i.status === "active")
      .length,
    totalDocuments: documents.value.length,
  };
});

// 獲取專案詳情
const fetchProject = async () => {
  try {
    loading.value = true;
    const response = await getProjectById(projectId.value);
    project.value = response.data;
    // 更新專案名稱到 projectStore 中，用於麵包屑顯示
    projectStore.setProjectName(response.data.name);
  } catch (error) {
    console.error("獲取專案詳情失敗:", error);
    ElMessage.error("獲取專案詳情失敗");
  } finally {
    loading.value = false;
  }
};

// 獲取專案的流程實例
const fetchInstances = async () => {
  try {
    instancesLoading.value = true;
    const response = await getProjectInstances(projectId.value);
    instances.value = response.data;
  } catch (error) {
    console.error("獲取流程實例失敗:", error);
    ElMessage.error("獲取流程實例失敗");
  } finally {
    instancesLoading.value = false;
  }
};

// 獲取所有流程模板
const fetchTemplates = async () => {
  try {
    templatesLoading.value = true;
    const response = await getFlowTemplates();

    // 過濾並增強模板數據，添加縮圖屬性
    templates.value = response.data.map((template) => ({
      ...template,
      // 這裡可以添加縮圖URL，如果後端有提供的話
      thumbnail: template.thumbnail || null,
    }));
  } catch (error) {
    console.error("獲取流程模板失敗:", error);
    ElMessage.error("獲取流程模板失敗");
  } finally {
    templatesLoading.value = false;
  }
};

// 獲取專案的文件
const fetchDocuments = async () => {
  try {
    documentsLoading.value = true;
    const response = await getDocumentsByProject(projectId.value);
    documents.value = response.data || [];
  } catch (error) {
    console.error("獲取專案文件失敗:", error);
    ElMessage.error("獲取專案文件失敗");
  } finally {
    documentsLoading.value = false;
  }
};

// 處理返回
const handleBack = () => {
  router.push("/projects");
};

// 處理刷新
const handleRefresh = () => {
  fetchProject();
  fetchInstances();
  fetchDocuments();
};

// 處理編輯專案
const handleEditProject = () => {
  isEdit.value = true;
  dialogVisible.value = true;
};

// 處理刪除專案
const handleDeleteProject = async () => {
  try {
    await ElMessageBox.confirm(
      "確定要刪除此專案嗎？此操作將同時刪除所有關聯的分析流程實例，且不可恢復。",
      "刪除確認",
      {
        confirmButtonText: "確定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteProject(project.value.id);
    ElMessage.success("專案刪除成功");
    router.push("/projects");
  } catch (error) {
    if (error !== "cancel") {
      console.error("刪除專案失敗:", error);
      ElMessage.error("刪除專案失敗");
    }
  }
};

// 處理提交
const handleSubmit = async (formData) => {
  try {
    submitLoading.value = true;

    await updateProject(formData.id, {
      name: formData.name,
      description: formData.description,
      status: formData.status,
    });

    ElMessage.success("專案更新成功");
    dialogVisible.value = false;
    fetchProject();
  } catch (error) {
    console.error("提交失敗:", error);
    ElMessage.error(error.response?.data?.message || "操作失敗");
  } finally {
    submitLoading.value = false;
  }
};

// 處理查看流程實例
const handleViewInstance = (instance) => {
  console.log("查看流程實例", instance);
  router.push({
    path: `/flow-instances/${instance.id}`,
    query: {
      from: "project",
      projectId: projectId.value,
      projectName: project.value.name,
      instanceName: instance.template?.name || "流程實例",
    },
  });
};

// 處理創建流程實例
const handleCreateInstance = async () => {
  try {
    // 檢查是否有可用的模板
    if (templates.value.length === 0) {
      templatesLoading.value = true;
      await fetchTemplates();

      if (templates.value.length === 0) {
        ElMessage.warning("沒有可用的流程模板，請先創建模板");
        return;
      }
    }

    // 重置選中的模板ID
    selectedTemplateId.value = "";

    // 顯示對話框
    templateDialogVisible.value = true;
  } catch (error) {
    console.error("準備創建流程實例失敗:", error);
    ElMessage.error("準備創建流程實例失敗");
  }
};

// 確認創建流程實例
const confirmCreateInstance = async () => {
  try {
    if (!selectedTemplateId.value) {
      ElMessage.warning("請選擇流程模板");
      return;
    }

    // 創建流程實例
    const response = await createFlowInstance({
      projectId: projectId.value,
      templateId: selectedTemplateId.value,
    });

    ElMessage.success("流程實例創建成功");

    // 關閉對話框
    templateDialogVisible.value = false;

    // 獲取選中的模板名稱
    const selectedTemplate = templates.value.find(
      (t) => t.id === selectedTemplateId.value
    );
    const templateName = selectedTemplate?.name || "流程實例";

    // 直接導航到流程實例詳情頁
    router.push({
      path: `/flow-instances/${response.data.id}`,
      query: {
        from: "project",
        projectId: projectId.value,
        projectName: project.value.name,
        instanceName: templateName,
      },
    });
  } catch (error) {
    console.error("創建流程實例失敗:", error);
    ElMessage.error("創建流程實例失敗");
  }
};

// 處理刪除流程實例
const handleDeleteInstance = async (instance) => {
  try {
    await ElMessageBox.confirm(
      "確定要刪除此流程實例嗎？此操作不可恢復。",
      "刪除確認",
      {
        confirmButtonText: "確定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteFlowInstance(instance.id);
    ElMessage.success("流程實例刪除成功");
    fetchInstances();
  } catch (error) {
    if (error !== "cancel") {
      console.error("刪除流程實例失敗:", error);
      ElMessage.error("刪除流程實例失敗");
    }
  }
};

onMounted(() => {
  fetchProject();
  fetchInstances();
  fetchTemplates();
  fetchDocuments();
});
</script>

<style scoped>
.el-tag {
  margin-right: 0.5rem;
}

/* 流程範本選擇對話框樣式 */
.template-dialog-content {
  padding: 0 20px;
}

.template-select {
  width: 100%;
}

:deep(.template-select-dropdown) {
  width: auto !important;
  min-width: 100% !important;
}

:deep(.template-select-container) {
  margin-top: 16px;
  width: 100%;
}

/* 深色模式樣式調整 */
:deep(.el-dialog) {
  --el-dialog-bg-color: var(--el-bg-color);
  --el-dialog-content-font-color: var(--el-text-color-primary);
}

html.dark :deep(.el-dialog) {
  --el-dialog-bg-color: #1e293b;
  --el-dialog-content-font-color: #e2e8f0;
  --el-dialog-border-color: #334155;
}

html.dark :deep(.el-dialog__title) {
  color: #e2e8f0;
}

html.dark :deep(.el-select-dropdown__item) {
  color: #e2e8f0;
}

html.dark :deep(.el-select-dropdown__item.hover),
html.dark :deep(.el-select-dropdown__item:hover) {
  background-color: #334155;
}

html.dark :deep(.el-select-dropdown) {
  background-color: #1e293b;
  border-color: #334155;
}

html.dark :deep(.el-popper__arrow::before) {
  background-color: #1e293b;
  border-color: #334155;
}

/* 新增暗黑模式下的按鈕樣式 */
html.dark :deep(.el-button) {
  --el-button-border-color: #334155;
}

html.dark :deep(.el-button--default) {
  --el-button-bg-color: #1e293b;
  --el-button-text-color: #e2e8f0;
  --el-button-hover-bg-color: #263548;
  --el-button-hover-text-color: #ffffff;
}

html.dark :deep(.dialog-footer) {
  border-top-color: #334155;
}

html.dark :deep(.el-empty__description) {
  color: #e2e8f0;
}
</style>
