<template>
  <div class="employee-list">
    <div class="header-actions">
      <div class="flex items-center gap-2">
        <!-- 搜尋欄位 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜尋姓名/員工編號"
          class="!w-48"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button
          type="primary"
          @click="handleSearch">
          搜尋
        </el-button>

        <!-- 重整按鈕 -->
        <el-button
          type="info"
          plain
          class="flex items-center"
          :loading="loading"
          @click="handleRefresh">
          <el-icon
            ><RefreshCw
              class="mr-1"
              :size="16"
          /></el-icon>
          重整
        </el-button>
        <!-- 部門篩選 -->
        <!-- <el-select
          v-model="selectedGroup"
          placeholder="選擇部門"
          clearable
          @change="handleSearch"
          class="w-48">
          <el-option
            v-for="group in groups"
            :key="group.groupId"
            :label="group.groupName"
            :value="group.groupId" />
        </el-select> -->
      </div>
    </div>

    <el-table
      :data="loading ? Array(5).fill({}) : tableData"
      style="width: 100%"
      v-loading="loading"
      @sort-change="handleSortChange">
      <el-table-column
        type="index"
        label="序號"
        width="80"
        align="center" />
      <el-table-column
        prop="employeeNo"
        label="員工工號"
        sortable="custom"
        width="120" />
      <el-table-column
        prop="name"
        label="姓名"
        sortable="custom"
        width="120" />
      <el-table-column
        prop="groupName"
        label="部門"
        sortable="custom"
        width="350" />
      <el-table-column
        prop="titleName"
        label="職稱"
        sortable="custom"
        width="150" />
      <el-table-column
        prop="email"
        label="電子郵件"
        min-width="200" />
      <el-table-column
        prop="birthday"
        label="生日"
        sortable="custom"
        width="150">
        <template #default="{ row }">
          {{
            formatTimestamp(row.birthday, {
              yearFormat: "YYYY/MM/DD",
              monthFormat: "MM/DD",
            })
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="telephone"
        label="分機"
        width="100" />
      <el-table-column
        prop="mobile"
        label="手機"
        width="120" />

      <el-table-column
        label="狀態"
        width="100"
        align="center">
        <template #default="{ row }">
          <el-tag
            :type="
              row.isLockedOut
                ? 'danger'
                : row.isSuspended
                ? 'warning'
                : 'success'
            "
            size="small">
            {{
              row.isLockedOut ? "已鎖定" : row.isSuspended ? "已停用" : "正常"
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              :icon="Edit"
              @click="handleView(row)">
              <Eye
                :size="14"
                class="mr-1" />
              檢視
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分頁 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalPages"
        :pager-count="11"
        layout="total, sizes, prev, pager, next"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 檢視員工詳情對話框 -->
    <el-dialog
      v-model="dialogVisible"
      title="員工詳情"
      width="800px">
      <el-tabs>
        <el-tab-pane label="基本資料">
          <el-descriptions
            :column="2"
            border>
            <el-descriptions-item label="員工工號">
              {{ currentEmployee?.employeeNo }}
            </el-descriptions-item>
            <el-descriptions-item label="姓名">
              {{ currentEmployee?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="暱稱">
              {{ currentEmployee?.nickname || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="性別">
              {{ currentEmployee?.sex || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="生日">
              {{ formatTimestamp(currentEmployee?.birthday) }}
            </el-descriptions-item>
            <el-descriptions-item label="部門">
              {{ currentEmployee?.groupName }}
            </el-descriptions-item>
            <el-descriptions-item label="職稱">
              {{ currentEmployee?.titleName }}
            </el-descriptions-item>
            <el-descriptions-item label="職等">
              {{ currentEmployee?.titleRank || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="到職日">
              {{ formatTimestamp(currentEmployee?.arriveDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="離職日">
              {{ formatTimestamp(currentEmployee?.leaveDate) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="聯絡資訊">
          <el-descriptions
            :column="2"
            border>
            <el-descriptions-item label="電子郵件">
              {{ currentEmployee?.email }}
            </el-descriptions-item>
            <el-descriptions-item label="電子郵件 2">
              {{ currentEmployee?.emailA || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="電子郵件 3">
              {{ currentEmployee?.emailB || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="電子郵件 4">
              {{ currentEmployee?.emailC || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="分機">
              {{ currentEmployee?.telephone }}
            </el-descriptions-item>
            <el-descriptions-item label="手機">
              {{ currentEmployee?.mobile }}
            </el-descriptions-item>
            <el-descriptions-item label="地址">
              {{ currentEmployee?.address || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="LINE">
              {{ currentEmployee?.line || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="Skype">
              {{ currentEmployee?.skype || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="WeChat">
              {{ currentEmployee?.wechat || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="帳號資訊">
          <el-descriptions
            :column="2"
            border>
            <el-descriptions-item label="帳號代碼">
              {{ currentEmployee?.accountGuid }}
            </el-descriptions-item>
            <el-descriptions-item label="AD 帳號">
              {{ currentEmployee?.account || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="AD 網域">
              {{ currentEmployee?.domain || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="CA 憑證號碼">
              {{ currentEmployee?.caSerialNum || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="USB 認證">
              {{ currentEmployee?.isUsbAuth ? "是" : "否" }}
            </el-descriptions-item>
            <el-descriptions-item label="USB 密碼">
              {{ currentEmployee?.usbKey || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="語系">
              {{ currentEmployee?.lang || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="帳號類別">
              {{ currentEmployee?.userType || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="帳號狀態">
          <el-descriptions
            :column="2"
            border>
            <el-descriptions-item label="帳號狀態">
              <el-tag
                :type="
                  currentEmployee?.isLockedOut
                    ? 'danger'
                    : currentEmployee?.isSuspended
                    ? 'warning'
                    : 'success'
                "
                size="small">
                {{
                  currentEmployee?.isLockedOut
                    ? "已鎖定"
                    : currentEmployee?.isSuspended
                    ? "已停用"
                    : "正常"
                }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="密碼重置">
              {{ currentEmployee?.isPasswordReset ? "是" : "否" }}
            </el-descriptions-item>
            <el-descriptions-item label="密碼重置原因">
              {{ currentEmployee?.pwResetReason || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="密碼試錯次數">
              {{ currentEmployee?.passwordInvalidAttempts || "0" }}
            </el-descriptions-item>
            <el-descriptions-item label="帳號到期日">
              {{ formatTimestamp(currentEmployee?.expireDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後使用日期">
              {{ formatTimestamp(currentEmployee?.lastActivityDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後鎖定日期">
              {{ formatTimestamp(currentEmployee?.lastLockedOutDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後密碼變更日期">
              {{ formatTimestamp(currentEmployee?.lastPasswordChangeDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後停用日期">
              {{ formatTimestamp(currentEmployee?.lastSuspendedDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後更新個人資訊日期">
              {{ currentEmployee?.lastUpdatePersonalInfoDate || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Eye } from "lucide-vue-next";
import { ElMessage } from "element-plus";
import { getOrgGroups } from "@/api/modules/orgGroup";
import { searchEmployees } from "@/api/modules/orgEmployee";
import { logger } from "@/utils/logger";
import { formatTimestamp } from "@/utils/dateUtils";
// 狀態定義
const loading = ref(false);
const searchQuery = ref("");
const selectedGroup = ref("");
const currentPage = ref(1);
const pageSize = ref(25);
const totalPages = ref(0);
const tableData = ref([]);
const dialogVisible = ref(false);
const currentEmployee = ref(null);
const groups = ref([]);
const sortConfig = ref({ prop: "", order: "" });

// 獲取部門列表
const fetchGroups = async () => {
  try {
    const response = await getOrgGroups();
    groups.value = response.data;
  } catch (error) {
    ElMessage.error("獲取部門列表失敗");
  }
};

// 獲取員工列表
const fetchEmployees = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchQuery.value,
      employeeNo: searchQuery.value,
      groupId: selectedGroup.value,
      sortBy: sortConfig.value.prop || "employeeNo",
      sortOrder: sortConfig.value.order || "desc",
    };

    const response = await searchEmployees(params);
    logger.debug(response);
    // 根據新的API回傳格式調整
    logger.debug(response.data);
    if (response.status === "success") {
      tableData.value = response.data.data;
      totalPages.value = response.data.pagination.totalPages;
    } else {
      throw new Error(response.message || "獲取員工列表失敗");
    }
  } catch (error) {
    ElMessage.error("獲取員工列表失敗");
  } finally {
    loading.value = false;
  }
};

// 處理搜尋
const handleSearch = () => {
  currentPage.value = 1;
  fetchEmployees();
};

// 處理排序變更
const handleSortChange = ({ prop, order }) => {
  sortConfig.value = { prop, order };
  fetchEmployees();
};

// 處理頁碼變更
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchEmployees();
};

// 處理每頁筆數變更
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchEmployees();
};

// 處理檢視員工詳情
const handleView = (row) => {
  currentEmployee.value = row;
  dialogVisible.value = true;
};

// 處理重整
const handleRefresh = () => {
  searchQuery.value = "";
  selectedGroup.value = "";
  currentPage.value = 1;
  sortConfig.value = { prop: "", order: "" };
  fetchEmployees();
};

// 組件掛載時執行
onMounted(() => {
  fetchGroups();
  fetchEmployees();
});
</script>

<style scoped>
.employee-list {
  padding: 5px 0;
}

.header-actions {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
