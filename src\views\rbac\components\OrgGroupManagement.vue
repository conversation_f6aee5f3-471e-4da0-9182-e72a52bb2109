<template>
  <div
    class="org-group-management"
    ref="containerRef">
    <div class="header-actions">
      <div class="left-section">
        <el-input
          v-model="searchKeyword"
          style="width: 200px"
          placeholder="請輸入關鍵字"
          @input="handleSearch" />
      </div>
      <div class="center-section">
        <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :total="totalItem"
          @current-change="handleCurrentChange" />
      </div>
      <div class="right-section"></div>
    </div>
    <el-table
      :data="loading ? Array(3).fill({}) : orgGroups"
      style="width: 100%"
      v-loading="loading">
      <el-table-column
        prop="groupCode"
        label="群組代號"
        width="100" />
      <el-table-column
        prop="companyId"
        label="公司別ID"
        width="100" />
      <el-table-column
        prop="groupName"
        label="群組名稱"
        min-width="150"
        align="left" />
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import request from "@/api/request";
import { debounce } from "lodash";

const orgGroups = ref([]);
const pageSize = ref(19);
const currentPage = ref(1);
const totalItem = ref(0);
const totalPage = ref(0);
const searchKeyword = ref("");
const containerRef = ref(null);
const containerHeight = ref(0);

const fetchOrgGroups = async (pageSize, currentPage, searchKeyword) => {
  const data = await request.get("http://localhost:3001/api/org-groups", {
    params: {
      pageSize: pageSize,
      page: currentPage,
      keyword: searchKeyword,
    },
  });

  orgGroups.value = data["data"]["groups"];
  totalItem.value = data["data"]["pagination"]["total"];
  totalPage.value = data["data"]["pagination"]["totalPage"];
};

const handleCurrentChange = async (page) => {
  currentPage.value = page;
  await fetchOrgGroups(pageSize.value, currentPage.value, searchKeyword.value);
};

const debouncedSearch = debounce(async (keyword) => {
  await fetchOrgGroups(pageSize.value, currentPage.value, keyword);
}, 700);

const handleSearch = (keyword) => {
  searchKeyword.value = keyword;
  debouncedSearch(keyword);
};

const getContainerHeight = debounce(() => {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight;
    const itemMaxCount = (containerHeight.value - 42 - 40) / 40;
    pageSize.value = itemMaxCount;
  }
}, 100);

onMounted(() => {
  fetchOrgGroups(pageSize.value, currentPage.value, searchKeyword.value);
  getContainerHeight();

  window.addEventListener("resize", getContainerHeight);
});

onUnmounted(() => {
  getContainerHeight.cancel();
  window.removeEventListener("resize", getContainerHeight);
});
</script>

<style scoped>
.org-group-management {
  position: relative;
  height: 100%;
  overflow: auto;
}

/* 如果需要隱藏滾動條 */
.org-group-management::-webkit-scrollbar {
  display: none;
}
.org-group-management {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.header-actions {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
  margin-bottom: 10px;
  display: grid;
  grid-template-columns: 1fr auto 1fr; /* 三欄布局：左邊、中間、右邊 */
  align-items: center;
}

.left-section {
  justify-self: start; /* 靠左對齊 */
  margin-left: 10px;
}

.center-section {
  justify-self: center; /* 置中對齊 */
}

.right-section {
  justify-self: end; /* 靠右對齊（目前為空） */
}
</style>
