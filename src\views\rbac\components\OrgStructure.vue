<template>
  <div class="p-0">
    <div class="mb-3">
      <div class="flex items-center justify-end mr-2 gap-2">
        <el-button
          type="info"
          plain
          @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          重新整理
        </el-button>
        <!-- <el-button
        type="success"
        @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增部門
      </el-button> -->
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      row-key="groupId"
      border
      :load="loadNode"
      lazy
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column
        prop="groupName"
        label="部門名稱"
        min-width="200">
        <template #default="{ row }">
          <span>{{ row.groupName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="groupCode"
        label="部門代號"
        min-width="150">
        <template #default="{ row }">
          <span>{{ row.groupCode }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="employeeCount"
        label="部門人數"
        width="100"
        align="center">
        <template #default="{ row }">
          <span>{{ row.employeeCount || "-" }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="操作"
        width="120"
        fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="handleEdit(row)">
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button
            type="danger"
            link
            disabled
            @click="handleDelete(row)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 新增/編輯部門對話框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增部門' : '編輯部門'"
      width="500px">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px">
        <el-form-item
          label="部門代碼"
          prop="groupId">
          <el-input
            v-model="form.groupId"
            :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item
          label="部門名稱"
          prop="groupName">
          <el-input v-model="form.groupName" />
        </el-form-item>
        <el-form-item
          label="部門類別"
          prop="groupType">
          <el-select
            v-model="form.groupType"
            placeholder="請選擇部門類別">
            <el-option
              label="部門"
              value="Department" />
            <el-option
              label="單位"
              value="Unit" />
            <el-option
              label="小組"
              value="Team" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="上級部門"
          prop="parentGroupId">
          <el-tree-select
            v-model="form.parentGroupId"
            :data="treeData"
            :props="defaultProps"
            placeholder="請選擇上級部門"
            clearable />
        </el-form-item>
        <el-form-item
          label="部門代號"
          prop="groupCode">
          <el-input v-model="form.groupCode" />
        </el-form-item>
        <el-form-item
          label="公司別"
          prop="companyId">
          <el-input v-model="form.companyId" />
        </el-form-item>
        <el-form-item
          label="是否啟用"
          prop="active">
          <el-switch v-model="form.active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            disabled
            @click="handleSubmit"
            >確定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Plus, Edit, Delete } from "@element-plus/icons-vue";
import {
  getOrgGroupTree,
  createOrgGroup,
  updateOrgGroup,
  deleteOrgGroup,
} from "@/api/modules/orgGroup";

const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const dialogType = ref("add");
const formRef = ref(null);
const form = ref({
  groupId: "",
  groupName: "",
  groupType: "Department",
  parentGroupId: null,
  groupCode: "",
  companyId: "",
  active: true,
});

const defaultProps = {
  children: "children",
  label: "groupName",
  isLeaf: (data) => !data.hasChildren,
};

const rules = {
  groupId: [{ required: true, message: "請輸入部門代碼", trigger: "blur" }],
  groupName: [{ required: true, message: "請輸入部門名稱", trigger: "blur" }],
  groupType: [{ required: true, message: "請選擇部門類別", trigger: "change" }],
};

// 獲取部門樹狀結構
const fetchTreeData = async () => {
  try {
    loading.value = true;
    const { data } = await getOrgGroupTree();
    console.log("data", data);
    tableData.value = data.data;
  } catch (error) {
    ElMessage.error("獲取部門結構失敗");
  } finally {
    loading.value = false;
  }
};

// 懶加載子節點
const loadNode = async (row, treeNode, resolve) => {
  try {
    const { data } = await getOrgGroupTree({
      parentGroupId: row.groupId,
    });
    console.log("data", data);
    resolve(data.data);
  } catch (error) {
    console.log("error", error);
    ElMessage.error("獲取子部門失敗");
    resolve([]);
  }
};

// 重新整理
const handleRefresh = () => {
  fetchTreeData();
};

// 新增部門
const handleAdd = () => {
  dialogType.value = "add";
  form.value = {
    groupId: "",
    groupName: "",
    groupType: "Department",
    parentGroupId: null,
    groupCode: "",
    companyId: "",
    active: true,
  };
  dialogVisible.value = true;
};

// 編輯部門
const handleEdit = (data) => {
  dialogType.value = "edit";
  form.value = { ...data };
  dialogVisible.value = true;
};

// 刪除部門
const handleDelete = async (data) => {
  try {
    await ElMessageBox.confirm("確定要刪除該部門嗎？", "提示", {
      type: "warning",
    });
    await deleteOrgGroup(data.groupId);
    ElMessage.success("刪除成功");
    fetchTreeData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("刪除失敗");
    }
  }
};

// 提交表單
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate();
  try {
    if (dialogType.value === "add") {
      await createOrgGroup(form.value);
      ElMessage.success("新增成功");
    } else {
      await updateOrgGroup(form.value.groupId, form.value);
      ElMessage.success("更新成功");
    }
    dialogVisible.value = false;
    fetchTreeData();
  } catch (error) {
    ElMessage.error(dialogType.value === "add" ? "新增失敗" : "更新失敗");
  }
};

// 點擊節點
const handleNodeClick = (data) => {
  console.log("點擊節點:", data);
};

onMounted(() => {
  fetchTreeData();
});
</script>

<style scoped>
:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-table__row) {
  cursor: pointer;
}
</style>
