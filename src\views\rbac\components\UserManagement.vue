<template>
  <div class="user-management">
    <!-- 用戶列表區域 -->
    <div class="user-list-section">
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用戶..."
          class="search-input"
          clearable
          @clear="handleSearch"
          @input="handleSearch">
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 重整按鈕 -->
        <el-button
          type="info"
          plain
          class="flex items-center"
          :loading="loading"
          @click="handleRefresh">
          <el-icon
            ><RefreshCw
              class="mr-1"
              :size="16"
          /></el-icon>
          重整
        </el-button>

        <el-button
          type="primary"
          @click="handleAddUser">
          <el-icon><Plus /></el-icon>新增用戶
        </el-button>
      </div>

      <el-table
        :data="loading ? Array(5).fill({}) : filteredUsers"
        style="width: 100%"
        :highlight-current-row="true"
        @row-click="handleUserSelect">
        <el-table-column
          type="index"
          label="序號"
          width="80"
          align="center">
          <template #default="scope">
            <template v-if="loading">
              <el-skeleton-item
                variant="text"
                style="width: 100%" />
            </template>
            <template v-else>
              {{ scope.$index + 1 }}
            </template>
          </template>
        </el-table-column>

        <el-table-column
          label="頭像"
          width="100"
          align="center">
          <template #default="{ row }">
            <template v-if="loading">
              <el-skeleton-item
                variant="circle"
                style="width: 40px; height: 40px" />
            </template>
            <template v-else>
              <div
                class="avatar-container"
                @click.stop="handleAvatarClick(row)">
                <el-avatar
                  :size="40"
                  :src="row.avatar"
                  :alt="row.username">
                  {{ row.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <div class="avatar-overlay">
                  <el-icon><Upload /></el-icon>
                </div>
              </div>
              <input
                type="file"
                :ref="(el) => (avatarInputRefs[row.id] = el)"
                class="hidden-file-input"
                accept="image/*"
                @change="(event) => handleAvatarChange(event, row)" />
            </template>
          </template>
        </el-table-column>

        <el-table-column
          prop="username"
          label="用戶名"
          sortable>
          <template #default="{ row }">
            <template v-if="loading">
              <el-skeleton-item
                variant="text"
                style="width: 80%" />
            </template>
            <template v-else>
              {{ row.username }}
            </template>
          </template>
        </el-table-column>

        <el-table-column
          prop="email"
          label="郵箱"
          sortable>
          <template #default="{ row }">
            <template v-if="loading">
              <el-skeleton-item
                variant="text"
                style="width: 90%" />
            </template>
            <template v-else>
              {{ row.email }}
            </template>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          label="狀態"
          width="100"
          align="center">
          <template #default="{ row }">
            <template v-if="loading">
              <el-skeleton-item
                variant="text"
                style="width: 60px" />
            </template>
            <template v-else>
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === "active" ? "啟用" : "停用" }}
              </el-tag>
            </template>
          </template>
        </el-table-column>

        <el-table-column
          label="已分配角色"
          min-width="200">
          <template #default="{ row }">
            <template v-if="loading">
              <div class="flex gap-2">
                <el-skeleton-item
                  variant="text"
                  style="width: 60px" />
                <el-skeleton-item
                  variant="text"
                  style="width: 60px" />
              </div>
            </template>
            <template v-else>
              <template v-if="!row.isEditingRoles">
                <el-tooltip
                  content="點擊兩下可編輯角色"
                  placement="top"
                  effect="light"
                  :show-after="500">
                  <div
                    class="role-display"
                    @dblclick="handleRoleEditStart(row)">
                    <template v-if="row.userRoles && row.userRoles.length">
                      <el-tag
                        v-for="userRole in row.userRoles"
                        :key="userRole.roleId"
                        class="role-tag"
                        size="small"
                        :type="getRoleTagType(userRole.role.name)">
                        {{ userRole.role.name }}
                      </el-tag>
                    </template>
                    <span
                      v-else
                      class="no-roles"
                      >未分配角色</span
                    >
                  </div>
                </el-tooltip>
              </template>
              <div
                v-else
                class="role-edit-container"
                tabindex="0"
                @blur="handleRoleEditBlur($event, row)"
                @mousedown.stop="handleContainerMouseDown">
                <el-select
                  v-model="row.tempRoles"
                  multiple
                  tag-effect="plain"
                  :loading="row.rolesLoading"
                  placeholder="選擇角色"
                  class="role-select">
                  <el-option
                    v-for="role in allRoles"
                    :key="role.id"
                    :label="role.name"
                    :value="role.id">
                    <span>{{ role.name }}</span>
                    <el-tooltip
                      :content="role.description"
                      placement="right"
                      effect="light">
                      <el-icon class="role-info-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </el-option>
                </el-select>
                <div class="role-edit-actions">
                  <el-button
                    type="primary"
                    size="small"
                    :loading="row.rolesLoading"
                    @click="handleRoleUpdate(row)">
                    更新
                  </el-button>
                  <el-button
                    size="small"
                    @click="handleRoleEditCancel(row)">
                    取消
                  </el-button>
                </div>
              </div>
            </template>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          fixed="right">
          <template #default="{ row }">
            <template v-if="loading">
              <div class="flex gap-2">
                <el-skeleton-item
                  variant="button"
                  style="width: 60px" />
                <el-skeleton-item
                  variant="button"
                  style="width: 60px" />
              </div>
            </template>
            <template v-else>
              <el-button-group>
                <el-button
                  type="primary"
                  :icon="Edit"
                  @click.stop="handleEditUser(row)">
                  <Pencil
                    :size="14"
                    class="mr-1" />
                  編輯
                </el-button>
                <el-button
                  type="danger"
                  :icon="Delete"
                  @click.stop="handleDeleteUser(row)">
                  <Trash
                    :size="14"
                    class="mr-1" />
                  刪除
                </el-button>
              </el-button-group>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 用戶表單對話框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '編輯用戶' : '新增用戶'"
      width="500px"
      @close="handleDialogClose">
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px">
        <el-form-item
          label="用戶名"
          prop="username">
          <el-input v-model="userForm.username" />
        </el-form-item>
        <el-form-item
          label="郵箱"
          prop="email">
          <el-input
            v-model="userForm.email"
            :disabled="isEdit" />
          <div
            class="form-item-tip"
            v-if="isEdit">
            <el-icon><InfoFilled /></el-icon>
            <span>郵箱是用戶的唯一標識，不可更改</span>
          </div>
        </el-form-item>
        <el-form-item
          label="密碼"
          prop="password"
          v-if="!isEdit">
          <el-input
            v-model="userForm.password"
            type="password"
            show-password />
        </el-form-item>
        <el-form-item
          label="狀態"
          prop="status">
          <el-switch
            v-model="userForm.status"
            :active-value="'active'"
            :inactive-value="'inactive'"
            active-text="啟用"
            inactive-text="停用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmitUser"
          :loading="submitting">
          確定
        </el-button>
      </template>
    </el-dialog>

    <!-- 刪除確認對話框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="確認刪除"
      width="400px">
      <p>確定要刪除此用戶嗎？此操作不可恢復。</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          @click="handleDeleteConfirm"
          :loading="deleting">
          確定刪除
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { InfoFilled } from "@element-plus/icons-vue";
import { useRbacStore } from "@/stores/rbac";
import { useUserStore } from "@/stores/user";

// Store
const rbacStore = useRbacStore();
const userStore = useUserStore();

// 狀態
const loading = ref(false);
const rolesLoading = ref(false);
const searchQuery = ref("");
const selectedUser = ref(null);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const isEdit = ref(false);
const submitting = ref(false);
const deleting = ref(false);

// 表單相關
const userFormRef = ref(null);
const userForm = ref({
  username: "",
  email: "",
  password: "",
  status: "active",
});

// 表單驗證規則
const userFormRules = {
  username: [
    { required: true, message: "請輸入用戶名", trigger: "blur" },
    { min: 2, max: 20, message: "長度在 2 到 20 個字符", trigger: "blur" },
  ],
  email: [
    { required: !isEdit, message: "請輸入郵箱", trigger: "blur" },
    { type: "email", message: "請輸入正確的郵箱格式", trigger: "blur" },
  ],
  password: [
    { required: true, message: "請輸入密碼", trigger: "blur" },
    { min: 6, message: "密碼長度不能小於 6 個字符", trigger: "blur" },
  ],
};

// 計算屬性
const filteredUsers = computed(() => {
  const query = searchQuery.value.toLowerCase();
  return userStore.users
    .map((user) => ({
      ...user,
      rolesLoading: false,
      isEditingRoles: false,
      tempRoles: user.userRoles ? user.userRoles.map((ur) => ur.roleId) : [],
    }))
    .filter(
      (user) =>
        user.username.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query)
    );
});

// 所有角色
const allRoles = computed(() =>
  rbacStore.roles.map((role) => ({
    id: role.id,
    name: role.name,
    description: role.description,
  }))
);

// 分配角色
const assignedRoleIds = computed({
  get: () => selectedUser.value?.userRoles?.map((ur) => ur.role.id) || [],
  set: async (newValue) => {
    if (!selectedUser.value) return;

    rolesLoading.value = true;
    try {
      // 這裡需要實現角色分配的邏輯
      await rbacStore.updateUserRoles(selectedUser.value.id, newValue);
      ElMessage.success("角色更新成功");
    } catch (error) {
      ElMessage.error("角色更新失敗");
    } finally {
      rolesLoading.value = false;
    }
  },
});

// 獲取角色名稱的方法
const getRoleName = (roleId) => {
  const role = allRoles.value.find((r) => r.id === roleId);
  return role ? role.name : "";
};

// 獲取角色標籤類型
const getRoleTagType = (roleName) => {
  switch (roleName) {
    case "SUPER_ADMIN":
      return "danger";
    case "ADMIN":
      return "warning";
    default:
      return "success";
  }
};

// 處理容器失去焦點
const handleRoleEditBlur = (event, row) => {
  // 檢查相關目標是否是容器內的元素
  if (
    event.relatedTarget &&
    event.currentTarget.contains(event.relatedTarget)
  ) {
    return;
  }
  handleRoleEditCancel(row);
};

// 處理容器的鼠標按下事件
const handleContainerMouseDown = (event) => {
  // 防止容器在點擊內部元素時失去焦點
  event.preventDefault();
};

// 開始編輯角色
const handleRoleEditStart = (row) => {
  if (row.userRoles) {
    row.tempRoles = row.userRoles.map((ur) => ur.roleId);
  } else {
    row.tempRoles = [];
  }
  row.isEditingRoles = true;
  // 在下一個事件循環中設置焦點
  setTimeout(() => {
    const container = document.querySelector(".role-edit-container");
    if (container) {
      container.focus();
    }
  });
};

// 取消編輯角色
const handleRoleEditCancel = (row) => {
  if (row.userRoles) {
    row.tempRoles = row.userRoles.map((ur) => ur.roleId);
  } else {
    row.tempRoles = [];
  }
  row.isEditingRoles = false;
};

// 更新角色
const handleRoleUpdate = async (row) => {
  console.log("update", row);

  row.rolesLoading = true;
  try {
    await rbacStore.updateUserRoles(row.id, row.tempRoles);
    // 更新成功後，重新獲取用戶列表
    await userStore.fetchUsers();
    ElMessage.success("角色更新成功");
    row.isEditingRoles = false;
  } catch (error) {
    console.error("角色更新失敗:", error);
    ElMessage.error("角色更新失敗");
    // 恢復原始角色
    if (row.userRoles) {
      row.tempRoles = row.userRoles.map((ur) => ur.roleId);
    } else {
      row.tempRoles = [];
    }
  } finally {
    row.rolesLoading = false;
  }
};

// 方法
const handleSearch = () => {
  // 搜索邏輯已通過 computed 屬性實現
};

// 選擇用戶
const handleUserSelect = (row) => {
  selectedUser.value = row;
};

// 新增用戶
const handleAddUser = () => {
  isEdit.value = false;
  userForm.value = {
    username: "",
    email: "",
    password: "",
    status: "active",
  };
  dialogVisible.value = true;
};

// 編輯用戶
const handleEditUser = (user) => {
  isEdit.value = true;
  userForm.value = {
    id: user.id,
    username: user.username,
    email: user.email,
    status: user.status,
  };
  dialogVisible.value = true;
};

// 刪除用戶
const handleDeleteUser = (user) => {
  deleteDialogVisible.value = true;
  selectedUser.value = user;
};

// 對話框關閉
const handleDialogClose = () => {
  userFormRef.value?.resetFields();
};

// 提交用戶
const handleSubmitUser = async () => {
  if (!userFormRef.value) return;

  await userFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitting.value = true;
    try {
      if (isEdit.value) {
        await userStore.updateUserInfo(userForm.value.id, userForm.value);
        ElMessage.success("用戶更新成功");
      } else {
        await userStore.createUserInfo(userForm.value);
        ElMessage.success("用戶創建成功");
      }
      dialogVisible.value = false;
    } catch (error) {
      ElMessage.error(error.message || "操作失敗");
    } finally {
      submitting.value = false;
    }
  });
};

// 刪除確認
const handleDeleteConfirm = async () => {
  if (!selectedUser.value) return;

  deleting.value = true;
  try {
    await userStore.deleteUserInfo(selectedUser.value.id);
    ElMessage.success("用戶刪除成功");
    deleteDialogVisible.value = false;
    selectedUser.value = null;
  } catch (error) {
    ElMessage.error(error.message || "刪除失敗");
  } finally {
    deleting.value = false;
  }
};

// 頭像相關
const avatarInputRefs = ref({});
const uploadingUser = ref(null);

const handleAvatarClick = async (user) => {
  try {
    await ElMessageBox.confirm(
      `確定要更改 ${user.username} 的頭像嗎？`,
      "更改用戶頭像",
      {
        confirmButtonText: "確定",
        cancelButtonText: "取消",
        type: "warning",
        showClose: true,
        draggable: true,
        message: h("div", null, [
          h(
            "p",
            {
              style: "color: #f56c6c; font-weight: bold; margin-bottom: 10px;",
            },
            "【請注意】："
          ),
          h("ul", { style: "margin: 10px 0; color: #f56c6c;" }, [
            h("li", null, "上傳的頭像將立即生效"),
            h("li", null, "此操作無法復原"),
            h("li", null, "僅支援 JPG、PNG、GIF 格式"),
            h("li", null, "檔案大小不能超過 5MB"),
          ]),
        ]),
      }
    );
    avatarInputRefs.value[user.id]?.click();
  } catch (error) {
    // 用戶取消操作，不做任何事
  }
};

const handleAvatarChange = async (event, user) => {
  const file = event.target.files[0];
  if (!file) return;

  // 檢查文件類型
  const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error("只允許上傳 JPG、PNG 或 GIF 格式的圖片");
    event.target.value = "";
    return;
  }

  // 檢查文件大小（5MB）
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error("圖片大小不能超過 5MB");
    event.target.value = "";
    return;
  }

  try {
    await userStore.uploadUserAvatar(user.id, file);
    ElMessage.success("頭像上傳成功");
  } catch (error) {
    ElMessage.error("頭像上傳失敗");
  } finally {
    // 清空文件輸入框，以便可以重複上傳同一個文件
    event.target.value = "";
  }
};

// 處理重整
const handleRefresh = async () => {
  loading.value = true;
  try {
    await Promise.all([userStore.fetchUsers(), rbacStore.initialize()]);
    ElMessage.success("資料已更新");
  } catch (error) {
    ElMessage.error("更新失敗");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(async () => {
  loading.value = true;
  try {
    await Promise.all([userStore.fetchUsers(), rbacStore.initialize()]);
  } catch (error) {
    ElMessage.error("初始化失敗");
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.user-management {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding: 0 10px;
}

.search-input {
  max-width: 300px;
}

.user-list-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.user-detail-section {
  margin-top: 20px;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

.user-form {
  max-width: 800px;
  margin: 0 auto;
}

.avatar-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay .el-icon {
  color: white;
  font-size: 16px;
}

.hidden-file-input {
  display: none;
}

.role-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  min-height: 24px;
  align-items: center;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
}

.role-display:hover {
  background-color: var(--el-fill-color-light);
}

html.dark .role-display:hover {
  background-color: #2c2c2c;
}

.role-edit-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: var(--el-fill-color);
  border: 1px solid var(--el-border-color-light);
}

html.dark .role-edit-container {
  background-color: #2c2c2c;
  border-color: #4c4c4c;
}

.role-select {
  width: 100%;
}

.role-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.role-tag {
  margin-right: 4px;
}

.role-info-icon {
  margin-left: 4px;
  color: var(--el-color-info);
}

.no-roles {
  color: var(--el-text-color-secondary);
  font-style: italic;
  font-size: 12px;
}

/* 暗黑模式下的輸入框樣式 */
html.dark .search-input :deep(.el-input__wrapper) {
  background-color: #2c2c2c;
  border-color: #4c4c4c;
}

html.dark .search-input :deep(.el-input__inner) {
  color: #e0e0e0;
}

/* 暗黑模式下的選項表單樣式 */
html.dark .el-form-item__label {
  color: #e0e0e0;
}

/* 暗黑模式下無角色標籤的樣式 */
html.dark .no-roles {
  color: #909399;
}
</style>
