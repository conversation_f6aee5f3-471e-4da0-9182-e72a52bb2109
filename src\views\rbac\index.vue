<template>
  <div class="h-full w-full relative">
    <el-tabs
      v-model="activeTab"
      v-loading="loading">
      <el-tab-pane
        label="用戶管理"
        name="users">
        <user-management />
      </el-tab-pane>
      <el-tab-pane
        label="角色管理"
        name="roles">
        <role-management />
      </el-tab-pane>
      <el-tab-pane
        label="權限列表"
        name="permissions">
        <permission-list />
      </el-tab-pane>

      <el-tab-pane
        label="員工清單"
        name="employees">
        <org-employee-list />
      </el-tab-pane>

      <el-tab-pane
        label="組織架構"
        name="org-structure">
        <org-structure />
      </el-tab-pane>

      <!-- <el-tab-pane label="用戶角色" name="user-roles">
        <user-role-management />
      </el-tab-pane> -->

      <div id="rbac-header-actions">222</div>
    </el-tabs>
  </div>
</template>

<script setup>
import UserManagement from "./components/UserManagement.vue";
import RoleManagement from "./components/RoleManagement.vue";
import PermissionList from "./components/PermissionList.vue";
import OrgGroupManagement from "./components/OrgGroupManagement.vue";
import OrgEmployeeManagement from "./components/OrgEmployeeMangement.vue";
import UserRoleManagement from "./components/UserRoleManagement.vue";
import OrgEmployeeList from "./components/OrgEmployeeList.vue";
import OrgStructure from "./components/OrgStructure.vue";
import { useRbacStore } from "@/stores/rbac";

const activeTab = ref("users");
const rbacStore = useRbacStore();
const loading = computed(() => rbacStore.loading);

onMounted(async () => {
  await rbacStore.initialize();
});
</script>

<style scoped>
:deep(.el-tabs) {
  @apply h-full p-2 bg-light-mode dark:bg-dark-mode;
}

:deep(.el-tabs__header) {
  @apply dark:border-gray-700;
  border-bottom-color: var(--el-border-color) !important;
}

:deep(.el-tabs__content) {
  @apply h-[calc(100%-40px)] overflow-auto;
}

:deep(.el-tab-pane) {
  @apply h-full;
}
</style>
