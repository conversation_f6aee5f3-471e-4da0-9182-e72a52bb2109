<template>
  <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
    <h3 class="text-lg font-medium mb-4">File 資訊</h3>
    <div
      v-if="fileData"
      class="space-y-4">
      <div
        v-if="!fileData.files?.length"
        class="text-center text-gray-500 dark:text-gray-400">
        暫無相關文件
      </div>
      <template v-else>
        <div class="grid gap-4">
          <div
            v-for="file in fileData.files"
            :key="file.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <div class="flex items-center space-x-3">
              <div
                class="w-10 h-10 flex items-center justify-center file-icon-wrapper"
                :class="getFileTypeClass(file.type, file.name)">
                <component
                  :is="getFileIcon(file.type, file.name)"
                  :size="30"
                  :width="30"
                  :height="30"
                  class="file-icon"
                  :class="getIconColorClass(file.type, file.name)" />
              </div>
              <div>
                <p class="text-sm font-medium">{{ file.name }}</p>
                <p
                  v-if="file.type === 'sticky'"
                  class="text-xs text-gray-500 italic">
                  {{
                    file.content.length > 30
                      ? file.content.slice(0, 30) + "..."
                      : file.content
                  }}
                </p>
                <p
                  v-else
                  class="text-xs text-gray-500">
                  {{ formatFileSize(file.size) }}
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <template v-if="file.type === 'sticky'">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleViewStickyNote(file)">
                  查看內容
                </el-button>
              </template>
              <template v-else>
                <el-button
                  v-if="file.url"
                  type="primary"
                  size="small"
                  link
                  @click="handlePreview(file)">
                  預覽
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleDownload(file)">
                  下載
                </el-button>
              </template>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <!-- 檔案預覽對話框 -->
  <Teleport to="body">
    <div
      v-if="previewVisible && currentFile"
      class="custom-dialog-container">
      <div
        class="custom-dialog-overlay"
        @click="previewVisible = false"></div>
      <div
        class="custom-dialog"
        :class="{ 'is-fullscreen': isFullscreen }">
        <div class="custom-dialog-header">
          <component
            :is="getFileIcon(currentFile.type, currentFile.name)"
            :size="24"
            :width="24"
            :height="24"
            class="file-icon-preview"
            :class="getIconColorClass(currentFile.type, currentFile.name)" />
          <h4 class="custom-dialog-title">{{ currentFile.name }}</h4>
          <div class="custom-dialog-buttons">
            <!-- 縮放控制，只對圖片顯示 -->
            <div
              v-if="isImage(currentFile.name)"
              class="button-group">
              <el-button
                link
                @click="handleZoomOut">
                <ZoomOut :size="16" />
              </el-button>
              <el-button
                link
                @click="handleZoomIn">
                <ZoomIn :size="16" />
              </el-button>
            </div>
            <!-- 全螢幕切換 -->
            <el-button
              link
              @click="toggleFullscreen">
              <component
                :is="isFullscreen ? Minimize2 : Maximize2"
                :size="16" />
            </el-button>
            <!-- 關閉按鈕 -->
            <el-button
              link
              @click="previewVisible = false">
              <X :size="16" />
            </el-button>
          </div>
        </div>

        <div
          class="preview-content"
          :class="{ 'is-fullscreen': isFullscreen }">
          <!-- 圖片預覽 -->
          <div
            v-if="isImage(currentFile.name)"
            class="image-preview">
            <img
              ref="imageRef"
              :src="currentFile.url"
              :alt="currentFile.name"
              :style="{
                transform: `scale(${zoomLevel})`,
                cursor: isDragging ? 'grabbing' : 'grab',
              }"
              @mousedown.stop="startDrag"
              @mousemove.stop="onDrag"
              @mouseup.stop="stopDrag"
              @mouseleave.stop="stopDrag" />
          </div>

          <!-- 影片預覽 -->
          <div
            v-else-if="isVideo(currentFile.name)"
            class="video-preview">
            <video
              ref="videoRef"
              :src="currentFile.url"
              class="video-player"
              controls
              autoplay
              playsinline>
              您的瀏覽器不支持影片播放。
            </video>
          </div>

          <!-- PDF 預覽 -->
          <div
            v-else-if="isPdf(currentFile.name)"
            class="pdf-preview">
            <iframe
              :src="getPdfViewerUrl(currentFile.url)"
              class="pdf-iframe"
              frameborder="0"
              referrerpolicy="no-referrer"
              allow="fullscreen"></iframe>
          </div>

          <!-- PPT 預覽 -->
          <div
            v-else-if="isPpt(currentFile.name)"
            class="ppt-preview">
            <div class="ppt-container">
              <vue-office-pptx
                :key="pptKey"
                :src="currentFile.url"
                class="ppt-content" />
            </div>
          </div>

          <!-- Word預覽 -->
          <div
            v-else-if="isWord(currentFile.name)"
            class="word-preview">
            <vue-office-docx
              :key="wordKey"
              :src="currentFile.url"
              style="height: 100%; width: 100%; overflow: visible" />
          </div>
          <!-- Excel預覽 -->
          <div
            v-else-if="isExcel(currentFile.name)"
            class="excel-preview">
            <vue-office-excel
              :key="excelKey"
              :src="currentFile.url"
              style="height: 80vh" />
          </div>

          <!-- TXT文件預覽 -->
          <div
            v-else-if="isTxt(currentFile.name)"
            class="txt-preview">
            <div class="txt-content-wrapper">
              <pre class="txt-content">{{ textContent }}</pre>
            </div>
          </div>

          <!-- 其他檔案類型預覽 -->
          <div
            v-else
            class="generic-preview">
            <iframe
              v-if="currentFile.url"
              :src="currentFile.url"
              class="generic-iframe"
              frameborder="0"></iframe>
            <div
              v-else
              class="preview-error">
              <AlertTriangle
                :size="48"
                class="text-yellow-500 mb-2" />
              <p>無法預覽此檔案類型</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import {
  Document,
  Picture,
  Files as FilesIcon,
  Memo,
  QuestionFilled,
  VideoPlay,
} from "@element-plus/icons-vue";
import {
  Download,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Minimize2,
  X,
  AlertTriangle,
} from "lucide-vue-next";
import { ElMessageBox } from "element-plus";
// 導入自定義圖標組件
import FileWord from "@/assets/icons/FileWord.vue";
import FileExcel from "@/assets/icons/FileExcel.vue";
import FilePowerpoint from "@/assets/icons/FilePowerpoint.vue";
import FilePDF from "@/assets/icons/FilePDF.vue";
// 導入文件預覽相關
import { useFilePreview } from "@/composables/useFilePreview";
import VueOfficePptx from "@vue-office/pptx";
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
import { computed, ref, watch } from "vue";

const props = defineProps({
  flowInstance: {
    type: Object,
    default: null,
  },
});

// 使用檔案預覽 composable
const {
  previewVisible,
  zoomLevel,
  isDragging,
  imageRef,
  videoRef,
  isFullscreen,
  currentPage,
  totalPages,
  pptKey,
  wordKey,
  excelKey,
  isImage,
  isVideo,
  isPdf,
  isWord,
  isExcel,
  isPpt,
  isTxt,
  isPreviewable,
  getPdfViewerUrl,
  handlePreview: previewFile,
  handleDownload: downloadFile,
  handleZoomIn,
  handleZoomOut,
  startDrag,
  onDrag,
  stopDrag,
  handlePdfLoad,
  formatFileSize,
  toggleFullscreen,
  getFileIcon,
  getIconColorClass,
  getFileTypeClass,
} = useFilePreview();

// 當前預覽的文件
const currentFile = ref(null);
// 文本內容
const textContent = ref("");

// 監聽當前文件變化，自動載入文本內容
watch(
  () => [currentFile.value, previewVisible.value],
  async ([file, visible]) => {
    // 重置文本內容
    textContent.value = "";

    // 如果當前文件是文本文件且對話框可見，則載入內容
    if (file && visible && isTxt(file.name) && file.url) {
      try {
        const response = await fetch(file.url);
        if (response.ok) {
          textContent.value = await response.text();
        } else {
          console.error(`無法讀取文本檔案內容: ${response.statusText}`);
        }
      } catch (error) {
        console.error("讀取文本檔案失敗:", error);
      }
    }
  },
  { immediate: true }
);

// 檢查是否為文檔
const isDocument = (filename) => {
  if (!filename) return false;
  return (
    isPdf(filename) ||
    isWord(filename) ||
    isExcel(filename) ||
    isPpt(filename) ||
    isTxt(filename)
  );
};

const fileData = computed(() => {
  if (!props.flowInstance) return { files: [] };

  // 從 flowInstance.nodes 中取出 type 為 file 或 sticky 的節點
  const nodes = props.flowInstance.nodes || [];
  const fileNodes = nodes.filter(
    (node) => node.type === "file" || node.type === "sticky"
  );
  console.log("檔案節點資料:", fileNodes);

  // 處理檔案資料，從節點資料轉換為檔案資料格式
  const processedFiles = fileNodes.map((node) => {
    const fileData = node.data?.fileData || {};

    return node.type === "sticky"
      ? {
          id: node.id,
          name: "便利貼",
          type: "sticky",
          content: node.data?.content || "",
        }
      : {
          id: node.id,
          name: node.data?.fileName || "未命名檔案",
          type: node.data?.fileType || "unknown",
          size: node.data?.fileSize || NaN,
          url: node.data?.fileUrl || "",
        };
  });

  console.log("處理後的檔案資料:", processedFiles);

  return {
    files: processedFiles,
    totalCount: processedFiles.length,
    totalSize: processedFiles.reduce((sum, file) => sum + (file.size || 0), 0),
  };
});

// 處理預覽
const handlePreview = (file) => {
  currentFile.value = file;
  previewFile(file);
};

// 處理下載
const handleDownload = (file) => {
  downloadFile(file);
};

// 查看便利貼內容
const handleViewStickyNote = (file) => {
  ElMessageBox.alert(
    file.content.replace(/\n/g, "<br/>") || "沒有內容",
    "便利貼內容",
    {
      confirmButtonText: "確定",
      dangerouslyUseHTMLString: true,
      customClass: "sticky-note-dialog",
    }
  );
};
</script>

<style scoped>
.file-icon {
  display: inline-block;
  min-width: 30px;
  min-height: 30px;
  max-width: 30px;
  max-height: 30px;
  object-fit: contain;
}

.file-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  width: 40px;
  height: 40px;
  background-color: rgba(241, 245, 249, 0.7);
}

/* 設置元素圖標大小 */
.el-icon {
  font-size: 30px;
  width: 30px;
  height: 30px;
}

/* 設置 Element Plus 圖標大小 */
.file-icon svg,
:deep(.el-icon svg),
:deep(.el-icon img) {
  width: 100%;
  height: 100%;
}

/* 設置自定義文件圖標 */
.file-icon img {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

/* 特定文件類型的樣式調整 */
.file-pdf {
  background-color: rgba(254, 226, 226, 0.3); /* 紅色淺背景 */
}

.file-word {
  background-color: rgba(219, 234, 254, 0.3); /* 藍色淺背景 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-excel {
  background-color: rgba(220, 252, 231, 0.3); /* 綠色淺背景 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-powerpoint {
  background-color: rgba(255, 237, 213, 0.3); /* 橙色淺背景 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-image {
  background-color: rgba(237, 233, 254, 0.3); /* 紫色淺背景 */
}

.file-video {
  background-color: rgba(224, 242, 254, 0.3); /* 藍色淺背景 */
}

.file-text {
  background-color: rgba(243, 244, 246, 0.3); /* 灰色淺背景 */
}

.file-sticky {
  background-color: rgba(254, 249, 195, 0.3); /* 黃色淺背景 */
}

/* 自定義對話框樣式 */
.custom-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(2px);
}

.custom-dialog {
  position: relative;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  width: 1000px;
}

.custom-dialog.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 0;
  max-width: 100%;
  max-height: 100%;
}

.custom-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f7f7f9;
  border-bottom: 1px solid #eaeaea;
}

.custom-dialog-title {
  margin: 0 0 0 8px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 預覽對話框中的文件圖標 */
.file-icon-preview {
  min-width: 24px;
  min-height: 24px;
  max-width: 24px;
  max-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 預覽對話框中的文件圖標內部SVG */
.file-icon-preview svg,
.file-icon-preview :deep(svg) {
  width: 100%;
  height: 100%;
}

.custom-dialog-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 2px;
  border-right: 1px solid #eaeaea;
  padding-right: 4px;
  margin-right: 4px;
}

.preview-content {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: #fafafa;
}

.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fafafa;
  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.video-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
}

.video-player {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.pdf-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fafafa;
  padding-top: 0;
  overflow: hidden;
}

.pdf-iframe {
  width: 100%;
  height: 80vh;
  border: none;
}

.ppt-preview {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: white;
  overflow: hidden;
}

.word-preview,
.excel-preview {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: white;
}

.ppt-container {
  width: 100%;
  height: 80vh;
  overflow: hidden;
  position: relative;
}

.ppt-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ppt-preview :deep(.slide-container),
.word-preview :deep(.docx),
.excel-preview :deep(.excel-container) {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

/* 專門針對 PPT 幻燈片元素的樣式 */
.ppt-preview :deep(.slide) {
  overflow-x: hidden !important;
  max-width: 100% !important;
}

/* 修復捲軸問題的特定樣式 */
.ppt-preview :deep(.canvas-container) {
  overflow-x: hidden !important;
  max-width: 100% !important;
}

.ppt-preview :deep(.upper-canvas),
.ppt-preview :deep(.lower-canvas) {
  max-width: 100% !important;
  width: auto !important;
}

/* 確保幻燈片內容自適應 */
.ppt-preview :deep(.slide-content) {
  transform-origin: center center;
  max-width: 100% !important;
  overflow: hidden !important;
}

/* 對 PPT 內部所有元素隱藏捲軸 */
.ppt-preview :deep(*) {
  scrollbar-width: none;
}

.ppt-preview :deep(*::-webkit-scrollbar) {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

/* TXT文件預覽樣式 */
.txt-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fafafa;
  padding-top: 0;
}

.txt-content-wrapper {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: white;
  overflow: auto;
  border-radius: 4px;
}

.txt-content {
  font-family: "Consolas", "Source Code Pro", monospace;
  font-size: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  padding: 0;
  color: #333;
  max-width: 100%;
}

.generic-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.generic-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #666;
  padding: 20px;
  background-color: #f9f9f9;
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .custom-dialog {
    background: #1e1e2d;
  }
  .custom-dialog-header {
    background: #252536;
    border-bottom-color: #333345;
  }
  .custom-dialog-title {
    color: #e0e0e0;
  }
  .preview-content {
    background-color: #151521;
  }
  .preview-error {
    color: #aaa;
    background-color: #252536;
  }
  .txt-content-wrapper {
    background-color: #252536;
  }
  .txt-content {
    color: #e0e0e0;
  }
  .txt-preview {
    background-color: #151521;
  }
  .pdf-preview {
    background-color: #151521;
  }
  .pdf-iframe {
    background-color: #252536;
  }
  .ppt-preview {
    background-color: #151521;
  }
  .ppt-preview :deep(.slide-container) {
    background-color: #252536;
    overflow-x: hidden !important;
  }
  .ppt-container {
    background-color: #252536;
  }
  .ppt-preview :deep(.slide) {
    overflow-x: hidden !important;
  }
}
</style>
