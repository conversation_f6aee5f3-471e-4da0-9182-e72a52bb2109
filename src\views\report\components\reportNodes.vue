<template>
  <div class="p-4 rounded-lg shadow">
    <div
      v-if="nodeData"
      class="space-y-8">
      <div
        v-if="!nodeData.length"
        class="text-center text-gray-500 dark:text-gray-400">
        暫無相關節點
      </div>
      <template v-else>
        <div class="space-y-8">
          <div
            v-for="node in nodeData"
            :key="node.id"
            class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
            <div
              v-if="loadedComponents[node.type]"
              class="w-full relative">
              <component
                :is="loadedComponents[node.type]"
                v-bind="getNodeDataWithContext(node)"
                :id="node.id"
                :selected="false"
                :isReportMode="true"
                nodeWidth="100%"
                nodeHeight="100%" />
            </div>
            <div
              v-else
              class="p-4">
              <h4 class="text-sm font-medium">
                {{ node.data?.name || "未命名節點" }}
              </h4>
              <p class="text-xs text-gray-500">
                {{ node.data?.description || "無描述" }}
              </p>
              <p class="text-xs text-red-500">
                無法載入對應的組件類型: {{ node.type }}
              </p>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { useFlowNodeComponents } from "@/composables/useFlowNodeComponents";
import { useThemeStore } from "@/stores/theme";
import { wait } from "@/utils/dateUtils";

const props = defineProps({
  flowInstance: {
    type: Object,
    default: null,
  },
});

// 取得主題狀態
const themeStore = useThemeStore();
const isDark = computed(() => themeStore.isDark);

// 節點組件管理
const { flowNodeComponents, currentComponent, loadFlowNodeComponents } =
  useFlowNodeComponents();
const loadedComponents = ref({});

const nodeData = computed(() => {
  if (!props.flowInstance) return [];

  const nodes = props.flowInstance.nodes || [];
  const edges = props.flowInstance.edges || [];
  console.log("節點資料:", nodes);
  console.log("邊資料:", edges);

  // 處理鄰接表
  const graph = {};
  nodes.forEach((node) => {
    graph[node.id] = [];
  });
  edges.forEach((edge) => {
    if (graph[edge.source]) {
      graph[edge.source].push(edge.target);
    }
  });

  // 計算入度
  const inDegree = {};
  nodes.forEach((node) => {
    inDegree[node.id] = 0;
  });
  edges.forEach((edge) => {
    inDegree[edge.target]++;
  });

  // 找出所有入度為0的節點(起始節點)
  const queue = [];
  Object.keys(inDegree).forEach((nodeId) => {
    if (inDegree[nodeId] === 0) {
      queue.push(nodeId);
    }
  });

  // 拓撲排序
  let topologicalOrder = [];
  while (queue.length > 0) {
    const nodeId = queue.shift();
    const node = nodes.find((n) => n.id === nodeId);
    if (node) {
      topologicalOrder.push(node);
    }

    graph[nodeId].forEach((neighbor) => {
      inDegree[neighbor]--;
      if (inDegree[neighbor] === 0) {
        queue.push(neighbor);
      }
    });
  }
  console.log("拓撲排序結果:", topologicalOrder);

  // 處理排序後的節點
  const processedNodes = topologicalOrder
    .map((node) => ({
      ...node,
      selected: false,
      draggable: false,
      selectable: false,
      connectable: false,
    }))
    .filter((node) => {
      // 只返回有對應已載入組件的節點
      const hasComponent = loadedComponents.value[node.type];
      if (!hasComponent) {
        console.log(`節點類型 ${node.type} 的組件尚未載入，將不會顯示`);
      }
      return hasComponent;
    });
  console.log("processedNodes:", processedNodes);
  return processedNodes;
});

const loadNodeTypes = async (nodes, runCount = 0) => {
  console.log("DO loadNodeTypes!");
  if (!nodes || nodes.length === 0) {
    console.log("無節點數據，跳過載入組件");
    return;
  }

  // 剔除 file 與便利貼節點
  const uniqueTypes = [
    ...new Set(
      nodes
        .map((node) => node.type)
        .filter((type) => type && type !== "file" && type !== "sticky")
    ),
  ];
  // console.log("待載入的節點類型:", uniqueTypes);

  // 確保流程組件已載入
  if (
    !flowNodeComponents.value ||
    Object.keys(flowNodeComponents.value).length === 0
  ) {
    if (runCount >= 5) {
      console.error("flowNodeComponents載入失敗，請檢查組件路徑或重新載入頁面");
      return;
    }
    console.log(
      `flowNodeComponents尚未載入，等候嘗試再次載入流程組件 當前執行次數: ${runCount}`
    );
    await wait(0.3); // 等待0.3秒
    loadNodeTypes(nodes, runCount + 1); // 遞迴調用
    return;
  }

  // 重置已載入的組件
  loadedComponents.value = {};

  // 載入節點組件
  for (const [key, value] of Object.entries(flowNodeComponents.value)) {
    if (!key.startsWith("/src/components/flow-nodes/business/")) continue;

    const pathParts = key.split("/");
    const componentName = pathParts[pathParts.length - 1].replace(".vue", "");

    if (!uniqueTypes.includes(componentName)) continue;

    try {
      if (!value || !value.default) {
        console.error(`組件 ${componentName} 載入失敗: 無效的組件定義`);
        continue;
      }
      loadedComponents.value[componentName] = value.default;
      console.log(`成功載入組件 ${componentName}`);
    } catch (error) {
      console.error(`載入組件 ${componentName} 時發生錯誤:`, error);
    }
  }

  console.log("載入的節點組件:", loadedComponents.value);
};

onMounted(async () => {
  console.log("Component[reportNodes] onMounted!");
  try {
    // 預載所有節點組件
    await loadFlowNodeComponents();
    console.log("預載所有節點組件:", flowNodeComponents.value);
  } catch (error) {
    console.error("組件初始化失敗:", error);
    ElMessage.error("組件初始化失敗! 請稍後重試");
  }
});

// Watch flowInstance changes
watch(
  () => props.flowInstance,
  async (newVal) => {
    // console.log("flowInstance 變更:", newVal);
    if (newVal && newVal?.nodes) {
      // console.log("DO flowInstance.nodes loadNodeTypes!");
      try {
        await loadNodeTypes(newVal.nodes);
      } catch (error) {
        console.error("載入節點類型失敗:", error);
      }
    }
  },
  { immediate: true }
);

// 從 flowInstance.context 獲取節點資料並與 node.data 合併
const getNodeDataWithContext = (node) => {
  // 將flowInstance.context加入data
  if (!node || !node.data) return { data: {} };
  if (!props.flowInstance || !props.flowInstance.context)
    return { data: node.data };

  // 回傳只包含data的物件
  const result = {
    data: {
      ...node.data,
      context: props.flowInstance.context,
    },
  };

  console.log("getNodeDataWithContext 傳遞的資料:", result);
  return result;
};
</script>

<style scoped>
.node-enter-active,
.node-leave-active {
  transition: all 0.3s ease;
}

.node-enter-from,
.node-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
