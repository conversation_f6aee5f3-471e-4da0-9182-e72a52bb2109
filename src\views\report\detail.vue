<template>
  <div class="p-0">
    <Teleport
      to="#header-actions"
      defer>
      <div class="flex items-center space-x-2">
        <!-- Star 按鈕 -->
        <div
          class="cursor-pointer mr-4"
          @click.stop="toggleFavorite">
          <Star
            :size="20"
            :class="[
              isFavorited
                ? 'text-yellow-400 dark:text-yellow-400 fill-yellow-400'
                : 'text-gray-400 dark:text-gray-400',
              'hover:text-yellow-500 dark:hover:text-yellow-500',
            ]" />
        </div>

        <!-- 排版格式標題和選擇器 -->
        <div class="flex items-center space-x-2">
          <span class="text-gray-600 dark:text-gray-400">排版格式：</span>
          <el-select
            v-model="layoutMode"
            class="!w-32">
            <el-option
              v-for="option in layoutOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value" />
          </el-select>

          <!-- 流程實例按鈕 -->
          <el-button
            type="primary"
            @click="navigateToFlowInstance"
            class="ml-2">
            <GitBranch
              class="mr-1"
              :size="16" />
            流程實例
          </el-button>
        </div>
      </div>
    </Teleport>

    <div class="p-4">
      <!-- loading 狀態 -->
      <el-skeleton
        :loading="loading"
        animated>
        <template #template>
          <div class="space-y-4">
            <el-skeleton-item
              variant="p"
              style="width: 100%; height: 100%" />
          </div>
        </template>

        <template #default>
          <!-- Tab 模式 -->
          <template v-if="layoutMode === 'tab'">
            <el-tabs>
              <el-tab-pane label="Node">
                <ReportNodes :flow-instance="flowInstance" />
              </el-tab-pane>
              <el-tab-pane label="File">
                <ReportFiles :flow-instance="flowInstance" />
              </el-tab-pane>
            </el-tabs>
          </template>

          <!-- 左右模式 -->
          <template v-else-if="layoutMode === 'horizontal'">
            <div class="grid grid-cols-2 gap-4">
              <ReportNodes :flow-instance="flowInstance" />
              <ReportFiles :flow-instance="flowInstance" />
            </div>
          </template>

          <!-- 上下模式 -->
          <template v-else-if="layoutMode === 'vertical'">
            <div class="space-y-4">
              <ReportNodes :flow-instance="flowInstance" />
              <ReportFiles :flow-instance="flowInstance" />
            </div>
          </template>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Star, GitBranch } from "lucide-vue-next";
import ReportNodes from "./components/reportNodes.vue";
import ReportFiles from "./components/reportFiles.vue";
import { useUserStore } from "@/stores/user";
import { useFavoriteStore } from "@/stores/favorite";
import { useFlowStore } from "@/stores/flowStore";
import { getFlowInstanceById } from "@/api/modules/flow";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const favoriteStore = useFavoriteStore();
const flowInstance = ref(null); // 流程實例資料
const flowStore = useFlowStore();

// 排版選項
const layoutOptions = [
  { value: "tab", label: "分頁" },
  { value: "horizontal", label: "左右" },
  { value: "vertical", label: "上下" },
];
const instanceId = route.params.id;
const loading = ref(false);
const layoutMode = ref("tab"); // 當前排版模式，預設為 tab

// 檢查是否已關注
const isFavorited = computed(() => {
  return favoriteStore.isFavorited("report", instanceId);
});

// 切換關注狀態
const toggleFavorite = async () => {
  if (!userStore.user?.id) {
    ElMessage.warning("請先登入");
    return;
  }

  try {
    if (isFavorited.value) {
      await favoriteStore.removeFromFavorite("report", route.params.id);
    } else {
      await favoriteStore.addToFavorite({
        type: "report",
        resourceId: route.params.id,
        name: flowInstance.value?.template?.name || `報表 ${route.params.id}`,
        path: `/report/${route.params.id}`,
        createdBy: userStore.user.id,
      });
    }
  } catch (error) {
    console.error("切換關注狀態失敗:", error);
    ElMessage.error("操作失敗，請稍後重試");
  }
};

// 導航到流程實例頁面
const navigateToFlowInstance = () => {
  if (instanceId) {
    router.push(`/flow-instances/${instanceId}`);
  } else {
    ElMessage.warning("無法找到流程實例 ID");
  }
};

// 初始化
onMounted(async () => {
  console.log("Instance ID:", instanceId);

  if (!instanceId) {
    ElMessage.error("缺少實例 ID");
    return;
  }

  try {
    loading.value = true;

    // 獲取流程實例資料，包括已刪除的流程
    const response = await getFlowInstanceById(instanceId, {
      showDeleted: "true",
    });
    flowInstance.value = response.data;
    console.log("載入實例數據成功", flowInstance.value);

    // 設置麵包屑
    if (flowInstance.value) {
      flowStore.setBreadcrumbInstance(flowInstance.value, {
        fromProject: true,
        noLoadProject: true,
      });
    }
  } catch (error) {
    console.error("初始化失敗:", error);
    ElMessage.error("載入資料失敗");
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
:deep(.el-tabs__content) {
  padding: 0;
}
</style>
