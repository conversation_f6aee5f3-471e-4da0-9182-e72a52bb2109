<template>
  <div class="p-0">
    <Teleport
      to="#header-actions"
      defer>
      <div class="flex items-center space-x-2">
        <template v-if="isAllowAll">
          <el-radio-group
            v-model="queryMode"
            size="small">
            <el-radio-button label="all">全部數據</el-radio-button>
            <el-radio-button label="personal">個人數據</el-radio-button>
          </el-radio-group>
        </template>

        <el-select
          v-model="filters.projectIds"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="選擇專案"
          clearable
          class="!w-96 !ml-6">
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id" />
        </el-select>

        <div class="flex items-center">
          <span class="mr-2 text-sm text-gray-600 dark:text-gray-400"
            >最後更新時間:</span
          >
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            value-format="YYYY-MM-DD"
            class="!w-80" />
        </div>

        <el-checkbox
          v-model="showDeleted"
          class="!ml-6"
          label="顯示已刪除流程" />
      </div>
    </Teleport>

    <div class="p-4">
      <!-- 統計卡片 -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h3 class="text-lg font-medium text-blue-700 dark:text-blue-300">
            總專案數
          </h3>
          <p class="text-3xl font-bold text-blue-600 dark:text-blue-200">
            {{ statistics.displayedProjects }}/{{
              statistics.totalProjects || 0
            }}
          </p>
        </div>
        <div
          class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg hover:transform hover:translate-y-[-2px] hover:shadow-md transition-all duration-300">
          <el-tooltip
            placement="top"
            :show-after="500"
            :content="projectInstancesTooltip"
            raw-content>
            <div>
              <h3
                class="text-lg font-medium text-green-700 dark:text-green-300">
                總流程數
              </h3>
              <p class="text-3xl font-bold text-green-600 dark:text-green-200">
                {{ statistics.displayedInstances }}/{{
                  statistics.totalInstances || 0
                }}
              </p>
            </div>
          </el-tooltip>
        </div>
      </div>

      <!-- 資料表格 -->
      <el-card class="box-card">
        <el-table
          :data="tableData"
          v-loading="loading"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName">
          <el-table-column
            prop="projectName"
            label="專案名稱"
            min-width="180"
            sortable="custom">
            <template #default="{ row }">
              <el-tooltip
                :content="row.projectDescription"
                placement="top"
                :show-after="500">
                <span>{{ row.projectName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="sequenceNumber"
            width="100"
            align="center">
            <template #header>
              <div class="flex items-center justify-center">
                流程編號
                <el-tooltip
                  content="流程於專案中建立排序"
                  placement="top"
                  :show-after="500">
                  <span class="ml-1">
                    <el-icon
                      class="text-gray-400 dark:text-gray-500 mt-1"
                      :size="16"
                      ><InfoFilled
                    /></el-icon>
                  </span>
                </el-tooltip>
              </div>
            </template>
            <template #default="{ row }">
              {{ row.sequenceNumber }}
            </template>
          </el-table-column>
          <el-table-column
            prop="templateName"
            label="流程模板名稱"
            min-width="180">
            <template #default="{ row }">
              <el-tooltip
                :content="row.templateDescription"
                placement="top"
                :show-after="500">
                <span>{{ row.templateName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="creatorName"
            label="建立者"
            width="120">
            <template #default="{ row }">
              <UserAvatar
                :user="row.creator"
                :show-name="true"
                shape="circle"
                class="mr-1" />
            </template>
          </el-table-column>
          <el-table-column
            prop="createdAt"
            label="建立時間"
            width="180"
            sortable="custom">
            <template #default="{ row }">
              {{ formatTimestamp(row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="updatedAt"
            label="最後更新時間"
            width="180"
            sortable="custom">
            <template #default="{ row }">
              {{ formatTimestamp(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="status"
            label="狀態"
            width="120">
            <template #default="{ row }">
              <el-tag
                v-if="row.status === 'deleted'"
                type="danger"
                effect="dark"
                >已刪除</el-tag
              >
              <el-tag
                v-else
                type="info"
                >{{ row.status }}</el-tag
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { getProjectsWithInstances } from "@/api/modules/report";
import { formatTimestamp } from "@/utils/dateUtils";
import UserAvatar from "@/components/UserAvatar.vue";

const userStore = useUserStore();
// console.log("userStore.user.role:", userStore.user.roles);

const router = useRouter();
const loading = ref(false);
const projects = ref([]);
const reportData = ref([]);

// 檢查是否允許查看所有數據
const isAllowAll = computed(() => {
  // TODO 整理權限時需修改
  const roles = userStore.user?.roles || [];
  return roles.some((role) => ["SUPERADMIN", "ADMIN"].includes(role.name));
});

// 查詢模式（全查/個人），預設為個人
const queryMode = ref("personal");

// 過濾器
const sortDefault = {
  by: "updatedAt", // 預設排序欄位
  order: "descending", // 預設排序方向
};
const filters = reactive({
  projectIds: [],
  dateRange: [],
  sortBy: sortDefault.by,
  sortOrder: sortDefault.order,
});

// 統計資訊
const statistics = reactive({
  totalProjects: 0,
  totalInstances: 0,
  displayedProjects: 0,
  displayedInstances: 0,
});

// 計算每個專案的流程數提示文字
const projectInstancesTooltip = computed(() => {
  const projectCounts = reportData.value.map(
    (project) => `${project.name}：${project.flowInstances.length} 個流程`
  );
  return `<div class="font-medium mb-2">專案流程統計</div>${projectCounts.join(
    "<br>"
  )}`;
});

// 是否顯示已刪除的流程實例（預設不顯示）
const showDeleted = ref(false);

// 計算表格資料
const tableData = computed(() => {
  let data = [];

  // 先收集所有實例並按 updatedAt 排序
  reportData.value.forEach((project) => {
    // 根據 showDeleted 狀態過濾已刪除的流程實例
    const filteredInstances = project.flowInstances.filter((instance) => {
      // 如果showDeleted為true，則顯示所有實例，否則只顯示非deleted狀態的實例
      return showDeleted.value || instance.status !== "deleted";
    });

    // 對每個專案的實例先按 updatedAt 排序
    const sortedInstances = [...filteredInstances].sort(
      (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
    );

    // 添加序號並轉換資料
    sortedInstances.forEach((instance, index) => {
      data.push({
        id: instance.id,
        projectId: project.id,
        projectName: project.name,
        projectDescription: project.description,
        sequenceNumber: index + 1, // 專案內的序號
        templateName: instance.template?.name,
        templateDescription: instance.template?.description,
        creator: instance.creator,
        createdAt: instance.createdAt,
        updatedAt: instance.updatedAt,
        status: instance.status, // 添加狀態欄位，用於識別已刪除的流程
      });
    });
  });

  // 應用過濾器
  if (filters.projectIds.length > 0) {
    data = data.filter((item) => filters.projectIds.includes(item.projectId));
  }

  if (filters.dateRange?.length === 2) {
    data = data.filter((item) => {
      const updateDate = new Date(item.updatedAt).toISOString().split("T")[0];
      return (
        updateDate >= filters.dateRange[0] && updateDate <= filters.dateRange[1]
      );
    });
  }

  // 應用排序（除了 sequenceNumber，因為它是專案內的序號）
  if (filters.sortBy && filters.sortBy !== "sequenceNumber") {
    data.sort((a, b) => {
      let val1 = a[filters.sortBy];
      let val2 = b[filters.sortBy];

      // 處理日期類型
      if (filters.sortBy === "createdAt" || filters.sortBy === "updatedAt") {
        val1 = new Date(val1);
        val2 = new Date(val2);
      }

      if (val1 < val2) return filters.sortOrder === "ascending" ? -1 : 1;
      if (val1 > val2) return filters.sortOrder === "ascending" ? 1 : -1;
      return 0;
    });
  }

  return data;
});

// 監聽數據變化，更新統計信息
watch(
  [reportData, tableData, showDeleted],
  ([newReportData, newTableData]) => {
    // 計算總數
    statistics.totalProjects = newReportData?.length || 0;

    // 計算總流程數，若不顯示已刪除則不計入
    if (showDeleted.value) {
      // 包含所有流程（包括已刪除）
      statistics.totalInstances =
        newReportData?.reduce(
          (sum, project) => sum + project.flowInstances.length,
          0
        ) || 0;
    } else {
      // 只計算非刪除狀態的流程
      statistics.totalInstances =
        newReportData?.reduce(
          (sum, project) =>
            sum +
            project.flowInstances.filter(
              (instance) => instance.status !== "deleted"
            ).length,
          0
        ) || 0;
    }

    // 計算當前顯示數
    const uniqueProjects = new Set(newTableData.map((item) => item.projectId));
    statistics.displayedProjects = uniqueProjects.size;
    statistics.displayedInstances = newTableData.length;
  },
  { immediate: true }
);

// 監聽查詢模式變化，重新獲取數據
watch(queryMode, () => {
  initData();
});

// 初始化數據
const initData = async () => {
  try {
    loading.value = true;
    const params = {};
    // 如果是個人模式或非管理員，添加用戶 ID
    if (queryMode.value === "personal") {
      params.userIds = [userStore.user.id];
    }
    const result = await getProjectsWithInstances(params);
    reportData.value = result.data;

    // 更新專案列表(用於過濾)
    projects.value = result.data.map((p) => ({
      id: p.id,
      name: p.name,
    }));
  } catch (error) {
    console.error("獲取報表數據失敗:", error);
    ElMessage.error("獲取報表數據失敗");
  } finally {
    loading.value = false;
  }
};

// 處理排序變更
const handleSortChange = ({ prop, order }) => {
  // console.log("handleSortChange:", prop, order);
  if (!order) {
    // 如果沒有排序順序，則還原默認
    filters.sortBy = sortDefault.by;
    filters.sortOrder = sortDefault.order;
    console.log("還原默認排序:", filters.sortBy, filters.sortOrder);
    return;
  }
  filters.sortBy = prop;
  filters.sortOrder = order || "descending";
};

// 處理表格行樣式
const tableRowClassName = ({ row }) => {
  // 為已刪除的行添加特殊樣式
  if (row.status === "deleted") {
    return "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 deleted-row";
  }
  return "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800";
};

// 處理行點擊
const handleRowClick = (row) => {
  router.push(`/report/${row.id}`);
};

onMounted(() => {
  initData();
});
</script>

<style scoped>
:deep(.hover\:bg-gray-50:hover) {
  --tw-bg-opacity: 0.5;
}

:deep(.dark .hover\:bg-gray-800:hover) {
  --tw-bg-opacity: 0.5;
}

:deep(.el-tooltip__popper) {
  white-space: pre-line;
}

:deep(.deleted-row) {
  opacity: 0.7;
  color: #909399;
}

:deep(.deleted-row .el-tag) {
  text-decoration: none;
}
</style>
