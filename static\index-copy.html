<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>統計分析 API 測試</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-image {
            max-width: 100%;
            height: auto;
            margin-top: 20px;
        }
        .error-message {
            color: red;
            margin-top: 10px;
        }
        .success-message {
            color: green;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">統計分析 API 測試</h1>

        <!-- T 檢定表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>T 檢定</h2>
            </div>
            <div class="card-body">
                <form id="tTestForm">
                    <div class="mb-3">
                        <label for="group1" class="form-label">組別 1 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="group1" value="1.2, 2.3, 3.1">
                    </div>
                    <div class="mb-3">
                        <label for="group2" class="form-label">組別 2 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="group2" value="1.8, 2.5, 3.0">
                    </div>
                    <button type="submit" class="btn btn-primary">執行 T 檢定</button>
                </form>
                <div id="tTestResult" class="mt-3">
                    <div id="tTestError" class="error-message"></div>
                    <div id="tTestSuccess" class="success-message"></div>
                    <pre id="tTestData" class="mt-3"></pre>
                    <img id="tTestPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 假設檢定表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>假設檢定</h2>
            </div>
            <div class="card-body">
                <form id="hypothesisForm">
                    <div class="mb-3">
                        <label for="hypothesisData" class="form-label">數據 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="hypothesisData" value="1.2, 2.3, 3.1">
                    </div>
                    <div class="mb-3">
                        <label for="hypothesisValue" class="form-label">假設值</label>
                        <input type="number" class="form-control" id="hypothesisValue" value="2.0" step="0.1">
                    </div>
                    <button type="submit" class="btn btn-primary">執行假設檢定</button>
                </form>
                <div id="hypothesisResult" class="mt-3">
                    <div id="hypothesisError" class="error-message"></div>
                    <div id="hypothesisSuccess" class="success-message"></div>
                    <pre id="hypothesisData" class="mt-3"></pre>
                    <img id="hypothesisHistPlot" class="result-image">
                    <img id="hypothesisQQPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 線性回歸表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>線性回歸</h2>
            </div>
            <div class="card-body">
                <form id="regressionForm">
                    <div class="mb-3">
                        <label for="xValues" class="form-label">X 值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="xValues" value="1, 2, 3">
                    </div>
                    <div class="mb-3">
                        <label for="yValues" class="form-label">Y 值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="yValues" value="2, 4, 6">
                    </div>
                    <div class="mb-3">
                        <label for="predictX" class="form-label">預測 X 值 (選填)</label>
                        <input type="number" class="form-control" id="predictX" value="4">
                    </div>
                    <button type="submit" class="btn btn-primary">執行線性回歸</button>
                </form>
                <div id="regressionResult" class="mt-3">
                    <div id="regressionError" class="error-message"></div>
                    <div id="regressionSuccess" class="success-message"></div>
                    <pre id="regressionData" class="mt-3"></pre>
                    <img id="regressionPlot" class="result-image">
                    <img id="residualPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 相關性分析表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>相關性分析</h2>
            </div>
            <div class="card-body">
                <form id="correlationForm">
                    <div class="mb-3">
                        <label for="correlationX" class="form-label">X 值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="correlationX" value="1, 2, 3">
                    </div>
                    <div class="mb-3">
                        <label for="correlationY" class="form-label">Y 值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="correlationY" value="2, 4, 6">
                    </div>
                    <button type="submit" class="btn btn-primary">執行相關性分析</button>
                </form>
                <div id="correlationResult" class="mt-3">
                    <div id="correlationError" class="error-message"></div>
                    <div id="correlationSuccess" class="success-message"></div>
                    <pre id="correlationData" class="mt-3"></pre>
                    <img id="correlationPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 卡方檢定表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>卡方檢定</h2>
            </div>
            <div class="card-body">
                <form id="chiSquareForm">
                    <div class="mb-3">
                        <label for="observed" class="form-label">觀察值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="observed" value="10, 20, 30">
                    </div>
                    <div class="mb-3">
                        <label for="expected" class="form-label">期望值 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="expected" value="15, 25, 35">
                    </div>
                    <button type="submit" class="btn btn-primary">執行卡方檢定</button>
                </form>
                <div id="chiSquareResult" class="mt-3">
                    <div id="chiSquareError" class="error-message"></div>
                    <div id="chiSquareSuccess" class="success-message"></div>
                    <pre id="chiSquareData" class="mt-3"></pre>
                    <img id="chiSquarePlot" class="result-image">
                    <img id="contributionPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- ANOVA 分析表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>ANOVA 分析</h2>
            </div>
            <div class="card-body">
                <form id="anovaForm">
                    <div id="anovaGroups">
                        <div class="mb-3">
                            <label class="form-label">組別 1 (用逗號分隔)</label>
                            <input type="text" class="form-control anova-group" value="1.2, 2.3, 3.1, 2.8, 2.5">
                            <input type="text" class="form-control mt-2" placeholder="組別名稱（選填）" value="控制組">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">組別 2 (用逗號分隔)</label>
                            <input type="text" class="form-control anova-group" value="1.8, 2.5, 3.0, 2.9, 2.7">
                            <input type="text" class="form-control mt-2" placeholder="組別名稱（選填）" value="實驗組A">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">組別 3 (用逗號分隔)</label>
                            <input type="text" class="form-control anova-group" value="2.1, 2.8, 3.3, 3.1, 2.9">
                            <input type="text" class="form-control mt-2" placeholder="組別名稱（選填）" value="實驗組B">
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary mb-3" onclick="addAnovaGroup()">添加組別</button>
                    <button type="submit" class="btn btn-primary">執行 ANOVA 分析</button>
                </form>
                <div id="anovaResult" class="mt-3">
                    <div id="anovaError" class="error-message"></div>
                    <div id="anovaSuccess" class="success-message"></div>
                    <pre id="anovaData" class="mt-3"></pre>
                    <img id="anovaBoxPlot" class="result-image">
                    <img id="anovaViolinPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 配對樣本 t 檢定表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>配對樣本 t 檢定</h2>
            </div>
            <div class="card-body">
                <form id="pairedTTestForm">
                    <div class="mb-3">
                        <label for="preTest" class="form-label">前測數據 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="preTest" value="1.2, 2.3, 3.1">
                    </div>
                    <div class="mb-3">
                        <label for="postTest" class="form-label">後測數據 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="postTest" value="1.8, 2.5, 3.0">
                    </div>
                    <button type="submit" class="btn btn-primary">執行配對樣本 t 檢定</button>
                </form>
                <div id="pairedTTestResult" class="mt-3">
                    <div id="pairedTTestError" class="error-message"></div>
                    <div id="pairedTTestSuccess" class="success-message"></div>
                    <pre id="pairedTTestData" class="mt-3"></pre>
                    <img id="pairedTTestBoxPlot" class="result-image">
                    <img id="pairedTTestDiffPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 描述性統計分析表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>描述性統計分析</h2>
            </div>
            <div class="card-body">
                <form id="descriptiveForm">
                    <div class="mb-3">
                        <label for="descriptiveData" class="form-label">數據 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="descriptiveData" value="10.2, 15.3, 20.1, 25.4, 30.2, 18.7, 22.3, 19.8, 24.1, 27.5">
                    </div>
                    <button type="submit" class="btn btn-primary">執行描述性統計分析</button>
                </form>
                <div id="descriptiveResult" class="mt-3">
                    <div id="descriptiveError" class="error-message"></div>
                    <div id="descriptiveSuccess" class="success-message"></div>
                    <pre id="descriptiveData" class="mt-3"></pre>
                    <img id="descriptiveDistPlot" class="result-image">
                    <img id="descriptiveBoxPlot" class="result-image">
                    <img id="descriptiveQQPlot" class="result-image">
                </div>
            </div>
        </div>

        <!-- 存活分析表單 -->
        <div class="card mb-4">
            <div class="card-header">
                <h2>存活分析</h2>
            </div>
            <div class="card-body">
                <form id="survivalForm">
                    <div class="mb-3">
                        <label for="durations" class="form-label">時間數據 (用逗號分隔)</label>
                        <input type="text" class="form-control" id="durations" value="10.2, 15.3, 20.1, 25.4, 30.2">
                    </div>
                    <div class="mb-3">
                        <label for="events" class="form-label">事件指標 (1表示事件發生，0表示刪失)</label>
                        <input type="text" class="form-control" id="events" value="1, 1, 0, 1, 0">
                    </div>
                    <div class="mb-3">
                        <label for="survivalGroups" class="form-label">分組指標 (用逗號分隔，選填)</label>
                        <input type="text" class="form-control" id="survivalGroups" value="1, 1, 2, 2, 2">
                    </div>
                    <div class="mb-3">
                        <label for="groupNames" class="form-label">組別名稱 (用逗號分隔，選填)</label>
                        <input type="text" class="form-control" id="groupNames" value="治療組, 對照組">
                    </div>
                    <button type="submit" class="btn btn-primary">執行存活分析</button>
                </form>
                <div id="survivalResult" class="mt-3">
                    <div id="survivalError" class="error-message"></div>
                    <div id="survivalSuccess" class="success-message"></div>
                    <pre id="survivalData" class="mt-3"></pre>
                    <img id="survivalPlot" class="result-image">
                    <img id="hazardPlot" class="result-image">
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // T 檢定表單提交
            $('#tTestForm').on('submit', function(e) {
                e.preventDefault();
                const group1 = $('#group1').val().split(',').map(x => parseFloat(x.trim()));
                const group2 = $('#group2').val().split(',').map(x => parseFloat(x.trim()));

                $.ajax({
                    url: '/api/v1/statistics/t-test',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 't_test',
                        data: {
                            group1: group1,
                            group2: group2
                        }
                    }),
                    success: function(response) {
                        $('#tTestError').hide();
                        $('#tTestSuccess').show().text('分析完成！');
                        $('#tTestData').html(JSON.stringify(response.result, null, 2));
                        $('#tTestPlot').attr('src', 'data:image/png;base64,' + response.result.plot_base64);
                    },
                    error: function(xhr) {
                        $('#tTestSuccess').hide();
                        $('#tTestError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#tTestData').html('');
                        $('#tTestPlot').attr('src', '');
                    }
                });
            });

            // 假設檢定表單提交
            $('#hypothesisForm').on('submit', function(e) {
                e.preventDefault();
                const data = $('#hypothesisData').val().split(',').map(x => parseFloat(x.trim()));
                const hypothesisValue = parseFloat($('#hypothesisValue').val());

                $.ajax({
                    url: '/api/v1/statistics/hypothesis-test',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'hypothesis_test',
                        data: {
                            data: data,
                            hypothesis_value: hypothesisValue
                        }
                    }),
                    success: function(response) {
                        $('#hypothesisError').hide();
                        $('#hypothesisSuccess').show().text('分析完成！');
                        $('#hypothesisData').html(JSON.stringify(response.result, null, 2));
                        $('#hypothesisHistPlot').attr('src', 'data:image/png;base64,' + response.result.hist_plot);
                        $('#hypothesisQQPlot').attr('src', 'data:image/png;base64,' + response.result.qq_plot);
                    },
                    error: function(xhr) {
                        $('#hypothesisSuccess').hide();
                        $('#hypothesisError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#hypothesisData').html('');
                        $('#hypothesisHistPlot').attr('src', '');
                        $('#hypothesisQQPlot').attr('src', '');
                    }
                });
            });

            // 線性回歸表單提交
            $('#regressionForm').on('submit', function(e) {
                e.preventDefault();
                const xValues = $('#xValues').val().split(',').map(x => parseFloat(x.trim()));
                const yValues = $('#yValues').val().split(',').map(x => parseFloat(x.trim()));
                const predictX = $('#predictX').val() ? parseFloat($('#predictX').val()) : null;

                $.ajax({
                    url: '/api/v1/statistics/linear-regression',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'linear_regression',
                        data: {
                            x: xValues,
                            y: yValues,
                            predict_x: predictX
                        }
                    }),
                    success: function(response) {
                        $('#regressionError').hide();
                        $('#regressionSuccess').show().text('分析完成！');
                        $('#regressionData').html(JSON.stringify(response.result, null, 2));
                        $('#regressionPlot').attr('src', 'data:image/png;base64,' + response.result.plot_base64);
                        $('#residualPlot').attr('src', 'data:image/png;base64,' + response.result.residual_plot);
                    },
                    error: function(xhr) {
                        $('#regressionSuccess').hide();
                        $('#regressionError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#regressionData').html('');
                        $('#regressionPlot').attr('src', '');
                        $('#residualPlot').attr('src', '');
                    }
                });
            });

            // 相關性分析表單提交
            $('#correlationForm').on('submit', function(e) {
                e.preventDefault();
                const xValues = $('#correlationX').val().split(',').map(x => parseFloat(x.trim()));
                const yValues = $('#correlationY').val().split(',').map(x => parseFloat(x.trim()));

                $.ajax({
                    url: '/api/v1/statistics/correlation',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'correlation_analysis',
                        data: {
                            x: xValues,
                            y: yValues
                        }
                    }),
                    success: function(response) {
                        $('#correlationError').hide();
                        $('#correlationSuccess').show().text('分析完成！');
                        $('#correlationData').html(JSON.stringify(response.result, null, 2));
                        $('#correlationPlot').attr('src', 'data:image/png;base64,' + response.result.plot_base64);
                    },
                    error: function(xhr) {
                        $('#correlationSuccess').hide();
                        $('#correlationError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#correlationData').html('');
                        $('#correlationPlot').attr('src', '');
                    }
                });
            });

            // 卡方檢定表單提交
            $('#chiSquareForm').on('submit', function(e) {
                e.preventDefault();
                const observed = $('#observed').val().split(',').map(x => parseFloat(x.trim()));
                const expected = $('#expected').val().split(',').map(x => parseFloat(x.trim()));

                $.ajax({
                    url: '/api/v1/statistics/chi-square',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'chi_square_test',
                        data: {
                            observed: observed,
                            expected: expected
                        }
                    }),
                    success: function(response) {
                        $('#chiSquareError').hide();
                        $('#chiSquareSuccess').show().text('分析完成！');
                        $('#chiSquareData').html(JSON.stringify(response.result, null, 2));
                        $('#chiSquarePlot').attr('src', 'data:image/png;base64,' + response.result.plot_base64);
                        $('#contributionPlot').attr('src', 'data:image/png;base64,' + response.result.contribution_plot);
                    },
                    error: function(xhr) {
                        $('#chiSquareSuccess').hide();
                        $('#chiSquareError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#chiSquareData').html('');
                        $('#chiSquarePlot').attr('src', '');
                        $('#contributionPlot').attr('src', '');
                    }
                });
            });

            // ANOVA 分析相關函數
            function addAnovaGroup() {
                const groupCount = $('.anova-group').length + 1;
                const newGroup = `
                    <div class="mb-3">
                        <label class="form-label">組別 ${groupCount} (用逗號分隔)</label>
                        <input type="text" class="form-control anova-group">
                        <input type="text" class="form-control mt-2" placeholder="組別名稱（選填）" value="組別${groupCount}">
                    </div>
                `;
                $('#anovaGroups').append(newGroup);
            }

            $('#anovaForm').on('submit', function(e) {
                e.preventDefault();
                const groups = [];
                const group_names = [];
                
                $('.anova-group').each(function(index) {
                    const groupData = $(this).val().split(',').map(x => parseFloat(x.trim()));
                    const groupName = $(this).next('input').val().trim();
                    if (groupData.length > 0 && !groupData.some(isNaN)) {
                        groups.push(groupData);
                        group_names.push(groupName || `組別${index + 1}`);
                    }
                });

                if (groups.length < 2) {
                    $('#anovaError').show().text('錯誤：ANOVA 分析需要至少兩組有效數據');
                    return;
                }

                $.ajax({
                    url: '/api/v1/statistics/anova',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'anova',
                        data: {
                            groups: groups,
                            group_names: group_names
                        }
                    }),
                    success: function(response) {
                        $('#anovaError').hide();
                        $('#anovaSuccess').show().text('分析完成！');
                        $('#anovaData').html(JSON.stringify(response.result, null, 2));
                        if (response.result.box_plot) {
                            $('#anovaBoxPlot').attr('src', 'data:image/png;base64,' + response.result.box_plot);
                        }
                        if (response.result.violin_plot) {
                            $('#anovaViolinPlot').attr('src', 'data:image/png;base64,' + response.result.violin_plot);
                        }
                    },
                    error: function(xhr) {
                        $('#anovaSuccess').hide();
                        $('#anovaError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#anovaData').html('');
                        $('#anovaBoxPlot').attr('src', '');
                        $('#anovaViolinPlot').attr('src', '');
                    }
                });
            });

            // 配對樣本 t 檢定表單提交
            $('#pairedTTestForm').on('submit', function(e) {
                e.preventDefault();
                const preTest = $('#preTest').val().split(',').map(x => parseFloat(x.trim()));
                const postTest = $('#postTest').val().split(',').map(x => parseFloat(x.trim()));

                $.ajax({
                    url: '/api/v1/statistics/paired-t-test',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'paired_t_test',
                        data: {
                            pre_test: preTest,
                            post_test: postTest
                        }
                    }),
                    success: function(response) {
                        $('#pairedTTestError').hide();
                        $('#pairedTTestSuccess').show().text('分析完成！');
                        $('#pairedTTestData').html(JSON.stringify(response.result, null, 2));
                        $('#pairedTTestBoxPlot').attr('src', 'data:image/png;base64,' + response.result.box_plot);
                        $('#pairedTTestDiffPlot').attr('src', 'data:image/png;base64,' + response.result.difference_plot);
                    },
                    error: function(xhr) {
                        $('#pairedTTestSuccess').hide();
                        $('#pairedTTestError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#pairedTTestData').html('');
                        $('#pairedTTestBoxPlot').attr('src', '');
                        $('#pairedTTestDiffPlot').attr('src', '');
                    }
                });
            });

            // 描述性統計分析表單提交
            $('#descriptiveForm').on('submit', function(e) {
                e.preventDefault();
                const data = $('#descriptiveData').val().split(',').map(x => parseFloat(x.trim()));

                $.ajax({
                    url: '/api/v1/statistics/descriptive',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'descriptive',
                        data: {
                            data: data
                        }
                    }),
                    success: function(response) {
                        $('#descriptiveError').hide();
                        $('#descriptiveSuccess').show().text('分析完成！');
                        $('#descriptiveData').html(JSON.stringify(response.result, null, 2));
                        $('#descriptiveDistPlot').attr('src', 'data:image/png;base64,' + response.result.distribution_plot);
                        $('#descriptiveBoxPlot').attr('src', 'data:image/png;base64,' + response.result.box_plot);
                        $('#descriptiveQQPlot').attr('src', 'data:image/png;base64,' + response.result.qq_plot);
                    },
                    error: function(xhr) {
                        $('#descriptiveSuccess').hide();
                        $('#descriptiveError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#descriptiveData').html('');
                        $('#descriptiveDistPlot').attr('src', '');
                        $('#descriptiveBoxPlot').attr('src', '');
                        $('#descriptiveQQPlot').attr('src', '');
                    }
                });
            });

            // 存活分析表單提交
            $('#survivalForm').on('submit', function(e) {
                e.preventDefault();
                const durations = $('#durations').val().split(',').map(x => parseFloat(x.trim()));
                const events = $('#events').val().split(',').map(x => parseInt(x.trim()));
                const groups = $('#survivalGroups').val() ? $('#survivalGroups').val().split(',').map(x => parseInt(x.trim())) : null;
                const groupNames = $('#groupNames').val() ? $('#groupNames').val().split(',').map(x => x.trim()) : null;

                $.ajax({
                    url: '/api/v1/statistics/survival',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        method: 'survival_analysis',
                        data: {
                            durations: durations,
                            events: events,
                            groups: groups,
                            group_names: groupNames
                        }
                    }),
                    success: function(response) {
                        $('#survivalError').hide();
                        $('#survivalSuccess').show().text('分析完成！');
                        $('#survivalData').html(JSON.stringify(response.result, null, 2));
                        $('#survivalPlot').attr('src', 'data:image/png;base64,' + response.result.survival_plot);
                        if (response.result.hazard_plot) {
                            $('#hazardPlot').attr('src', 'data:image/png;base64,' + response.result.hazard_plot);
                        }
                    },
                    error: function(xhr) {
                        $('#survivalSuccess').hide();
                        $('#survivalError').show().text('錯誤：' + xhr.responseJSON.detail);
                        $('#survivalData').html('');
                        $('#survivalPlot').attr('src', '');
                        $('#hazardPlot').attr('src', '');
                    }
                });
            });
        });
    </script>
</body>
</html> 