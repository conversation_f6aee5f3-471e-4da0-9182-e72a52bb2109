/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      maxWidth: {
        "8xl": "88rem", // 1408px
      },
      backgroundColor: {
        "dark-mode": "var(--color-dark-bg)",
        "light-mode": "var(--color-light-bg)",
        primary: "var(--color-primary)",
        "primary-dark": "var(--color-primary-dark)",
        "dark-mode-hover": "var(--color-dark-bg-hover)",
        "light-mode-hover": "var(--color-light-bg-hover)",
        "dark-secondary": "#3b3b3b",
        "light-secondary": "var(--color-light-secondary)",
      },
      textColor: {
        "dark-mode": "var(--color-dark-text)",
        "light-mode": "var(--color-light-text)",
        primary: "var(--color-primary)",
        "primary-dark": "var(--color-primary-dark)",
        secondary: "var(--color-secondary)",
        "secondary-dark": "var(--color-secondary-dark)",
        "dark-secondary": "var(--color-dark-secondary)",
        "light-secondary": "var(--color-light-secondary)",
      },
      colors: {
        "node-dark": "#2C2B30",
        "node-dark-border": "#4C4B50",
      },
      borderColor: {
        "node-dark": "#4C4B50",
        "light-mode": "#e4e7ed",
        "dark-mode": "#2C2B30",
      },
    },
  },
  plugins: [require("@tailwindcss/typography")],
};
