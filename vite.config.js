import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import fs from "fs";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

// 創建 JSON 伺服器(用於測試)
function createJsonServer() {
  return {
    name: "json-server",
    configureServer(server) {
      server.middlewares.use(async (req, res, next) => {
        if (req.url.endsWith(".json")) {
          // 支援延遲響應（模擬網絡延遲）
          const delay = req.url.includes("delay=")
            ? parseInt(req.url.split("delay=")[1])
            : 0;

          try {
            // 移除 URL 參數
            const cleanUrl = req.url.split("?")[0];
            const jsonPath = path.join(__dirname, "public", cleanUrl);
            const jsonContent = fs.readFileSync(jsonPath, "utf-8");

            // 支援 CORS
            res.setHeader("Access-Control-Allow-Origin", "*");
            res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            res.setHeader("Access-Control-Allow-Headers", "Content-Type");
            res.setHeader("Content-Type", "application/json");

            // 添加延遲
            if (delay) {
              await new Promise((resolve) => setTimeout(resolve, delay));
            }

            res.end(jsonContent);
            return;
          } catch (error) {
            console.error("JSON 檔案處理錯誤:", error);
            res.statusCode = 404;
            res.end(
              JSON.stringify({
                error: "File not found",
                path: req.url,
              })
            );
            return;
          }
        }
        next();
      });
    },
  };
}
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    createJsonServer(),
    AutoImport({
      imports: ["vue", "vue-router", "pinia"],
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  build: {
    sourcemap: true, // 啟用 Source Maps,
    rollupOptions: {
      output: {
        manualChunks: {
          pdfjs: ["pdfjs-dist"],
        },
      },
    },
  },
  server: {
    sourcemap: true, // 確保在開發環境下生成 Source Maps
    host: "0.0.0.0", // 允許外部設備訪問
    port: 5173, // 可選：設定開發伺服器的埠號
  },
  optimizeDeps: {
    include: ["pdfjs-dist"],
  },
});
